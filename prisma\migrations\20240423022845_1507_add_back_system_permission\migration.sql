
INSERT INTO "Permission" ("description", "permissionKey", "permissionType", "envs") 
VALUES ('Write All Chat Session Data', 'group-*:activate', 'SYSTEM' , '{TEST,PROD}') ON CONFLICT DO NOTHING;


INSERT INTO "RolePermission" ("roleId", "permissionId") SELECT * FROM ( VALUES (
(SELECT id FROM "Role" WHERE "systemName" = 'SUDO' AND "roleType" = 'SYSTEM_DEFAULT'),
(SELECT id FROM "Permission" WHERE "permissionKey" = 'group-*:activate')))
AS v("roleId", "permissionId") WHERE v."roleId" IS NOT NULL AND v."permissionId" IS NOT NULL;