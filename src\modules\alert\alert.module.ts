import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from 'src/providers/prisma/prisma.module';
import { GroupsModule } from '../groups/groups.module';
import { AlertHistoryController } from './alert-history.controller';
import { AlertHistoryService } from './alert-history.service';
import { MailModule } from 'src/providers/mail/mail.module';

@Module({
  imports: [ConfigModule, PrismaModule, GroupsModule, MailModule],
  providers: [AlertHistoryService],
  controllers: [AlertHistoryController],
  exports: [AlertHistoryService],
})
export class AlertModule {}
