apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "bot-builder-backend.fullname" . }}
  labels:
    {{- include "bot-builder-backend.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "bot-builder-backend.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        {{- include "bot-builder-backend.deploymentDateTimeAnnotations" . | nindent 8 }}
      {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "bot-builder-backend.labels" . | nindent 8 }}
	{{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bot-builder-backend.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.targetPort }}
              protocol: TCP
          {{- with  .Values.livenessProbe }}
          livenessProbe:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with  .Values.livenessProbe }}
          readinessProbe:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- with .Values.volumeMounts }}
          volumeMounts:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          envFrom:
            {{- if .Values.enableEnvFromConfigMap }}
            - configMapRef:
                name: {{ include "bot-builder-backend.fullname" . }}-configmap
            {{- end }}
            {{- if .Values.enableEnvFromSecret }}
            - secretRef:
                name: {{ include "bot-builder-backend.fullname" . }}-secret
            {{- end }}
          {{- if not (and (eq .Values.env nil) (eq .Values.additionalEnv nil)) }}
          env:
            {{- with .Values.env }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
            {{- with .Values.additionalEnv }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
          {{- end }}
        {{- range .Values.secondaryContainers }}
        - name: {{ .name }}
          {{- toYaml .spec | nindent 10 }}
        {{- end }}

      {{- with .Values.volumes }}
      volumes:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
