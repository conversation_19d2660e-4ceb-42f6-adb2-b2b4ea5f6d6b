import { Module } from '@nestjs/common';
import { OptionsService } from './options.service';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { OptionsController } from './options.controller';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [PrismaModule, ConfigModule],
  controllers: [OptionsController],
  providers: [OptionsService],
  exports: [OptionsService],
})
export class OptionsModule {}
