import { Prisma } from '@prisma/client';
import { Transform } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';

export class FeatureFlagDTO {
  @IsString()
  @IsNotEmpty()
  key: string;

  @IsString()
  description: string;

  @IsString()
  value?: string;

  @Transform((val) => {
    try {
      return JSON.parse(val);
    } catch (err) {
      throw new ApiException(ErrorCode.INVALID_META_DATA);
    }
  })
  @IsOptional()
  metaData?: JsonValue;

  @IsBoolean()
  isEnabled: boolean;

  @IsBoolean()
  isForClientSide: boolean;

  @IsBoolean()
  @IsOptional()
  isPublic?: boolean;
}

export declare type JsonValue = Prisma.JsonObject;

export class GetOverrideFeatureFlagsOrDefaultDTO {
  key: string;
  description: string;
  value?: string;
  metaData?: JsonValue;
  isEnabled: boolean;
}
