INSERT INTO "PermissionGroupSetting" ("permissionId", "groupType", "isCustomRoleAllowed", "isApiKeyAllowed", "isActiveOnly" ,"updatedAt") VALUES
 ( (SELECT id FROM "Permission" WHERE "permissionKey" = 'group-{groupId}:write-chat-session'), 'FLOW', true, false, false, NOW()) ON CONFLICT DO NOTHING;

INSERT INTO "PermissionGroupSetting" ("permissionId", "groupType", "isCustomRoleAllowed", "isApiKeyAllowed", "isActiveOnly" ,"updatedAt") VALUES
 ( (SELECT id FROM "Permission" WHERE "permissionKey" = 'group-{groupId}:read-chat-session'), 'FLOW', true, false, false, NOW()) ON CONFLICT DO NOTHING;