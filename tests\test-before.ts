import { execSync } from 'child_process';
import { pathExists, writeFile } from 'fs-extra';
import { join } from 'path';
import dotenv from 'dotenv';
dotenv.config();

export const beforeRunningApp = async () => {
  const databaseURL = process.env['DATABASE_URL'];
  if (databaseURL) {
    console.log(execSync('npx prisma db push --preview-feature').toString());
  } else {
    throw new Error('No .env file specified');
  }
};
beforeRunningApp();
