import { ApiProperty, ApiPropertyOptional, IntersectionType } from '@nestjs/swagger';
import { $Enums, Environment, Group, GroupType, Prisma, PrismaClient } from '@prisma/client';
import { Transform, Type } from 'class-transformer';
import {
  ArrayMaxSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { File } from '../llm-models/dto/chat-llm-model.dto';
import { CreateOrUpdateCustomPlanDto } from '../plans/plans.dto';
import { PrismaPaginationDto, PrismaStringFilterDto } from 'src/providers/prisma/prisma.interface';

const AzureSpeechTtsVoices = [
  'af-ZA-AdriNeural',
  'af-ZA-WillemNeural',
  'am-ET-MekdesNeural',
  'am-ET-AmehaNeural',
  'ar-AE-FatimaNeural',
  'ar-AE-HamdanNeural',
  'ar-BH-LailaNeural',
  'ar-BH-AliNeural',
  'ar-DZ-AminaNeural',
  'ar-DZ-IsmaelNeural',
  'ar-EG-SalmaNeural',
  'ar-EG-ShakirNeural',
  'ar-IQ-RanaNeural',
  'ar-IQ-BasselNeural',
  'ar-JO-SanaNeural',
  'ar-JO-TaimNeural',
  'ar-KW-NouraNeural',
  'ar-KW-FahedNeural',
  'ar-LB-LaylaNeural',
  'ar-LB-RamiNeural',
  'ar-LY-ImanNeural',
  'ar-LY-OmarNeural',
  'ar-MA-MounaNeural',
  'ar-MA-JamalNeural',
  'ar-OM-AyshaNeural',
  'ar-OM-AbdullahNeural',
  'ar-QA-AmalNeural',
  'ar-QA-MoazNeural',
  'ar-SA-ZariyahNeural',
  'ar-SA-HamedNeural',
  'ar-SY-AmanyNeural',
  'ar-SY-LaithNeural',
  'ar-TN-ReemNeural',
  'ar-TN-HediNeural',
  'ar-YE-MaryamNeural',
  'ar-YE-SalehNeural',
  'az-AZ-BanuNeural',
  'az-AZ-BabekNeural',
  'bg-BG-KalinaNeural',
  'bg-BG-BorislavNeural',
  'bn-BD-NabanitaNeural',
  'bn-BD-PradeepNeural',
  'bn-IN-TanishaaNeural',
  'bn-IN-BashkarNeural',
  'bs-BA-VesnaNeural',
  'bs-BA-GoranNeural',
  'ca-ES-JoanaNeural',
  'ca-ES-EnricNeural',
  'ca-ES-AlbaNeural',
  'cs-CZ-VlastaNeural',
  'cs-CZ-AntoninNeural',
  'cy-GB-NiaNeural',
  'cy-GB-AledNeural',
  'da-DK-ChristelNeural',
  'da-DK-JeppeNeural',
  'de-AT-IngridNeural',
  'de-AT-JonasNeural',
  'de-CH-LeniNeural',
  'de-CH-JanNeural',
  'de-DE-KatjaNeural',
  'de-DE-ConradNeural',
  'de-DE-AmalaNeural',
  'de-DE-BerndNeural',
  'de-DE-ChristophNeural',
  'de-DE-ElkeNeural',
  'de-DE-GiselaNeural',
  'de-DE-KasperNeural',
  'de-DE-KillianNeural',
  'de-DE-KlarissaNeural',
  'de-DE-KlausNeural',
  'de-DE-LouisaNeural',
  'de-DE-MajaNeural',
  'de-DE-RalfNeural',
  'de-DE-TanjaNeural',
  'el-GR-AthinaNeural',
  'el-GR-NestorasNeural',
  'en-AU-NatashaNeural',
  'en-AU-WilliamNeural',
  'en-AU-AnnetteNeural',
  'en-AU-CarlyNeural',
  'en-AU-DarrenNeural',
  'en-AU-DuncanNeural',
  'en-AU-ElsieNeural',
  'en-AU-FreyaNeural',
  'en-AU-JoanneNeural',
  'en-AU-KenNeural',
  'en-AU-KimNeural',
  'en-AU-NeilNeural',
  'en-AU-TimNeural',
  'en-AU-TinaNeural',
  'en-CA-ClaraNeural',
  'en-CA-LiamNeural',
  'en-GB-SoniaNeural',
  'en-GB-RyanNeural',
  'en-GB-LibbyNeural',
  'en-GB-AbbiNeural',
  'en-GB-AlfieNeural',
  'en-GB-BellaNeural',
  'en-GB-ElliotNeural',
  'en-GB-EthanNeural',
  'en-GB-HollieNeural',
  'en-GB-MaisieNeural',
  'en-GB-NoahNeural',
  'en-GB-OliverNeural',
  'en-GB-OliviaNeural',
  'en-GB-ThomasNeural',
  'en-HK-YanNeural',
  'en-HK-SamNeural',
  'en-IE-EmilyNeural',
  'en-IE-ConnorNeural',
  'en-IN-NeerjaNeural',
  'en-IN-PrabhatNeural',
  'en-KE-AsiliaNeural',
  'en-KE-ChilembaNeural',
  'en-NG-EzinneNeural',
  'en-NG-AbeoNeural',
  'en-NZ-MollyNeural',
  'en-NZ-MitchellNeural',
  'en-PH-RosaNeural',
  'en-PH-JamesNeural',
  'en-SG-LunaNeural',
  'en-SG-WayneNeural',
  'en-TZ-ImaniNeural',
  'en-TZ-ElimuNeural',
  'en-US-JennyMultilingualNeural',
  'en-US-JennyNeural',
  'en-US-GuyNeural',
  'en-US-AriaNeural',
  'en-US-DavisNeural',
  'en-US-AmberNeural',
  'en-US-AnaNeural',
  'en-US-AshleyNeural',
  'en-US-BrandonNeural',
  'en-US-ChristopherNeural',
  'en-US-CoraNeural',
  'en-US-ElizabethNeural',
  'en-US-EricNeural',
  'en-US-JacobNeural',
  'en-US-JaneNeural',
  'en-US-JasonNeural',
  'en-US-MichelleNeural',
  'en-US-MonicaNeural',
  'en-US-NancyNeural',
  'en-US-RogerNeural',
  'en-US-SaraNeural',
  'en-US-SteffanNeural',
  'en-US-TonyNeural',
  'en-US-AIGenerate1Neural',
  'en-US-AIGenerate2Neural',
  'en-US-AndrewNeural',
  'en-US-BlueNeural',
  'en-US-BrianNeural',
  'en-US-EmmaNeural',
  'en-US-JennyMultilingualV2Neural',
  'en-US-RyanMultilingualNeural',
  'en-ZA-LeahNeural',
  'en-ZA-LukeNeural',
  'es-AR-ElenaNeural',
  'es-AR-TomasNeural',
  'es-BO-SofiaNeural',
  'es-BO-MarceloNeural',
  'es-CL-CatalinaNeural',
  'es-CL-LorenzoNeural',
  'es-CO-SalomeNeural',
  'es-CO-GonzaloNeural',
  'es-CR-MariaNeural',
  'es-CR-JuanNeural',
  'es-CU-BelkysNeural',
  'es-CU-ManuelNeural',
  'es-DO-RamonaNeural',
  'es-DO-EmilioNeural',
  'es-EC-AndreaNeural',
  'es-EC-LuisNeural',
  'es-ES-ElviraNeural',
  'es-ES-AlvaroNeural',
  'es-ES-AbrilNeural',
  'es-ES-ArnauNeural',
  'es-ES-DarioNeural',
  'es-ES-EliasNeural',
  'es-ES-EstrellaNeural',
  'es-ES-IreneNeural',
  'es-ES-LaiaNeural',
  'es-ES-LiaNeural',
  'es-ES-NilNeural',
  'es-ES-SaulNeural',
  'es-ES-TeoNeural',
  'es-ES-TrianaNeural',
  'es-ES-VeraNeural',
  'es-GQ-TeresaNeural',
  'es-GQ-JavierNeural',
  'es-GT-MartaNeural',
  'es-GT-AndresNeural',
  'es-HN-KarlaNeural',
  'es-HN-CarlosNeural',
  'es-MX-DaliaNeural',
  'es-MX-JorgeNeural',
  'es-MX-BeatrizNeural',
  'es-MX-CandelaNeural',
  'es-MX-CarlotaNeural',
  'es-MX-CecilioNeural',
  'es-MX-GerardoNeural',
  'es-MX-LarissaNeural',
  'es-MX-LibertoNeural',
  'es-MX-LucianoNeural',
  'es-MX-MarinaNeural',
  'es-MX-NuriaNeural',
  'es-MX-PelayoNeural',
  'es-MX-RenataNeural',
  'es-MX-YagoNeural',
  'es-NI-YolandaNeural',
  'es-NI-FedericoNeural',
  'es-PA-MargaritaNeural',
  'es-PA-RobertoNeural',
  'es-PE-CamilaNeural',
  'es-PE-AlexNeural',
  'es-PR-KarinaNeural',
  'es-PR-VictorNeural',
  'es-PY-TaniaNeural',
  'es-PY-MarioNeural',
  'es-SV-LorenaNeural',
  'es-SV-RodrigoNeural',
  'es-US-PalomaNeural',
  'es-US-AlonsoNeural',
  'es-UY-ValentinaNeural',
  'es-UY-MateoNeural',
  'es-VE-PaolaNeural',
  'es-VE-SebastianNeural',
  'et-EE-AnuNeural',
  'et-EE-KertNeural',
  'eu-ES-AinhoaNeural',
  'eu-ES-AnderNeural',
  'fa-IR-DilaraNeural',
  'fa-IR-FaridNeural',
  'fi-FI-SelmaNeural',
  'fi-FI-HarriNeural',
  'fi-FI-NooraNeural',
  'fil-PH-BlessicaNeural',
  'fil-PH-AngeloNeural',
  'fr-BE-CharlineNeural',
  'fr-BE-GerardNeural',
  'fr-CA-SylvieNeural',
  'fr-CA-JeanNeural',
  'fr-CA-AntoineNeural',
  'fr-CH-ArianeNeural',
  'fr-CH-FabriceNeural',
  'fr-FR-DeniseNeural',
  'fr-FR-HenriNeural',
  'fr-FR-AlainNeural',
  'fr-FR-BrigitteNeural',
  'fr-FR-CelesteNeural',
  'fr-FR-ClaudeNeural',
  'fr-FR-CoralieNeural',
  'fr-FR-EloiseNeural',
  'fr-FR-JacquelineNeural',
  'fr-FR-JeromeNeural',
  'fr-FR-JosephineNeural',
  'fr-FR-MauriceNeural',
  'fr-FR-YvesNeural',
  'fr-FR-YvetteNeural',
  'ga-IE-OrlaNeural',
  'ga-IE-ColmNeural',
  'gl-ES-SabelaNeural',
  'gl-ES-RoiNeural',
  'gu-IN-DhwaniNeural',
  'gu-IN-NiranjanNeural',
  'he-IL-HilaNeural',
  'he-IL-AvriNeural',
  'hi-IN-SwaraNeural',
  'hi-IN-MadhurNeural',
  'hr-HR-GabrijelaNeural',
  'hr-HR-SreckoNeural',
  'hu-HU-NoemiNeural',
  'hu-HU-TamasNeural',
  'hy-AM-AnahitNeural',
  'hy-AM-HaykNeural',
  'id-ID-GadisNeural',
  'id-ID-ArdiNeural',
  'is-IS-GudrunNeural',
  'is-IS-GunnarNeural',
  'it-IT-ElsaNeural',
  'it-IT-IsabellaNeural',
  'it-IT-DiegoNeural',
  'it-IT-BenignoNeural',
  'it-IT-CalimeroNeural',
  'it-IT-CataldoNeural',
  'it-IT-FabiolaNeural',
  'it-IT-FiammaNeural',
  'it-IT-GianniNeural',
  'it-IT-ImeldaNeural',
  'it-IT-IrmaNeural',
  'it-IT-LisandroNeural',
  'it-IT-PalmiraNeural',
  'it-IT-PierinaNeural',
  'it-IT-RinaldoNeural',
  'ja-JP-NanamiNeural',
  'ja-JP-KeitaNeural',
  'ja-JP-AoiNeural',
  'ja-JP-DaichiNeural',
  'ja-JP-MayuNeural',
  'ja-JP-NaokiNeural',
  'ja-JP-ShioriNeural',
  'jv-ID-SitiNeural',
  'jv-ID-DimasNeural',
  'ka-GE-EkaNeural',
  'ka-GE-GiorgiNeural',
  'kk-KZ-AigulNeural',
  'kk-KZ-DauletNeural',
  'km-KH-SreymomNeural',
  'km-KH-PisethNeural',
  'kn-IN-SapnaNeural',
  'kn-IN-GaganNeural',
  'ko-KR-SunHiNeural',
  'ko-KR-InJoonNeural',
  'ko-KR-BongJinNeural',
  'ko-KR-GookMinNeural',
  'ko-KR-JiMinNeural',
  'ko-KR-SeoHyeonNeural',
  'ko-KR-SoonBokNeural',
  'ko-KR-YuJinNeural',
  'lo-LA-KeomanyNeural',
  'lo-LA-ChanthavongNeural',
  'lt-LT-OnaNeural',
  'lt-LT-LeonasNeural',
  'lv-LV-EveritaNeural',
  'lv-LV-NilsNeural',
  'mk-MK-MarijaNeural',
  'mk-MK-AleksandarNeural',
  'ml-IN-SobhanaNeural',
  'ml-IN-MidhunNeural',
  'mn-MN-YesuiNeural',
  'mn-MN-BataaNeural',
  'mr-IN-AarohiNeural',
  'mr-IN-ManoharNeural',
  'ms-MY-YasminNeural',
  'ms-MY-OsmanNeural',
  'mt-MT-GraceNeural',
  'mt-MT-JosephNeural',
  'my-MM-NilarNeural',
  'my-MM-ThihaNeural',
  'nb-NO-PernilleNeural',
  'nb-NO-FinnNeural',
  'nb-NO-IselinNeural',
  'ne-NP-HemkalaNeural',
  'ne-NP-SagarNeural',
  'nl-BE-DenaNeural',
  'nl-BE-ArnaudNeural',
  'nl-NL-FennaNeural',
  'nl-NL-MaartenNeural',
  'nl-NL-ColetteNeural',
  'pl-PL-AgnieszkaNeural',
  'pl-PL-MarekNeural',
  'pl-PL-ZofiaNeural',
  'ps-AF-LatifaNeural',
  'ps-AF-GulNawazNeural',
  'pt-BR-FranciscaNeural',
  'pt-BR-AntonioNeural',
  'pt-BR-BrendaNeural',
  'pt-BR-DonatoNeural',
  'pt-BR-ElzaNeural',
  'pt-BR-FabioNeural',
  'pt-BR-GiovannaNeural',
  'pt-BR-HumbertoNeural',
  'pt-BR-JulioNeural',
  'pt-BR-LeilaNeural',
  'pt-BR-LeticiaNeural',
  'pt-BR-ManuelaNeural',
  'pt-BR-NicolauNeural',
  'pt-BR-ValerioNeural',
  'pt-BR-YaraNeural',
  'pt-PT-RaquelNeural',
  'pt-PT-DuarteNeural',
  'pt-PT-FernandaNeural',
  'ro-RO-AlinaNeural',
  'ro-RO-EmilNeural',
  'ru-RU-SvetlanaNeural',
  'ru-RU-DmitryNeural',
  'ru-RU-DariyaNeural',
  'si-LK-ThiliniNeural',
  'si-LK-SameeraNeural',
  'sk-SK-ViktoriaNeural',
  'sk-SK-LukasNeural',
  'sl-SI-PetraNeural',
  'sl-SI-RokNeural',
  'so-SO-UbaxNeural',
  'so-SO-MuuseNeural',
  'sq-AL-AnilaNeural',
  'sq-AL-IlirNeural',
  'sr-Latn-RS-NicholasNeural',
  'sr-Latn-RS-SophieNeural',
  'sr-RS-SophieNeural',
  'sr-RS-NicholasNeural',
  'su-ID-TutiNeural',
  'su-ID-JajangNeural',
  'sv-SE-SofieNeural',
  'sv-SE-MattiasNeural',
  'sv-SE-HilleviNeural',
  'sw-KE-ZuriNeural',
  'sw-KE-RafikiNeural',
  'sw-TZ-RehemaNeural',
  'sw-TZ-DaudiNeural',
  'ta-IN-PallaviNeural',
  'ta-IN-ValluvarNeural',
  'ta-LK-SaranyaNeural',
  'ta-LK-KumarNeural',
  'ta-MY-KaniNeural',
  'ta-MY-SuryaNeural',
  'ta-SG-VenbaNeural',
  'ta-SG-AnbuNeural',
  'te-IN-ShrutiNeural',
  'te-IN-MohanNeural',
  'th-TH-PremwadeeNeural',
  'th-TH-NiwatNeural',
  'th-TH-AcharaNeural',
  'tr-TR-EmelNeural',
  'tr-TR-AhmetNeural',
  'uk-UA-PolinaNeural',
  'uk-UA-OstapNeural',
  'ur-IN-GulNeural',
  'ur-IN-SalmanNeural',
  'ur-PK-UzmaNeural',
  'ur-PK-AsadNeural',
  'uz-UZ-MadinaNeural',
  'uz-UZ-SardorNeural',
  'vi-VN-HoaiMyNeural',
  'vi-VN-NamMinhNeural',
  'wuu-CN-XiaotongNeural',
  'wuu-CN-YunzheNeural',
  'yue-CN-XiaoMinNeural',
  'yue-CN-YunSongNeural',
  'zh-CN-XiaoxiaoNeural',
  'zh-CN-YunxiNeural',
  'zh-CN-YunjianNeural',
  'zh-CN-XiaoyiNeural',
  'zh-CN-YunyangNeural',
  'zh-CN-XiaochenNeural',
  'zh-CN-XiaohanNeural',
  'zh-CN-XiaomengNeural',
  'zh-CN-XiaomoNeural',
  'zh-CN-XiaoqiuNeural',
  'zh-CN-XiaoruiNeural',
  'zh-CN-XiaoshuangNeural',
  'zh-CN-XiaoxuanNeural',
  'zh-CN-XiaoyanNeural',
  'zh-CN-XiaoyouNeural',
  'zh-CN-XiaozhenNeural',
  'zh-CN-YunfengNeural',
  'zh-CN-YunhaoNeural',
  'zh-CN-YunxiaNeural',
  'zh-CN-YunyeNeural',
  'zh-CN-YunzeNeural',
  'zh-CN-XiaorouNeural',
  'zh-CN-YunjieNeural',
  'zh-CN-guangxi-YunqiNeural',
  'zh-CN-henan-YundengNeural',
  'zh-CN-liaoning-XiaobeiNeural',
  'zh-CN-shaanxi-XiaoniNeural',
  'zh-CN-shandong-YunxiangNeural',
  'zh-CN-sichuan-YunxiNeural',
  'zh-HK-HiuMaanNeural',
  'zh-HK-WanLungNeural',
  'zh-HK-HiuGaaiNeural',
  'zh-TW-HsiaoChenNeural',
  'zh-TW-YunJheNeural',
  'zh-TW-HsiaoYuNeural',
  'zu-ZA-ThandoNeural',
  'zu-ZA-ThembaNeural',
];

const AzureSpeechSttRecognitionLanguage = [
  'af-ZA',
  'am-ET',
  'ar-AE',
  'ar-BH',
  'ar-DZ',
  'ar-EG',
  'ar-IL',
  'ar-IQ',
  'ar-JO',
  'ar-KW',
  'ar-LB',
  'ar-LY',
  'ar-MA',
  'ar-OM',
  'ar-PS',
  'ar-QA',
  'ar-SA',
  'ar-SY',
  'ar-TN',
  'ar-YE',
  'az-AZ',
  'bg-BG',
  'bn-IN',
  'bs-BA',
  'ca-ES',
  'cs-CZ',
  'cy-GB',
  'da-DK',
  'de-AT',
  'de-CH',
  'de-DE',
  'el-GR',
  'en-AU',
  'en-CA',
  'en-GB',
  'en-GH',
  'en-HK',
  'en-IE',
  'en-IN',
  'en-KE',
  'en-NG',
  'en-NZ',
  'en-PH',
  'en-SG',
  'en-TZ',
  'en-US',
  'en-ZA',
  'es-AR',
  'es-BO',
  'es-CL',
  'es-CO',
  'es-CR',
  'es-CU',
  'es-DO',
  'es-EC',
  'es-ES',
  'es-GQ',
  'es-GT',
  'es-HN',
  'es-MX',
  'es-NI',
  'es-PA',
  'es-PE',
  'es-PR',
  'es-PY',
  'es-SV',
  'es-US',
  'es-UY',
  'es-VE',
  'et-EE',
  'eu-ES',
  'fa-IR',
  'fi-FI',
  'fil-PH',
  'fr-BE',
  'fr-CA',
  'fr-CH',
  'fr-FR',
  'ga-IE',
  'gl-ES',
  'gu-IN',
  'he-IL',
  'hi-IN',
  'hr-HR',
  'hu-HU',
  'hy-AM',
  'id-ID',
  'is-IS',
  'it-CH',
  'it-IT',
  'ja-JP',
  'jv-ID',
  'ka-GE',
  'kk-KZ',
  'km-KH',
  'kn-IN',
  'ko-KR',
  'lo-LA',
  'lt-LT',
  'lv-LV',
  'mk-MK',
  'ml-IN',
  'mn-MN',
  'mr-IN',
  'ms-MY',
  'mt-MT',
  'my-MM',
  'nb-NO',
  'ne-NP',
  'nl-BE',
  'nl-NL',
  'pa-IN',
  'pl-PL',
  'ps-AF',
  'pt-BR',
  'pt-PT',
  'ro-RO',
  'ru-RU',
  'si-LK',
  'sk-SK',
  'sl-SI',
  'so-SO',
  'sq-AL',
  'sr-RS',
  'sv-SE',
  'sw-KE',
  'sw-TZ',
  'ta-IN',
  'te-IN',
  'th-TH',
  'tr-TR',
  'uk-UA',
  'ur-IN',
  'uz-UZ',
  'vi-VN',
  'wuu-CN',
  'yue-CN',
  'zh-CN',
  'zh-CN-shandong',
  'zh-CN-sichuan',
  'zh-HK',
  'zh-TW',
  'zu-ZA',
];

export class CreateGroupDto {
  @IsString()
  @IsNotEmpty()
  name!: string;

  @IsOptional()
  @IsEnum(GroupType)
  groupType?: GroupType;

  @IsString()
  @IsNotEmpty()
  businessUnit: string;

  @IsString()
  @IsNotEmpty()
  department: string;

  @IsString()
  @IsNotEmpty()
  ccc: string;

  @IsString()
  @IsNotEmpty()
  description: string;
}

export class UpdateGroupDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  businessUnit: string;

  @IsString()
  @IsNotEmpty()
  department: string;

  @IsString()
  @IsNotEmpty()
  ccc: string;

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsString()
  @IsOptional()
  profilePictureUrl: string;
}

export class ActivateGroupDto {
  @IsBoolean()
  @IsNotEmpty()
  active: boolean;
}

export class ReplaceGroupDto {
  @IsBoolean()
  @IsNotEmpty()
  autoJoinDomain!: boolean;

  @IsBoolean()
  @IsNotEmpty()
  forceTwoFactor!: boolean;

  @IsArray()
  @IsNotEmpty()
  ipRestrictions!: string;

  @IsString()
  @IsNotEmpty()
  name!: string;

  @IsBoolean()
  @IsNotEmpty()
  onlyAllowDomain!: boolean;

  @IsString()
  @IsNotEmpty()
  profilePictureUrl!: string;

  @IsObject()
  @IsNotEmpty()
  attributes!: Record<string, any>;
}

export class ProcessFileDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'Document id' })
  docId: string;
}

export class AzureSpeechTtsDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'The text to be synthesized.' })
  text: string;

  @IsIn(AzureSpeechTtsVoices)
  @ApiProperty({
    description: 'The voice name to be synthesized with.',
    // supported voices: https://learn.microsoft.com/en-us/azure/ai-services/speech-service/language-support?tabs=tts
    enum: AzureSpeechTtsVoices,
  })
  voice_name: string;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ description: 'stream response', default: false })
  stream?: boolean;
}

export class AzureSpeechSttDto {
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: 'The filename responded by UploadTempFile API',
    deprecated: true,
  })
  file?: string;

  @IsArray()
  @IsOptional()
  @ArrayMaxSize(1)
  @ApiPropertyOptional({
    type: [File],
    description: 'Support single file only for stt',
    example: {
      value: [
        {
          data_source: 'upload',
          file_id: '1234',
        },
      ],
      summary: 'Chat with file example',
      description: 'Chat with file example',
    },
  })
  files?: File[];

  @IsIn(AzureSpeechSttRecognitionLanguage)
  @IsOptional()
  @ApiProperty({
    description: 'The name of spoken language to be recognized.',
    // supported languages:
    enum: AzureSpeechSttRecognitionLanguage,
  })
  recognition_language?: string;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({
    description:
      'Set to true if want to auto detect the language. Will ignore `recognition_language` if true.',
  })
  auto_detect_source_language?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ description: 'stream response', default: false })
  stream?: boolean;
}

export class PlanRequestDto {
  @IsArray()
  // @ValidateNested({ each: true })
  subscribedPlanIds: number[];
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateOrUpdateCustomPlanDto)
  customPlans: CreateOrUpdateCustomPlanDto[];
}

export class GroupDto implements Group {
  name: string;
  description: string;
  autoJoinDomain: boolean;
  createdAt: Date;
  forceTwoFactor: boolean;
  id: number;
  ipRestrictions: string;
  env: $Enums.Environment;
  onlyAllowDomain: boolean;
  profilePictureUrl: string;
  attributes: Prisma.JsonValue;
  ccc: string;
  department: string;
  businessUnit: string;
  updatedAt: Date;
  parentId: number;
  pairId: number;
  deletedAt: Date;
  groupType: $Enums.GroupType;
  createdByUserId: number;
  isDeprecated: boolean;
}

export class GroupListRes {
  list: GroupDto[];
  count: number;
}

export class PlanRequestOperateDto {
  @IsOptional()
  @IsString()
  crNum?: string;

  @IsOptional()
  @IsString()
  rejectReason?: string;
}

export class CreateBotResourcePlanDto {
  @IsString()
  @IsNotEmpty()
  @Transform((val: string) => val.trim())
  name: string;
  @IsNumber()
  resourceId: number;
  @IsString()
  description: string;
  @IsNumber()
  quotaValue: number;
  @IsOptional()
  @IsEnum(Environment)
  env?: Environment;
}

export class UpdateBotResourcePlanDto {
  @IsNumber()
  planId: number;
  @IsString()
  name: string;
  @IsString()
  description: string;
  @IsNumber()
  quotaValue: number;
}

export class SetBotResourcePlanDefaultDto {
  @IsNumber()
  planId: number;
}

export class SetBotResourcePlanActivateDto {
  @IsNumber()
  planId: number;
}

export class GroupNameResponseDto {
  id: number;
  name: string;
  env: string;
}

export type GroupInfo = { isEnableConnectAPI: boolean } & Group;




export class GroupLLmModelQueueWhereDto {
  @IsOptional()
  @Type(() => Boolean)
  makeLiveToPublic: boolean;
}

export class GroupsWhereDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => PrismaStringFilterDto)
  name?: PrismaStringFilterDto;

  @IsEnum($Enums.GroupType)
  @IsOptional()
  groupType?: GroupType;

  @ValidateIf((o) => o.groupType && o.groupType == GroupType.BOT)
  @IsOptional()
  @ValidateNested()
  @Type(() => GroupLLmModelQueueWhereDto)
  llmModel?: GroupLLmModelQueueWhereDto;

  @IsOptional()
  @IsEnum(Environment)
  env?: Environment;
}

export class GroupsQueueDto extends PrismaPaginationDto<Group> {
  @IsOptional()
  fromDate?: string;

  @IsOptional()
  toDate?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => GroupsWhereDto)
  where?: GroupsWhereDto;
}
