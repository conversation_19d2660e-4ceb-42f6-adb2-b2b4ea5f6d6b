-- CreateEnum
CREATE TYPE "PiiFileStatus" AS ENUM ('SCANNING', 'SCANNING_FAILED', 'COMPLETED');

-- AlterTable
ALTER TABLE "ModelFile" ADD COLUMN     "detectedPii" TEXT,
ADD COLUMN     "errCode" TEXT,
ADD COLUMN     "fullScanReportCreatedAt" TIMESTAMP(3),
ADD COLUMN     "fullScanReportPath" TEXT,
ADD COLUMN     "fullScanReportUpdatedAt" TIMESTAMP(3),
ADD COLUMN     "hasPii" TEXT,
ADD COLUMN     "piiFileStatus" "PiiFileStatus";


--  Data patch will be performed for retrieving value from field "errMsg" to update "hasPII" and "detectedPII" and "errCode" in existing records
update "ModelFile" set "hasPii" = 'PENDING' where status  = 'UPLOADED' and "hasPii"  is null;

update "ModelFile" set "hasPii" = 'NO' where ("errorMsg" is null or "errorMsg" = '') and "hasPii"  is null and status <> 'COMPLETED';

UPDATE "ModelFile" mf
SET "detectedPii"  = subquery.extracted_content,  "hasPii" ='YES'
FROM (
  SELECT id,
         string_agg(match, ',') AS extracted_content
  FROM (
    SELECT id,
           unnest(regexp_matches("errorMsg", '\{(.*?)\}', 'g')) AS match
    FROM "ModelFile"
    WHERE "errorMsg" IS NOT NULL AND "hasPii" IS NULL
  ) AS subquery_inner
  GROUP BY id
) AS subquery
WHERE mf.id = subquery.id;

update "ModelFile" set "hasPii" = 'YES' where "errorMsg" ='Potential PII detected' and "hasPii"  is null;

update "ModelFile" set "hasPii" = 'PENDING', "errCode" = 'DP001002' where "errorMsg" like 'empty text%' and "hasPii" is null;

update "ModelFile" set "hasPii" = 'ERROR', "errCode" = 'DP002502' where "errorMsg" like 'Error in scanning PII data%' and "hasPii" is null;

update "ModelFile" set "hasPii" = 'ERROR', "errCode" = 'DP002502' where "errorMsg" like 'HTTPConnectionPool(%' and "hasPii" is null;

update "ModelFile" set "hasPii" = 'PENDING', "errCode" = 'DP001003' where "errorMsg" like '%is not support, supported formats%' and "hasPii" is null;

update "ModelFile" set "hasPii" = 'PENDING', "errCode" = 'DP001003' where "errorMsg" like '%codec can''t decode%' and "hasPii" is null;

update "ModelFile" set "hasPii" = 'PENDING', "errCode" = 'DP001005' where "errorMsg" like 'no. of pages / rows over limit%' and "hasPii" is null;

update "ModelFile" set "hasPii" = 'PENDING', "errCode" = 'DP001004' where "errorMsg" in ('PyCryptodome is required for AES algorithm','No columns to parse from file') and "hasPii" is null;

update "ModelFile" set "hasPii" = 'PENDING', "errCode" = 'DP001002' where "errorMsg" ='no text' and "hasPii" is null;

update "ModelFile" set "hasPii" = 'PENDING', "errCode" = 'DP001004' where "errorMsg" like 'Invalid Elementary Object starting%' and "hasPii" is null;

update "ModelFile" set "hasPii" = 'PENDING', "errCode" = 'DP001004' where "errorMsg" in ('log file is not preferred','Stream has ended unexpectedly','''/Root''') and "hasPii" is null;

update "ModelFile"  set "hasPii" = null where "createdAt" <= '2023-11-29 12:16:59' and status in ('PROCESSING','COMPLETED','VERIFY_SUCCESS');

