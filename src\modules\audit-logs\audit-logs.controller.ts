import { Controller, Get, Query } from '@nestjs/common';
import { AuditLog } from '@prisma/client';
import { OptionalIntPipe } from '../../pipes/optional-int.pipe';
import { WherePipe } from '../../pipes/where.pipe';
import { Scopes } from '../auth/scope.decorator';
import { AuditLogsService } from './audit-logs.service';

@Controller('audit-logs')
export class AuditLogController {
  constructor(private auditLogsService: AuditLogsService) {}

  /** Get all audit logs*/
  @Get()
  @Scopes('system:read-audit-log')
  async getAll(
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
  ): Promise<{ list: AuditLog[]; count: number }> {
    const list = await this.auditLogsService.getAuditLogs({
      skip,
      take,
      orderBy: { createdAt: 'desc' },
      where,
    });
    const count = await this.auditLogsService.getCount(where);
    return { list, count };
  }
}
