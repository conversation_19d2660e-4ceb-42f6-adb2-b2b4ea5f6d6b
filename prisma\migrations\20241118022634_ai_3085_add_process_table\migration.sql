-- CreateEnum
CREATE TYPE "ModelPriceProcessStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'ERROR');

-- CreateTable
CREATE TABLE "ModelPriceProcess" (
    "id" SERIAL NOT NULL,
    "year" INTEGER NOT NULL,
    "month" INTEGER NOT NULL,
    "groupId" INTEGER NOT NULL,
    "status" "ModelPriceProcessStatus" NOT NULL,
    "metadata" JSONB NOT NULL,
    "createdBy" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedBy" INTEGER NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ModelPriceProcess_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ModelPriceProcess_year_month_idx" ON "ModelPriceProcess"("year", "month");

-- CreateIndex
CREATE INDEX "ModelPriceProcess_year_month_groupId_idx" ON "ModelPriceProcess"("year", "month", "groupId");
