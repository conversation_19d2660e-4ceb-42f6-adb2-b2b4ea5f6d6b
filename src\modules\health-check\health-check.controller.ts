import { Controller, Get, Header } from '@nestjs/common';
import { Public } from '../auth/public.decorator';
import { HealthCheckService } from './health-check.service';
import { HealthCheckResponse, HealthCheckStatus } from './interface';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@Controller('health-check')
@ApiBearerAuth('bearer-auth')
@ApiTags('Health Check')
export class HealthCheckController {
  constructor(private readonly healthCheckService: HealthCheckService) {}

  @Get()
  @Public()
  @Header('Cache-Control', 'no-cache')
  async healthCheck(): Promise<HealthCheckResponse> {
    return { status: HealthCheckStatus.OK, serverTime: new Date() };
  }

  @Get('sso')
  @Public()
  @Header('Cache-Control', 'no-cache')
  async ssoHealthCheck(): Promise<HealthCheckResponse> {
    return this.healthCheckService.ssoHealthCheck();
  }

  @Get('gravitee/management')
  @Public()
  @Header('Cache-Control', 'no-cache')
  async graviteeManagementHealthCheck(): Promise<HealthCheckResponse> {
    return this.healthCheckService.graviteeManagementHealthCheck();
  }

  @Get('llm-backend')
  @Public()
  @Header('Cache-Control', 'no-cache')
  async llmBackendHealthCheck(): Promise<HealthCheckResponse> {
    return this.healthCheckService.llmBackendHealthCheck();
  }
}
