import { Module } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { EmbeddingsController } from './embeddings.controller';

import { LLMBackendModule } from '../../providers/llm-backend/llm-backend.module';
import { ApiKeysModule } from '../api-keys/api-keys.module';
import { LLMModelsModule } from '../llm-models/llm-models.module';

@Module({
  imports: [PrismaModule, ApiKeysModule, LLMBackendModule, LLMModelsModule],
  controllers: [EmbeddingsController],
  providers: [],
})
export class EmbeddingsModule {}
