import { IsNotEmpty, <PERSON>N<PERSON>ber, IsString } from 'class-validator';

export class CreateInsightCaseDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  query: string;

  @IsString()
  @IsNotEmpty()
  searchEngineSlug: string;

  @IsString()
  @IsNotEmpty()
  llmEngineSlug: string;

  @IsNumber()
  timeFrame: number;

  @IsNumber()
  maxSearchNum: number;
}

export class UpdateInsightCaseDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  query: string;

  @IsString()
  @IsNotEmpty()
  searchEngineSlug: string;

  @IsString()
  @IsNotEmpty()
  llmEngineSlug: string;

  @IsNumber()
  timeFrame: number;

  @IsNumber()
  maxSearchNum: number;
}

export class CreateInsightReportDto {
  @IsNumber()
  insightCaseId: number;
}

export class InsightCaseResDto {
  id: number;
  name: string;
  query: string;
  searchEngineSlug: string;
  llmEngineSlug: string;
  domain: string | null;
  timeFrame: number;
  maxSearchNum: number;
  createdBy: number;
  createdAt: Date;
  updatedAt: Date;
  updatedBy: number | null;
  groupId: number;
  latestStatus: string | null;
  latestArticleDate: Date | null;
  latestTotalCompletionToken: number | null;
}

export class InsightReportResDto {
  id: number;
  insightId: number;
  completionTokenConsumed: number | null;
  promptTokenConsumed: number | null;
  recipientNum: number | null;
  triggeredBy: number;
  triggeredAt: Date;
  status: string;
  completedAt: Date | null;
  s3Path: string | null;
  summary: string | null;
  duration: number | null;
  errorMsg: string | null;
  groupId: number;
  createdAt: Date;
  updatedAt: Date;
  jobId: string;
}

export class InsightReportStatusResDto {
  status: string;
  completedCount: number;
  failedCount: number;
}
export class InsightReportDetailResDto {
  id: number;
  title: string;
  link: string;
  summary: string | null;
  relevantScore: number | null;
  date: Date | null;
  promptTokenConsumed: number | null;
  completionTokenConsumed: number | null;
  duration: number | null;
  insightReportId: number;
  status: string;
  jobId: string;
  errorMsg: string | number;
}
