import { IsIn, IsOptional, IsString } from 'class-validator';
import { Usage } from 'src/providers/llm-backend/llm-backend.interface';

export type FileVerifyStatus = 'verify_success' | 'verify_failed' | 'completed' | 'processing';

export class UpdateModelFileStatusDto {
  @IsIn(['verify_success', 'verify_failed', 'completed', 'processing'])
  @IsString()
  status?: FileVerifyStatus;

  @IsString()
  @IsOptional()
  error?: string;

  @IsString()
  @IsOptional()
  errCode?: string;

  @IsString()
  @IsOptional()
  hasPii?: string;

  @IsString()
  @IsOptional()
  detectedPii?: string;

  @IsOptional()
  usage?: Usage;
}
