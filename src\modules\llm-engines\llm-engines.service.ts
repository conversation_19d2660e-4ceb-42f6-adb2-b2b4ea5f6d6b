import { Prisma, Environment, LlmEngine, GroupType } from '@prisma/client';
import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import moment from 'moment';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import {
  GROUP_LLM_ENGINE_COMPLETION_TOKEN_USAGE_KEY,
  GROUP_LLM_ENGINE_PROMPT_TOKEN_USAGE_KEY,
} from 'src/providers/redis/redis.constants';
import { RedisService } from 'src/providers/redis/redis.service';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { ResourceQuotaService } from '../auth/quota.interface';
import {
  getPlanIsEnabledDto,
  getResourceUsageDto,
  PlanResourceHandleService,
  ResourceUsageDto,
} from '../plans/plans.dto';
import { ResourceQuotaDto } from '../scope/scope.dto';
import { isAfter, subWeeks } from 'date-fns';
import { ScopeService } from '../scope/scope.service';
import { UserRequest } from '../auth/auth.interface';
import { LabelsService } from '../labels/labels.service';
import { BatchUpdateLlmEngineDto, CreateLlmEngineDto, UpdateLlmEngineDto } from './llm-engines.dto';
import { QuotaService } from '../quotas/quota.service';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { ConfigService } from '@nestjs/config';
import { ResizeImageService } from '../../providers/resize-image/resize-image.service';
import { S3Service } from '../../providers/s3/s3.service';
import { isJSON } from 'src/helpers/string';
import { PatchLabelsDto } from '../labels/dto/patch-labels.dto';
import { LabelEntityType, LabelType } from '.prisma/client';
import { TokensService } from 'src/providers/tokens/tokens.service';

@Injectable()
export class LlmEnginesService implements ResourceQuotaService, PlanResourceHandleService {
  private logger = new Logger(LlmEnginesService.name);
  constructor(
    private scopeService: ScopeService,
    private prisma: PrismaService,
    private redis: RedisService,
    private featureFlagService: FeatureFlagService,
    private readonly labelsService: LabelsService,
    private readonly quotaService: QuotaService,
    private configService: ConfigService,
    private resizeImageService: ResizeImageService,
    private s3Service: S3Service,
    private tokensService: TokensService,
  ) {}

  async findAll(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.LlmEngineWhereUniqueInput;
    where?: Prisma.LlmEngineWhereInput & {
      labels?: { in: string[] };
      categories?: { in: string[] };
      isMostlyUsed?: boolean;
      isNew?: boolean;
    };
    orderBy?: Prisma.LlmEngineOrderByWithRelationInput;
    includesInactive?: boolean;
  }): Promise<LlmEngine[]> {
    try {
      const { skip, take, cursor, where, orderBy } = params;
      const newWhere: Prisma.LlmEngineWhereInput =
        this.labelsService.formatLabelsAndCategoriesFilter('LLM_ENGINE', where);
      if (!params.includesInactive) {
        newWhere.isActive = true;
      }
      // eslint-disable-next-line prefer-const
      let { where: _newWhere, summaryAll } = await this.generateWhereQuery(newWhere);
      const llmEngines = await this.prisma.llmEngine.findMany({
        include: { ...(this.labelsService.getLabelsPrismaQuery('LLM_ENGINE') as any) },
        skip,
        take,
        cursor,
        where: _newWhere,
        ...(orderBy ? { orderBy } : { orderBy: { sequence: 'asc' } }),
      });
      if (llmEngines.length === 0) return [];
      if (!summaryAll) {
        summaryAll = await this.getMostlyUsedLLMnEngines();
      }
      const llmSlugList = [];
      llmEngines.forEach((item) => {
        llmSlugList.push(item.slug);
      });
      const llmUsedNumberList = await this.getLLMEnginesUsedNumbers(llmSlugList);
      const getLlmEngines = llmEngines.map(async (llmEngine) => ({
        ...llmEngine,
        isMostlyUsed: summaryAll.find((item) => item.engineSlug === llmEngine.slug) != undefined,
        used:
          llmUsedNumberList.find((item) => item.engineSlug === llmEngine.slug)?._sum?.value ?? 0,
        isNew: isAfter(llmEngine.createdAt, subWeeks(new Date(), 2)),
        ...this.labelsService.formatLabelsAndCategoriesData(llmEngine),
      }));
      return await Promise.all(getLlmEngines);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async count(params: {
    where?: Prisma.LlmEngineWhereInput & {
      labels?: { in: string[] };
      categories?: { in: string[] };
    };
    groupType?: GroupType;
    includesInactive?: boolean;
  }) {
    const { where } = params;
    const newWhere: Prisma.LlmEngineWhereInput = this.labelsService.formatLabelsAndCategoriesFilter(
      'LLM_ENGINE',
      where,
    );
    if (!params.includesInactive) {
      newWhere.isActive = true;
    }
    const { where: _newWhere } = await this.generateWhereQuery(newWhere);

    return this.prisma.llmEngine.count({ where: _newWhere });
  }

  /**
   * @description generate the calculated query for the llm engine with the given where condition and return the where and summaryAll
   * @param where
   * @returns { where, summaryAll }
   */
  public async generateWhereQuery(
    where: Prisma.LlmEngineWhereInput & { isMostlyUsed?: boolean; isNew?: boolean },
  ) {
    let summaryAll;
    if (where?.isMostlyUsed) {
      summaryAll = await this.getMostlyUsedLLMnEngines();
      where.slug = {
        in: summaryAll.map((item) => item.engineSlug),
      };
      delete where.isMostlyUsed;
    }
    if (where.isNew) {
      where.createdAt = {
        gte: subWeeks(new Date(), 2),
      };
      delete where.isNew;
    }
    let _where = this.orConditionCheck(where as Record<string, number | string>);
    if (
      _where?.OR &&
      (_where.OR as Array<Prisma.LlmEngineWhereInput>)?.filter(
        (item) => item?.['isNew'] || item?.['isMostlyUsed'],
      ).length != 0
    ) {
      const newOrArray = [];
      for (const orQuery of _where.OR as Array<any>) {
        if (!(orQuery?.['isNew'] || orQuery?.['isMostlyUsed'])) {
          newOrArray.push(orQuery);
        }
        if (orQuery?.isMostlyUsed) {
          summaryAll = await this.getMostlyUsedLLMnEngines();
          newOrArray.push({
            slug: {
              in: summaryAll.map((item) => item.engineSlug),
            },
          });
        }
        if (orQuery?.isNew) {
          newOrArray.push({
            createdAt: {
              gte: subWeeks(new Date(), 2),
            },
          });
        }
      }
      _where = { ..._where, OR: newOrArray };
    }
    return { where: _where, summaryAll };
  }

  async create(createLlmEngineDto: CreateLlmEngineDto) {
    const data: Prisma.LlmEngineCreateInput = {
      ...createLlmEngineDto,
    };
    return this.prisma.llmEngine.create({ data });
  }

  async update(engineId: number, updateLlmEngineDto: UpdateLlmEngineDto, userReq: UserRequest) {
    try {
      const updateLlmEngineInput = { ...updateLlmEngineDto, entityLabels: undefined };
      const data: Prisma.LlmEngineUpdateInput = {
        ...updateLlmEngineInput,
      };

      const { needRemoveIds, createEntityLabels, createLabels } =
        this.labelsService.formatPatchLabelsDto(
          updateLlmEngineDto as any,
          userReq,
          'LLM_ENGINE',
          engineId,
        );
      const updated = await this.prisma.$transaction(async (tx) => {
        await this.labelsService.patchUpdateWithTransaction(
          { needRemoveIds, createEntityLabels, createLabels },
          tx,
          'LLM_ENGINE',
          engineId,
        );
        const updated = await tx.llmEngine.update({
          include: { ...(this.labelsService.getLabelsPrismaQuery('LLM_ENGINE') as any) },
          where: { id: engineId },
          data,
        });
        const updatedResult = this.labelsService.formatLabelsAndCategoriesData(updated);
        return updatedResult;
      });
      return updated;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
  /**
   * @description just find isActive llm Engine if llm not active match will return first Engine order By sequence
   * @param slug
   * @param env
   * @returns
   */
  async findDefaulLlmEngine(slug: string) {
    const llm = await this.prisma.llmEngine.findFirst({
      where: { slug: slug },
    });
    if (llm.isActive) {
      return llm;
    }
    this.logger.error(`llm ${llm} is not Active. fetch first one llm `);
    const defautLlm = await this.prisma.llmEngine.findFirst({
      where: {
        isActive: true,
      },
      orderBy: { sequence: 'asc' },
    });
    return defautLlm;
  }

  async findLlmEngine(slug: string) {
    const llm = await this.prisma.llmEngine.findFirst({
      where: { slug: slug },
    });
    if (!llm) {
      throw new ApiException(ErrorCode.LLM_ENGINE_NOT_FOUND);
    }
    return llm;
  }

  async getPlanIsEnabled(input: getPlanIsEnabledDto) {
    let group = null;
    if (input.subscriberId) {
      group = await this.prisma.group.findUnique({
        where: { id: input.subscriberId },
        select: { env: true, groupType: true },
      });
    }
    // resource key will in llm-engine-{slug} format
    const llmEngineSlug = input.resourceKey.replace('llm-engine-', '');
    const llm = await this.prisma.llmEngine.findUnique({
      select: { isActive: true },
      where: {
        slug: llmEngineSlug,
      },
    });
    // If group type is insight, check insight feat flag to get back the allowed llm list
    // If the llm engine slug is not in the allowed list, return false
    if (group && group.groupType === GroupType.INSIGHT) {
      const { metaData } = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
        group.id,
        FeatureFlagKey.INSIGHT_GENERATOR_SUPPORTED_LLM_MODELS,
      );
      if (!((metaData?.['values'] as string[]) ?? []).includes(llmEngineSlug)) {
        return false;
      }
    }
    return llm?.isActive ?? false;
  }

  async getResourceUsage(input: getResourceUsageDto): Promise<ResourceUsageDto | null> {
    const { env, groupType } = await this.prisma.group.findUnique({
      where: { id: input.subscriberId },
      select: { env: true, groupType: true },
    });
    const quota = await this.scopeService.getResourceQuota(
      input.resourceId,
      input.subscriberId,
      groupType,
      env,
    );
    let usageLimit = 0;
    if (quota.length > 0) {
      usageLimit = quota[0].quotaValue.value;
    }
    // get llm engine from plan key, plan key format: llm-engine-{llmEngine}-plan-{randomStr}
    const llmEngine = input.resourceKey.split('llm-engine-')[1];

    const usageConsumption = await this.getUsageConsumption(
      input.subscriberId.toString(),
      llmEngine,
    );
    return { limit: usageLimit, consumption: usageConsumption };
  }

  async getUsageConsumption(groupId: string, llmEngine: string): Promise<number> {
    this.logger.log('getting quota for group: ' + groupId + ' llm engine: ' + llmEngine);
    // get the prompt token usage for the current month
    const promptTokenUsageRedisKey = GROUP_LLM_ENGINE_PROMPT_TOKEN_USAGE_KEY.replace(
      '{GROUP_ID}',
      groupId,
    ).replace('{LLM_ENGINE}', llmEngine);
    const currentEnginePromptTokenUsage = await this.redis.getOrSet<number>(
      promptTokenUsageRedisKey,
      async () => {
        const summary = await this.prisma.summary.aggregate({
          _sum: {
            value: true,
          },
          where: {
            engineSlug: llmEngine,
            groupId: Number(groupId),
            key: 'PROMPT_TOKENS_TOTAL',
            startDate: {
              gte: moment().startOf('month').toISOString(),
              lte: moment().endOf('month').toISOString(),
            },
          },
        });
        return summary?._sum?.value ?? 0;
      },
      10 * 60,
    );
    // get the completion token usage for the current month
    const completionTokenUsageRedisKey = GROUP_LLM_ENGINE_COMPLETION_TOKEN_USAGE_KEY.replace(
      '{GROUP_ID}',
      groupId,
    ).replace('{LLM_ENGINE}', llmEngine);
    const currentEngineCompletionTokenUsage = await this.redis.getOrSet<number>(
      completionTokenUsageRedisKey,
      async () => {
        const summary = await this.prisma.summary.aggregate({
          _sum: {
            value: true,
          },
          where: {
            engineSlug: llmEngine,
            groupId: Number(groupId),
            key: 'COMPLETION_TOKENS_TOTAL',
            startDate: {
              gte: moment().startOf('month').toISOString(),
              lte: moment().endOf('month').toISOString(),
            },
          },
        });
        return summary?._sum?.value ?? 0;
      },
      10 * 60,
    );
    // TODO: need to calculate embedding token?
    // ...
    // decide whether the quota is reached or not
    const totalTokenUsage = currentEnginePromptTokenUsage + currentEngineCompletionTokenUsage;
    return totalTokenUsage;
  }

  async checkQuotaIsAllowed(input: ResourceQuotaDto): Promise<boolean> {
    // quota will be in group-{groupId}:llm-engine-{llmEngine}-quota-{x} format
    // split the group id and llm engine from quota key
    const groupId = input.quotaKey.match(/group-(\d+)/)[1];
    const llmEngine = input.quotaKey.split('llm-engine-')[1].split('-quota')[0];
    const totalTokenUsage = await this.getUsageConsumption(groupId, llmEngine);
    this.logger.log('current token usage: ' + totalTokenUsage + ' quota: ' + input.quotaValue);
    const quota = input.quotaValue;

    if (totalTokenUsage >= quota) {
      throw new ApiException(ErrorCode.BOT_MONTHLY_TOKEN_USAGE_EXCEEDED);
    }
    return true;
  }

  // copy from the feature branch ai-2161.
  orConditionCheck(where: Record<string, number | string>) {
    // if it does not exist ,returns an empty object
    if (!where) {
      return {};
    }
    //List all Keys
    const whereKeys = Object.keys(where);
    const orWhereList = [];

    whereKeys.forEach((key) => {
      if (key.includes('|')) {
        // first isolate the detected Key from other Keys
        const { [key]: keyValue, ...otherWhere } = where;
        //split keys and combine conditions
        const fieldList = key.split('|');
        fieldList.forEach((field) => {
          //field and keyvalue must have values
          if (field && keyValue) {
            orWhereList.push({ [field]: keyValue });
          }
        });
        //remove the detected Key
        where = otherWhere;
      }
    });

    let newWhere: any;
    if (orWhereList.length > 0) {
      newWhere = {
        ...where,
        OR: orWhereList,
      };
    } else {
      newWhere = where;
    }

    return newWhere;
  }

  public async getMostlyUsedLLMnEngines() {
    const summaryAll = await this.prisma.summaryAll.groupBy({
      by: ['engineSlug'],
      _sum: { value: true },
      where: { key: 'CALL_TOTAL' },
      take: 5,
      orderBy: { _sum: { value: 'desc' } },
    });
    return summaryAll;
  }

  public async getLLMEnginesUsedNumbers(slugs: string[]) {
    const llmEngineUsedNumber = await this.prisma.summaryAll.groupBy({
      by: ['engineSlug'],
      _sum: { value: true },
      where: {
        key: 'CALL_TOTAL',
        engineSlug: { in: slugs },
      },
      orderBy: { _sum: { value: 'desc' } },
    });
    return llmEngineUsedNumber;
  }

  async checkChatTokenLimit(groupId: number, llmEngines: string[]) {
    const groupResourceQuotas = await this.quotaService.getGroupResourceQuotas(groupId);
    for (const llmEngine of llmEngines) {
      const quotaKey = `group-${groupId}:llm-engine-${llmEngine}-quota`;
      const resourceQuota = groupResourceQuotas.find((quota) => quota.ruleKey === quotaKey);
      if (!resourceQuota) {
        this.logger.error(`no resource quota are found to match - ${quotaKey}`);
        throw new ApiException(ErrorCode.RESOURCE_QUOTA_NOT_FOUND);
      }
      await this.checkQuotaIsAllowed(resourceQuota);
    }
  }

  async uploadIcon(
    engineId: number,
    file: Express.Multer.File,
    updateLlmEngineDto: UpdateLlmEngineDto,
  ) {
    const iconPictureUrl = await this.uploadIconImg(engineId, file, updateLlmEngineDto);
    return await this.prisma.llmEngine.update({
      where: { id: engineId },
      data: { iconPictureUrl },
    });
  }

  async uploadIconImg(
    engineId: number,
    file: Express.Multer.File,
    updateLlmEngineDto: UpdateLlmEngineDto,
  ) {
    if (file) {
      // upload icon image
      // check file size > 1MB
      if (file.size > 1 * 1024 * 1024) throw new ApiException(ErrorCode.FILE_TOO_LARGE);
      const fileExtension = file.originalname.split('.').pop();
      // check file extension in ['jpg', 'png']
      if (!['jpg', 'png'].includes(fileExtension.toLowerCase())) {
        throw new ApiException(ErrorCode.INVALID_FILE_EXTENSION);
      }
      if (!this.configService.get<string>(`s3.publicFilesBuckets`))
        throw new InternalServerErrorException('Static file bucket not set');
      await this.removeIconFile(engineId);
      // resize image to 256x256
      const newImage = await this.resizeImageService.resizeImage(file.buffer, 256, 256);
      // upload image to s3
      const { Location: iconPictureUrl } = await this.s3Service.uploadAndCacheControl(
        `icon/picture-${engineId}-${this.tokensService.generateUuid()}.${fileExtension.toLowerCase()}`,
        file.originalname,
        newImage,
        file.mimetype,
        engineId,
        'max-age=600',
        this.configService.get<string>(`s3.publicFilesBuckets`),
        false,
      );
      return iconPictureUrl;
    } else if (!updateLlmEngineDto.iconPictureUrl) {
      await this.removeIconFile(engineId);
      return '';
    }
    return updateLlmEngineDto.iconPictureUrl;
  }

  async getLlmEngineDefaultParamSetting(model: string) {
    const llmEngine = await this.findLlmEngine(model);
    if (!llmEngine.isActive) {
      throw new ApiException(
        ErrorCode.LLM_ENGINE_IS_NOT_ACTIVE.replaceAll('{engine}', llmEngine.slug),
      );
    }
    const configJson = isJSON(llmEngine.config.toString())
      ? JSON.parse(llmEngine.config.toString())
      : {};
    if (Object.values(configJson).length === 0) {
      return {};
    }
    const llmEngineDefaultParam = Object.entries(configJson).reduce((data, [key, value]) => {
      if ((value?.['default'] ?? '') !== '') {
        data[key] = value['default'];
      }
      return data;
    }, {});
    return llmEngineDefaultParam;
  }

  async prepareBatchUpdateData(llmEngine: BatchUpdateLlmEngineDto) {
    const entity = await this.prisma.llmEngine.findFirst({
      where: { slug: llmEngine.slug.trim() },
    });
    if (!entity) {
      throw new ApiException(ErrorCode.LLM_ENGINE_NOT_FOUND);
    }
    const entityLabels: PatchLabelsDto[] = [];
    //if need update label, all should be not null
    if (llmEngine.labelAction || llmEngine.labelName || llmEngine.labelColor) {
      if (!llmEngine.labelAction || !llmEngine.labelName || !llmEngine.labelColor) {
        throw new ApiException(ErrorCode.LABEL_FIELDS_REQUIRED);
      }
      const label = await this.prepareUpdateLabelData(
        llmEngine.labelAction,
        LabelType.LABELS,
        llmEngine.labelName,
        entity.id,
        llmEngine.labelColor,
      );
      if (label) {
        entityLabels.push(label);
      }
    }
    //if need update category, all should be not null
    if (llmEngine.categoryAction || llmEngine.categoryName) {
      if (!llmEngine.categoryAction || !llmEngine.categoryName) {
        throw new ApiException(ErrorCode.CATEGORY_FIELDS_REQUIRED);
      }
      const category = await this.prepareUpdateLabelData(
        llmEngine.categoryAction,
        LabelType.CATEGORIES,
        llmEngine.categoryName,
        entity.id,
      );
      if (category) {
        entityLabels.push(category);
      }
    }
    const data: UpdateLlmEngineDto = {
      name: llmEngine.name,
      isActive: llmEngine.isActive,
      isClientDeprecated: llmEngine.isClientDeprecated,
      isRecommended: llmEngine.isRecommended,
      entityLabels: entityLabels,
      sequence: llmEngine.sequence,
    };
    return { engineId: entity.id, data: data };
  }

  private async prepareUpdateLabelData(
    actionType: 'REMOVE' | 'CREATE',
    labelType: LabelType,
    name: string,
    llmEngingId: number,
    color?: string,
  ) {
    let patchLable: PatchLabelsDto = { actionType: actionType, labelType: labelType };
    const where: Prisma.LabelsWhereInput = {
      name: name,
      color: color ?? undefined,
      labelType: labelType,
    };
    const label = await this.prisma.labels.findFirst({
      where,
      include: { EntityLabel: true },
    });
    if (actionType === 'CREATE') {
      if (label) {
        const entityLabel = label.EntityLabel.find(
          (item) =>
            item.LabelEntityType === LabelEntityType.LLM_ENGINE && item.entityId === llmEngingId,
        );
        if (!entityLabel) {
          patchLable = { ...patchLable, id: label.id };
        } else {
          patchLable = null;
        }
      } else {
        patchLable = {
          ...patchLable,
          name: name,
          color: color ?? undefined,
        };
      }
    }
    if (actionType === 'REMOVE') {
      const entityLabel = label?.EntityLabel.find(
        (item) =>
          item.LabelEntityType === LabelEntityType.LLM_ENGINE && item.entityId === llmEngingId,
      );
      if (entityLabel) {
        patchLable = {
          ...patchLable,
          entityLabelsId: entityLabel.id,
        };
      } else {
        patchLable = null;
      }
    }
    return patchLable;
  }

  getBatchUploadFileTemplate() {
    return [
      {
        slug: 'deepseek-r1',
        name: 'Deepseek R1',
        ordering: 1,
        enabled: true,
        deprecated: true,
        isRecommended: true,
        labelColor: 'yellow',
        labelName: 'chat with data',
        labelAction: 'CREATE',
        categoryName: 'chat with file',
        categoryAction: 'CREATE',
      },
      {
        slug: 'deepseek-r1',
        name: 'Deepseek R1',
        ordering: 1,
        enabled: true,
        deprecated: true,
        isRecommended: true,
        labelColor: 'yellow',
        labelName: 'chat with data',
        labelAction: 'REMOVE',
      },
      {
        slug: 'gpt-4o-mini',
        name: 'Gpt 4o Mini',
        ordering: 1,
        enabled: true,
        deprecated: true,
        isRecommended: true,
        labelColor: 'yellow',
        labelName: 'chat with data',
        labelAction: 'CREATE',
      },
      {
        slug: 'gpt-4o-mini',
        name: 'Gpt 4o Mini',
        ordering: 1,
        enabled: true,
        deprecated: true,
        isRecommended: true,
        categoryName: 'chat with file',
        categoryAction: 'CREATE',
      },
      {
        slug: 'phi-4',
        name: 'Phi 4',
        ordering: 1,
        enabled: true,
        deprecated: true,
        isRecommended: true,
      },
    ];
  }

  async findAllPlatform() {
    const platforms = await this.prisma.llmEngine.findMany({
      select: { platform: true },
      distinct: ['platform'],
    });
    return platforms.map((item) => item.platform);
  }

  async removeIconFile(engineId: number) {
    const llm = await this.findLlmEngineById(engineId);
    const url =
      'https://' +
      `${this.configService.get<string>(
        's3.publicFilesBuckets',
      )}.s3.${this.configService.get<string>('s3.region')}.amazonaws.com/`;
    if (llm?.iconPictureUrl && llm.iconPictureUrl.startsWith(url)) {
      // get the path
      const s3Path = llm.iconPictureUrl.replace(url, '');
      // remove file
      await this.s3Service.delete(
        `${this.configService.get<string>('s3.publicFilesBuckets')}`,
        s3Path,
      );
    }
  }

  async findLlmEngineById(engineId: number) {
    const llm = await this.prisma.llmEngine.findFirst({
      where: { id: engineId },
    });
    if (!llm) {
      throw new ApiException(ErrorCode.LLM_ENGINE_NOT_FOUND);
    }
    return llm;
  }
}
