import { PrismaModule } from 'src/providers/prisma/prisma.module';
import { UserBookmarkService } from './user-bookmark.service';
import { Module } from '@nestjs/common';

@Module({
  //maybe group and botSecurity will Circular dependency,so use forwardRef
  imports: [PrismaModule],
  controllers: [],
  providers: [UserBookmarkService],
  exports: [UserBookmarkService],
})
export class UserBookmarkModule {}
