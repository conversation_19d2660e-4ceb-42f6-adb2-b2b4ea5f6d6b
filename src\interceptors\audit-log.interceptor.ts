import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, Logger, NestInterceptor } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import type { FeatureFlagOverride, Prisma } from '@prisma/client';
import { getClientIp } from 'request-ip';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import UAParser from 'ua-parser-js';
import { STAART_AUDIT_LOG_DATA } from '../modules/audit-logs/audit-log.constants';
import { UserRequest } from '../modules/auth/auth.interface';
import { WebhooksService } from '../modules/webhooks/webhooks.service';
import { GeolocationService } from '../providers/geolocation/geolocation.service';
import { PrismaService } from '../providers/prisma/prisma.service';
import { LLMModelsService } from 'src/modules/llm-models/llm-models.service';
import { GroupsService } from 'src/modules/groups/groups.service';

@Injectable()
export class AuditLogger implements NestInterceptor {
  logger = new Logger(AuditLogger.name);

  constructor(
    private readonly reflector: Reflector,
    private prisma: PrismaService,
    private geolocationService: GeolocationService,
    private webhooksService: WebhooksService,
    private groupsService: GroupsService,
    private llmModelsService: LLMModelsService,
  ) {}

  async translateEvent(groupId: number, event: string, request: UserRequest) {
    switch (event) {
      case 'approve-or-process-file': {
        const isGroupRequireSecondaryFileApproval =
          await this.llmModelsService.isRequireSecondaryFileApproval(groupId);
        return isGroupRequireSecondaryFileApproval ? 'approve-file' : 'approve-and-process-file';
      }
      case 'activate-or-deactivate-group':
        return request.body.active ? 'activate-group' : 'deactivate-group';
      default:
        return event;
    }
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    let auditLog = this.reflector.get<string | string[]>(
      STAART_AUDIT_LOG_DATA,
      context.getHandler(),
    );
    return next.handle().pipe(
      tap((response) => {
        (async () => {
          if (auditLog) {
            if (typeof auditLog === 'string') auditLog = [auditLog];
            const request = context.switchToHttp().getRequest() as UserRequest;
            let groupId = parseInt(request.params?.['groupId']);
            if (
              !groupId &&
              request.get('groupId') != null &&
              typeof request.headers['s-api-key'] === 'string'
            )
              groupId = parseInt(request.get('groupId'));
            const ip = getClientIp(request);
            const location = await this.geolocationService.getLocation(ip);
            const userAgent = request.get('user-agent');
            const ua = new UAParser(userAgent);
            for await (const rawEvent of auditLog) {
              let event = await this.translateEvent(groupId, rawEvent, request);
              if (request.user.id && request.user.type === 'user')
                event = event.replace('{userId}', request.user.id.toString());
              if (request.get('userId') != null && typeof request.headers['s-api-key'] === 'string')
                event = event.replace('{userId}', request.get('userId'));
              if (groupId) event = event.replace('{groupId}', groupId.toString());
              const data: Prisma.AuditLogCreateInput = {
                event,
                rawEvent,
                eventMetadata: {
                  entityId: response?.id,
                  entityName: response?.name,
                  filename: response?.filename,
                },
                city: location?.city?.names?.en,
                region: location?.subdivisions?.pop()?.names?.en,
                timezone: location?.location?.timeZone,
                countryCode: location?.country?.isoCode,
                userAgent,
                browser:
                  `${ua.getBrowser().name ?? ''} ${ua.getBrowser().version ?? ''}`.trim() ||
                  undefined,
                operatingSystem:
                  `${ua.getOS().name ?? ''} ${ua.getOS().version ?? ''}`
                    .replace('Mac OS', 'macOS')
                    .trim() || undefined,
              };
              if (request.user.id && request.user.type === 'user')
                data.user = { connect: { id: request.user.id } };
              //For callback request using secure api key
              if (
                request.get('userId') != null &&
                typeof request.headers['s-api-key'] === 'string'
              ) {
                data.user = { connect: { id: parseInt(request.get('userId')) } };
              }
              if (
                request.user.id &&
                request.user.type === 'api-key' &&
                typeof request.headers['s-api-key'] !== 'string'
              )
                data.apiKey = { connect: { id: request.user.id } };
              else if (typeof request.headers['s-api-key'] === 'string') {
                data.apiKey = undefined;
              }

              if (groupId) data.group = { connect: { id: groupId } };

              if (request.body != null && request.body.eventDetails != null) {
                data.eventDetails = request.body.eventDetails;
              }

              if (rawEvent === 'upsert-feature-flags') {
                const res = response as FeatureFlagOverride;
                data.eventMetadata = {
                  ...(data.eventMetadata as any),
                  metaData: res.metaData,
                  isEnabled: res.isEnabled,
                  featureFlag: (res as any)?.featureFlag,
                };
              }

              await this.prisma.auditLog.create({ data });
              if (groupId) this.webhooksService.triggerWebhook(groupId, event, request);
            }
          }
        })()
          .then(() => {
            const request = context.switchToHttp().getRequest() as UserRequest;
            const groupId = parseInt(request.params?.['groupId']);
            if (groupId) {
              this.logger.log(`Request Completed: [Group ID] - ${groupId}`);
            }
          })
          .catch((err) => this.logger.error(err, 'Unable to save audit log'));
      }),
    );
  }
}
