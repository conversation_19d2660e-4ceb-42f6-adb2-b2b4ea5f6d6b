import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  DataPromotionRequest,
  DataPromotionRequestStatus,
  EntitySnapshot,
  Prisma,
  SnapshotEntityType,
} from '@prisma/client';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { OrderByPipe } from 'src/pipes/order-by.pipe';
import { WherePipe } from 'src/pipes/where.pipe';
import { UserRequest } from '../auth/auth.interface';
import { Scopes } from '../auth/scope.decorator';
import { ChangeManagementService } from './change-management.service';
import {
  CreateDataDeletionRequestDto,
  EntitySnapshotStatus,
} from './dto/create-data-deletion-request.dto';
import { CreateDataPromotionRequestDto } from './dto/create-data-promotion-request.dto';
import { CreateEntitySnapshotRequestDto } from './dto/create-entity-snapshot-request.dto';
import { DataPromotionRequestDto } from './dto/data-promotion-request.dto';
import { EntitySnapshotDto } from './dto/entity-snapshot.dto';
import { UpdateDataPromotionRequestDto } from './dto/update-data-promotion-request.dto';

// Controller for managing changes at the group level, enabling group members to send promotion requests.
@ApiTags('Change Management - Group')
@Controller('groups/:groupId/change-management')
export class ChangeManagementGroupController {
  constructor(private readonly changeManagementService: ChangeManagementService) {}

  @ApiOperation({ summary: 'Search entity snapshots in specific group' })
  @ApiResponse({
    status: 200,
    description: 'Success',
    schema: {
      properties: {
        list: {
          type: 'array',
          items: {
            $ref: '#/components/schemas/EntitySnapshotDto',
          },
        },
        count: {
          type: 'number',
        },
      },
    },
  })
  @Get('snapshots')
  @Scopes('group-{groupId}:read-entity-snapshot')
  async searchEntitySnapshots(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
    @Query('latestOnly') latestOnly?: boolean,
  ): Promise<{ list: object[]; count: number }> {
    return this.changeManagementService.searchEntitySnapshots(
      latestOnly,
      skip,
      take,
      orderBy,
      groupId,
      where?.['entityType'] as Prisma.EnumSnapshotEntityTypeFilter | SnapshotEntityType | undefined,
      where?.['entityId'] as Prisma.StringFilter | string | undefined,
    );
  }

  // lightweight api for version select dropdown
  @ApiOperation({ summary: 'Lightweight search api for entity version' })
  @ApiResponse({
    status: 200,
    description: 'Success',
    schema: {
      properties: {
        list: {
          type: 'array',
          items: {
            $ref: '#/components/schemas/EntitySnapshotDto',
          },
        },
        count: {
          type: 'number',
        },
      },
    },
  })
  @Get('snapshots/version-date')
  @Scopes('group-{groupId}:read-entity-snapshot')
  async findVersionsByEntityTypeAndEntityIdAndGroupId(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('entityType') entityType?: string,
    @Query('entityId') entityId?: string,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('equals') equals?: string,
    @Query('lt') lt?: string,
    @Query('lte') lte?: string,
    @Query('gt') gt?: string,
    @Query('gte') gte?: string,
  ): Promise<{ versions: { id: number; versionDate: Date; status: EntitySnapshotStatus }[] }> {
    const versions =
      await this.changeManagementService.findVersionsByEntityTypeAndEntityIdAndGroupId(
        SnapshotEntityType[entityType],
        entityId,
        groupId,
        skip,
        take,
        equals,
        lt,
        lte,
        gt,
        gte,
      );
    return { versions };
  }

  @ApiOperation({ summary: 'Find entity snapshot by id' })
  @ApiResponse({ type: EntitySnapshotDto })
  @Get('snapshots/:id')
  @Scopes('group-{groupId}:read-entity-snapshot')
  async findSnapshotByGroupIdAndId(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.changeManagementService.findSnapshotByIdAndGroupId(id, groupId);
  }

  @ApiOperation({ summary: 'Create entity snapshot' })
  @ApiResponse({ type: EntitySnapshotDto })
  @Post('snapshots')
  @Scopes('group-{groupId}:write-entity-snapshot')
  async createEntitySnapshot(
    @Req() request: UserRequest,
    @Body() dto: CreateEntitySnapshotRequestDto,
    @Param('groupId', ParseIntPipe) groupId: number,
  ): Promise<EntitySnapshot> {
    return this.changeManagementService.createEntitySnapshot(
      groupId,
      request.user.id,
      dto.name,
      dto.entityType,
      dto.entityId,
    );
  }

  @ApiOperation({ summary: 'Delete entity snapshot' })
  @ApiResponse({ type: EntitySnapshotDto })
  @Delete('snapshots/:id')
  @Scopes('group-{groupId}:delete-entity-snapshot')
  async deleteEntitySnapshot(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<EntitySnapshot> {
    return this.changeManagementService.deleteEntitySnapshot(groupId, id);
  }

  @ApiOperation({ summary: 'Search data promotion requests of a group' })
  @ApiResponse({
    status: 200,
    description: 'Success',
    schema: {
      properties: {
        list: {
          type: 'array',
          items: {
            $ref: '#/components/schemas/DataPromotionRequestDto',
          },
        },
        count: {
          type: 'number',
        },
      },
    },
  })
  @Get('data-promotion-requests')
  @Scopes('group-{groupId}:read-data-promotion-request')
  async searchDataPromotionRequests(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<{ list: object[]; count: number }> {
    return this.changeManagementService.searchDataPromotionRequests(
      skip,
      take,
      orderBy,
      where,
      groupId,
    );
  }

  @ApiOperation({ summary: 'Create data promotion request' })
  @ApiResponse({ type: DataPromotionRequestDto })
  @Post('data-promotion-requests/action-type/PROMOTE')
  @Scopes('group-{groupId}:write-data-promotion-request')
  async createDataPromotionRequest(
    @Req() request: UserRequest,
    @Body() dto: CreateDataPromotionRequestDto,
    @Param('groupId', ParseIntPipe) groupId: number,
  ): Promise<DataPromotionRequest> {
    return this.changeManagementService.createDataPromotionRequest(
      groupId,
      request.user.id,
      dto.env,
      dto.snapshotId,
      dto.comment,
    );
  }

  @ApiOperation({ summary: 'Create data deletion request' })
  @ApiResponse({ type: DataPromotionRequestDto })
  @Post('data-promotion-requests/action-type/DELETE')
  @Scopes('group-{groupId}:write-data-promotion-request')
  async createDataDeletionRequest(
    @Req() request: UserRequest,
    @Body() dto: CreateDataDeletionRequestDto,
    @Param('groupId', ParseIntPipe) groupId: number,
  ): Promise<DataPromotionRequest> {
    return this.changeManagementService.createDataDeletionRequest(
      groupId,
      request.user.id,
      dto.env,
      dto.entityType,
      dto.entityId,
      dto.comment,
    );
  }

  @Patch('data-promotion-requests/:id')
  @Scopes('group-{groupId}:write-data-promotion-request')
  async updateDataPromotionRequest(
    @Req() request: UserRequest,
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateDataPromotionRequestDto,
  ): Promise<DataPromotionRequest> {
    return this.changeManagementService.updateDataPromotionRequest(request.user.id, id, dto);
  }

  @ApiOperation({ summary: 'Cancel data promotion request' })
  @ApiResponse({ type: DataPromotionRequestDto })
  @Patch('data-promotion-requests/:id/status/:status')
  @Scopes('group-{groupId}:write-data-promotion-request')
  async updateDataPromotionRequestStatus(
    @Req() request: UserRequest,
    @Param('id', ParseIntPipe) id: number,
    @Param('status') status: string,
    @Body() dto: UpdateDataPromotionRequestDto,
  ): Promise<DataPromotionRequest> {
    switch (status) {
      case DataPromotionRequestStatus.CANCELED:
        return this.changeManagementService.cancelDataPromotionRequest(
          request.user.id,
          id,
          dto.comment,
        );
      default:
        throw new ApiException(ErrorCode.DATA_PROMOTION_REQUEST_STATUS_NOT_UNSUPPORTED);
    }
  }
}
