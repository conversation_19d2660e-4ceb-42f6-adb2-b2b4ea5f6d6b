import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { Configuration } from '../../config/configuration.interface';
import { ConfigService } from '@nestjs/config';
import {
  CreateApiKeyResult,
  CreateGraviteeApplicationResponse,
  CreateGraviteeSubscriptionResponse,
} from './gravitee.interface';
import { ApiPlan } from '@prisma/client';

@Injectable()
export class GraviteeService {
  axios?: AxiosInstance;
  private logger = new Logger(GraviteeService.name);
  orgId: string;
  envId: string;
  graviteeHost: string;
  graviteeApiKey: string;
  botApiId: string;
  apiPathPattern: string;

  constructor(private configService: ConfigService) {
    const graviteeConfig = this.configService.get<Configuration['gravitee']>('gravitee');
    this.axios = axios.create({
      timeout: 30000,
    });
    this.orgId = graviteeConfig.orgId;
    this.envId = graviteeConfig.envId;
    this.graviteeHost = graviteeConfig.graviteeHost;
    this.botApiId = graviteeConfig.botApiId;
    this.graviteeApiKey = graviteeConfig.graviteeApiKey;
    this.apiPathPattern = graviteeConfig.apiPathPattern;
  }

  async createGraviteeApplication(
    name: string,
    description: string,
  ): Promise<CreateGraviteeApplicationResponse> {
    try {
      //create application
      this.logger.log('Create Gravitee Application');
      const createAppResponse = await this.axios.post(
        `${this.graviteeHost}/management/organizations/${this.orgId}/environments/${this.envId}/applications`,
        {
          name,
          description,
        },
        {
          headers: {
            Authorization: `Bearer ${this.graviteeApiKey}`,
          },
        },
      );
      return createAppResponse.data;
    } catch (e) {
      this.logger.error(e, 'failed to create gravitee application');
    }
    return null;
  }

  async createGraviteeSubscription(
    applicationId: string,
    apiKey: string,
    plan: ApiPlan,
  ): Promise<CreateGraviteeSubscriptionResponse> {
    try {
      this.logger.log('Gravitee Application Created');
      this.logger.log('Add Subscription to Gravitee Application');
      const createSubResponse = await this.axios.post(
        `${this.graviteeHost}/management/organizations/${this.orgId}/environments/${this.envId}/v4/apis/${this.botApiId}/subscriptions?application=${applicationId}&plan=${plan.planId}&customApiKey=${apiKey}`,
        {},
        {
          headers: {
            Authorization: `Bearer ${this.graviteeApiKey}`,
          },
        },
      );
      return createSubResponse.data;
    } catch (e) {
      this.logger.error(e, 'failed to create gravitee subscription');
    }
    //create api subscription
    return null;
  }

  async createGraviteeApplicationWithSubscription(
    name: string,
    description: string,
    apiKey: string,
    plan: ApiPlan,
  ): Promise<CreateApiKeyResult | null> {
    const appData = await this.createGraviteeApplication(name, description);
    if (appData && appData.id) {
      const subData = await this.createGraviteeSubscription(appData.id, apiKey, plan);
      if (subData && subData.id) {
        this.logger.log('Subscription Created');
        return {
          applicationId: appData.id,
          subscriptionId: subData.id,
        } as CreateApiKeyResult;
      }
    }
    return null;
  }

  async removeGraviteeApplication(applicationId: string): Promise<boolean> {
    try {
      const response = await this.axios.delete(
        `${this.graviteeHost}/management/organizations/${this.orgId}/environments/${this.envId}/applications/${applicationId}`,
        {
          headers: {
            Authorization: `Bearer ${this.graviteeApiKey}`,
          },
        },
      );
      this.logger.log(response.data);
      return true;
    } catch (e) {
      this.logger.error(e);
    }
    return false;
  }

  async healthCheck() {
    return this.axios.get(
      `${this.graviteeHost}/management/organizations/${this.orgId}/environments/${this.envId}`,
      {
        headers: {
          Authorization: `Bearer ${this.graviteeApiKey}`,
        },
      },
    );
  }
}
