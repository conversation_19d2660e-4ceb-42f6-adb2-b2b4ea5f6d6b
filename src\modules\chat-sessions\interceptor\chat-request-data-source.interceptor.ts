import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, Logger, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { LLMModelsService } from '../../llm-models/llm-models.service';
import { ChatSessionsService } from '../chat-sessions.service';
import { ChatFilesService } from '../../chat-files/chat-files.service';
import { UserRequest } from '../../auth/auth.interface';
import { plainToClass } from 'class-transformer';
import { ChatLlmModelDto, File } from '../../llm-models/dto/chat-llm-model.dto';
import { ApiException, ErrorCode } from '../../../errors/errors.constants';
import { ChatSession, FileStatus, LLMModel } from '@prisma/client';
import { ChatFilesApproach } from '../../chat-files/chat-files.dto';
import {
  AutoSelectDto,
  AutoSelectType,
  DataSourceFile,
  DataSourceFileInfo,
  DataSourceGenKb,
  DataSourceModelFile,
} from '../dto/update-data-source-request.dto';
import { CHAT_APPROACH, SOURCE } from 'src/providers/llm-backend/llm-backend.interface';

@Injectable()
export class ChatRequestDataSourceInterceptor implements NestInterceptor {
  constructor(
    private readonly llmModelsService: LLMModelsService,
    private readonly chatSessionsService: ChatSessionsService,
    private readonly chatFilesService: ChatFilesService,
  ) {}

  private logger = new Logger(ChatRequestDataSourceInterceptor.name);

  private readonly dataSourceKey = 'dataSource';
  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<unknown>> {
    const request = context.switchToHttp().getRequest() as UserRequest;
    if (request.url.includes('/llm-model/internal/chat') && !request?.body?.needIncludeDataSource) {
      return next.handle();
    }
    const chatRequest = plainToClass(ChatLlmModelDto, request.body);
    const isChatSessionCall =
      (chatRequest.chatSessionType || chatRequest.chatSessionId) &&
      chatRequest.history?.length &&
      request.user.type === 'user' &&
      request.user.id;
    const groupId = parseInt(request.params?.['groupId']);
    const userId = request.user?.id;
    const llmModel = await this.llmModelsService.findOneByGroupId(groupId);
    let chatSession: ChatSession;
    if (isChatSessionCall) {
      chatSession = await this.chatSessionsService.findChatSessionOrDefault(
        groupId,
        userId,
        request.body.chatSessionId,
        request.body.chatSessionType,
      );
      if (!chatSession) {
        throw new ApiException(ErrorCode.CHAT_SESSIOIN_NOT_FOUND);
      }
    }

    if (request.body.approach === CHAT_APPROACH.RRR) {
      request.body.overrides = await this.getEmbeddingFilesForRag(request, chatSession, llmModel);
    } else {
      // append the files to file request object and disable embedding functionality with other approach
      request.body.overrides.rag = false;
      await this.appendFilesToRequest(request, chatSession, llmModel);
    }

    // add back the s3 path to the request files
    if (request.body.files && request.body.files.length > 0) {
      const chatFiles = await this.llmModelsService.collectS3Paths(
        groupId,
        userId,
        request.body.files,
      );
      request.body.files = chatFiles;
    }
    this.logger.debug(
      `ChatRequestDataSourceInterceptor request request body ${JSON.stringify(request.body)}`,
    );
    return next.handle();
  }

  async getEmbeddingFilesForRag(
    request: UserRequest,
    chatSession: ChatSession,
    llmModel: LLMModel,
  ) {
    let overrides = request.body.overrides ?? {};
    let filesFilter = [];
    // obtain the data source from system default / chat session default
    let dataSource = llmModel.parameters?.[this.dataSourceKey];
    const isAPICallAndHasFileInRequestBody =
      !chatSession && request.body?.files && request.body.files?.length > 0;
    if (chatSession && !chatSession.isDefault) {
      dataSource = chatSession.chatSetting?.[this.dataSourceKey];
    }
    // for chatting by api key, allow them to append the file directly
    if (isAPICallAndHasFileInRequestBody) {
      const files = [] as File[];
      // need confirm this
      request.body.files.forEach((file: File) => {
        if (file.data_source == SOURCE.VECTOR_STORE) {
          filesFilter.push({ file_id: file.file_id });
        } else {
          files.push(file);
        }
      });
      request.body.files = files;
    }
    // getting file from "Shared Space"
    const dataSourceFile: DataSourceFile<DataSourceModelFile> = dataSource?.modelFile;
    const genKb = dataSource?.genKb;
    overrides = this.checkDataSourceGenKbAndSetToOverrides(genKb, overrides);
    const rag = dataSourceFile?.enable ?? true;
    overrides.rag = rag;
    // if isAutoSelectAll = true, then we will select all the files, rag = true and delete file_filters
    if (rag && dataSourceFile?.autoSelect?.type !== AutoSelectType.All) {
      filesFilter = [...filesFilter, ...this.getDataSourceModelFiles(dataSourceFile)];
      //get the filter files for auto select
      if (dataSourceFile?.autoSelect?.type === AutoSelectType.Filter) {
        // if the user select the file, then we will use the selected file
        filesFilter = [
          ...filesFilter,
          ...(await this.getModelFilesByAutoSelect(
            filesFilter,
            llmModel.groupId,
            dataSourceFile.autoSelect,
          )),
        ];
      }
      // apply the selected file to file_filter if selected
      const isDataSourceRagConfigured =
        dataSource && dataSource?.modelFile && dataSource?.modelFile?.enable;
      if (isDataSourceRagConfigured) {
        if (filesFilter.length > 0) {
          filesFilter = await this.llmModelsService.getValidModelFileDocIds(filesFilter, [
            FileStatus.COMPLETED,
          ]);
        }
        if (filesFilter.length > 0) {
          overrides.file_filters = filesFilter;
        } else {
          // if no file selected, disable embedding
          overrides.rag = false;
        }
      }
    }
    // Get the file list that also enable "CWF"
    const chatWithFileList =
      await this.getDataSourceModelFilesEnableChatWithFileList(dataSourceFile);
    if (chatWithFileList.length != 0) {
      request.body.files = [...(request.body?.files ?? []), ...chatWithFileList];
    }
    return overrides;
  }

  getDataSourceModelFiles(dataSourceFile: DataSourceFile<DataSourceModelFile>) {
    if (dataSourceFile?.enable !== false && dataSourceFile?.files?.length > 0) {
      return dataSourceFile.files
        .filter((file) => !(file?.chatWithFile ?? false))
        .map((file) => file.file_id);
    }
    return [];
  }

  /**
   * @description this function will get the file list as chatWithFile is true .
   * @param {DataSourceFile} dataSourceFile
   * @returns {DataSourceFile[]}
   */
  async getDataSourceModelFilesEnableChatWithFileList(
    dataSourceFile: DataSourceFile<DataSourceModelFile>,
  ) {
    if (dataSourceFile?.enable !== false && dataSourceFile?.files?.length > 0) {
      return dataSourceFile.files
        .filter((file) => file?.chatWithFile)
        .map((file) => ({ ...file, data_source: SOURCE.VECTOR_STORE }));
    }
    return [];
  }

  checkDataSourceGenKbAndSetToOverrides(genKb: DataSourceGenKb, overrides: any) {
    const _overrides = { ...overrides };
    // _overrides.genkb = true;
    // _overrides.genkb_top = genKb?.kbTop ?? 3;
    if (overrides?.genkb == null && genKb) {
      _overrides.genkb = genKb.enable;
      _overrides.genkb_top = genKb.kbTop;
    }
    return _overrides;
  }

  getDataSourceFiles(
    dataSourceFile: DataSourceFile<DataSourceModelFile | DataSourceFileInfo>,
    dataSourceKey: string,
    enable?: boolean,
  ) {
    if (enable === false) {
      return [];
    }
    if (dataSourceFile?.enable !== false && dataSourceFile?.files?.length > 0) {
      return dataSourceFile?.files.map((file) => {
        if (file.range) {
          return { file_id: file.file_id, data_source: dataSourceKey, range: file.range };
        }
        return { file_id: file.file_id, data_source: dataSourceKey };
      });
    }
    return [];
  }

  async appendFilesToRequest(request: UserRequest, chatSession: ChatSession, llmModel: LLMModel) {
    let chatFiles = [];
    let modelFiles = [];
    if (chatSession) {
      chatFiles = this.getDataSourceFiles(
        chatSession.chatSetting?.[this.dataSourceKey]?.chatFile,
        'upload',
      );
      if (!chatSession.isDefault) {
        modelFiles = this.getDataSourceFiles(
          chatSession.chatSetting?.[this.dataSourceKey]?.modelFile,
          'vector-store',
        );
      }
    }
    if (!chatSession || chatSession.isDefault) {
      modelFiles = this.getDataSourceFiles(
        llmModel.parameters?.[this.dataSourceKey]?.modelFile,
        'vector-store',
      );
    }
    if (chatFiles?.length > 0) {
      const chatFileApproach =
        request.body.approach === 'cwf' ? ChatFilesApproach.CWF : ChatFilesApproach.CWD;
      const supportExt = this.chatFilesService.getSupportedFileExtensions(chatFileApproach);
      chatFiles = chatFiles.filter((file) => supportExt.includes(file.file_id.split('.').pop()));
      //filter the expired files
      const validFiles = await this.chatFilesService.getValidFiles(
        chatFiles.map((file) => file.file_id),
      );
      chatFiles = chatFiles.filter((file) => validFiles.includes(file.file_id));
      request.body.files = request.body.files ?? [];
      chatFiles.forEach((file) => {
        if (
          !request.body.files.find((f) => f.file_id === file.file_id && f.data_source === 'upload')
        ) {
          request.body.files.push(file);
        }
      });
    }
    if (modelFiles?.length > 0) {
      request.body.files = request.body.files ?? [];
      //filter the deleted files
      const validFiles = await this.llmModelsService.getApprovalModelFileDocIds(
        modelFiles.map((file) => file.file_id),
      );
      modelFiles.forEach((file) => {
        if (
          !request.body.files.find(
            (f) => f.file_id === file.file_id && f.data_source === 'vector-store',
          ) &&
          validFiles.includes(file.file_id)
        ) {
          request.body.files.push(file);
        }
      });
    }
  }

  private async getModelFilesByAutoSelect(
    filesFilters: string[],
    groupId: number,
    filter: AutoSelectDto,
  ) {
    if (!filter || !(filter.fileTypes?.length > 0 || filter.fileTags?.length > 0)) {
      return [];
    }
    const files = await this.llmModelsService.getModelFilesByAutoSelectFilters(
      groupId,
      filter.fileTypes,
      filter.fileTags,
    );
    if (files?.length > 0) {
      // filter the files by file_id
      return files.filter((file) => !filesFilters.includes(file.docId)).map((file) => file.docId);
    }
    return [];
  }
}
