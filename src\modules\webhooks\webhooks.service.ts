import { Injectable, Logger, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import type { Prisma } from '@prisma/client';
import { Webhook } from '@prisma/client';
import axios from 'axios';
import PQueue from 'p-queue';
import pRetry from 'p-retry';
import WebhookEventMap from 'src/helpers/webhook-event-map';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { Expose } from '../../providers/prisma/prisma.interface';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { UserRequest } from '../auth/auth.interface';

@Injectable()
export class WebhooksService {
  private readonly logger = new Logger(WebhooksService.name);
  private queue = new PQueue({ concurrency: 1 });

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
  ) {}

  async createWebhook(
    groupId: number,
    data: Omit<Omit<Prisma.WebhookCreateInput, 'webhook'>, 'group'>,
  ): Promise<Webhook> {
    return this.prisma.webhook.create({
      data: { ...data, group: { connect: { id: groupId } } },
    });
  }

  async getWebhooks(
    groupId: number,
    params: {
      skip?: number;
      take?: number;
      cursor?: Prisma.WebhookWhereUniqueInput;
      where?: Prisma.WebhookWhereInput;
      orderBy?: Prisma.WebhookOrderByWithRelationInput;
    },
  ): Promise<Expose<Webhook>[]> {
    const { skip, take, cursor, where, orderBy } = params;
    try {
      const webhooks = await this.prisma.webhook.findMany({
        skip,
        take,
        cursor,
        where: { ...where, group: { id: groupId } },
        orderBy,
      });
      return webhooks.map((group) => this.prisma.expose<Webhook>(group));
    } catch (error) {
      return [];
    }
  }

  async getWebhooksCount(groupId: number, where?: Prisma.WebhookWhereInput) {
    return await this.prisma.webhook.count({
      where: { ...where, group: { id: groupId } },
    });
  }

  async getWebhook(groupId: number, id: number): Promise<Expose<Webhook>> {
    const webhook = await this.prisma.webhook.findUnique({
      where: { id },
    });
    if (!webhook) throw new ApiException(ErrorCode.WEBHOOK_NOT_FOUND);
    if (webhook.groupId !== groupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    return this.prisma.expose<Webhook>(webhook);
  }

  async updateWebhook(
    groupId: number,
    id: number,
    data: Prisma.WebhookUpdateInput,
  ): Promise<Expose<Webhook>> {
    const testWebhook = await this.prisma.webhook.findUnique({
      where: { id },
    });
    if (!testWebhook) throw new ApiException(ErrorCode.WEBHOOK_NOT_FOUND);
    if (testWebhook.groupId !== groupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    const webhook = await this.prisma.webhook.update({
      where: { id },
      data,
    });
    return this.prisma.expose<Webhook>(webhook);
  }

  async replaceWebhook(
    groupId: number,
    id: number,
    data: Prisma.WebhookCreateInput,
  ): Promise<Expose<Webhook>> {
    const testWebhook = await this.prisma.webhook.findUnique({
      where: { id },
    });
    if (!testWebhook) throw new ApiException(ErrorCode.WEBHOOK_NOT_FOUND);
    if (testWebhook.groupId !== groupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    const webhook = await this.prisma.webhook.update({
      where: { id },
      data,
    });
    return this.prisma.expose<Webhook>(webhook);
  }

  async deleteWebhook(groupId: number, id: number): Promise<Expose<Webhook>> {
    const testWebhook = await this.prisma.webhook.findUnique({
      where: { id },
    });
    if (!testWebhook) throw new ApiException(ErrorCode.WEBHOOK_NOT_FOUND);
    if (testWebhook.groupId !== groupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    const webhook = await this.prisma.webhook.delete({
      where: { id },
    });
    return this.prisma.expose<Webhook>(webhook);
  }

  triggerWebhook(groupId: number, event: string, originalRequest?: UserRequest) {
    this.prisma.webhook
      .findMany({
        where: { group: { id: groupId }, isActive: true, event },
      })
      .then((webhooks) => {
        webhooks.forEach((webhook) =>
          this.queue
            .add(() =>
              pRetry(() => this.callWebhook(webhook, originalRequest), {
                retries: this.configService.get<number>('webhooks.retries') ?? 3,
                onFailedAttempt: (error) => {
                  this.logger.error(
                    `Triggering webhoook failed, retrying (${error.retriesLeft} attempts left)`,
                    error.name,
                  );
                  if (error.retriesLeft === 0)
                    // eslint-disable-next-line @typescript-eslint/no-floating-promises
                    this.prisma.webhook.update({
                      where: { id: webhook.id },
                      data: { isActive: false },
                    });
                },
              }),
            )
            .catch((error: Error) => {
              this.logger.error(`queue.add error ${error.message}`, error.stack);
            }),
        );
      })
      .catch((error: Error) =>
        this.logger.error(`Unable to get webhooks ${error.message}`, error.stack),
      );
  }

  private async callWebhook(webhook: Webhook, originalRequest?: UserRequest) {
    const webhookEvent = WebhookEventMap[webhook.event];
    if (!webhookEvent) return;

    await axios({
      method: webhookEvent.httpMethod,
      url: webhook.url,
      headers: JSON.parse(webhook.headers ? webhook.headers : '{}'),
      data: webhookEvent.generateBody(originalRequest),
    });

    await this.prisma.webhook.update({
      where: { id: webhook.id },
      data: { lastFiredAt: new Date() },
    });
  }
}
