-- DropIndex
DROP INDEX "PermissionGroupSetting_permissionId_groupType_isApiKeyAllow_idx";

-- DropIndex
DROP INDEX "PermissionGroupSetting_permissionId_groupType_isCustomRoleA_idx";

-- AlterTable
ALTER TABLE "PermissionGroupSetting" ADD COLUMN     "featureId" INTEGER;

-- CreateTable
CREATE TABLE "PermissionGroupFeature" (
    "id" SERIAL NOT NULL,
    "groupTypes" "GroupType"[] DEFAULT ARRAY[]::"GroupType"[],
    "featureName" TEXT NOT NULL,
    "featureKey" TEXT NOT NULL,
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "isBasic" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "PermissionGroupFeature_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "PermissionGroupFeature_featureKey_key" ON "PermissionGroupFeature"("featureKey");

-- CreateIndex
CREATE INDEX "PermissionGroupSetting_featureId_idx" ON "PermissionGroupSetting"("featureId");

-- CreateIndex
CREATE INDEX "PermissionGroupSetting_permissionId_groupType_isCustomRoleA_idx" ON "PermissionGroupSetting"("permissionId", "groupType", "isCustomRoleAllowed", "featureId");

-- CreateIndex
CREATE INDEX "PermissionGroupSetting_permissionId_groupType_isApiKeyAllow_idx" ON "PermissionGroupSetting"("permissionId", "groupType", "isApiKeyAllowed", "featureId");





-- DropIndex
DROP INDEX "Role_roleType_groupId_idx";

-- AlterTable
ALTER TABLE "Role" ADD COLUMN     "isCustomRoleTemplateAllowed" BOOLEAN DEFAULT false;

-- CreateIndex
CREATE INDEX "Role_roleType_groupId_isCustomRoleTemplateAllowed_idx" ON "Role"("roleType", "groupId", "isCustomRoleTemplateAllowed");
