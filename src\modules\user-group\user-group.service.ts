import { Injectable, Logger } from '@nestjs/common';
import { Email, UserGroup, type Prisma } from '@prisma/client';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { UpdateUserGroupDto, UserGroupDto } from './user-group.dto';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { NotificationBackendService } from '../../providers/notification-backend/notification-backend.service';

@Injectable()
export class UserGroupService {
  private logger = new Logger(UserGroupService.name);
  constructor(
    private prisma: PrismaService,
    private notificationBackendService: NotificationBackendService,
  ) {}
  async getAll(
    skip?: number,
    take?: number,
    where?: Record<string, number | string>,
    orderBy?: Record<string, number | string>,
  ): Promise<UserGroup[]> {
    return await this.prisma.userGroup.findMany({
      skip,
      take,
      where,
      orderBy,
      include: {
        userGroupFilter: true,
        updatedBy: {
          select: { name: true },
        },
        createdBy: {
          select: { name: true },
        },
      },
    });
  }

  async getCount(where?: Record<string, number | string>): Promise<number> {
    return await this.prisma.userGroup.count({ where });
  }

  async create(data: UserGroupDto, userId: number): Promise<UserGroup> {
    if (await this.checkUserGroupNameExisted(data.name))
      throw new ApiException(ErrorCode.USER_GROUP_NAME_DUPLICATED);
    return await this.prisma.userGroup.create({
      data: {
        name: data.name,
        additionalEmails: data.additionalEmails,
        createdByUserId: userId,
        updatedByUserId: userId,
        userGroupFilter: data.userGroupFilter
          ? {
              create: {
                groupIds: data.userGroupFilter.groupIds,
                groupRoleIds: data.userGroupFilter.groupRoleIds,
                systemRoleIds: data.userGroupFilter.systemRoleIds,
                cccList: data.userGroupFilter.cccList,
                businessUnits: data.userGroupFilter.businessUnits,
                groupType: data.userGroupFilter.groupType,
              },
            }
          : undefined,
      },
      include: {
        userGroupFilter: true,
        updatedBy: {
          select: { name: true },
        },
        createdBy: {
          select: { name: true },
        },
      },
    });
  }

  private getUpdateArgs(
    id: number,
    data: UserGroupDto,
    userId: number,
  ): Prisma.UserGroupUpdateArgs {
    return {
      where: { id },
      data: {
        name: data.name,
        additionalEmails: data.additionalEmails,
        updatedByUserId: userId,
        userGroupFilter: data.userGroupFilter
          ? {
              upsert: {
                update: {
                  groupIds: data.userGroupFilter.groupIds,
                  groupRoleIds: data.userGroupFilter.groupRoleIds,
                  systemRoleIds: data.userGroupFilter.systemRoleIds,
                  cccList: data.userGroupFilter.cccList,
                  businessUnits: data.userGroupFilter.businessUnits,
                  groupType: data.userGroupFilter.groupType,
                },
                create: {
                  groupIds: data.userGroupFilter.groupIds,
                  groupRoleIds: data.userGroupFilter.groupRoleIds,
                  systemRoleIds: data.userGroupFilter.systemRoleIds,
                  cccList: data.userGroupFilter.cccList,
                  businessUnits: data.userGroupFilter.businessUnits,
                  groupType: data.userGroupFilter.groupType,
                },
              },
            }
          : undefined,
      },
      include: {
        userGroupFilter: true,
        updatedBy: {
          select: { name: true },
        },
        createdBy: {
          select: { name: true },
        },
      },
    };
  }

  async update(id: number, data: UpdateUserGroupDto, userId: number): Promise<UserGroup> {
    if (await this.checkUserGroupNameExisted(data.name))
      throw new ApiException(ErrorCode.USER_GROUP_NAME_DUPLICATED);
    return await this.prisma.userGroup.update({
      where: { id },
      data: {
        name: data.name,
        updatedByUserId: userId,
      },
      include: {
        userGroupFilter: true,
        updatedBy: {
          select: { name: true },
        },
        createdBy: {
          select: { name: true },
        },
      },
    });
  }

  async delete(id: number, userId: number): Promise<{ userGroupId: number }> {
    this.logger.log(`user [${userId}] delete member group id - ${id}`);
    const where = {
      userGroupId: id,
    };
    const broadcastData = await this.notificationBackendService.getBroadcasts(0, 0, where);
    if (broadcastData.count > 0) throw new ApiException(ErrorCode.INVALID_STATUS_TO_DELETE);
    let deleteUserGroup: UserGroup;
    await this.prisma.$transaction(async (tx) => {
      const userGroupFilter = await tx.userGroupFilter.findUnique({ where: { userGroupId: id } });
      if (userGroupFilter) await tx.userGroupFilter.delete({ where: { userGroupId: id } });
      deleteUserGroup = await tx.userGroup.delete({ where: { id } });
    });
    return { userGroupId: deleteUserGroup.id };
  }

  async checkUserGroupNameExisted(name: string): Promise<boolean> {
    const count = await this.prisma.userGroup.count({ where: { name: name } });
    return count > 0;
  }

  async getEmails(
    skip?: number,
    take?: number,
    where?: Prisma.EmailWhereInput,
    orderBy?: Prisma.EmailOrderByWithRelationInput,
  ): Promise<Email[]> {
    return await this.prisma.email.findMany({
      skip,
      take,
      where,
      orderBy,
    });
  }
}
