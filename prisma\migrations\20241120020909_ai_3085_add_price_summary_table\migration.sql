-- AlterEnum
ALTER TYPE "SummaryKeyType" ADD VALUE 'MODEL_COST';
ALTER TYPE "SummaryKeyType" ADD VALUE 'MODEL_PRICE';
ALTER TYPE "SummaryKeyType" ADD VALUE 'MODEL_USAGE';

-- CreateTable
CREATE TABLE "ModelPriceSummary" (
    "id" SERIAL NOT NULL,
    "startDate" TIMESTAMP(6) NOT NULL,
    "endDate" TIMESTAMP(6) NOT NULL,
    "groupId" INTEGER NOT NULL,
    "modelPriceUnitId" INTEGER NOT NULL,
    "engineSlug" TEXT NOT NULL,
    "key" "SummaryKeyType" NOT NULL,
    "feature" "Feature" NOT NULL,
    "channel" "SummaryCallingType" NOT NULL,
    "value" DECIMAL(65,30) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ModelPriceSummary_pkey" PRIMARY KEY ("id")
);
