-- Create<PERSON>num
CREATE TYPE "BroadcastStatus" AS ENUM ('SENT', 'FAILED', 'SENDING', 'PENDING');

-- CreateEnum
CREATE TYPE "NotificationChannel" AS ENUM ('EMAIL', 'IN_APP');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "BroadcastTargetType" AS ENUM ('USER_GROUP', 'ALL');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "UserNotificationViewStatus" AS ENUM ('UNSEEN', 'SEEN');

-- CreateEnum
CREATE TYPE "UserNotificationStatus" AS ENUM ('SUCCESS', 'FAILED');

-- CreateEnum
CREATE TYPE "BroadcastScheduleType" AS ENUM ('IMMEDIATE', 'SCHEDULE');

-- Create<PERSON>num
CREATE TYPE "NotificationSource" AS ENUM ('BROADCAST', 'DIRECT_MESSAGE');

-- CreateTable
CREATE TABLE "Broadcast" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "startedAt" TIMESTAMP(3),
    "recipients" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "numberSent" TEXT,
    "channel" "NotificationChannel" NOT NULL,
    "status" "BroadcastStatus" NOT NULL,
    "createdByUserId" INTEGER NOT NULL,
    "userGroupId" INTEGER,
    "targetType" "BroadcastTargetType" NOT NULL,
    "scheduleTime" TIMESTAMP(3),
    "scheduleType" "BroadcastScheduleType" NOT NULL,
    "jobId" INTEGER,

    CONSTRAINT "Broadcast_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserNotification" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "source" "NotificationSource" NOT NULL,
    "sourceId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userId" INTEGER NOT NULL,
    "channel" "NotificationChannel" NOT NULL,
    "viewStatus" "UserNotificationViewStatus" NOT NULL,
    "status" "UserNotificationStatus" NOT NULL,

    CONSTRAINT "UserNotification_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "broadcast_index" ON "Broadcast"("title");

-- CreateIndex
CREATE INDEX "user_notification_index" ON "UserNotification"("title");
