import { ModuleMocker, MockFunctionMetadata } from 'jest-mock';
import { mockDeep, DeepMockProxy } from 'jest-mock-extended';
import { Test, TestingModule } from '@nestjs/testing';
import { Environment, Membership } from '@prisma/client';
import { ExtendedMembership, MembershipsService } from './memberships.service';
import { UserMembershipController } from './memberships-user.controller';
const moduleMocker = new ModuleMocker(global);

describe('MembershipsService', () => {
  let membershipsService: DeepMockProxy<MembershipsService>;

  let userMembershipController: UserMembershipController;
  beforeEach(async () => {
    membershipsService = mockDeep<MembershipsService>();
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserMembershipController],
      providers: [{ provide: MembershipsService, useValue: membershipsService }],
    })
      .useMocker((token) => {
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>;
          const Mock = moduleMocker.generateFromMetadata(mockMetadata);
          return new Mock();
        }
      })
      .compile();
    userMembershipController = module.get(UserMembershipController);
  });

  describe('getAll', () => {
    it('should be return membership', async () => {
      const memberships = [
        {
          id: 1,
          groupId: 1,
          userId: 1,
          roleId: 1,
        },
        {
          id: 2,
          groupId: 2,
          userId: 2,
          roleId: 2,
        },
      ] as ExtendedMembership[];
      const count = {
        count: 2,
      };
      const check = {
        list: [
          {
            id: 1,
            groupId: 1,
            userId: 1,
            roleId: 1,
          },
          {
            id: 2,
            groupId: 2,
            userId: 2,
            roleId: 2,
          },
        ],
        ...count,
      };
      membershipsService.getMemberships.mockResolvedValue(memberships);
      membershipsService.getMembershipsCount.mockResolvedValue(count.count);
      const res = await userMembershipController.getAll(1);
      expect(res).toEqual(check);
    });
  });

  describe('getList', () => {
    it('should be return membership', async () => {
      const memberships = [
        {
          id: 1,
          groupId: 1,
          userId: 1,
          roleId: 1,
        },
        {
          id: 2,
          groupId: 2,
          userId: 2,
          roleId: 2,
        },
      ] as ExtendedMembership[];
      const check = [
        {
          id: 1,
          groupId: 1,
          userId: 1,
          roleId: 1,
        },
        {
          id: 2,
          groupId: 2,
          userId: 2,
          roleId: 2,
        },
      ];
      membershipsService.getMemberships.mockResolvedValue(memberships);
      const res = await userMembershipController.getList(1, Environment.TEST);
      expect(res).toEqual(check);
    });
  });

  describe('get', () => {
    it('should be return membership', async () => {
      const memberships = {
        id: 1,
        groupId: 1,
        userId: 1,
        roleId: 1,
      } as Membership;
      const check = {
        id: 1,
        groupId: 1,
        userId: 1,
        roleId: 1,
      };
      membershipsService.getUserMembership.mockResolvedValue(memberships);
      const res = await userMembershipController.get(1, 1);
      expect(res).toEqual(check);
    });
  });

  describe('remove', () => {
    it('should be return membership', async () => {
      const memberships = {
        id: 1,
        groupId: 1,
        userId: 1,
        roleId: 1,
      } as Membership;
      const check = {
        id: 1,
        groupId: 1,
        userId: 1,
        roleId: 1,
      };
      membershipsService.deleteUserMembership.mockResolvedValue(memberships);
      const res = await userMembershipController.remove(1, 1);
      expect(res).toEqual(check);
    });
  });
});
