import { Module, forwardRef } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { FlowBotRequestsService } from './flow-bot-requests.service';
import { ConfigService } from '@nestjs/config';
import { GroupsModule } from '../groups/groups.module';

@Module({
  imports: [PrismaModule, forwardRef(() => GroupsModule)],
  providers: [FlowBotRequestsService, ConfigService],
  exports: [FlowBotRequestsService],
})
export class FlowBotRequestsModule {}
