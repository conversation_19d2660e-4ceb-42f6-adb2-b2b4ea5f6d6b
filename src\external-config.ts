import axios from 'axios';
interface ExternalConfigData {
  [key: string]: string;
}

interface ExternalConfigResBody {
  success: boolean;
  data: ExternalConfigData;
}

export async function loadExternalConfig() {
  try {
    const externalConfigRes = await axios.get<ExternalConfigResBody>(
      process.env['CONFIG_SERVICE_GET_CONFIG_URL'],
    );
    if (!externalConfigRes?.data?.success) throw new Error();
    process.env = Object.assign(externalConfigRes.data.data, process.env);
    console.log('External config loaded.');
  } catch (error) {
    throw new Error('Failed to load external env.', { cause: error });
  }
}
