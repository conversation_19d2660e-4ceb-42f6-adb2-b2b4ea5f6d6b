-- CreateEnum
CREATE TYPE "AiResourceStatus" AS ENUM ('PROCESSING', 'GENERATED', 'FAILED');

-- CreateTable
CREATE TABLE "AiResource" (
    "id" SERIAL NOT NULL,
    "llmEngineId" INTEGER NOT NULL,
    "groupId" INTEGER NOT NULL,
    "prompt" TEXT,
    "files" VARCHAR(1024)[],
    "status" "AiResourceStatus" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "generatedAt" TIMESTAMP(3),

    CONSTRAINT "AiResource_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "AiResource" ADD CONSTRAINT "AiResource_llmEngineId_fkey" FOREIGN KEY ("llmEngineId") REFERENCES "LlmEngine"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AiResource" ADD CONSTRAINT "AiResource_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
