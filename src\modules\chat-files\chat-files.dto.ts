import { ApiProperty } from '@nestjs/swagger';
import { ChatFileEntity } from './entities/chat-files.entity';

export enum ChatFilesApproach {
  CWF = 'cwf',
  CWD = 'cwd',
}

export class ChatFilesResponse {
  @ApiProperty({ description: 'chat files object' })
  list: ChatFileEntity[];

  @ApiProperty({ description: 'files count' })
  count: number;

  constructor(list: ChatFileEntity[]) {
    this.list = list;
    this.count = list.length;
  }
}

export class ChatFilesConfigResponse {
  @ApiProperty({ description: 'The number of file that allow to be upload' })
  maxFileNum: number;

  @ApiProperty({ description: 'max file size that allow to be upload' })
  maxFileSize: number;

  @ApiProperty({ description: 'max selected number of file that allow to used for chatting' })
  maxSelectionNum: number;
}
