import { Injectable, Logger } from '@nestjs/common';
import {
  CreateInsightCaseDto,
  InsightCaseResDto,
  InsightReportDetailResDto,
  InsightReportResDto,
  InsightReportStatusResDto,
  UpdateInsightCaseDto,
} from './insight.dto';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { Stream } from 'stream';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import axios, { AxiosInstance } from 'axios';
import { Configuration } from 'src/config/configuration.interface';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class InsightService {
  private logger = new Logger(InsightService.name);
  axios?: AxiosInstance;
  constructor(
    private configService: ConfigService,
    private prisma: PrismaService,
    private featureFlagService: FeatureFlagService,
  ) {
    const config = this.configService.get<Configuration['insightGenerator']>('insightGenerator');
    if (config) {
      this.axios = axios.create({
        baseURL: config.host,
        timeout: config.timeout,
      });
    } else {
      this.logger.error('No LLM backend URL set.');
      throw new Error('No LLM Backend URL set.');
    }
  }

  async getInsightCaseSearchEngines(caseId: number, groupId: number) {
    try {
      this.logger.log(`getting insight cases search engines`);
      const res = await this.axios.get<{ list: { key: string; value: string }[] }>(
        `/insight-generator/${groupId}/cases/${caseId}/search-engines`,
      );
      return res.data;
    } catch (err) {
      this.logger.error(err, `get insight cases search engines failed`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightCaseLLMEngines(groupId: number) {
    const supportedLLMFeatureFlag = await this.featureFlagService.getOne(
      FeatureFlagKey.INSIGHT_GENERATOR_SUPPORTED_LLM_MODELS,
    );
    const group = await this.prisma.group.findUnique({
      select: { env: true },
      where: { id: groupId },
    });
    if (!group) {
      throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    }
    const supportedLLmList: string[] = supportedLLMFeatureFlag.metaData['values'] ?? [];
    const llmEngines = await this.prisma.llmEngine.findMany({
      where: { slug: { in: supportedLLmList }, isActive: true },
      orderBy: {
        sequence: 'asc',
      },
    });
    return llmEngines;
  }

  async getInsightCases(groupId: number, queryStr: string) {
    try {
      this.logger.log(`getting insight cases  - ${groupId}`);
      const res = await this.axios.get<{ list: InsightCaseResDto[]; count: number }>(
        `/insight-generator/${groupId}/cases?${queryStr}`,
      );

      const getList = res.data.list.map(async (item) => {
        const creator = await this.prisma.user.findUnique({
          where: { id: item.createdBy },
          select: { name: true, id: true },
        });
        let updatedBy = null;
        if (item.updatedBy) {
          updatedBy = await this.prisma.user.findUnique({
            where: { id: item.updatedBy },
            select: { name: true, id: true },
          });
        }
        return { ...item, createdBy: creator, updatedBy };
      });
      const list = await Promise.all(getList);
      return { list, count: res.data.count };
    } catch (err) {
      this.logger.error(err, `get insight cases failed - ${groupId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightCaseById(insightId: number, groupId: number) {
    try {
      this.logger.log(`getting insight case , id  - ${insightId} `);
      const res = await this.axios.get<InsightCaseResDto>(
        `/insight-generator/${groupId}/cases/${insightId}`,
      );

      return res.data;
    } catch (err) {
      this.logger.error(err, `getting insight case , id  - ${insightId} `);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async createInsightCase(userId: number, groupId: number, data: CreateInsightCaseDto) {
    try {
      this.logger.log(data, `creating insight case by user - ${userId}`);
      const res = await this.axios.post<InsightCaseResDto>(`/insight-generator/${groupId}/cases`, {
        ...data,
        groupId,
        createdBy: userId,
      });

      return res.data;
    } catch (err) {
      this.logger.error(err, `creating insight case by user - ${userId}`);
      throw new ApiException(ErrorCode.CREATE_INSIGHT_FAILED);
    }
  }

  async updateInsightCase(id: number, data: UpdateInsightCaseDto, groupId: number, userId: number) {
    try {
      this.logger.log(data, `updating insight case - id: ${id}`);
      const res = await this.axios.patch<InsightCaseResDto>(
        `/insight-generator/${groupId}/cases/${id}`,
        { ...data, updatedBy: userId },
      );

      return res.data;
    } catch (err) {
      this.logger.error(err, `updating insight case - id: ${id}`);
      throw new ApiException(ErrorCode.CREATE_INSIGHT_FAILED);
    }
  }

  async getInsightReports(groupId: number, queryStr: string) {
    try {
      this.logger.log(`getting insight reports  - ${groupId}`);
      const res = await this.axios.get<{ list: InsightReportResDto[]; count: number }>(
        `/insight-generator/${groupId}/reports?${queryStr}`,
      );

      const getList = res.data.list.map(async (item) => {
        const user = await this.prisma.user.findUnique({
          where: { id: item.triggeredBy },
          select: { name: true, id: true },
        });
        return { ...item, triggeredBy: user };
      });
      const list = await Promise.all(getList);
      return { list, count: res.data.count };
    } catch (err) {
      this.logger.error(err, `get insight reports failed - ${groupId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightReportById(insightReportId: number, groupId: number) {
    try {
      this.logger.log(`getting insight report , id  - ${insightReportId} `);
      const res = await this.axios.get<InsightReportResDto>(
        `/insight-generator/${groupId}/reports/${insightReportId}`,
      );

      return res.data;
    } catch (err) {
      this.logger.error(err, `getting insight report , id  - ${insightReportId} `);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightReportStatusById(insightReportId: number, groupId: number) {
    try {
      this.logger.log(`getting insight report status , id  - ${insightReportId} `);
      const res = await this.axios.get<InsightReportStatusResDto>(
        `/insight-generator/${groupId}/reports/${insightReportId}/status`,
      );

      return res.data;
    } catch (err) {
      this.logger.error(err, `getting insight report status , id  - ${insightReportId} `);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async createInsightReport(groupId: number, userId: number, insightCaseId: number) {
    try {
      this.logger.log(
        `create insight report, insight case id  - ${insightCaseId}, group id - ${groupId}, user id - ${userId} `,
      );
      const group = await this.prisma.group.findUnique({
        where: { id: groupId },
        select: { id: true },
      });
      if (!group) {
        throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
      }
      const res = await this.axios.post<{
        insightReport: InsightReportResDto;
        searchResultNum: number;
      }>(`/insight-generator/${groupId}/reports`, {
        groupId: group.id,
        userId,
        caseId: insightCaseId,
      });

      return res.data;
    } catch (err) {
      this.logger.error(
        err,
        `create insight report, insight case id  - ${insightCaseId}, group id - ${groupId}, user id - ${userId} `,
      );
      if ((err as any)?.response?.status === 403) {
        throw new ApiException(ErrorCode.BOT_MONTHLY_TOKEN_USAGE_EXCEEDED);
      }
      throw new ApiException(ErrorCode.CREATE_INSIGHT_FAILED);
    }
  }

  async getInsightReportDetails(insightReportId: number, queryStr: string, groupId: number) {
    try {
      this.logger.log(`getting insight report details  - ${insightReportId}`);
      const res = await this.axios.get<{ list: InsightReportDetailResDto[]; count: number }>(
        `/insight-generator/${groupId}/reports/${insightReportId}/details?${queryStr}`,
      );

      return res.data;
    } catch (err) {
      this.logger.error(err, `get insight report details failed - ${insightReportId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightReportCaseOptions(groupId: number) {
    try {
      this.logger.log(`getting insight report case options  - ${groupId}`);
      const res = await this.axios.get<{ options: { id: number; name: string }[] }>(
        `/insight-generator/${groupId}/reports/case-options`,
      );

      return res.data;
    } catch (err) {
      this.logger.error(err, `get insight report case options failed - ${groupId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightReportFile(insightReportId: number, groupId: number) {
    try {
      this.logger.log(`getting insight report file  - ${insightReportId}`);
      const res = await this.axios.get<Stream>(
        `/insight-generator/${groupId}/reports/${insightReportId}/report-file`,
        {
          responseType: 'stream',
        },
      );

      this.logger.log(`finished getting insight report file  - ${insightReportId}`);
      return res.data;
    } catch (err) {
      this.logger.error(`failed to get insight report file  - ${insightReportId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }
}
