import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { TokensModule } from '../../providers/tokens/tokens.module';
import { RolesGroupController } from './roles-group.controller';
import { RolesService } from './roles.service';
import { GroupsModule } from '../groups/groups.module';
import { RolesUsersController } from './role-user.controller';
import { PermissionsModule } from '../permissions/permissions.module';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';

@Module({
  imports: [
    PrismaModule,
    ConfigModule,
    TokensModule,
    forwardRef(() => GroupsModule),
    PermissionsModule,
    FeatureFlagModule,
  ],
  controllers: [RolesGroupController, RolesUsersController],
  providers: [RolesService],
  exports: [RolesService],
})
export class RolesModule {}
