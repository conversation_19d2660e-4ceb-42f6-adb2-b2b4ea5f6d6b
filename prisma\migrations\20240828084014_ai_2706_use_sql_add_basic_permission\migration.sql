-- This is an empty migration.
delete from public."RolePermission" where
        "roleId" in (SELECT rp."roleId" FROM public."Role" r
                                                 inner join public."RolePermission" rp on r."id" = rp."roleId" and r."roleType"='GROUP_CUSTOM'
                     where rp."permissionId"=(select id from public."Permission" where "permissionKey"='group-{groupId}:read-llm-model-basic' and "permissionType"='GROUP'))
                                      and "permissionId" in (SELECT  rp."permissionId"  FROM public."Role" r
                                                                                                 inner join public."RolePermission" rp on r."id" = rp."roleId" and r."roleType"='GROUP_CUSTOM'
                                                             where rp."permissionId"=(select id from public."Permission" where "permissionKey"='group-{groupId}:read-llm-model-basic' and "permissionType"='GROUP'));


INSERT INTO public."RolePermission"(
    "createdAt", "updatedAt", "roleId", "permissionId")
SELECT now() as createdAt,
       now() as updatedAt,
       "id" as roleId,
       (select id from public."Permission" p where p."permissionKey"='group-{groupId}:read-llm-model-basic' and p."permissionType"='GROUP') AS permissionId
FROM public."Role" where "roleType"='GROUP_CUSTOM';