import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { ChatSessionType, Prisma } from '@prisma/client';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { OrderByPipe } from 'src/pipes/order-by.pipe';
import { WherePipe } from 'src/pipes/where.pipe';
import { UserRequest } from '../auth/auth.interface';
import { Scopes } from '../auth/scope.decorator';
import { ChatSessionsService } from './chat-sessions.service';
import { CreateChatHistoryRequestDto } from './dto/create-chat-history-request.dto';
import { CreateChatSessionRequestDto } from './dto/create-chat-session-request.dto';
import { UpdateChatSessionRequestDto } from './dto/update-chat-session-request.dto';
import {
  AutoSelectType,
  FileHandleStrategy,
  UpdateDataSourceRequestDto,
} from './dto/update-data-source-request.dto';
import { ApiException, ErrorCode } from '../../errors/errors.constants';

@Controller('groups/:groupId')
@ApiBearerAuth('bearer-auth')
@ApiTags('Chat Sessions')
export class ChatSessionsController {
  constructor(private readonly chatSessionsService: ChatSessionsService) {}

  @Get('chat-sessions')
  @Scopes('group-{groupId}:read-chat-session')
  async searchChatSessions(
    @Req() request: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    return this.chatSessionsService.searchChatSessions(
      groupId,
      request.user.id,
      skip,
      take,
      where?.['name'] as Prisma.StringFilter | string | undefined,
      where?.['chatSessionType'] as Prisma.EnumChatSessionTypeFilter | ChatSessionType | undefined,
      orderBy,
    );
  }

  @Post('chat-sessions')
  @Scopes('group-{groupId}:write-chat-session')
  // ValidationPipe to ignore unwanted attibutes in ChatSetting
  @UsePipes(new ValidationPipe())
  async createChatSession(
    @Req() request: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() dto: CreateChatSessionRequestDto,
  ) {
    const dataSource = dto?.chatSetting?.dataSource;
    delete dto.chatSetting.dataSource;
    const chatSession = await this.chatSessionsService.createChatSession(
      groupId,
      request.user.id,
      dto.name,
      dto.chatSessionType,
      dto.chatSetting,
      dto.messageTemplateId,
    );
    if (dataSource?.['chatFile']?.files || dataSource?.['modelFile']?.files) {
      const updateDataSourceDto = {
        ...dataSource,
        chatSessionId: chatSession.id,
        chatSessionType: chatSession.chatSessionType,
      };
      const updateDataSource = await this.chatSessionsService.updateDataSource(
        groupId,
        chatSession,
        request.user.id,
        updateDataSourceDto as UpdateDataSourceRequestDto,
      );
      return updateDataSource;
    }
    return chatSession;
  }

  @Patch('chat-sessions/:id')
  @Scopes('group-{groupId}:write-chat-session')
  // ValidationPipe to ignore unwanted attibutes in ChatSetting
  @UsePipes(new ValidationPipe())
  async updateChatSession(
    @Req() request: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateChatSessionRequestDto,
  ) {
    return await this.chatSessionsService.updateChatSession(
      groupId,
      request.user.id,
      id,
      dto.name,
      dto.chatSetting,
      dto?.messageTemplateId,
    );
  }

  @Delete('chat-sessions/:id')
  @Scopes('group-{groupId}:write-chat-session')
  async deleteChatSession(
    @Req() request: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return await this.chatSessionsService.deleteChatSession(groupId, request.user.id, id);
  }

  @Get('chat-histories')
  @Scopes(
    'group-{groupId}:read-chat-session',
    'group-{groupId}:read-playground',
    'group-{groupId}:read-flow-playground',
  )
  async searchChatHistory(
    @Req() request: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('chatSessionId', OptionalIntPipe) chatSessionId?: number,
    @Query('chatSessionType') chatSessionType?: string,
  ) {
    return this.chatSessionsService.searchChatHistory(
      groupId,
      request.user.id,
      chatSessionId,
      ChatSessionType[chatSessionType],
      skip,
      take,
    );
  }

  @Post('chat-histories')
  @Scopes('group-{groupId}:write-chat-session')
  async createChatHistory(
    @Req() request: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() dto: CreateChatHistoryRequestDto,
  ) {
    const chatSession = await this.chatSessionsService.findChatSessionOrCreateDefault(
      dto.chatSessionId,
      dto.chatSessionType,
      groupId,
      request.user.id,
    );
    return await this.chatSessionsService.createChatHistory(
      chatSession.id,
      dto.contentType,
      dto.message,
    );
  }

  @Patch('chat-histories/:chatHistoryId')
  @Scopes('group-{groupId}:write-chat-session', 'user-*:read-public-bot')
  @UsePipes(new ValidationPipe())
  async updateChatHistory(
    @Param('chatHistoryId', ParseIntPipe) chatHistoryId: number,
    @Req() request: UserRequest,
    @Body() dto: CreateChatHistoryRequestDto,
  ) {
    return await this.chatSessionsService.updateChatHistory(
      request,
      chatHistoryId,
      dto.rating,
      dto.comment,
    );
  }

  @Post('chat-sessions/data-source')
  @Scopes('group-{groupId}:write-chat-session')
  @UsePipes(new ValidationPipe())
  async updateChatSessionDataSource(
    @Req() request: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() dto: UpdateDataSourceRequestDto,
  ) {
    this.validateAutoSelect(dto);
    const chatSession = await this.chatSessionsService.findChatSessionOrDefault(
      groupId,
      request.user.id,
      dto.chatSessionId,
      dto.chatSessionType,
    );

    if (chatSession.isDefault) {
      throw new ApiException(ErrorCode.INVALID_CHAT_SESSION_TO_UPDATE);
    }
    return await this.chatSessionsService.updateDataSource(
      groupId,
      chatSession,
      request.user.id,
      dto,
    );
  }

  @Post('global/data-source')
  @Scopes('group-{groupId}:write-global-data-source')
  @UsePipes(new ValidationPipe())
  async updateGlobalDataSource(
    @Req() request: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() dto: UpdateDataSourceRequestDto,
  ) {
    this.validateAutoSelect(dto);
    const chatSession = await this.chatSessionsService.findChatSessionOrDefault(
      groupId,
      request.user.id,
      dto.chatSessionId,
      dto.chatSessionType,
    );
    if (!chatSession.isDefault) {
      throw new ApiException(ErrorCode.INVALID_CHAT_SESSION_TO_UPDATE);
    }
    return await this.chatSessionsService.updateDataSource(
      groupId,
      chatSession,
      request.user.id,
      dto,
    );
  }

  private validateAutoSelect(dto: UpdateDataSourceRequestDto) {
    // if autoSelect is Filter, at least one filter should be provided
    if (
      dto.fileHandleStrategy === FileHandleStrategy.Embedding &&
      dto.modelFile?.autoSelect?.type === AutoSelectType.Filter
    ) {
      if (
        !(dto.modelFile?.autoSelect?.fileTypes?.length > 0) &&
        !(dto.modelFile?.autoSelect?.fileTags?.length > 0)
      ) {
        throw new ApiException(ErrorCode.INVALID_AUTO_SELECT_FILTER);
      }
    }
  }
}
