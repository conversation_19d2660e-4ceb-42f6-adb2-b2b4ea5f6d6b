import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { Configuration } from '../../config/configuration.interface';
import { ConfigService } from '@nestjs/config';

export interface AzureUserResponse {
  mail: string;
  userPrincipalName: string;
  displayName: string;
  businessPhones: Array<string>;
  givenName: string;
  surname: string;
  id: string;
  mobilePhone: string;
  officeLocation: string;
  preferredLanguage: string;
  jobTitle: string;
  error: AzureSSOError;
}
export interface AzureSSOError {
  code: string;
  message: string;
}

@Injectable()
export class AzureSSOService {
  axios?: AxiosInstance;
  private logger = new Logger(AzureSSOService.name);
  userInfoEndpoint: string;

  constructor(private configService: ConfigService) {
    const ssoConfig = this.configService.get<Configuration['ssoAzure']>('ssoAzure');
    this.userInfoEndpoint = ssoConfig.userInfo;
    this.axios = axios.create({
      timeout: 30000,
    });
  }

  async getUserInfoFormToken(token: string): Promise<AzureUserResponse> {
    try {
      const resp = await fetch(this.userInfoEndpoint, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: 'application/json',
        },
      });

      const responseData = await resp.json();
      responseData.mail = responseData.mail ? responseData.mail.toLowerCase() : '';

      return responseData;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
