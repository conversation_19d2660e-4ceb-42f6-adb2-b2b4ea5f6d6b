import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { $Enums, ChatFile, ChatFileType } from '@prisma/client';

export class ChatFileEntity implements ChatFile {
  
  @ApiProperty({ description: 'id' })
  id: number;

  @ApiProperty({ description: 'group id' })
  groupId: number;

  @ApiProperty({ description: 'user id' })
  userId: number;

  @ApiProperty({ description: 'file name' })
  filename: string;

  @ApiProperty({ description: 'file id' })
  s3Basename: string;

  @ApiProperty({ description: 'file size' })
  fileSize: number;

  @ApiProperty({ description: 'file expiry time' })
  expiresAt: Date;

  @ApiHideProperty()
  type: ChatFileType;

  @ApiProperty({ description: 'file scan report path' })
  fullScanReportPath: string;

  @ApiProperty({ description: 'file scan report create time' })
  fullScanReportCreatedAt: Date;

  @ApiProperty({ description: 'file scan report update time' })
  fullScanReportUpdatedAt: Date;

  @ApiProperty({ description: 'error code' })
  errCode: string;

  @ApiProperty({ description: 'has pii ?' })
  hasPii: string;

  @ApiProperty({ description: 'has prompt injection' })
  hasPromptInjection: $Enums.HasPromptInjection;

  @ApiProperty({ description: 'detected pii' })
  detectedPii: string;

  @ApiProperty({ description: 'pii scan status' })
  piiFileStatus: $Enums.PiiFileStatus;

  @ApiProperty({ description: 'file scan malware status' })
  scanMalwareStatus: $Enums.ScanMalwareStatus;

  @ApiProperty({ description: 'malware rating' })
  malwareRating: string;

  @ApiProperty({ description: 'file verify status' })
  verifyStatus: $Enums.FileVerifyStatus;

  @ApiProperty({ description: 'file verify error code' })
  verifyErrCode: string;

  @ApiProperty({ description: 'error message' })
  errorMsg: string;

  @ApiProperty({ description: 'file verify error message' })
  verifyErrorMsg: string;

  @ApiProperty({ description: 'has pii scan skip' })
  hasPiiScanSkip: boolean;

  @ApiProperty({ description: 'fullScanReportVersion' })
  fullScanReportVersion: number;

  @ApiProperty({ description: 'Malware scan version' })
  scanMalwareVersion: string;

  @ApiProperty({ description: 'bullmq id' })
  jobId: string;

  @ApiProperty({ description: 'job position' })
  position?: number;
}
