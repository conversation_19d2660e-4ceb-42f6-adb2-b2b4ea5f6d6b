import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  Res,
  Logger,
} from '@nestjs/common';

import { Membership } from '@prisma/client';
import { OptionalIntPipe } from '../../pipes/optional-int.pipe';
import { OrderByPipe } from '../../pipes/order-by.pipe';
import { WherePipe } from '../../pipes/where.pipe';
import { Expose } from '../../providers/prisma/prisma.interface';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { Scopes } from '../auth/scope.decorator';
import {
  BatchImportMembershipDto,
  CreateGroupMembershipDto,
  CreateReviewNorminateDto,
  UpdateMembershipDto,
  UpdateReviewNorminateDto,
} from './memberships.dto';
import { ExtendedMembership, MembershipsService } from './memberships.service';
import { UserRequest } from '../auth/auth.interface';
import { Api<PERSON>earerAuth, ApiTags } from '@nestjs/swagger';
import papa from 'papaparse';
import { Readable } from 'stream';
import { Response } from 'express';

@Controller('groups/:groupId/memberships')
@ApiBearerAuth('bearer-auth')
@ApiTags('Group Membership')
export class GroupMembershipController {
  constructor(private membershipsService: MembershipsService) {}
  private logger = new Logger(GroupMembershipController.name);

  /** To get batch upload csv template */
  @Get('batch/template')
  @Scopes('group-{groupId}:read-membership')
  async getTemplate(@Res() res: Response) {
    const csvData = this.membershipsService.getBatchUploadFileTemplate();
    const csvString = papa.unparse(csvData, { header: true });
    const stream = Readable.from(csvString);
    stream.pipe(res);
  }

  /** To add member to a group by batch */
  @Post('batch')
  @AuditLog('add-membership')
  @Scopes('group-{groupId}:write-membership')
  async createBatch(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: BatchImportMembershipDto,
  ) {
    const { members } = data;
    const successList = [];
    const failureList = [];
    for (const member of members) {
      try {
        await this.membershipsService.createGroupMembership(groupId, member, req.user);
        successList.push(member);
      } catch (err) {
        this.logger.error(err, `Failed to insert member to ${groupId}: ', ${member.email} `);
        failureList.push({
          ...member,
          errMsg:
            (err as any)?.response?.error?.message ??
            'Internal Server Error. Please try to fix input and re-upload again.',
        });
      }
    }
    return {
      successList,
      failureList,
    };
  }

  /** Add a member to a group */
  @Post()
  @AuditLog('add-membership')
  @Scopes('group-{groupId}:write-membership')
  async create(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: CreateGroupMembershipDto,
  ): Promise<Expose<Membership>> {
    return this.membershipsService.createGroupMembership(groupId, data, req.user);
  }

  /** Get memberships for a group */
  @Get()
  @Scopes('group-{groupId}:read-membership', 'group-*:read-member-ship-internal')
  async getAll(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<{ list: ExtendedMembership[]; count: number }> {
    const newWhere: Record<string, any> = { ...where, group: { id: groupId } };
    if (where && 'name' in where && where['name']) {
      newWhere['user'] = where;
      delete newWhere['name'];
    }
    if (where && 'email' in where && where['email']) {
      newWhere['user'] = {
        emails: { some: { email: where['email'] } },
      };
      delete newWhere['email'];
    }

    const list = await this.membershipsService.getMemberships({
      skip,
      take,
      orderBy,
      where: newWhere,
    });
    const count = await this.membershipsService.getMembershipsCount(newWhere);
    return {
      list,
      count,
    };
  }

  @Get('role-options')
  @Scopes('group-{groupId}:read-membership')
  async getRoleOptions(@Req() req: UserRequest, @Param('groupId', ParseIntPipe) groupId: number) {
    return this.membershipsService.getMembershipRoleOptions(groupId, req.user);
  }

  /** Get a membership for a group */
  @Get(':id')
  @Scopes('group-{groupId}:read-membership')
  async get(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Expose<Membership>> {
    return this.membershipsService.getGroupMembership(groupId, id);
  }

  /** To update member to a group by batch */
  @Patch('batch')
  @AuditLog('update-membership')
  @Scopes('group-{groupId}:write-membership')
  async updateBatch(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: BatchImportMembershipDto,
  ) {
    const { members } = data;
    const successList = [];
    const failureList = [];
    for (const member of members) {
      try {
        await this.membershipsService.updateGroupMembershipByEmail(groupId, member, req.user);
        successList.push(member);
      } catch (err) {
        this.logger.error(err, `Failed to insert member to ${groupId}: ', ${member.email} `);
        failureList.push({
          ...member,
          errMsg:
            (err as any)?.response?.error?.message ??
            'Internal Server Error. Please try to fix input and re-upload again.',
        });
      }
    }
    return {
      successList,
      failureList,
    };
  }

  /** Update a membership for a group */
  @Patch(':id')
  @AuditLog('update-membership')
  @Scopes('group-{groupId}:write-membership')
  async update(
    @Req() req: UserRequest,
    @Body() data: UpdateMembershipDto,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Expose<Membership>> {
    return this.membershipsService.updateGroupMembership(groupId, id, data, req.user);
  }

  /** To remove member to a group by batch */
  @Delete('batch')
  @AuditLog('delete-membership')
  @Scopes('group-{groupId}:write-membership')
  async removeBatch(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: BatchImportMembershipDto,
  ) {
    const { members } = data;
    const successList = [];
    const failureList = [];
    for (const member of members) {
      try {
        await this.membershipsService.deleteGroupMembershipByEmail(groupId, member, req.user);
        successList.push(member);
      } catch (err) {
        this.logger.error(err, `Failed to insert member to ${groupId}: ', ${member.email} `);
        failureList.push({
          ...member,
          errMsg:
            (err as any)?.response?.error?.message ??
            'Internal Server Error. Please try to fix input and re-upload again.',
        });
      }
    }
    return {
      successList,
      failureList,
    };
  }

  /** Remove a member from a group */
  @Delete(':id')
  @AuditLog('delete-membership')
  @Scopes('group-{groupId}:write-membership')
  async remove(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.membershipsService.deleteGroupMembership(groupId, id, req.user);
  }

  @Post('botReviewNomination')
  @AuditLog('create-botReview-nomination')
  @Scopes('group-{groupId}:write-review-nomination')
  async createReviewNomination(
    @Body() data: CreateReviewNorminateDto,
    @Param('groupId', ParseIntPipe) groupId: number,
  ) {
    return await this.membershipsService.createReviewNomination(data);
  }

  @Patch('botReviewNomination/:id')
  @AuditLog('update-botReview-nomination')
  @Scopes('group-{groupId}:write-review-nomination')
  async updateReviewNomination(
    @Body() data: UpdateReviewNorminateDto,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return await this.membershipsService.updateReviewNomination(id, data);
  }
  @Delete('botReviewNomination/:id')
  @AuditLog('delete-botReview-nomination')
  @Scopes('group-{groupId}:write-review-nomination')
  async deleteReviewNomination(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return await this.membershipsService.deleteReviewNomination(id);
  }
}
