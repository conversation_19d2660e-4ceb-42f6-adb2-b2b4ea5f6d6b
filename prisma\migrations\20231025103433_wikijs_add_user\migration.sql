/*
  Warnings:

  - You are about to drop the column `wikijsId` on the `Group` table. All the data in the column will be lost.

*/
-- CreateEnum
CREATE TYPE "WikiJSGroupRole" AS ENUM ('VIEWER', 'EDITOR');

-- DropIndex
DROP INDEX "Group_wikijsId_key";

-- AlterTable
ALTER TABLE "Group" DROP COLUMN "wikijsId";

-- CreateTable
CREATE TABLE "GroupWikijs" (
    "id" SERIAL NOT NULL,
    "groupId" INTEGER NOT NULL,
    "wikijsId" INTEGER NOT NULL,
    "role" "WikiJSGroupRole" NOT NULL DEFAULT 'VIEWER',

    CONSTRAINT "GroupWikijs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "GroupWikiJS.groupId_wikijsId_index" ON "GroupWikijs"("groupId", "wikijsId");
