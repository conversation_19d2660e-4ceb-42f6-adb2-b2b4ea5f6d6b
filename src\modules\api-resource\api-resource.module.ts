import { Module, forwardRef } from '@nestjs/common';
import { ApiResourceService } from './api-resource.service';
import { ApiResourceController } from './api-resource.controller';
import { PrismaModule } from 'src/providers/prisma/prisma.module';
import { ConfigModule } from '@nestjs/config';
import { S3Module } from 'src/providers/s3/s3.module';
import { GroupsModule } from '../groups/groups.module';
import { LLMBackendModule } from 'src/providers/llm-backend/llm-backend.module';
import { LLMModelsModule } from '../llm-models/llm-models.module';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';

@Module({
  imports: [
    PrismaModule,
    ConfigModule,
    S3Module,
    LLMBackendModule,
    forwardRef(() => LLMModelsModule),
    FeatureFlagModule,
    forwardRef(() => GroupsModule),
  ],
  controllers: [ApiResourceController],
  providers: [ApiResourceService],
  exports: [ApiResourceService],
})
export class ApiResourceModule {}
