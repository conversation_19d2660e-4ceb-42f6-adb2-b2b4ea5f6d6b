import { GroupType } from '@prisma/client';

export interface GroupFeatureSeed {
  name: string;
  key: string;
  groupTypes: GroupType[];
  isBasic?: boolean;
}

export enum GroupPermissionFeature {
  BASIC = 'basic',
  GROUP_SETTING = 'group-setting',
  UPLOAD_DATA = 'upload-data',
  GEN_KB = 'gen-kb',
  CONNECT_API = 'connect-api',
  AI_RESOURCE = 'ai-resource',
  TEST_AUTOMATION = 'test-automation',
  MEMBERSHIP = 'membership',
  MESSAGE_TEMPLATE = 'message-template',
  FLOW_SETTING = 'flow-setting',
  DASHBOARD = 'dashboard',
  BILLING_REPORT = 'billing-report',
  FEATURE_FLAG = 'feature-flag',
  ROLE = 'role',
  CHANGE_MANAGEMENT = 'change-management',
  API_KEY = 'api-key',
  AUDIT_LOG = 'audit-log',
  CHAT_REPORT = 'chat-report',
  SECURITY_SCAN = 'security-scan',
  PLAN_SUB = 'plan-sub',
  NOTIFICATION = 'notification',
  WEBHOOK = 'webhook',
  INSIGHT = 'insight',
  MODEL_PRICE = 'model-price',
  MODEL_PRICE_EVENT = 'model-price-event',
  MODEL_PRICE_SUMMARY = 'model-price-summary',
  PUBLIC_BOT = 'public-bot',
  BATCH_PROCESS = 'batch-process',
}

export const groupPermissionFeatureDetails: Record<GroupPermissionFeature, GroupFeatureSeed> = {
  [GroupPermissionFeature.BASIC]: {
    name: 'Basic',
    key: 'basic',
    groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT],
    isBasic: true,
  },
  [GroupPermissionFeature.GROUP_SETTING]: {
    name: 'Group Setting',
    key: 'group-setting',
    groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT],
    isBasic: false,
  },
  [GroupPermissionFeature.UPLOAD_DATA]: {
    name: 'Upload Data',
    key: 'upload-data',
    groupTypes: [GroupType.BOT, GroupType.INSIGHT],
    isBasic: false,
  },
  [GroupPermissionFeature.GEN_KB]: {
    name: 'GenKB',
    key: 'gen-kb',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.CONNECT_API]: {
    name: 'Connect API',
    key: 'connect-api',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.AI_RESOURCE]: {
    name: 'AI Resource',
    key: 'ai-resource',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.TEST_AUTOMATION]: {
    name: 'Test Automation',
    key: 'test-automation',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.MEMBERSHIP]: {
    name: 'Membership',
    key: 'membership',
    groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT],
    isBasic: false,
  },
  [GroupPermissionFeature.MESSAGE_TEMPLATE]: {
    name: 'Message Template',
    key: 'message-template',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.FLOW_SETTING]: {
    name: 'Flow Setting',
    key: 'flow-setting',
    groupTypes: [GroupType.FLOW],
    isBasic: false,
  },
  [GroupPermissionFeature.DASHBOARD]: {
    name: 'Dashboard',
    key: 'dashboard',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.BILLING_REPORT]: {
    name: 'Billing Report',
    key: 'billing-report',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.FEATURE_FLAG]: {
    name: 'Feature Flag',
    key: 'feature-flag',
    groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT],
    isBasic: false,
  },
  [GroupPermissionFeature.ROLE]: {
    name: 'Role',
    key: 'role',
    groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT],
    isBasic: false,
  },
  [GroupPermissionFeature.CHANGE_MANAGEMENT]: {
    name: 'Change Management',
    key: 'change-management',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.API_KEY]: {
    name: 'API Key',
    key: 'api-key',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.AUDIT_LOG]: {
    name: 'Audit Log',
    key: 'audit-log',
    groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT],
    isBasic: false,
  },
  [GroupPermissionFeature.CHAT_REPORT]: {
    name: 'Chat Report',
    key: 'chat-report',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.SECURITY_SCAN]: {
    name: 'Security Scan',
    key: 'security-scan',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.PLAN_SUB]: {
    name: 'Plan & Sub',
    key: 'plan-sub',
    groupTypes: [GroupType.FLOW, GroupType.BOT, GroupType.INSIGHT],
    isBasic: false,
  },
  [GroupPermissionFeature.NOTIFICATION]: {
    name: 'Notification',
    key: 'notification',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.WEBHOOK]: {
    name: 'Webhook',
    key: 'webhook',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.INSIGHT]: {
    name: 'Insight',
    key: 'insight',
    groupTypes: [GroupType.INSIGHT],
    isBasic: false,
  },
  [GroupPermissionFeature.MODEL_PRICE]: {
    name: 'Model Price',
    key: 'model-price',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.MODEL_PRICE_EVENT]: {
    name: 'Model Price Event',
    key: 'model-price-event',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.MODEL_PRICE_SUMMARY]: {
    name: 'Model Price Summary',
    key: 'model-price-summary',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.PUBLIC_BOT]: {
    name: 'public bot',
    key: 'public-bot',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
  [GroupPermissionFeature.BATCH_PROCESS]: {
    name: 'Batch Process',
    key: 'batch-process',
    groupTypes: [GroupType.BOT],
    isBasic: false,
  },
};
