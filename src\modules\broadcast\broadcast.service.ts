import { Injectable, Logger } from '@nestjs/common';
import { GroupType, Prisma, UserGroup, UserGroupFilter } from '@prisma/client';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { BroadcastDto, UserNotificationDto, UserNotificationResponse } from './broadcast.dto';
import { UserGroupService } from '../user-group/user-group.service';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { BROADCAST_TARGET_TYPE_ALL, SYSTEM_TEXT } from './broadcast.contants';
import { UserGroupFilterDto } from '../user-group/user-group.dto';
import { NotificationBackendService } from '../../providers/notification-backend/notification-backend.service';
import { BroadcastResponse } from '../../providers/notification-backend/notification-backend.interface';
import { NotificationGroupDto } from '../user-notification/user-notification.dto';

@Injectable()
export class BroadcastService {
  private logger = new Logger(BroadcastService.name);

  constructor(
    private prisma: PrismaService,
    private userGroupService: UserGroupService,
    private notificationBackendService: NotificationBackendService,
  ) {}

  async getAll(
    skip?: number,
    take?: number,
    where?: Record<string, number | string>,
    orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    const responseData = await this.notificationBackendService.getBroadcasts(
      skip,
      take,
      where,
      orderBy,
    );
    if (responseData?.list?.length > 0) {
      for (const responseDatum of responseData.list) {
        if (responseDatum.userGroupId) {
          const userGroup = await this.prisma.userGroup.findUnique({
            where: { id: responseDatum.userGroupId },
            select: { name: true },
          });
          responseDatum.recipient = userGroup?.name;
        } else {
          responseDatum.recipient = responseDatum.targetType === 'ALL' ? 'All User' : '';
        }
        const user = await this.prisma.user.findUnique({
          where: { id: responseDatum.createdByUserId },
          select: { name: true },
        });
        responseDatum.createdBy = user?.name;
      }
    }
    return responseData;
  }

  async getUserGroup(userGroupId: number) {
    return await this.prisma.userGroup.findUnique({
      where: { id: userGroupId },
      include: {
        userGroupFilter: true,
      },
    });
  }

  async create(data: BroadcastDto, userId: number): Promise<BroadcastResponse> {
    await this.checkUserGroupIdValid(data.userGroupId);
    if (
      await this.checkSystemTextValid(
        data.htmlContent,
        data.targetType === BROADCAST_TARGET_TYPE_ALL ? null : data.userGroupId,
      )
    ) {
      return await this.notificationBackendService.createBroadcast({
        title: data.title,
        htmlContent: data.htmlContent,
        textContent: data.textContent,
        targetType: data.targetType,
        userId: userId,
        userGroupId: data.userGroupId,
        channel: data.channel,
        scheduleType: data.scheduleType,
        scheduleTime: data.scheduleTime,
      });
    }
    throw new ApiException(ErrorCode.INVALID_SYSTEM_TEXT);
  }

  async update(id: number, data: BroadcastDto, userId: number): Promise<BroadcastResponse> {
    this.logger.log(`user [${userId}] update broadcast id - ${id}`);
    await this.checkUserGroupIdValid(data.userGroupId);
    if (await this.checkSystemTextValid(data.htmlContent, data.userGroupId)) {
      return await this.notificationBackendService.updateBroadcast(id, {
        title: data.title,
        htmlContent: data.htmlContent,
        textContent: data.textContent,
        targetType: data.targetType,
        userGroupId: data.userGroupId,
        channel: data.channel,
        scheduleType: data.scheduleType,
        scheduleTime: data.scheduleTime,
      });
    }
    throw new ApiException(ErrorCode.INVALID_SYSTEM_TEXT);
  }

  async delete(id: number, userId: number): Promise<{ broadcastId: number }> {
    this.logger.log(`user [${userId}] delete broadcast id - ${id}`);
    await this.notificationBackendService.deleteBroadcast(id);
    return { broadcastId: id };
  }

  async getSupportSystemTexts(userGroupId?: number) {
    const texts: string[] = [];
    texts.push(SYSTEM_TEXT.USER_NAME);
    texts.push(SYSTEM_TEXT.CCC);
    texts.push(SYSTEM_TEXT.BU_OR_FU_NAME);
    texts.push(SYSTEM_TEXT.SYSTEM_ROLE);
    if (userGroupId) {
      const userGroup = await this.getUserGroup(userGroupId);
      if (!userGroup) throw new ApiException(ErrorCode.USER_GROUP_NOT_FOUND);
      if (
        userGroup.userGroupFilter?.cccList?.length > 0 ||
        userGroup.userGroupFilter?.businessUnits?.length > 0 ||
        userGroup.userGroupFilter?.groupIds?.length > 0
      ) {
        texts.push(SYSTEM_TEXT.GROUP_LIST);
      }
    }
    return texts;
  }

  async checkSystemTextValid(content: string, userGroupId?: number) {
    const regex = /\{\{([^}]+)\}\}/g;
    const matches = content.match(regex);
    if (matches?.length > 0) {
      const supportSystemText: string[] = await this.getSupportSystemTexts(userGroupId);
      return matches.every((item) => supportSystemText.includes(item));
    }
    return true;
  }

  async getUserNotificationsData(
    includeAllUser: boolean,
    userGroupId?: number,
  ): Promise<UserNotificationResponse> {
    const userNotifications: UserNotificationDto[] = [];
    let groupNames: string[] = [];
    if (includeAllUser) {
      const dataList = await this.getUserNotificationsDataByUserInfo(null);
      return { userNotifications: dataList, groupNames: [] };
    }
    const userGroup = await this.getUserGroup(userGroupId);
    if (!userGroup) throw new ApiException(ErrorCode.USER_GROUP_NOT_FOUND);
    const filter = userGroup.userGroupFilter;
    if (filter) {
      //get group relate membership data
      if (this.isExistGroupFilter(filter)) {
        const groupsInfo = await this.getGroups(filter);
        groupNames = groupsInfo.groupNames;
        const membershipWhere = this.getMembershipWhereInput(false, groupsInfo.groupIds, filter);
        const dataList = await this.getUserNotificationsDataByGroupInfo(membershipWhere);
        userNotifications.push(...dataList);
      } else if (this.isExistGroupRoleFilter(filter)) {
        const membershipWhere = this.getMembershipWhereInput(true, null, filter);
        const dataList = await this.getUserNotificationsDataByGroupInfo(membershipWhere);
        userNotifications.push(...dataList);
      } else {
        if (this.isExistUserFilter(filter)) {
          const userWhere = this.getUserWhereInput(filter);
          const dataList = await this.getUserNotificationsDataByUserInfo(userWhere);
          userNotifications.push(...dataList);
        }
      }
    }
    const emails = userGroup.additionalEmails;
    if (emails?.length > 0) {
      const dataList = await this.getUserNotificationsDataByEmails(emails);
      if (userNotifications.length === 0) {
        userNotifications.push(...dataList);
      } else {
        dataList.forEach((item) => {
          if (!userNotifications.find((element) => element.userId === item.userId)) {
            userNotifications.push(item);
          }
        });
      }
    }
    return { userNotifications: userNotifications, groupNames };
  }

  async getUserNotificationsDataByUserInfo(
    where: Prisma.UserWhereInput,
  ): Promise<UserNotificationDto[]> {
    const users = await this.prisma.user.findMany({
      where: {
        ...where,
        active: true,
      },
      select: {
        emails: {
          select: {
            email: true,
          },
        },
        userRole: {
          select: {
            systemName: true,
          },
        },
        id: true,
        name: true,
        ccc: true,
        businessUnit: true,
      },
    });
    const userNotifications: UserNotificationDto[] = [];
    users.forEach((user) => {
      userNotifications.push({
        userId: user.id,
        userName: user.name,
        email: user.emails[0].email,
        ccc: user.ccc,
        businessUnit: user.businessUnit,
        roleName: user.userRole.systemName,
      });
    });
    return userNotifications;
  }

  async getUserNotificationsDataByGroupInfo(
    where: Prisma.MembershipWhereInput,
  ): Promise<UserNotificationDto[]> {
    const members = await this.prisma.membership.findMany({
      where,
      distinct: ['userId'],
      include: {
        user: {
          include: {
            emails: {
              select: {
                email: true,
              },
            },
            userRole: {
              select: {
                systemName: true,
              },
            },
          },
        },
      },
    });
    const userNotifications: UserNotificationDto[] = [];
    members.forEach((member) => {
      userNotifications.push({
        userId: member.userId,
        userName: member.user.name,
        email: member.user.emails[0].email,
        ccc: member.user.ccc,
        businessUnit: member.user.businessUnit,
        roleName: member.user.userRole.systemName,
      });
    });
    return userNotifications;
  }

  async getUserNotificationsDataByEmails(emails: string[]) {
    const emailList = await this.prisma.email.findMany({
      where: {
        email: {
          in: emails,
        },
      },
      distinct: ['userId'],
      include: {
        user: {
          include: {
            userRole: {
              select: {
                systemName: true,
              },
            },
          },
        },
      },
    });
    const userNotifications: UserNotificationDto[] = [];
    emailList.forEach((email) => {
      userNotifications.push({
        userId: email.userId,
        userName: email.user.name,
        email: email.email,
        ccc: email.user.ccc,
        businessUnit: email.user.businessUnit,
        roleName: email.user.userRole.systemName,
      });
    });
    return userNotifications;
  }

  private getMembershipWhereInput(
    allGroup: boolean,
    groupIds: number[],
    filter: UserGroupFilterDto,
  ) {
    return {
      groupId: {
        in: allGroup ? undefined : groupIds,
      },
      roleId: {
        in: filter?.groupRoleIds?.length > 0 ? filter.groupRoleIds : undefined,
      },
      user: {
        roleId: {
          in: filter?.systemRoleIds?.length > 0 ? filter.systemRoleIds : undefined,
        },
        active: true,
      },
    };
  }

  private getUserWhereInput(filter: UserGroupFilterDto) {
    return {
      roleId: {
        in: filter?.systemRoleIds?.length > 0 ? filter.systemRoleIds : undefined,
      },
      active: true,
    };
  }

  async getGroups(filter: UserGroupFilter) {
    if (filter) {
      const whereInput: Prisma.GroupWhereInput = this.getGroupWhereInput(filter);
      const groups = await this.prisma.group.findMany({
        select: {
          id: true,
          name: true,
          groupType: true,
          llmModel: {
            select: {
              active: true,
            },
          },
        },
        where: whereInput,
      });
      const groupIds = [];
      const groupNames = [];
      groups?.forEach((g) => {
        if (g.groupType === GroupType.FLOW || g.llmModel?.active) {
          groupIds.push(g.id);
          groupNames.push(g.name);
        }
      });
      return {
        groupIds: groupIds ?? [],
        groupNames: groupNames ?? [],
      } as NotificationGroupDto;
    }
    return {
      groupIds: [],
      groupNames: [],
    } as NotificationGroupDto;
  }

  private getGroupWhereInput(userGroupFilter: UserGroupFilterDto): Prisma.GroupWhereInput {
    const whereInput: Prisma.GroupWhereInput = {
      OR:
        userGroupFilter.cccList?.length > 0
          ? [
              ...userGroupFilter.cccList.map((item) => ({
                ccc: {
                  startsWith: item,
                },
              })),
            ]
          : undefined,
    };
    whereInput.businessUnit = {
      in: userGroupFilter.businessUnits?.length > 0 ? userGroupFilter.businessUnits : undefined,
    };
    const groupIds = userGroupFilter.groupIds;
    if (userGroupFilter?.groupIds?.length > 0) {
      whereInput.id = { in: groupIds };
    }
    if (userGroupFilter?.groupIds?.length === 0) {
      whereInput.groupType = userGroupFilter.groupType ? userGroupFilter.groupType : undefined;
    }
    return whereInput;
  }

  private async checkUserGroupIdValid(userGroupId: number): Promise<void> {
    if (userGroupId) {
      const userGroup: UserGroup = await this.prisma.userGroup.findUnique({
        where: { id: userGroupId },
      });
      if (!userGroup) {
        throw new ApiException(ErrorCode.USER_GROUP_NOT_FOUND);
      }
    }
  }

  private isExistGroupFilter(filter: UserGroupFilter) {
    if (filter) {
      if (
        filter.groupIds?.length > 0 ||
        filter.cccList?.length > 0 ||
        filter.businessUnits?.length > 0 ||
        filter.groupType
      ) {
        return true;
      }
    }
    return false;
  }

  private isExistGroupRoleFilter(filter: UserGroupFilter) {
    if (filter) {
      if (filter.groupRoleIds?.length > 0) {
        return true;
      }
    }
    return false;
  }

  private isExistUserFilter(filter: UserGroupFilter) {
    if (filter) {
      if (filter.systemRoleIds?.length > 0) {
        return true;
      }
    }
    return false;
  }
}
