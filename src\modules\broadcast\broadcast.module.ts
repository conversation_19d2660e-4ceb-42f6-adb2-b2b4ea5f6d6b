import { Module } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { BroadcastController } from './broadcast.controller';
import { BroadcastService } from './broadcast.service';
import { UserGroupModule } from '../user-group/user-group.module';
import { NotificationBackendModule } from '../../providers/notification-backend/notification-backend.module';

@Module({
  imports: [PrismaModule, UserGroupModule, NotificationBackendModule],
  controllers: [BroadcastController],
  providers: [BroadcastService],
  exports: [BroadcastService],
})
export class BroadcastModule {}
