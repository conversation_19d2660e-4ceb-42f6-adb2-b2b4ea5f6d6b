-- Custom SQL: remove '*' permission
BEGIN;

DO $$
DECLARE
  permission_id_to_delete integer;

BEGIN

SELECT id INTO permission_id_to_delete from public."Permission" WHERE "permissionKey"='*';

DELETE FROM public."RolePermission" WHERE "permissionId"=permission_id_to_delete;
DELETE FROM public."PermissionGroupSetting" WHERE "permissionId"=permission_id_to_delete;
DELETE FROM public."Permission" WHERE id=permission_id_to_delete;

END; $$;

COMMIT;