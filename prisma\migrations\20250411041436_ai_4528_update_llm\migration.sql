-- This is an empty migration.
UPDATE "FeatureFlag"
SET "metaData" = jsonb_set(
        "metaData",
        '{values}',
        (
            SELECT jsonb_agg(distinct
                           CASE
                               WHEN value = '"vertexai-gemini-1.5-pro-001"'::jsonb THEN '"vertexai-gemini-2.0-flash-001"'::jsonb
                               WHEN value = '"vertexai-gemini-1.5-flash-001"'::jsonb THEN '"vertexai-gemini-2.0-flash-lite"'::jsonb
                               WHEN value = '"vertexai-gemini-2.0-flash-lite-preview"'::jsonb THEN '"vertexai-gemini-2.0-flash-lite"'::jsonb
                               WHEN value = '"vertexai-gemini-1.5-pro-002"'::jsonb THEN '"vertexai-gemini-2.0-flash-001"'::jsonb
                               WHEN value = '"vertexai-gemini-1.5-flash-002"'::jsonb THEN '"vertexai-gemini-2.0-flash-lite"'::jsonb
                               ELSE value
                               END
                       )
            FROM jsonb_array_elements("metaData"->'values') AS value
    )
    )
WHERE key = 'BOT.CONFIG_INSIGHT_GENERATOR_SUPPORTED_LLM_MODELS';


UPDATE "FeatureFlag"
SET "metaData" = jsonb_set(
        "metaData",
        '{values}',
        (
            SELECT jsonb_agg(distinct value)
            FROM jsonb_array_elements("metaData"->'values') AS value
            WHERE
            value::text NOT LIKE '"gpt-35%'

            AND value::text NOT LIKE '"nova%'

            AND value::text NOT LIKE '"vertexai-gemini-%'
    )
    )
WHERE key = 'AUTO_TEST.CONFIG_ALLOWED_RUN_AUTO_TEST_MODELS';

UPDATE "FeatureFlag"
SET "metaData" = jsonb_set(
        "metaData",
        '{allowedModels}',
        (
            SELECT jsonb_agg(distinct
                           CASE
                               WHEN value = '"vertexai-gemini-1.5-pro-001"'::jsonb THEN '"vertexai-gemini-2.0-flash-001"'::jsonb
                               WHEN value = '"vertexai-gemini-1.5-flash-001"'::jsonb THEN '"vertexai-gemini-2.0-flash-lite"'::jsonb
                               WHEN value = '"vertexai-gemini-2.0-flash-lite-preview"'::jsonb THEN '"vertexai-gemini-2.0-flash-lite"'::jsonb
                               WHEN value = '"vertexai-gemini-1.5-pro-002"'::jsonb THEN '"vertexai-gemini-2.0-flash-001"'::jsonb
                               WHEN value = '"vertexai-gemini-1.5-flash-002"'::jsonb THEN '"vertexai-gemini-2.0-flash-lite"'::jsonb
                               ELSE value
                               END
                       )
            FROM jsonb_array_elements("metaData"->'allowedModels') AS value
    )
    )
WHERE key = 'BOT.CONFIG_FILE_TAGGING';