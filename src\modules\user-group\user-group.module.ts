import { Module } from '@nestjs/common';
import { UserGroupController } from './user-group.controller';
import { UserGroupService } from './user-group.service';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { NotificationBackendModule } from '../../providers/notification-backend/notification-backend.module';

@Module({
  imports: [PrismaModule, NotificationBackendModule],
  controllers: [UserGroupController],
  providers: [UserGroupService],
  exports: [UserGroupService],
})
export class UserGroupModule {}
