import { Injectable, NestMiddleware } from '@nestjs/common';
import { NextFunction, Response } from 'express';
import apm from 'elastic-apm-node';

@Injectable()
export class InjectResponseHeaderMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    res.setHeader('Access-Control-Expose-Headers', '*');
    if (apm.isStarted()) {
      if (apm.currentTraceIds['trace.id']) {
        res.setHeader('X-Trace-Id', apm.currentTraceIds['trace.id']);
      }
    }
    next();
  }
}
