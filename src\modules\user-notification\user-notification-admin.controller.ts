import { <PERSON>, Get, Logger, Param, ParseIntPipe, Patch, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { OptionalIntPipe } from '../../pipes/optional-int.pipe';
import { WherePipe } from '../../pipes/where.pipe';
import { OrderByPipe } from '../../pipes/order-by.pipe';
import { UserNotificationService } from './user-notification.service';
import { Scopes } from '../auth/scope.decorator';

@Controller('user-notification')
@ApiBearerAuth('bearer-auth')
@ApiTags('User Notification Admin')
export class UserNotificationAdminController {
  private logger = new Logger(UserNotificationAdminController.name);

  constructor(private userNotificationService: UserNotificationService) {
  }

  @Get()
  @Scopes('system:read-broadcast')
  async getAll(
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    return await this.userNotificationService.getAll(
      skip,
      take,
      where,
      orderBy,
    );
  }
}