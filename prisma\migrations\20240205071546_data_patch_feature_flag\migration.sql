-- This is an empty migration.
-- Custom SQL: update feature flags
UPDATE "FeatureFlag" 
SET "value" = '', "isEnabled" = true, "isForClientSide" = true
WHERE "FeatureFlag"."key" in ('BOT_FILE_UPLOAD.ENABLE_SECONDARY_APPROVAL', 'ADMIN.ENABLE_DELETE_BOT',
  'ACCOUNT_MANAGEMENT.ENABLE_TRUE_DELETE_USER', 'AUTH.ENABLE_USERNAME_PASSWORD_LOGIN'
);

UPDATE "FeatureFlag" 
SET "value" = '', "isEnabled" = true, "isForClientSide" = false, "metaData" = '"{\"value\":10000}"'::jsonb 
WHERE "FeatureFlag"."key" = 'BOT_FILE_UPLOAD.CONFIG_MAX_UPLOADED_FILES_PER_USER';