import { Module } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { NotificationBackendModule } from '../../providers/notification-backend/notification-backend.module';
import { RedisModule } from '../../providers/redis/redis.module';
import { MailModule } from '../../providers/mail/mail.module';
import { GroupNotificationController } from './group-notification.controller';
import { GroupNotificationService } from './group-notification.service';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [PrismaModule, RedisModule, NotificationBackendModule, MailModule, ConfigModule],
  controllers: [GroupNotificationController],
  providers: [GroupNotificationService],
  exports: [GroupNotificationService],
})
export class GroupNotificationModule {}
