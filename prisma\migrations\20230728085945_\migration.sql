/*
  Warnings:

  - You are about to alter the column `batchId` on the `LogFileHistory` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - A unique constraint covering the columns `[botId,batchId,logDate]` on the table `LogFileHistory` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `updatedAt` to the `LogFileHistory` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "LogFileHistory.search_index";

-- AlterTable
ALTER TABLE "LogFileHistory" ADD COLUMN     "errorMsg" VARCHAR(255),
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL,
ALTER COLUMN "batchId" SET DATA TYPE VARCHAR(255);

-- CreateIndex
CREATE UNIQUE INDEX "LogFileHistory.search_index" ON "LogFileHistory"("botId", "batchId", "logDate");
