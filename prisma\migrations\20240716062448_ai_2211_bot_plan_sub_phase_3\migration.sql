-- Clear the existing bot plan information --
DELETE FROM "ResourceCategory" WHERE "subscriberType" = 'BOT';
DELETE FROM "Resource" WHERE "subscriberType" = 'BOT';
DELETE FROM "Plan" WHERE "planKey" LIKE '%llm-engine%';
DELETE FROM "PlanSubscription" WHERE 'subscriberEntityType' = 'BOT';
DELETE FROM "PlanSubscriptionRequest" WHERE 'subscriberEntityType' = 'BOT';
DELETE FROM "Permission" WHERE "permissionKey" LIKE '%llm-engine-%';
DELETE FROM "ResourceQuota" WHERE "quotaKey" LIKE '%llm-engine-%';
DELETE FROM "PlanQuota" pq WHERE (SELECT id FROM "ResourceQuota" q WHERE q.id = pq."quotaId" ) IS null or 
(SELECT id FROM "Plan" p WHERE p.id = pq."planId" ) IS null ;
DELETE FROM "PlanPermission" pp WHERE (SELECT id FROM "Permission" p WHERE p.id = pp."permissionId") IS null or 
(SELECT pp."planId" FROM "Plan" pl WHERE pl.id = pp."planId") IS null ;

-- DropIndex
DROP INDEX "plan_quota_index";

-- AlterTable
ALTER TABLE "Plan" ADD COLUMN     "entityId" INTEGER,
ADD COLUMN     "entityType" "ResourceSubsciberType",
ADD COLUMN     "groupEnvs" "Environment"[],
ADD COLUMN     "isDefault" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "isDisabledPlan" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "PlanQuota" DROP CONSTRAINT "PlanQuota_pkey",
DROP COLUMN "quotaId",
ADD COLUMN     "quotaValueId" INTEGER NOT NULL,
ADD CONSTRAINT "PlanQuota_pkey" PRIMARY KEY ("planId", "quotaValueId");

-- AlterTable
ALTER TABLE "Resource" ADD COLUMN     "permissionId" INTEGER,
ADD COLUMN     "quotaRuleId" INTEGER;

-- DropTable
DROP TABLE "ResourceQuota";

-- CreateTable
CREATE TABLE "ResourceQuotaRule" (
    "id" SERIAL NOT NULL,
    "ruleKey" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "quotaType" TEXT NOT NULL,

    CONSTRAINT "ResourceQuotaRule_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ResourceQuotaValue" (
    "id" SERIAL NOT NULL,
    "quotaKey" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "value" INTEGER NOT NULL,

    CONSTRAINT "ResourceQuotaValue_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ResourceQuotaRule_ruleKey_key" ON "ResourceQuotaRule"("ruleKey");

-- CreateIndex
CREATE UNIQUE INDEX "ResourceQuotaValue_quotaKey_key" ON "ResourceQuotaValue"("quotaKey");

-- CreateIndex
CREATE INDEX "plan_quota_index" ON "PlanQuota"("quotaValueId");

UPDATE "Plan" SET "isDefault" = true, "isDisabledPlan" = true WHERE "planKey" = 'disable-flow';
UPDATE "Resource" SET "permissionId" = (SELECT id FROM "Permission" p WHERE p."permissionKey"= 'system:create-flow');

ALTER TABLE "ResourceQuotaValue" ADD COLUMN     "ruleId" INTEGER NOT NULL,
ALTER COLUMN "description" DROP NOT NULL;

-- DropIndex
DROP INDEX "Plan_planRoleIdsRequired_idx";

-- CreateIndex
CREATE INDEX "Plan_resourceId_entityType_entityId_idx" ON "Plan"("resourceId", "entityType", "entityId");

-- CreateIndex
CREATE INDEX "Plan_resourceId_entityType_entityId_planRoleIdsRequired_gro_idx" ON "Plan"("resourceId", "entityType", "entityId", "planRoleIdsRequired", "groupEnvs");

-- CreateIndex
CREATE INDEX "PlanSubscriptionRequest_createdBy_idx" ON "PlanSubscriptionRequest"("createdBy");

-- CreateIndex
CREATE INDEX "PlanSubscriptionRequest_operatedBy_idx" ON "PlanSubscriptionRequest"("operatedBy");

-- CreateIndex
CREATE INDEX "PlanSubscriptionRequest_subscriberEntityId_idx" ON "PlanSubscriptionRequest"("subscriberEntityId");

-- CreateIndex
CREATE INDEX "Resource_permissionId_idx" ON "Resource"("permissionId");

-- CreateIndex
CREATE INDEX "Resource_quotaRuleId_idx" ON "Resource"("quotaRuleId");

-- CreateIndex
CREATE INDEX "ResourceQuotaValue_ruleId_idx" ON "ResourceQuotaValue"("ruleId");

CREATE INDEX "Plan_resourceId_isDefault_idx" ON "Plan"("resourceId", "isDefault");


DELETE FROM "FeatureFlag" f WHERE f."key" = 'ADMIN.CONFIG_DEFAULT_PLAN_SUBSCRIPTION';