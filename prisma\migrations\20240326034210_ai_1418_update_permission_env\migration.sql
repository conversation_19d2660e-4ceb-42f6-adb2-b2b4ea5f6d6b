-- custom sql: data patch
UPDATE "Permission" SET "envs" = enum_range(null::"Environment") WHERE "permissionKey" = 'group-{groupId}:write-info' AND "permissionType" = 'GROUP';
UPDATE "Permission" SET "envs" = enum_range(null::"Environment") WHERE "permissionKey" = 'group-{groupId}:write-membership-*' AND "permissionType" = 'GROUP';
UPDATE "Permission" SET "envs" = enum_range(null::"Environment") WHERE "permissionKey" = 'group-{groupId}:delete-membership-*' AND "permissionType" = 'GROUP';
UPDATE "Permission" SET "envs" = enum_range(null::"Environment") WHERE "permissionKey" = 'group-{groupId}:write-role-*' AND "permissionType" = 'GROUP';
UPDATE "Permission" SET "envs" = enum_range(null::"Environment") WHERE "permissionKey" = 'group-{groupId}:delete-role-*' AND "permissionType" = 'GROUP';
UPDATE "Permission" SET "envs" = enum_range(null::"Environment") WHERE "permissionKey" = 'group-{groupId}:write-message-template-*' AND "permissionType" = 'GROUP';
UPDATE "Permission" SET "envs" = enum_range(null::"Environment") WHERE "permissionKey" = 'group-{groupId}:delete-message-template-*' AND "permissionType" = 'GROUP';
UPDATE "Permission" SET "envs" = enum_range(null::"Environment") WHERE "permissionKey" = 'group-{groupId}:write-webhook-*' AND "permissionType" = 'GROUP';
UPDATE "Permission" SET "envs" = enum_range(null::"Environment") WHERE "permissionKey" = 'group-{groupId}:delete-webhook-*' AND "permissionType" = 'GROUP';
UPDATE "Permission" SET "envs" = enum_range(null::"Environment") WHERE "permissionKey" = 'group-{groupId}:write-ai-resource' AND "permissionType" = 'GROUP';
UPDATE "Permission" SET "envs" = enum_range(null::"Environment") WHERE "permissionKey" = 'group-{groupId}:write-playground-*' AND "permissionType" = 'GROUP';
UPDATE "Permission" SET "envs" = enum_range(null::"Environment") WHERE "permissionKey" = 'group-{groupId}:write-gen-kb-*' AND "permissionType" = 'GROUP';
UPDATE "Permission" SET "envs" = enum_range(null::"Environment") WHERE "permissionKey" = 'group-{groupId}:write-owner-role-*' AND "permissionType" = 'GROUP';
 
UPDATE "Permission" SET "envs" = '{TEST}' WHERE "permissionKey" = 'group-{groupId}:read-entity-snapshot' AND "permissionType" = 'GROUP';