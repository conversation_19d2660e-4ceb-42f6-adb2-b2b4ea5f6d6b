import { Body, Controller, Get, Param, Post, Req, Res, UseInterceptors } from '@nestjs/common';
import { Request, Response } from 'express';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GetAllNodesResponse } from 'src/providers/flow-backend/flow-backend.interface';
import { FlowBackendService } from '../../providers/flow-backend/flow-backend.service';
import { UserRequest } from '../auth/auth.interface';
import { Scopes } from '../auth/scope.decorator';
import { ChatHistoryInterceptor } from '../chat-sessions/interceptor/chat-history.interceptor';
import { ConvertSseInterceptor } from 'src/interceptors/convert-sse.interceptor';
import { FlowsService } from '../flows/flows.service';
import { FlowChatRequest, InternalFlowChatResponse, LoadMethodOption } from './flows.dto';
import { ChatRequestInterceptor } from '../chat-sessions/interceptor/chat-request.interceptor';

@Controller('flows')
@ApiBearerAuth('bearer-auth')
@ApiTags('Flow')
export class FlowsController {
  constructor(
    private flowsService: FlowsService,
    private flowBackendService: FlowBackendService,
  ) {}

  @Get(':groupId/nodes')
  @Scopes('group-{groupId}:write-flow')
  async getAllNodes(@Param('groupId') groupId: number): Promise<GetAllNodesResponse[]> {
    const nodes = await this.flowsService.getAllNodes(groupId);
    return nodes;
  }

  @Post(':groupId/node-load-method/:nodeName')
  @Scopes('group-{groupId}:write-flow')
  async nodeLoadMethod(
    @Param('nodeName') nodeName: string,
    @Req() req: Request,
  ): Promise<LoadMethodOption[]> {
    const res = await this.flowBackendService.callNodeItemMethod(req, nodeName);
    return res;
  }

  @Post(':groupId/internal-chat')
  @Scopes('group-{groupId}:read-flow-playground')
  @UseInterceptors(ChatRequestInterceptor, ChatHistoryInterceptor, ConvertSseInterceptor)
  async chatGroupFlow(
    @Param('groupId') groupId: number,
    @Body() data: FlowChatRequest,
    @Req() req: UserRequest,
    @Res() res: Response,
  ): Promise<InternalFlowChatResponse | Response<InternalFlowChatResponse>> {
    const result = await this.flowsService.internalChat(req, res, groupId, data);
    return result;
  }
}
