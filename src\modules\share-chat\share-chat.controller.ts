import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Req,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { CreateShareChatDto } from './dto/create-share-chat.dto';
import { ShareChatService } from './share-chat.service';
import { UserRequest } from '../auth/auth.interface';
import { Scopes } from '../auth/scope.decorator';
import { ApiTags } from '@nestjs/swagger';

@Controller('share-chat')
@ApiTags('share chat')
@Scopes('user-*:read-share-chat')
@UsePipes(new ValidationPipe({ whitelist: true }))
export class ShareChatController {
  constructor(private readonly shareChatService: ShareChatService) {}

  @Post(':groupId')
  create(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() createShareChatDto: CreateShareChatDto,
  ) {
    return this.shareChatService.create(groupId, req, createShareChatDto);
  }

  @Get('share/:shareId')
  findShare(@Req() req: UserRequest, @Param('shareId') shareId: string) {
    return this.shareChatService.findByShareId(shareId, req);
  }

  @Post('share/:shareId')
  async continueChat(@Req() req: UserRequest, @Param('shareId') shareId: string) {
    const chatSession = await this.shareChatService.createContinueChatSession(shareId, req);
    return chatSession;
  }
}
