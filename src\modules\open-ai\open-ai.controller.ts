import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Req,
  Res,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiOperation, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import OpenAI from 'openai';
import { UserRequest } from '../auth/auth.interface';
import { OpenAIService } from './open-ai.service';
import { Scopes } from '../auth/scope.decorator';
import { ExternalApi } from 'src/swagger-document';
import { ConvertSseInterceptor } from 'src/interceptors/convert-sse.interceptor';
import { Quota } from '../auth/quota.decorator';
import { ChatCompletionCreateParamsDto } from './open-ai.dto';

@ApiSecurity('api-key')
@ApiTags('OpenAI Compatible API')
@Controller('groups/:groupId/llm-model')
@UsePipes(new ValidationPipe({ whitelist: true }))
export class OpenAIController {
  constructor(private readonly openAIService: OpenAIService) {}

  @Post('chat/completions')
  @ExternalApi()
  @ApiOperation({ summary: 'Open AI Chat Completions', description: '', tags: ['Chat'] })
  @Quota(
    'group-{groupId}:llm-engine-{llmEngine}-quota',
    'group-{groupId}:rate-limit-{channel}-quota',
  )
  @UseInterceptors(ConvertSseInterceptor)
  @Scopes('group-{groupId}:read-playground')
  async createCompletion(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() createCompletionDto: ChatCompletionCreateParamsDto,
    @Req() req: UserRequest,
    @Res() res: Response,
  ) {
    const ChatResponse = (await this.openAIService.createCompletion(
      groupId,
      createCompletionDto,
      req,
      res,
    )) as unknown as OpenAI.Completions.Completion;
    if (!createCompletionDto.stream) {
      return res.json(ChatResponse);
    }
  }

  @Get('models')
  @ExternalApi()
  @Scopes('group-{groupId}:read-playground')
  @ApiOperation({ summary: 'Support Model Lists', description: '', tags: ['Models'] })
  async getActiveModels(@Param('groupId', ParseIntPipe) groupId: number) {
    const models = await this.openAIService.getActiveModels(groupId);
    return models;
  }
}
