/*
  Warnings:

  - The values [API_KEY_APPROVER] on the enum `UserRole` will be removed. If these variants are still used in the database, this will fail.

*/
-- Custom SQL: update API_KEY_APPROVER to USER
UPDATE "User" SET "role" = 'USER' WHERE "User"."role" = 'API_KEY_APPROVER';

-- AlterEnum
BEGIN;
CREATE TYPE "UserRole_new" AS ENUM ('SUDO', 'OPERATION_TEAM', 'SECURITY_TEAM', 'ACCOUNT_MANAGEMENT', 'BOT_CREATOR', 'USER');
ALTER TABLE "User" ALTER COLUMN "role" DROP DEFAULT;
ALTER TABLE "User" ALTER COLUMN "role" TYPE "UserRole_new" USING ("role"::text::"UserRole_new");
ALTER TYPE "UserRole" RENAME TO "UserRole_old";
ALTER TYPE "UserRole_new" RENAME TO "UserRole";
DROP TYPE "UserRole_old";
ALTER TABLE "User" ALTER COLUMN "role" SET DEFAULT 'USER';
COMMIT;
