import { Injectable, Logger } from '@nestjs/common';
import { BotSecurityEntity, ScannerObject, SecurityLevel } from './entities/bot-security.entity';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { GroupsService } from '../groups/groups.service';
import { Environment, Group, PrismaClient } from '@prisma/client';
import { SaveBotSecurityDTO } from './dto/save-bot-security.dto';
import { v4 } from 'uuid';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { InputScannersResultDTO } from './dto/input-scanners-result.DTO';
import { outputScannersResultDTO } from './dto/output-scanners-result.DTO';
import { PromotableService } from '../change-management/interfaces/promotable-service.interface';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { Prisma, SecurityDetectionType, BotSecurity } from '@prisma/client';
import { ChannelType, SecurityScanType, Content } from '../llm-models/dto/chat-llm-model.dto';
@Injectable()
export class BotSecurityService implements PromotableService {
  constructor(
    private prisma: PrismaService,
    private groupsService: GroupsService,
    private configService: ConfigService,
    private featureflagService: FeatureFlagService,
  ) {}
  logger = new Logger(BotSecurityService.name);

  async findByGroupId(groupId: number): Promise<BotSecurityEntity> {
    try {
      const botSecurity = await this.prisma.botSecurity.findFirst({
        where: {
          groupId,
        },
      });

      //if bot security config not found,use Default env security config
      if (!botSecurity) {
        //getGroup function already  checked for existence
        const group = await this.groupsService.getGroup(groupId, {});
        const defaultSecurity = await this.findDefaultSecurity(group.env);
        defaultSecurity.groupId = groupId;
        return defaultSecurity;
      }

      const entity: BotSecurityEntity = {
        groupId: botSecurity.groupId,
        botSecurityId: botSecurity.id.toString(),
        PIIScanners: {
          inputScanners: botSecurity.inputScanners,
          outputScanners: botSecurity.outputScanners,
        },
      };

      return entity;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async findByGroupIds(groupIds: number[]): Promise<BotSecurityEntity[]> {
    const botSecuritys = await this.prisma.botSecurity.findMany({
      where: {
        groupId: { in: groupIds },
      },
    });
    if (groupIds.length == botSecuritys.length) {
      return botSecuritys.map((item) => ({
        groupId: item.groupId,
        botSecurityId: item.id.toString(),
        PIIScanners: {
          inputScanners: item.inputScanners,
          outputScanners: item.outputScanners,
        },
      }));
    }
    const defaultDefaultSecurity = await this.findDefaultSecurity('PROD');
    const botSecurityMap = botSecuritys.reduce(
      (maps, item) => {
        const botSecurity = {
          groupId: item.groupId,
          botSecurityId: item.id.toString(),
          PIIScanners: {
            inputScanners: item.inputScanners,
            outputScanners: item.outputScanners,
          },
        };
        maps[item.groupId] = botSecurity;
        return maps;
      },
      {} as { [key: string]: BotSecurityEntity },
    );
    return groupIds.map(
      (groupId) => botSecurityMap?.[groupId] ?? { ...defaultDefaultSecurity, groupId },
    );
  }

  async findDefaultSecurity(
    env: Environment,
    tx?: Prisma.TransactionClient,
  ): Promise<BotSecurityEntity> {
    const prismaClient = tx ?? this.prisma;
    const defaultSecurityprefix = 'Default';
    const botDefaultSecurity = await prismaClient.botSecurity.findFirst({
      where: {
        securityId: `${defaultSecurityprefix}${env}`,
      },
    });

    return {
      groupId: botDefaultSecurity.groupId,
      botSecurityId: botDefaultSecurity.securityId,
      PIIScanners: {
        inputScanners: botDefaultSecurity.inputScanners,
        outputScanners: botDefaultSecurity.outputScanners,
      },
    };
  }

  async isChatEnablePIIScan(
    overrides: { security_scan?: SecurityScanType },
    requestChannel: ChannelType,
    enableSecurityScanType?: SecurityScanType[],
  ) {
    const needCheckEnableSecurityScanType = enableSecurityScanType
      ? enableSecurityScanType
      : [SecurityScanType.ON, SecurityScanType.PROMPT_INJECTION, SecurityScanType.ANONYMIZE];
    return (
      overrides?.security_scan &&
      needCheckEnableSecurityScanType.includes(overrides?.security_scan) &&
      requestChannel !== ChannelType.TEAMS &&
      (await this.EnablePromptPIICheckFLAG())
    );
  }

  async saveBotSecurity(
    saveBotSecurityDTO: SaveBotSecurityDTO,
    tx?: Prisma.TransactionClient,
  ): Promise<BotSecurityEntity> {
    const prismaclient = tx ?? this.prisma;
    try {
      const botSecurity = await prismaclient.botSecurity.findFirst({
        where: {
          groupId: saveBotSecurityDTO.groupId,
        },
      });

      // if botSecurity is no exist，create
      if (!botSecurity) {
        return await this.createBotSecurity(saveBotSecurityDTO, tx);
      }

      //get temple
      const templeBotSecurityData = await this.findDefaultSecurity(botSecurity.env);
      if (!templeBotSecurityData) {
        throw new ApiException(ErrorCode.BOT_SECURITY_TEMPLE_NOT_FOUND);
      }

      const updateScanners = (templeScanners: any, newScanners: any): BotSecurity[] => {
        const updatedScanners = templeScanners.map((defaultScanner) => {
          const newScanner = newScanners.find((ns) => ns.type === defaultScanner.type);
          if (newScanner) {
            return {
              ...defaultScanner,
              securityLevel: newScanner.securityLevel,
              params:
                newScanner.params && newScanner.params.length > 0
                  ? this.mergeObjects(defaultScanner, newScanner)
                  : defaultScanner.params,
            };
          }
          return defaultScanner;
        });
        return updatedScanners;
      };

      botSecurity.inputScanners = updateScanners(
        templeBotSecurityData.PIIScanners.inputScanners,
        saveBotSecurityDTO.inputScanners,
      ) as unknown as Prisma.JsonValue;
      botSecurity.outputScanners = updateScanners(
        templeBotSecurityData.PIIScanners.outputScanners,
        saveBotSecurityDTO.outputScanners,
      ) as unknown as Prisma.JsonValue;
      botSecurity.updatedAt = new Date();
      const resultEntity = await prismaclient.botSecurity.update({
        where: { groupId: botSecurity.groupId },
        data: botSecurity,
      });

      return {
        groupId: resultEntity.groupId,
        botSecurityId: resultEntity.id.toString(),
        PIIScanners: {
          inputScanners: resultEntity.inputScanners,
          outputScanners: resultEntity.outputScanners,
        },
      };
    } catch (error) {
      this.logger.error('saveBotSecurity error', saveBotSecurityDTO);
      throw error;
    }
  }

  async createBotSecurity(
    saveBotSecurityDTO: SaveBotSecurityDTO,
    tx?: Prisma.TransactionClient,
  ): Promise<BotSecurityEntity> {
    const prismaClient = tx ?? this.prisma;
    const group = await this.groupsService.getGroup(saveBotSecurityDTO.groupId, {});
    const botDefaultSecurity = await this.findDefaultSecurity(group.env);
    //builder dict  key: scanners name ,value:scanners config
    const inputScannersConfigDict = {};
    const outputScannersConfigDict = {};
    for (const inputScanner of saveBotSecurityDTO.inputScanners) {
      inputScannersConfigDict[inputScanner.type] = inputScanner.securityLevel;
    }
    for (const outputScanner of saveBotSecurityDTO.outputScanners) {
      outputScannersConfigDict[outputScanner.type] = outputScanner.securityLevel;
    }

    //assignment
    if (Array.isArray(botDefaultSecurity.PIIScanners.inputScanners)) {
      for (let scanner of botDefaultSecurity.PIIScanners.inputScanners) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        scanner = scanner as { [key: string]: any };

        scanner.securityLevel = inputScannersConfigDict[scanner.type as string];
      }
    }

    if (Array.isArray(botDefaultSecurity.PIIScanners.outputScanners)) {
      for (let scanner of botDefaultSecurity.PIIScanners.outputScanners) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        scanner = scanner as { [key: string]: any };

        scanner.securityLevel = outputScannersConfigDict[scanner.type as string];
      }
    }

    const resultEntity = await prismaClient.botSecurity.create({
      data: {
        groupId: group.id,
        securityId: v4(),
        env: group.env,
        inputScanners: botDefaultSecurity.PIIScanners.inputScanners,
        outputScanners: botDefaultSecurity.PIIScanners.outputScanners,
      },
    });
    return {
      groupId: resultEntity.groupId,
      botSecurityId: resultEntity.id.toString(),
      PIIScanners: {
        inputScanners: resultEntity.inputScanners,
        outputScanners: resultEntity.outputScanners,
      },
    };
  }

  async createDefaultBotSecurity(
    groupId: number,
    env: Environment,
    tx?: Prisma.TransactionClient,
  ): Promise<BotSecurityEntity> {
    const prismaClient = tx ?? this.prisma;
    const botDefaultSecurity = await this.findDefaultSecurity(env, tx);
    const resultEntity = await prismaClient.botSecurity.create({
      data: {
        groupId: groupId,
        securityId: v4(),
        env: env,
        inputScanners: botDefaultSecurity.PIIScanners.inputScanners,
        outputScanners: botDefaultSecurity.PIIScanners.outputScanners,
      },
    });

    return {
      groupId: resultEntity.groupId,
      botSecurityId: resultEntity.id.toString(),
      PIIScanners: {
        inputScanners: resultEntity.inputScanners,
        outputScanners: resultEntity.outputScanners,
      },
    };
  }

  //PII Detect
  async PromptDetect(
    prompt: string,
    groupId: number,
    loginId: number,
    securityScan: SecurityScanType,
  ): Promise<InputScannersResultDTO> {
    this.logger.debug('prompt:', prompt);
    this.logger.debug(
      'post llmguard url:',
      this.configService.get<string>('llmGuard.promptPrefix') + '/analyze/prompt',
    );
    try {
      const botSecurity = await this.findByGroupId(groupId);
      const inputSecurityScanType = securityScan.toString().replaceAll('_', '').toLowerCase();
      //build params
      const scannersConfig = [];
      //builder sercurityLevel dict(for later use)
      const scannerSecurityLevel = {};
      if (Array.isArray(botSecurity.PIIScanners.inputScanners)) {
        for (const scanner of botSecurity.PIIScanners.inputScanners) {
          const config = { type: '', params: '' };
          //if level is off,continue
          if (
            scanner.securityLevel === 'OFF' ||
            (inputSecurityScanType != 'on' && inputSecurityScanType != scanner.type.toLowerCase())
          ) {
            continue;
          }
          config.type = scanner.type;
          scannerSecurityLevel[scanner.type] = scanner.securityLevel;

          config.params = JSON.stringify(scanner.params);
          scannersConfig.push(config);
        }
      }

      //builde request
      const requestBody = {
        prompt: prompt,
        scanners_config: scannersConfig,
      };

      const response = await axios.post(
        this.configService.get<string>('llmGuard.promptPrefix') + '/analyze/prompt',
        requestBody,
      );

      if (!response) {
        this.logger.error('failed to call llmguard api: /analyze/prompt,No response');
        throw new ApiException(ErrorCode.PII_DETECTED);
      }

      this.logger.log('llmGuard Response:', response.data);
      for (const scannerResponse of response.data.scanners) {
        scannerResponse['security_level'] = scannerSecurityLevel[scannerResponse.name];
      }

      if (response.data.is_valid == false) {
        if (groupId && prompt && loginId) {
          const invalidScanners = response.data.scanners.filter((scanner) => !scanner.is_valid);
          const scannerNames = invalidScanners.map((scanner) => scanner.name);
          await this.prisma.promptOutputSecurityDetection.create({
            data: {
              groupId: groupId,
              user_prompt: prompt,
              masked_prompt: scannerNames.includes('Anonymize')
                ? response.data.sanitized_prompt
                : '',
              scanners: scannerNames,
              type: SecurityDetectionType.PROMPT,
              createdByUserId: loginId,
            },
          });
        }
        throw new ApiException(ErrorCode.PII_DETECTED, {
          input_scanners_result_is_valid: response.data.is_valid,
          input_scanners_result: {
            scanners: response.data.scanners,
            original_prompt: prompt,
            sanitized_prompt: response.data.sanitized_prompt,
          },
          content: `We've identified your prompt is potentially containing sensitive information or being vulnerable to malicious manipulation. To protect the security and privacy of our platform and users, we cannot proceed with your input at this time.`,
        });
      }

      return {
        input_scanners_result_is_valid: response.data.is_valid,
        input_scanners_result: {
          scanners: response.data.scanners,
          original_prompt: prompt,
          sanitized_prompt: response.data.sanitized_prompt,
        },
      };
    } catch (error) {
      this.logger.error(error, 'failed to call llmguard api: /analyze/prompt');
      throw error;
    }
  }

  //PII Detect
  async outputDetect(
    prompt: string | Content[],
    output: string,
    groupId: number,
    loginId: number,
  ): Promise<outputScannersResultDTO> {
    this.logger.debug('output:', output);
    this.logger.debug(
      'post llmguard url:',
      this.configService.get<string>('llmGuard.promptPrefix') + '/analyze/prompt',
    );
    // output='My name is peter Parker. I live in 32/F, PCCW Tower, Quarry Bay, my document ID is M5346148';
    try {
      const botSecurity = await this.findByGroupId(groupId);

      //build params
      const scannersConfig = [];
      //builder sercurityLevel dict(for later use)
      const scannerSecurityLevel = {};
      if (Array.isArray(botSecurity.PIIScanners.outputScanners)) {
        for (const scanner of botSecurity.PIIScanners.outputScanners) {
          const config = { type: '', params: '' };
          //if level is off,continue
          if (scanner.securityLevel === 'OFF') {
            continue;
          }
          config.type = scanner.type;
          scannerSecurityLevel[scanner.type] = scanner.securityLevel;

          config.params = JSON.stringify(scanner.params);
          scannersConfig.push(config);
        }
      }

      //builde request
      const requestBody = {
        prompt: '',
        output: output,
        scanners_config: scannersConfig,
      };

      const response = await axios.post(
        this.configService.get<string>('llmGuard.promptPrefix') + '/analyze/output',
        requestBody,
      );

      if (response.data.is_valid == false) {
        if (groupId && prompt && loginId) {
          const invalidScanners = response.data.scanners.filter((scanner) => !scanner.is_valid);
          const scannerNames = invalidScanners.map((scanner) => scanner.name);
          await this.prisma.promptOutputSecurityDetection.create({
            data: {
              groupId: groupId,
              user_prompt: this.convertContentsToString(prompt),
              output: output,
              masked_prompt: response.data.sanitized_output,
              scanners: scannerNames,
              type: SecurityDetectionType.OUTPUT,
              createdByUserId: loginId,
            },
          });
        }
      }

      if (!response) {
        this.logger.error('failed to call llmguard api: /analyze/output,No response');
        throw new ApiException(ErrorCode.PII_DETECTED);
      }

      this.logger.log('llmGuard Response:', response.data);
      for (const scannerResponse of response.data.scanners) {
        scannerResponse['security_level'] = scannerSecurityLevel[scannerResponse.name];
      }

      return {
        output_scanners_result_is_valid: response.data.is_valid,
        output_scanners_result: {
          scanners: response.data.scanners,
          original_output: output,
          sanitized_output: response.data.sanitized_output,
        },
      };
    } catch (error) {
      this.logger.error(error, 'failed to call llmguard api: /analyze/output');
      throw error;
    }
  }

  async generateEntityDataForSnapshot(groupId: number, entityId: string): Promise<object> {
    const botSecurity = await this.prisma.botSecurity.findFirst({
      where: {
        groupId: groupId,
      },
    });
    let botSecurityEntity: BotSecurityEntity;

    this.logger.log(`bot Security generateEntityDataForSnapshot groupId:${groupId}`);
    //if group use default BotSetting,create a new group Bottsetting
    if (!botSecurity) {
      this.logger.log(
        `bot Security generateEntityDataForSnapshot groupId:${groupId} have not Security entity,so create one`,
      );
      botSecurityEntity = await this.createDefaultBotSecurity(groupId, Environment.TEST);
    } else {
      botSecurityEntity = {
        groupId: botSecurity.groupId,
        botSecurityId: botSecurity.id.toString(),
        PIIScanners: {
          inputScanners: botSecurity.inputScanners,
          outputScanners: botSecurity.outputScanners,
        },
      };
    }

    return botSecurityEntity;
  }

  async deleteEntityDataForSnapshot(
    groupId: number,
    entityId: string,
    entityData: any,
  ): Promise<void> {
    return;
  }

  async promoteCreate(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    entityData: BotSecurityEntity,
    operatorId: number,
  ): Promise<string> {
    // since PROD BotSecurity is created by default without promotion, if check have not botSecurity(use Default botSecurity)，create a bot Security for PROD
    const botSecurity = await tx.botSecurity.findFirst({
      select: { securityId: true },
      where: { groupId: targetGroup.id },
    });
    if (botSecurity) {
      await this.promoteUpdate(
        tx,
        targetGroup,
        botSecurity.securityId.toString(),
        entityData,
        operatorId,
      );
      return botSecurity.securityId.toString();
    }
    this.logger.log(
      `targetgroup:${targetGroup.id}bot Security promoteCreate take the promoteCreate method`,
    );
    const createBotSecurityEntity: SaveBotSecurityDTO = {
      groupId: targetGroup.id,
      botSecurityId: '',
      inputScanners: entityData.PIIScanners.inputScanners,
      outputScanners: entityData.PIIScanners.outputScanners,
    };
    const tartgetBotSecurity = await this.createBotSecurity(createBotSecurityEntity, tx);

    return tartgetBotSecurity.botSecurityId.toString();
  }

  async promoteUpdate(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string,
    entityData: BotSecurityEntity,
    operatorId: number,
  ): Promise<void> {
    this.logger.log(`targetgroup:${targetGroup.id}bot Security  take the promoteUpdate method`);
    const saveBotSecurityDTO: SaveBotSecurityDTO = {
      botSecurityId: targetEntityId,
      groupId: targetGroup.id,
      inputScanners: entityData.PIIScanners.inputScanners,
      outputScanners: entityData.PIIScanners.outputScanners,
    };
    await this.saveBotSecurity(saveBotSecurityDTO, tx);
  }

  async promoteDelete(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string,
    operatorId: number,
  ): Promise<void> {
    throw new ApiException(ErrorCode.DATA_PROMOTION_DELETE_NOT_SUPPORTED);
  }

  async checkPromotedEntityValid(targetEntityId: string) {
    return this.prisma.botSecurity.findUnique({ where: { securityId: targetEntityId } });
  }

  async EnablePromptPIICheckFLAG(): Promise<boolean> {
    const enablePIICheckFLAG = await this.featureflagService.getDefaultFeatureFlagFromDb(
      FeatureFlagKey.BOT_PROMPT_ENABLE_PII_CHECK,
    );

    return enablePIICheckFLAG?.isEnabled;
  }

  mergeObjects(defaultObj: ScannerObject, newScannerObj: ScannerObject): any {
    const mergedObject = { ...defaultObj };

    newScannerObj.params.forEach((newParam) => {
      const defaultParam = mergedObject.params.find((param) => param.name === newParam.name);
      if (defaultParam) {
        defaultParam.value = newParam.value;
      }
    });

    return mergedObject.params;
  }

  convertContentsToString(content: String | Content[]) {
    if (typeof content === 'string') {
      return content;
    }
    else if (Array.isArray(content)) {
      return content
        .filter(item => item.type === 'text')
        .map(item => item.text)
        .join('\n');
    }
    return '';
  }
}
