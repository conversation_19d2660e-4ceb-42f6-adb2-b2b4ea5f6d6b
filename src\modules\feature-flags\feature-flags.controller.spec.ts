import { ModuleMocker, MockFunctionMetadata } from 'jest-mock';
import { mockDeep, DeepMockProxy } from 'jest-mock-extended';
import { Test, TestingModule } from '@nestjs/testing';
import { FeatureFlagController } from './feature-flags.controller';
import { FeatureFlagService } from './feature-flags.service';
import { FeatureFlag } from '@prisma/client';
import { FeatureFlagDTO, GetOverrideFeatureFlagsOrDefaultDTO } from './feature-flags-model.dto';
const moduleMocker = new ModuleMocker(global);

describe('GroupsService', () => {
  let featureFlagService: DeepMockProxy<FeatureFlagService>;

  let featureFlagController: FeatureFlagController;
  beforeEach(async () => {
    featureFlagService = mockDeep<FeatureFlagService>();
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FeatureFlagController],
      providers: [{ provide: FeatureFlagService, useValue: featureFlagService }],
    })
      .useMocker((token) => {
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>;
          const Mock = moduleMocker.generateFromMetadata(mockMetadata);
          return new Mock();
        }
      })
      .compile();
    featureFlagController = module.get(FeatureFlagController);
  });

  describe('getFeatFlags', () => {
    it('should be return feature flag', async () => {
      const featureFlag = [
        {
          id: 1,
          value: '',
          updatedByUserId: 1,
          metaData: '{"t":2,"test":1}',
          isEnabled: true,
        },
        {
          id: 2,
          value: '',
          updatedByUserId: 2,
          metaData: '{"t":2,"test":1}',
          isEnabled: true,
        },
        {
          id: 3,
          value: '',
          updatedByUserId: 3,
          metaData: '{"t":2,"test":1}',
          isEnabled: true,
        },
      ] as FeatureFlag[];
      const count = {
        count: 3,
      };
      const check = {
        list: [
          {
            id: 1,
            value: '',
            updatedByUserId: 1,
            metaData: '{"t":2,"test":1}',
            isEnabled: true,
          },
          {
            id: 2,
            value: '',
            updatedByUserId: 2,
            metaData: '{"t":2,"test":1}',
            isEnabled: true,
          },
          {
            id: 3,
            value: '',
            updatedByUserId: 3,
            metaData: '{"t":2,"test":1}',
            isEnabled: true,
          },
        ],
        ...count,
      };
      featureFlagService.getAll.mockResolvedValue(featureFlag);
      featureFlagService.getCount.mockResolvedValue(count.count);
      const res = await featureFlagController.getFeatFlags();
      expect(res).toEqual(check);
    });
  });

  describe('getClientSideFeatFlags', () => {
    it('should be return feature flag list', async () => {
      const featureFlag = [
        {
          key: 'test-1',
          description: 'string',
          value: 'string',
          isEnabled: true,
        },
        {
          key: 'test-2',
          description: 'string',
          value: 'string',
          isEnabled: true,
        },
      ] as GetOverrideFeatureFlagsOrDefaultDTO[];
      const count = {
        count: 2,
      };
      const check = {
        list: [
          {
            key: 'test-1',
            description: 'string',
            value: 'string',
            isEnabled: true,
          },
          {
            key: 'test-2',
            description: 'string',
            value: 'string',
            isEnabled: true,
          },
        ],
        ...count,
      };
      featureFlagService.getClientSideFeatureFlags.mockResolvedValue(featureFlag);
      const res = await featureFlagController.getClientSideFeatFlags();
      expect(res).toEqual(check);
    });
  });

  describe('createFeatFlag', () => {
    it('should be return feature flag', async () => {
      const featureFlag = {
        key: 'test-1',
        description: 'string',
        value: 'string',
        isEnabled: true,
      } as FeatureFlag;
      const req = {
        key: 'test-1',
        description: 'string',
        value: 'string',
        isEnabled: true,
      } as FeatureFlagDTO;
      const check = {
        key: 'test-1',
        description: 'string',
        value: 'string',
        isEnabled: true,
      };
      featureFlagService.create.mockResolvedValue(featureFlag);
      const res = await featureFlagController.createFeatFlag(req);
      expect(res).toEqual(check);
    });
  });

  describe('updateFeatFlag', () => {
    it('should be return feature flag', async () => {
      const featureFlag = {
        key: 'test-1',
        description: 'string',
        value: 'string',
        isEnabled: true,
      } as FeatureFlag;
      const req = {
        key: 'test-1',
        description: 'string',
        value: 'string',
        isEnabled: true,
      } as FeatureFlagDTO;
      const check = {
        key: 'test-1',
        description: 'string',
        value: 'string',
        isEnabled: true,
      };
      featureFlagService.update.mockResolvedValue(featureFlag);
      const res = await featureFlagController.updateFeatFlag(1, req);
      expect(res).toEqual(check);
    });
  });

  describe('updateFeatFlag', () => {
    it('should be return feature flag', async () => {
      const featureFlag = {
        key: 'test-1',
        description: 'string',
        value: 'string',
        isEnabled: true,
      } as FeatureFlag;
      const check = {
        key: 'test-1',
        description: 'string',
        value: 'string',
        isEnabled: true,
      };
      featureFlagService.delete.mockResolvedValue(featureFlag);
      const res = await featureFlagController.deleteFeatFlag(1);
      expect(res).toEqual(check);
    });
  });
});
