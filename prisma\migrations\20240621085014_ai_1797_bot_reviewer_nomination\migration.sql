-- CreateTable
CREATE TABLE "BotReviewNomination" (
    "id" SERIAL NOT NULL,
    "membershipId" INTEGER NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BotReviewNomination_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "BotReviewNomination_membershipId_key" ON "BotReviewNomination"("membershipId");

-- CreateIndex
CREATE INDEX "BotReviewNomination_membershipId_startDate_endDate_idx" ON "BotReviewNomination"("membershipId", "startDate", "endDate");

-- CreateIndex
CREATE INDEX "Membership_roleId_idx" ON "Membership"("roleId");
