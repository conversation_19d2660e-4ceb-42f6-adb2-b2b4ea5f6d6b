import { OptionType } from 'src/modules/options/options.inteface';
import { Public } from '../auth/public.decorator';
import { Controller, Get, Logger, Query } from '@nestjs/common';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { OptionsService } from './options.service';
import { Prisma } from '@prisma/client';
import { ApiTags } from '@nestjs/swagger';

@Public()
@Controller('options')
@ApiTags('Options')
export class OptionsController {
  private logger = new Logger(OptionsController.name);

  constructor(private readonly optionsService: OptionsService) {}

  // TODO: see if need to split APIs into BU, CCC, Department instead of Group and User
  //  https://theclub.atlassian.net/browse/AI-1238

  /**  Get Group Options */
  @Get('groups')
  async getGroupOptions(
    @Query('optionType') optionType: OptionType,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('contains') contains?: string,
    @Query('mode') mode?: Prisma.QueryMode,
  ) {
    const options = await this.optionsService.getGroupOptions(
      optionType,
      contains,
      mode,
      skip,
      take,
    );
    return { options };
  }

  /**  Get User Options */
  @Get('users')
  async getUserOptions(
    @Query('optionType') optionType: OptionType,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('contains') contains?: string,
    @Query('mode') mode?: Prisma.QueryMode,
  ) {
    const options = await this.optionsService.getUserOptions(
      optionType,
      contains,
      mode,
      skip,
      take,
    );
    return { options };
  }
}
