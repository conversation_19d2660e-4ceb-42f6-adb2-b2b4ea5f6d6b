import { Prisma, SnapshotEntityType } from '@prisma/client';
import { PromotableService } from './promotable-service.interface';

export type EntityPromotionHandler = {
  [entityType in SnapshotEntityType]: {
    // a function to add/remove/mask the original entityData
    toDisplayEntityData: (tx: Prisma.TransactionClient, entityData: any) => Promise<object>; // return a object to replace entityData for display

    // service implemented PromotableService interface
    service: PromotableService;
  };
};
