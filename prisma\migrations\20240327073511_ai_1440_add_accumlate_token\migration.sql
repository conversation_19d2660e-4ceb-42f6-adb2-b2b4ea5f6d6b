-- CreateTable
CREATE TABLE "SummaryAll" (
    "id" SERIAL NOT NULL,
    "groupId" INTEGER NOT NULL,
    "key" "SummaryKeyType" NOT NULL,
    "value" INTEGER NOT NULL,
    "engineSlug" TEXT,
    "flowId" INTEGER NOT NULL DEFAULT 0,
    "callingType" "SummaryCallingType" NOT NULL,
    "callingBy" INTEGER NOT NULL,

    CONSTRAINT "SummaryAll_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "summary_all_groupId_index" ON "SummaryAll"("groupId");

-- CreateIndex
CREATE INDEX "summary_all_key_date_index" ON "SummaryAll"("key");

-- CreateIndex
CREATE INDEX "summary_all_callingBy_callingType_groupId_date_index" ON "SummaryAll"("callingBy", "callingType", "groupId");

-- CreateIndex
CREATE INDEX "summary_all_flowId_groupId_date_index" ON "SummaryAll"("flowId", "groupId");

-- Data patch
INSERT INTO "SummaryAll" ("value", "engineSlug", "callingType", "callingBy", "groupId", "flowId", "key")
SELECT SUM("value")  as value, "engineSlug", "callingType", "callingBy", "groupId", "flowId", "key" FROM "Summary"  
GROUP BY "engineSlug" , "callingType","callingBy" , "groupId", "flowId" , "key";

-- Summary triggers
CREATE OR REPLACE FUNCTION summary_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF EXISTS (SELECT 1 FROM "SummaryAll" WHERE "engineSlug" = NEW."engineSlug" AND "callingType" = NEW."callingType" AND "callingBy" = NEW."callingBy" AND "groupId" = NEW."groupId" AND "flowId" = NEW."flowId" AND "key" = NEW."key") 
    THEN
        UPDATE "SummaryAll" sa SET value = (sa."value" + NEW."value")  
        WHERE "engineSlug" = NEW."engineSlug" AND "callingType" = NEW."callingType" AND  "callingBy" = NEW."callingBy" AND "groupId" = NEW."groupId" AND "flowId" = NEW."flowId" AND "key" = NEW."key";
    ELSE INSERT INTO "SummaryAll" ("value", "engineSlug", "callingType", "callingBy", "groupId", "flowId", "key") 
    VALUES (NEW."value", NEW."engineSlug", NEW."callingType", NEW."callingBy", NEW."groupId", NEW."flowId", NEW."key");
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER Summary_trigger
AFTER INSERT ON public."Summary"
FOR EACH ROW
EXECUTE FUNCTION summary_trigger_function();