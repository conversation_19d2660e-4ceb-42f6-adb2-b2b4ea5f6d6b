import { Injectable, OnM<PERSON>uleD<PERSON>roy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { City, WebServiceClient } from '@maxmind/geoip2-node';
import <PERSON>LRU from 'quick-lru';
import dotenv from 'dotenv';

dotenv.config();
@Injectable()
export class GeolocationService implements OnModuleDestroy {
  constructor(private configService: ConfigService) {}

  private lookup: City | null = null;
  private lru = new QuickLRU<string, Partial<City>>({
    maxSize: this.configService.get<number>('caching.geolocationLruSize') ?? 100,
  });
  private client = new WebServiceClient(
    process.env['MAXMIND_ACCOUNT_ID'] || '857445',
    process.env['MAXMIND_LICENSE_KEY'],
    { host: 'geolite.info' },
  );

  onModuleDestroy() {
    if (this.lookup) this.lookup = null;
  }

  /** Get the geolocation from an IP address */
  async getLocation(ipAddress: string): Promise<Partial<City>> {
    if (this.lru.has(ipAddress)) return this.lru.get(ipAddress) ?? {};
    // const result = await this.getSafeLocation(ipAddress);
    try {
      const result = await this.client.city(ipAddress);
      this.lru.set(ipAddress, result);
      return result;
    } catch (error) {
      return {};
    }
  }
}
