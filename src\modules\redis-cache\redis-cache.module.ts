import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RedisCacheController } from './redis-cache.controller';
import { RedisModule } from 'src/providers/redis/redis.module';
import { RedisCacheService } from './redis-cache.service';

@Module({
  imports: [ConfigModule, RedisModule],
  controllers: [RedisCacheController],
  providers: [RedisCacheService],
  exports: [RedisCacheService],
})
export class RedisCacheModule {}
