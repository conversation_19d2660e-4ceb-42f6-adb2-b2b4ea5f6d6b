import {
  <PERSON>,
  <PERSON>,
  Logger,
  <PERSON>m,
  ParseInt<PERSON>ip<PERSON>,
  Query,
  Req,
  Res,
  ClassSerializerInterceptor,
  UseInterceptors,
  NotFoundException,
} from '@nestjs/common';
import { FileHistoryService } from './file-history.service';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Scopes } from '../auth/scope.decorator';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { Response } from 'express';
import { FileHistoryEntity } from './entities/file-history.entity';
import { OptionalIntPipe } from '../../pipes/optional-int.pipe';
import { UserRequest } from '../auth/auth.interface';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { PrismaService } from 'src/providers/prisma/prisma.service';

@Controller('file-history')
@ApiBearerAuth('bearer-auth')
@ApiTags('File History')
export class FileHistoryController {
  private logger = new Logger(FileHistoryController.name);

  constructor(
    private fileHistoryService: FileHistoryService,
    private prisma: PrismaService,
  ) {}

  //download (call s3)
  @Get(':groupId/report/:reportId')
  @Scopes('group-{groupId}:download-security-report')
  @AuditLog('download-security-detection-report')
  async downloadSecurityDetectionReportForGroup(
    @Param('reportId', ParseIntPipe) id: number,
    @Res() response: Response,
  ) {
    return this.handleDownloadSecurityDetectionReport(id, response);
  }

  @Get(':groupId/report')
  @Scopes('group-{groupId}:read-security-report')
  @UseInterceptors(ClassSerializerInterceptor)
  async getSecurityDetectionReportListForGroup(
    @Req() request: UserRequest,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
  ): Promise<{ list: FileHistoryEntity[]; count: number }> {
    const rst = await this.fileHistoryService.getSecurityDetectionReportList({
      skip,
      take,
      request,
    });
    const count = await this.fileHistoryService.getSecurityDetectionReportCount(request);
    const list = rst.map((item) => new FileHistoryEntity(item));
    return { list, count };
  }

  //download (call s3)
  @Get('report/:reportId')
  @Scopes('system:download-security-report')
  @AuditLog('download-security-detection-report')
  async downloadSecurityDetectionReportForSecurityTeam(
    @Param('reportId', ParseIntPipe) id: number,
    @Res() response: Response,
  ) {
    return this.handleDownloadSecurityDetectionReport(id, response);
  }

  @Get('report')
  @Scopes('system:read-security-report')
  @UseInterceptors(ClassSerializerInterceptor)
  async getSecurityDetectionReportList(
    @Req() request: UserRequest,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
  ): Promise<{ list: FileHistoryEntity[]; count: number }> {
    const rst = await this.fileHistoryService.getSecurityDetectionReportList({
      skip,
      take,
      request,
    });
    const count = await this.fileHistoryService.getSecurityDetectionReportCount(request);
    const list = rst.map((item) => new FileHistoryEntity(item));
    return { list, count };
  }

  private async handleDownloadSecurityDetectionReport(id: number, response: Response) {
    const fileHistory = await this.prisma.fileHistory.findFirst({ where: { id } });

    if (!fileHistory) {
      throw new NotFoundException('fileHistory cannot be found in DB');
    }

    try {
      const stream = await this.fileHistoryService.downloadReport(fileHistory.s3FilePath);
      const doc = { id, filename: fileHistory.s3FilePath };

      stream
        .createReadStream()
        .on('error', (err) => {
          this.logger.error(err, 'report download failed');
          response.status(404).send(ErrorCode.FILE_DOWNLOAD_FAILED);
          response.end(); // Ensure response is ended after error
        })
        .pipe(response);

      return doc; // For consistency with original structure, though response is already being piped.
    } catch (error) {
      this.logger.error(error, 'download file error');
      throw new ApiException(ErrorCode.FILE_DOWNLOAD_FAILED);
    }
  }
}
