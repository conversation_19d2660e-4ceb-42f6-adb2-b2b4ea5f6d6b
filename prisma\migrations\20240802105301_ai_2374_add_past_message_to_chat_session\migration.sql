-- This is an empty migration.

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":50,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":1,\"max\":10,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":16384,\"default\":1000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"presence_penalty\":{\"label\":\"Presence Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model''s likelihood to talk about new topics.\"},\"frequency_penalty\":{\"label\":\"Frequency Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('gpt-35-turbo-16k');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":50,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":1,\"max\":10,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":32768,\"default\":1000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"presence_penalty\":{\"label\":\"Presence Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model''s likelihood to talk about new topics.\"},\"frequency_penalty\":{\"label\":\"Frequency Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('gpt-4-32k');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0.2,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"Temperature controls the degree of randomness in token selection. Lower temperatures are good for prompts that expect a true or correct response, while higher temperatures can lead to more diverse or unexpected results. A temperature of 0 is deterministic: the highest probability token is always selected. For most use cases, try starting with a temperature of .2.\"},\"max_tokens\":{\"label\":\"Token Limit\",\"min\":1,\"max\":32000,\"default\":1000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Token limit determines the maximum amount of text output from one prompt. A token is approximately four characters. The default value is 1000.\"},\"top_p\":{\"label\":\"Top P\",\"min\":0,\"max\":1,\"default\":0.8,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"Top-p changes how the model selects tokens for output. Tokens are selected from most probable to least until the sum of their probabilities equals the top-p value. For example, if tokens A, B, and C have a probability of .3, .2, and .1 and the top-p value is .5, then the model will select either A or B as the next token (using temperature). The default top-p value is .8.\"},\"top_k\":{\"label\":\"Tok K\",\"min\":1,\"max\":40,\"default\":40,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Top-k changes how the model selects tokens for output. A top-k of 1 means the selected token is the most probable among all tokens in the model’s vocabulary (also called greedy decoding), while a top-k of 3 means that the next token is selected from among the 3 most probable tokens (using temperature). The default top-k value is 40.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('vertexai-chat-bison-32k');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0.2,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"Temperature controls the degree of randomness in token selection. Lower temperatures are good for prompts that expect a true or correct response, while higher temperatures can lead to more diverse or unexpected results. A temperature of 0 is deterministic: the highest probability token is always selected. For most use cases, try starting with a temperature of .2.\"},\"max_tokens\":{\"label\":\"Token Limit\",\"min\":1,\"max\":1024,\"default\":256,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Token limit determines the maximum amount of text output from one prompt. A token is approximately four characters. The default value is 256.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('vertexai-codechat-bison-latest');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\": {\"label\": \"Temperature\",\"min\": 0,\"max\": 1,\"default\": 0,\"type\": \"number\",\"input\": \"slider\",\"step\": 0.1,\"description\": \"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\": {\"label\": \"Num. of relevant docs to retrieve\",\"min\": 0,\"max\": 50,\"default\": 3,\"type\": \"number\",\"input\": \"slider\",\"step\": 1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\": {\"label\": \"Top P\",\"min\": 0,\"max\": 1,\"default\": 1,\"type\": \"number\",\"input\": \"slider\",\"step\": 0.1,\"description\": \"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\": {\"label\": \"Max Tokens\",\"min\": 256,\"max\": 8192,\"default\": 4000,\"type\": \"number\",\"input\": \"slider\",\"step\": 1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"presence_penalty\": {\"label\": \"Presence Penalty\",\"min\": 0,\"max\": 2,\"default\": 0,\"type\": \"number\",\"input\": \"slider\",\"step\": 0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model''s likelihood to talk about new topics.\"},\"frequency_penalty\": {\"label\": \"Frequency Penalty\",\"min\": 0,\"max\": 2,\"default\": 0,\"type\": \"number\",\"input\": \"slider\",\"step\": 0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('gpt-4-0125');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\": {\"label\": \"Temperature\",\"min\": 0,\"max\": 1,\"default\": 0.2,\"type\": \"number\",\"input\": \"slider\",\"step\": 0.1,\"description\": \"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\": {\"label\": \"Num. of relevant docs to retrieve\",\"min\": 0,\"max\": 50,\"default\": 3,\"type\": \"number\",\"input\": \"slider\",\"step\": 1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\": {\"label\": \"Top P\",\"min\": 0,\"max\": 1,\"default\": 0.94,\"type\": \"number\",\"input\": \"slider\",\"step\": 0.01,\"description\": \"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\": {\"label\": \"Max Tokens\",\"min\": 256,\"max\": 8192,\"default\": 4000,\"type\": \"number\",\"input\": \"slider\",\"step\": 1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('vertexai-gemini-1.5-pro-preview-0409');


UPDATE public."LlmEngine"
	SET config='"{\"temperature\": {\"label\": \"Temperature\",\"min\": 0,\"max\": 1,\"default\": 0.2,\"type\": \"number\",\"input\": \"slider\",\"step\": 0.1,\"description\": \"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\": {\"label\": \"Num. of relevant docs to retrieve\",\"min\": 0,\"max\": 50,\"default\": 3,\"type\": \"number\",\"input\": \"slider\",\"step\": 1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\": {\"label\": \"Top P\",\"min\": 0,\"max\": 1,\"default\": 0.94,\"type\": \"number\",\"input\": \"slider\",\"step\": 0.01,\"description\": \"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\": {\"label\": \"Max Tokens\",\"min\": 256,\"max\": 8192,\"default\": 4000,\"type\": \"number\",\"input\": \"slider\",\"step\": 1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('vertexai-gemini-1.5-flash-preview-0514');


UPDATE public."LlmEngine"
	SET config='"{\"temperature\": {\"label\": \"Temperature\",\"min\": 0,\"max\": 1,\"default\": 0.2,\"type\": \"number\",\"input\": \"slider\",\"step\": 0.1,\"description\": \"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\": {\"label\": \"Num. of relevant docs to retrieve\",\"min\": 0,\"max\": 50,\"default\": 3,\"type\": \"number\",\"input\": \"slider\",\"step\": 1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\": {\"label\": \"Top P\",\"min\": 0,\"max\": 1,\"default\": 0.94,\"type\": \"number\",\"input\": \"slider\",\"step\": 0.01,\"description\": \"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\": {\"label\": \"Max Tokens\",\"min\": 256,\"max\": 8192,\"default\": 4000,\"type\": \"number\",\"input\": \"slider\",\"step\": 1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length.\"},\"internet_search\": {\"label\": \"Internet Search\",\"min\": 0,\"max\": 1,\"default\": 0,\"type\": \"number\",\"input\": \"slider\",\"step\": 1,\"description\": \"Internet Search capability\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('vertexai-gemini-1.5-pro-001');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\": {\"label\": \"Temperature\",\"min\": 0,\"max\": 1,\"default\": 0.2,\"type\": \"number\",\"input\": \"slider\",\"step\": 0.1,\"description\": \"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\": {\"label\": \"Num. of relevant docs to retrieve\",\"min\": 0,\"max\": 50,\"default\": 3,\"type\": \"number\",\"input\": \"slider\",\"step\": 1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\": {\"label\": \"Top P\",\"min\": 0,\"max\": 1,\"default\": 0.94,\"type\": \"number\",\"input\": \"slider\",\"step\": 0.01,\"description\": \"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\": {\"label\": \"Max Tokens\",\"min\": 256,\"max\": 8192,\"default\": 4000,\"type\": \"number\",\"input\": \"slider\",\"step\": 1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length.\"},\"internet_search\": {\"label\": \"Internet Search\",\"min\": 0,\"max\": 1,\"default\": 0,\"type\": \"number\",\"input\": \"slider\",\"step\": 1,\"description\": \"Internet Search capability\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('vertexai-gemini-1.5-flash-001');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0.01,\"max\":2,\"default\":0.8,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"What sampling temperature to use, between 0.01 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":50,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":0.01,\"max\":0.99,\"default\":0.7,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":32768,\"default\":2000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"repetition_penalty\":{\"label\":\"Repetition Penalty\",\"min\":0.05,\"max\":2,\"default\":1.05,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"Number between 0.05 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('nova-sensechat-5-cantonese');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0.01,\"max\":2,\"default\":0.8,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"What sampling temperature to use, between 0.01 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":50,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":0.01,\"max\":0.99,\"default\":0.7,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":131072,\"default\":2000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"repetition_penalty\":{\"label\":\"Repetition Penalty\",\"min\":0.05,\"max\":2,\"default\":1.0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"Number between 0.05 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('nova-sensechat-5');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0.01,\"max\":2,\"default\":0.8,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"What sampling temperature to use, between 0.01 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":50,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":0.01,\"max\":0.99,\"default\":0.7,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":131072,\"default\":2000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"repetition_penalty\":{\"label\":\"Repetition Penalty\",\"min\":0.05,\"max\":2,\"default\":1.05,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"Number between 0.05 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('nova-sensechat-128K');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top_p\":{\"label\":\"Top P\",\"min\":1,\"max\":10,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":2048,\"default\":2000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"repetition_penalty\":{\"label\":\"Repetition Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":50,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('nova-ptc-xl-v1');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top_p\":{\"label\":\"Top P\",\"min\":1,\"max\":10,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":2048,\"default\":2000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"repetition_penalty\":{\"label\":\"Repetition Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":50,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('nova-ptc-xs-v1');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top_p\":{\"label\":\"Top P\",\"min\":1,\"max\":10,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":2048,\"default\":2000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"repetition_penalty\":{\"label\":\"Repetition Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":50,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('nova-ptc-yue-xl-v1');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":50,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":1,\"max\":10,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":8192,\"default\":1000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"presence_penalty\":{\"label\":\"Presence Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model''s likelihood to talk about new topics.\"},\"frequency_penalty\":{\"label\":\"Frequency Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('gpt-4');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":50,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":1,\"max\":10,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":8192,\"default\":1000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"presence_penalty\":{\"label\":\"Presence Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model''s likelihood to talk about new topics.\"},\"frequency_penalty\":{\"label\":\"Frequency Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('gpt-4-turbo');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0.2,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"Temperature controls the degree of randomness in token selection. Lower temperatures are good for prompts that expect a true or correct response, while higher temperatures can lead to more diverse or unexpected results. A temperature of 0 is deterministic: the highest probability token is always selected. For most use cases, try starting with a temperature of .2.\"},\"max_tokens\":{\"label\":\"Token Limit\",\"min\":1,\"max\":1024,\"default\":256,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Token limit determines the maximum amount of text output from one prompt. A token is approximately four characters. The default value is 256.\"},\"top_p\":{\"label\":\"Top P\",\"min\":0,\"max\":1,\"default\":0.8,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"Top-p changes how the model selects tokens for output. Tokens are selected from most probable to least until the sum of their probabilities equals the top-p value. For example, if tokens A, B, and C have a probability of .3, .2, and .1 and the top-p value is .5, then the model will select either A or B as the next token (using temperature). The default top-p value is .8.\"},\"top_k\":{\"label\":\"Tok K\",\"min\":1,\"max\":40,\"default\":40,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Top-k changes how the model selects tokens for output. A top-k of 1 means the selected token is the most probable among all tokens in the model’s vocabulary (also called greedy decoding), while a top-k of 3 means that the next token is selected from among the 3 most probable tokens (using temperature). The default top-k value is 40.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('vertexai-chat-bison');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0.2,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"Temperature controls the degree of randomness in token selection. Lower temperatures are good for prompts that expect a true or correct response, while higher temperatures can lead to more diverse or unexpected results. A temperature of 0 is deterministic: the highest probability token is always selected. For most use cases, try starting with a temperature of .2.\"},\"max_tokens\":{\"label\":\"Token Limit\",\"min\":1,\"max\":1024,\"default\":256,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Token limit determines the maximum amount of text output from one prompt. A token is approximately four characters. The default value is 256.\"},\"top_p\":{\"label\":\"Top P\",\"min\":0,\"max\":1,\"default\":0.8,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"Top-p changes how the model selects tokens for output. Tokens are selected from most probable to least until the sum of their probabilities equals the top-p value. For example, if tokens A, B, and C have a probability of .3, .2, and .1 and the top-p value is .5, then the model will select either A or B as the next token (using temperature). The default top-p value is .8.\"},\"top_k\":{\"label\":\"Tok K\",\"min\":1,\"max\":40,\"default\":40,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Top-k changes how the model selects tokens for output. A top-k of 1 means the selected token is the most probable among all tokens in the model’s vocabulary (also called greedy decoding), while a top-k of 3 means that the next token is selected from among the 3 most probable tokens (using temperature). The default top-k value is 40.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('vertexai-chat-bison-001');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0.2,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"Temperature controls the degree of randomness in token selection. Lower temperatures are good for prompts that expect a true or correct response, while higher temperatures can lead to more diverse or unexpected results. A temperature of 0 is deterministic: the highest probability token is always selected. For most use cases, try starting with a temperature of .2.\"},\"max_tokens\":{\"label\":\"Token Limit\",\"min\":1,\"max\":1024,\"default\":256,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Token limit determines the maximum amount of text output from one prompt. A token is approximately four characters. The default value is 256.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('vertexai-codechat-bison-001');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0.2,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"Temperature controls the degree of randomness in token selection. Lower temperatures are good for prompts that expect a true or correct response, while higher temperatures can lead to more diverse or unexpected results. A temperature of 0 is deterministic: the highest probability token is always selected. For most use cases, try starting with a temperature of .2.\"},\"max_tokens\":{\"label\":\"Token Limit\",\"min\":1,\"max\":2048,\"default\":256,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Token limit determines the maximum amount of text output from one prompt. A token is approximately four characters. The default value is 256.\"},\"top_p\":{\"label\":\"Top P\",\"min\":0,\"max\":1,\"default\":0.8,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"Top-p changes how the model selects tokens for output. Tokens are selected from most probable to least until the sum of their probabilities equals the top-p value. For example, if tokens A, B, and C have a probability of .3, .2, and .1 and the top-p value is .5, then the model will select either A or B as the next token (using temperature). The default top-p value is .8.\"},\"top_k\":{\"label\":\"Tok K\",\"min\":1,\"max\":40,\"default\":40,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Top-k changes how the model selects tokens for output. A top-k of 1 means the selected token is the most probable among all tokens in the model’s vocabulary (also called greedy decoding), while a top-k of 3 means that the next token is selected from among the 3 most probable tokens (using temperature). The default top-k value is 40.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('vertexai-chat-bison-002');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0.2,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"Temperature controls the degree of randomness in token selection. Lower temperatures are good for prompts that expect a true or correct response, while higher temperatures can lead to more diverse or unexpected results. A temperature of 0 is deterministic: the highest probability token is always selected. For most use cases, try starting with a temperature of .2.\"},\"max_tokens\":{\"label\":\"Token Limit\",\"min\":1,\"max\":2048,\"default\":256,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Token limit determines the maximum amount of text output from one prompt. A token is approximately four characters. The default value is 256.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('vertexai-codechat-bison-002');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":50,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":1,\"max\":10,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":4096,\"default\":1000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"presence_penalty\":{\"label\":\"Presence Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model''s likelihood to talk about new topics.\"},\"frequency_penalty\":{\"label\":\"Frequency Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('gpt-35-turbo-0613');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":50,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":1,\"max\":10,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":4096,\"default\":1000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"presence_penalty\":{\"label\":\"Presence Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model''s likelihood to talk about new topics.\"},\"frequency_penalty\":{\"label\":\"Frequency Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('gpt-35-turbo-1106');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":50,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":1,\"max\":10,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":4096,\"default\":1000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"presence_penalty\":{\"label\":\"Presence Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model''s likelihood to talk about new topics.\"},\"frequency_penalty\":{\"label\":\"Frequency Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('gpt-35-turbo');


UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":50,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":0,\"max\":1,\"default\":0.95,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":4000,\"default\":1000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"max_input_token\":{\"label\":\"Input max Tokens\",\"min\":256,\"max\":128000,\"default\":1000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Input max tokens\", \"readOnly\":true},\"presence_penalty\":{\"label\":\"Presence Penalty\",\"min\":0,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"Number between 0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model''s likelihood to talk about new topics.\"},\"frequency_penalty\":{\"label\":\"Frequency Penalty\",\"min\":0,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"Number between 0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('gpt-4o-mini');


UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":50,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":0,\"max\":1,\"default\":0.95,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":4096,\"default\":1000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"max_input_token\":{\"label\":\"Input max Tokens\",\"min\":256,\"max\":128000,\"default\":1000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Input max tokens\", \"readOnly\":true},\"presence_penalty\":{\"label\":\"Presence Penalty\",\"min\":0,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"Number between 0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model''s likelihood to talk about new topics.\"},\"frequency_penalty\":{\"label\":\"Frequency Penalty\",\"min\":0,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"Number between 0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"},\"past_message\":{\"label\":\"Past message\",\"min\":1,\"max\":9,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":2,\"description\":\"Number of past messages to include in each new API request.\"}}"'::jsonb
	WHERE slug IN ('gpt-4o');
