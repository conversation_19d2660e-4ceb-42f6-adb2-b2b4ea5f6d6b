import { ApiProperty } from '@nestjs/swagger';
import { Environment, SnapshotEntityType } from '@prisma/client';
import { IsEnum, IsOptional, IsString } from 'class-validator';

export class CreateDataDeletionRequestDto {
  @ApiProperty({ enum: Environment })
  @IsEnum(Environment)
  env: Environment;

  @ApiProperty({ enum: SnapshotEntityType })
  @IsEnum(SnapshotEntityType)
  entityType: SnapshotEntityType;

  @ApiProperty()
  @IsString()
  entityId: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  comment?: string;
}

export enum EntitySnapshotStatus {
  APPROVED = 'APPROVED',
  PENDING = 'PENDING',
  TODO = 'TODO',
  REJECTED = 'REJECTED',
  CANCELED = 'CANCELED',
}
