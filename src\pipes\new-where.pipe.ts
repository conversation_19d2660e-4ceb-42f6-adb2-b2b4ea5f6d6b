import { Injectable, PipeTransform } from '@nestjs/common';

@Injectable()
export class NewWherePipe implements PipeTransform {
  transform(input: Record<string, any> | undefined | null) {
    if (!input) return input;
    const output: Record<string, any> = {};
    for (const key in input) {
      if (Array.isArray(input[key])) {
        output[key] = input[key].map((item) =>
          typeof item === 'object' ? this.transform(item) : this.transformStrValue(item),
        );
      } else if (typeof input[key] === 'object' && input[key] !== null) {
        output[key] = this.transform(input[key] as Record<string, any>);
      } else {
        if (key === 'contains') {
          output['mode'] = 'insensitive';
        }
        output[key] = this.transformStrValue(input[key] as string);
      }
    }
    return output;
  }
  private transformStrValue(input: string) {
    if (input.endsWith(')')) {
      const valueExtracted = /\(([^)]+)\)/.exec(input)?.[1] ?? null;
      if (input.startsWith('int(')) {
        return parseInt(valueExtracted);
      } else if (input.startsWith('date(') || input.startsWith('datetime('))
        return new Date(valueExtracted).toISOString();
      else if (input.startsWith('float(')) return parseFloat(valueExtracted);
      else if (input.startsWith('string(')) return valueExtracted;
      else if (input.startsWith('boolean(') || input.startsWith('bool('))
        return valueExtracted === 'true';
      return valueExtracted;
    } else {
      return input;
    }
  }
}
