import {
  ArgumentsHost,
  BadRequestException,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Response } from 'express';

import { ApiException, ErrorCode } from 'src/errors/errors.constants';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    this.logger.error(exception, `[Exception]`);

    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();
    const isStream = request?.body?.overrides?.stream || request?.body?.stream || false;

    const status =
      exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;
    let errorBody = ApiException.parseErrorString(ErrorCode.DEFAULT).message;
    if (exception instanceof ApiException) {
      (errorBody as any) = exception.getResponse();
    } else if (exception instanceof HttpException) {
      (errorBody as any) = this.translateHttpException(exception);
    } else {
      const { statusCode, code, message, extra } = ApiException.parseErrorString(ErrorCode.DEFAULT);
      (errorBody as any) = {
        statusCode,
        message: message,
        error: {
          code,
          message,
          extra,
        },
      };
    }
    if (isStream) {
      const streamData = `event: error\ndata:${JSON.stringify(errorBody)}\n\n`;
      response.write(streamData);
      response.end();
    } else {
      return response.status(status).json(errorBody);
    }
  }

  translateHttpException(exception: HttpException): Record<string, unknown> {
    const r = exception.getResponse() as Record<string, unknown>;
    const errorMessage = String(r?.['message'] || '');
    let [matched, code, message] = errorMessage.match(/^([^:]+): (.+)$/) ?? [];
    if (matched) {
      return { error: { code, message } };
    }

    matched = 'false';
    code = `${exception.getStatus()}-0-999`;
    message = (r['error'] ? r['error'] : r['message']) as string;
    let extra;

    if (exception instanceof BadRequestException) {
      if (r['message'] instanceof Array) {
        extra = r['message'].map((obj) => {
          // remove the target object, because it might contain the original request body, and thus PII info or password
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { target, ...rest } = obj;
          return rest;
        });
      }
    }

    return { error: { code, message, extra } };
  }
}
