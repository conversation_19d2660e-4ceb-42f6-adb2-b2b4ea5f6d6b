/* eslint-disable @typescript-eslint/no-explicit-any */
export class PIIScanners {
  inputScanners: any;
  outputScanners: any;
}
export class BotSecurityEntity {
  botSecurityId: string;
  groupId: number;
  PIIScanners: PIIScanners;
}

export enum SecurityLevel {
  HARD = 'HARD',
  SOFT = 'SOFT',
  OFF = 'OFF',
}

export class ScannerObject {
  type: string;
  params: Param[];
  explanation: string;
  securityLevel: string;
}

type Param = {
  name: string;
  type: string;
  value: any;
};
