import { Controller, Get, HttpStatus, Redirect } from '@nestjs/common';
import { Public } from '../auth/public.decorator';
import { ApiTags } from '@nestjs/swagger';
import { DeprecatedResources } from '../../decorators/deprecated.resources.decorator';

@Controller()
@Public()
@ApiTags('Meta')
export class MetaController {
  constructor() {}

  /** Redirect to staart/staart */
  @Get()
  @DeprecatedResources()
  @Redirect('https://github.com/staart/staart', HttpStatus.FOUND)
  get() {}
}
