/*
  Warnings:

  - A unique constraint covering the columns `[env,slug]` on the table `LlmEngine` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "ResourceEntityType" AS ENUM ('LLM_ENGINE');

-- DropIndex
DROP INDEX "LlmEngine_env_platform_slug_key";

-- AlterTable
ALTER TABLE "Resource" ADD COLUMN     "resourceEntityKey" TEXT,
ADD COLUMN     "resourceEntityType" "ResourceEntityType";

-- CreateIndex
CREATE UNIQUE INDEX "LlmEngine_env_slug_key" ON "LlmEngine"("env", "slug");

DELETE FROM "Resource" WHERE "subscriberType" = 'BOT';

ALTER TYPE "FileType" ADD VALUE 'PLAN_SUBSCRIPTION_HISTORY';
