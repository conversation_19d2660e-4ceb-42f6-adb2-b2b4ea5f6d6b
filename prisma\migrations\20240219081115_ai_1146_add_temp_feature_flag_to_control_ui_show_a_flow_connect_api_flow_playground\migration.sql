-- 

INSERT INTO "FeatureFlag"("key", "value", "metaData", "description", "isEnabled", "isForClientSide", "updatedAt")
SELECT 'CONNECT_API.ENABLE_CONNECT_API', '', '{}',
'', false, true, now() 
WHERE NOT EXISTS (
SELECT id FROM "FeatureFlag" WHERE "key" = 'CONNECT_API.ENABLE_CONNECT_API'
);

INSERT INTO "FeatureFlag"("key", "value", "metaData", "description", "isEnabled", "isForClientSide", "updatedAt")
SELECT 'FLOW.ENABLE_FLOW', '', '{}',
'', false, true, now() 
WHERE NOT EXISTS (
SELECT id FROM "FeatureFlag" WHERE "key" = 'FLOW.ENABLE_FLOW'
);

INSERT INTO "FeatureFlag"("key", "value", "metaData", "description", "isEnabled", "isForClientSide", "updatedAt")
SELECT 'FLOW.ENABLE_FLOW_PLAYGROUND', '', '{}',
'', false, true, now() 
WHERE NOT EXISTS (
SELECT id FROM "FeatureFlag" WHERE "key" = 'FLOW.ENABLE_FLOW_PLAYGROUND'
);

DELETE FROM "FeatureFlag" WHERE "key" = 'TEMP_ENABLE_FLOW';
