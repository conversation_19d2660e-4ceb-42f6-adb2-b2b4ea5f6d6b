import { PrismaClient, User, Email } from '@prisma/client';
import { RegisterDto } from '../src/modules/auth/auth.dto';
const prisma = new PrismaClient();

async function main() {
  const users = await prisma.user.findMany();
  console.log(users);

  const emails = await prisma.email.findMany();
  console.log(emails);

  const _data: RegisterDto = {
    email: '<EMAIL>',
    name: 'ben',
    password: '1234',
  };
  const id: number = 1234;

  const { email, origin, ...data } = _data;

  const emailSafe = email;

  const testUser = await prisma.user.findFirst({
    where: { emails: { some: { emailSafe } } },
  });

  const user = await prisma.user.create({
    data: {
      ...data,
      roleId: 0,
      // id,
      emails: {
        create: { email: email, emailSafe },
      },
    },
    include: { emails: { select: { id: true } } },
  });
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
