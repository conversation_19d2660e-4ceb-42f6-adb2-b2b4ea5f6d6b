import { Type } from 'class-transformer';
import {
  IsNumber,
  IsObject,
  IsOptional,
  IsPositive,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ChatSettingDto } from './chat-setting.dto';

export class UpdateChatSessionRequestDto {
  @IsString()
  name: string;

  @ValidateNested()
  @Type(() => ChatSettingDto)
  @IsObject()
  chatSetting: ChatSettingDto;

  @IsOptional()
  @IsPositive()
  @IsNumber()
  messageTemplateId?: number;
}
