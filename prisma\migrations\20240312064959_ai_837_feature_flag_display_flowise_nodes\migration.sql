
INSERT INTO "FeatureFlag" (description, key, value, "metaData", "createdAt", "updatedAt", "updatedByUserId", "isEnabled", "isForClientSide")
VALUES ('The components which are allowed to display within the component bar on the Flowise page.',
        'FLOWISE.CONFIG_ALLOWED_DISPLAY_NODES',
        '',
        '{"value": ["HKTGPTAIBotBuilder", "BotBuilderChainTool", "hktGoogleCustomSearch","hktConfigurableConversationalAgent"]}',
        now(), now(), null, true, true);

update "FeatureFlag" set "metaData" = (
    case when "metaData"->'value' is null
             then  jsonb_set("metaData", '{value}', '["FLOWISE.CONFIG_ALLOWED_DISPLAY_NODES"]')
         else  jsonb_insert("metaData", '{value,0}', '"FLOWISE.CONFIG_ALLOWED_DISPLAY_NODES"')
        end
    )
WHERE "key" = 'ADMIN.CONFIG_FLOW_FEATURE_FLAGS_FOR_GROUP_ADMIN';