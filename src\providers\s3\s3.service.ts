import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  S3Client,
  GetObjectCommand,
  HeadObjectCommand,
  PutObjectCommand,
  DeleteObjectCommand,
  DeleteObjectsCommand,
  CopyObjectCommand,
  GetObjectCommandOutput,
  HeadObjectCommandOutput,
  PutObjectCommandOutput,
  DeleteObjectCommandOutput,
  DeleteObjectsCommandOutput,
  CopyObjectCommandOutput
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { createPresignedPost } from '@aws-sdk/s3-presigned-post';
import { Upload } from '@aws-sdk/lib-storage';
import { Configuration } from '../../config/configuration.interface';
import { encodeBase64 } from 'bcryptjs';
import { Readable } from 'stream';

@Injectable()
export class S3Service {
  client?: S3Client;
  private logger = new Logger(S3Service.name);

  constructor(private configService: ConfigService) {
    const config = this.configService.get<Configuration['s3']>('s3');

    // use aws role
    this.client = new S3Client({
      region: config.region,
      ...(config.endpoint ? { endpoint: config.endpoint } : {}),
    });

    // else this.logger.warn('No S3 API key set');
  }

  /** Get a signed URL to access an S3 object for 5 minutes */
  async signedUrl(bucket: string, key: string): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: bucket,
        Key: key,
      });

      const signedUrl = await getSignedUrl(this.client, command, { expiresIn: 300 });
      return signedUrl;
    } catch (error) {
      throw error;
    }
  }

  /** Get a policy to upload to S3 directly */
  async postPolicy(bucket: string, key: string): Promise<any> {
    try {
      const presignedPost = await createPresignedPost(this.client, {
        Bucket: bucket,
        Key: key,
        Expires: 300,
      });
      return presignedPost;
    } catch (error) {
      throw error;
    }
  }

  async upload(
    name: string,
    originalFilename: string,
    body: Buffer,
    filetype: string,
    groupId: number,
    bucket?: string,
    publicRead?: boolean,
  ): Promise<any> {
    try {
      const upload = new Upload({
        client: this.client,
        params: {
          Bucket: bucket,
          Key: name,
          Body: body,
          ACL: publicRead ? 'public-read' : undefined,
          ContentType: filetype,
          Metadata: {
            filename: Buffer.from(originalFilename).toString('base64'),
            groupid: groupId.toString(),
          },
        },
      });

      const result = await upload.done();
      return result;
    } catch (error) {
      throw error;
    }
  }

  async getContentLength(bucket: string, name: string) {
    try {
      const command = new GetObjectCommand({
        Bucket: bucket,
        Key: name,
      });
      const { ContentLength } = await this.client.send(command);
      return ContentLength;
    } catch (err) {
      this.logger.error(err, 'Failed to get s3 object err');
      throw new Error();
    }
  }

  async head(bucket: string, name: string) {
    const command = new HeadObjectCommand({ Bucket: bucket, Key: name });
    return this.client.send(command);
  }

  async get(bucket: string, name: string) {
    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: name,
    });
    return await this.client.send(command);
  }

  async getFileObject(bucket: string, name: string) {
    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: name,
    });
    return await this.client.send(command);
  }

  // for createReadStream()
  // https://github.com/aws/aws-sdk-js/issues/4123
    // for createReadStream()
  // https://github.com/aws/aws-sdk-js/issues/4123
  // 修改 getObjectUnsafe 方法
async getObjectUnsafe(bucket: string, key: string) {
  
    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: key,
    });

    return await this.client.send(command);
  
}

  async delete(bucket: string, key: string): Promise<DeleteObjectCommandOutput> {
    try {
      const command = new DeleteObjectCommand({ Bucket: bucket, Key: key });
      const result = await this.client.send(command);
      return result;
    } catch (error) {
      throw error;
    }
  }

  async deleteMany(bucket: string, keys: string[]): Promise<DeleteObjectsCommandOutput> {
    try {
      const keysToDelete = keys.map((key) => ({ Key: key }));
      const command = new DeleteObjectsCommand({
        Bucket: bucket,
        Delete: { Objects: keysToDelete },
      });
      const result = await this.client.send(command);
      return result;
    } catch (error) {
      throw error;
    }
  }

  async copyFileObject(
    sourceBucket: string,
    sourcePath: string,
    targetBucket: string,
    targetKey: string,
  ) {
    const command = new CopyObjectCommand({
      CopySource: sourceBucket + '/' + sourcePath,
      Bucket: targetBucket,
      Key: targetKey,
    });
    return this.client.send(command);
  }

  async isExistedObject(bucket: string, key: string) {
    try {
      await this.head(bucket, key);
      return true;
    } catch (err) {
      return false;
    }
  }

  async getContentType(bucket: string, key: string) {
    try {
      const command = new HeadObjectCommand({
        Bucket: bucket,
        Key: key,
      });
      const headObjectResponse = await this.client.send(command);
      return headObjectResponse.ContentType;
    } catch (err) {
      this.logger.error(err, 'Failed to get s3 object err');
      throw new Error();
    }
  }

  async uploadAndCacheControl(
    name: string,
    originalFilename: string,
    body: Buffer,
    filetype: string,
    groupId: number,
    cacheControl: string,
    bucket?: string,
    publicRead?: boolean,
  ): Promise<any> {
    try {
      const upload = new Upload({
        client: this.client,
        params: {
          Bucket: bucket,
          Key: name,
          Body: body,
          ACL: publicRead ? 'public-read' : undefined,
          ContentType: filetype,
          CacheControl: cacheControl,
          Metadata: {
            filename: Buffer.from(originalFilename).toString('base64'),
            groupid: groupId.toString(),
          },
        },
      });

      const result = await upload.done();
      return result;
    } catch (error) {
      throw error;
    }
  }
}
