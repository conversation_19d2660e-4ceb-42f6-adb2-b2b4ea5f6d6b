import { Module } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { FeatureFlagController } from './feature-flags.controller';
import { FeatureFlagService } from './feature-flags.service';
import { RedisModule } from 'src/providers/redis/redis.module';

@Module({
  imports: [PrismaModule, RedisModule],
  controllers: [FeatureFlagController],
  providers: [FeatureFlagService],
  exports: [FeatureFlagService],
})
export class FeatureFlagModule {}
