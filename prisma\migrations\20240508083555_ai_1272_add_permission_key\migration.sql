/*
  Warnings:

  - You are about to drop the column `roleId` on the `ModelFilePermissionButton` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[isApproved,status,hasPII,isRequireSecondaryApproval,fileClassification,permissionKey,env]` on the table `ModelFilePermissionButton` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `permissionKey` to the `ModelFilePermissionButton` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "ModelFilePermissionButton_isApproved_status_hasPII_isRequir_key";

-- AlterTable
ALTER TABLE "ModelFilePermissionButton" DROP COLUMN "roleId",
ADD COLUMN     "permissionKey" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "ModelFilePermissionButton_isApproved_status_hasPII_isRequir_key" ON "ModelFilePermissionButton"("isApproved", "status", "hasPII", "isRequireSecondaryApproval", "fileClassification", "permissionKey", "env");
