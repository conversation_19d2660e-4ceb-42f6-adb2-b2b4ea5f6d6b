import { Module, forwardRef } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { GroupController } from './controllers/groups.controller';
import { GroupsService } from './groups.service';
import { LLMModelsModule } from '../llm-models/llm-models.module';
import { ApiKeysService } from '../api-keys/api-keys.service';
import { TokensService } from '../../providers/tokens/tokens.service';
import { ConfigService } from '@nestjs/config';
import { ElasticSearchService } from '../../providers/elasticsearch/elasticsearch.service';
import { LLMBackendModule } from '../../providers/llm-backend/llm-backend.module';
import { UsersService } from '../users/users.service';
import { AuthService } from '../auth/auth.service';
import { MailService } from '../../providers/mail/mail.service';
import { S3Module } from '../../providers/s3/s3.module';
import { S3Service } from '../..//providers/s3/s3.service';
import { PwnedService } from '../../providers/pwned/pwned.service';
import { GeolocationService } from '../../providers/geolocation/geolocation.service';
import { ApprovedSubnetsService } from '../approved-subnets/approved-subnets.service';
import { TwilioService } from '../../providers/twilio/twilio.service';
import { SsoService } from '../../providers/sso/sso.service';
import { ScopeService } from '../scope/scope.service';
import { FileHistoryService } from '../file-history/file-history.service';
import { GraviteeService } from '../../providers/gravitee/gravitee.service';
import { MembershipsService } from '../memberships/memberships.service';
import { AzureSSOService } from 'src/providers/sso/azure-sso.service';
import { WikijsModule } from '../wikijs/wikijs.module';
import { ChatSessionsModule } from '../chat-sessions/chat-sessions.module';
import { FlowsService } from '../flows/flows.service';
import { FlowBackendModule } from 'src/providers/flow-backend/flow-backend.module';
import { PermissionsService } from '../permissions/permissions.service';
import { FlowBotsService } from '../flow-bots/flow-bots.service';
import { ApiResourceModule } from '../api-resource/api-resource.module';
import { ApiResourceService } from '../api-resource/api-resource.service';
import { FlowBotRequestsService } from '../flow-bot-requests/flow-bot-requests.service';
import { SummaryModule } from '../summary/summary.module';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';
import { RedisModule } from 'src/providers/redis/redis.module';
import { GroupAdminController } from './controllers/groups-admin.controller';
import { PlansModule } from '../plans/plans.module';
import { BotSecurityModule } from '../bot-security/bot-security.module';
import { RateLimitModule } from 'src/providers/rate-limit/rate-limit.module';
import { ChatWithDataModule } from 'src/providers/chat-with-data/chat-with-data.module';
import { LlmEnginesModule } from '../llm-engines/llm-engines.module';
import { RolesModule } from '../roles/roles.module';
import { DocumentTagModule } from '../document-tag/document-tag.module';
import { QuotaModule } from '../quotas/quota.module';
import { ChatFilesModule } from '../chat-files/chat-files.module';
import { LastUsedGroupsModule } from '../../providers/last-used-groups/last-used-groups.module';
import { ResizeImageModule } from 'src/providers/resize-image/resize-image.module';
import { GroupNotificationModule } from '../group-notification/group-notification.module';
import { NotificationBackendModule } from 'src/providers/notification-backend/notification-backend.module';
import { BatchProcessorModule } from 'src/providers/batch-processor/batch-processor.module'; // Added import

import { LabelsModule } from '../labels/labels.module';
import { GroupBatchFilesController } from './controllers/group-batch-files.controller'; // Added import
import { GroupBatchProcessesController } from './controllers/group-batch-processes.controller'; // Added import
import { TokensModule } from '../../providers/tokens/tokens.module';

@Module({
  imports: [
    PlansModule,
    PrismaModule,
    LLMModelsModule,
    LLMBackendModule,
    S3Module,
    forwardRef(() => WikijsModule),
    ChatSessionsModule,
    FlowBackendModule,
    SummaryModule,
    forwardRef(() => ApiResourceModule),
    FeatureFlagModule,
    RedisModule,
    BotSecurityModule,
    RateLimitModule,
    ChatWithDataModule,
    LlmEnginesModule,
    forwardRef(() => RolesModule),
    DocumentTagModule,
    QuotaModule,
    ChatFilesModule,
    LastUsedGroupsModule,
    ResizeImageModule,
    GroupNotificationModule,
    NotificationBackendModule,
    LabelsModule,
    BatchProcessorModule, // Added module
    TokensModule,
  ],
  controllers: [
    GroupController,
    GroupAdminController,
    GroupBatchFilesController, // Added controller
    GroupBatchProcessesController, // Added controller
  ],
  providers: [
    GroupsService,
    ApiKeysService,
    TokensService,
    ConfigService,
    ElasticSearchService,
    UsersService,
    AuthService,
    MailService,
    S3Service,
    PwnedService,
    GeolocationService,
    ApprovedSubnetsService,
    TwilioService,
    SsoService,
    AzureSSOService,
    ScopeService,
    GraviteeService,
    FileHistoryService,
    ApiKeysService,
    MembershipsService,
    FlowsService,
    PermissionsService,
    FlowBotsService,
    ApiResourceService,
    FlowBotRequestsService,
  ],
  exports: [GroupsService],
})
export class GroupsModule {}
