import { IsBoolean, IsIn, IsInt, <PERSON>N<PERSON>ber, IsOptional } from 'class-validator';

export class LlmParamsDto {
  @IsNumber()
  @IsOptional()
  temperature?: number;

  @IsInt()
  @IsOptional()
  top?: number;

  @IsNumber()
  @IsOptional()
  top_p?: number;

  @IsInt()
  @IsOptional()
  max_tokens?: number;

  @IsNumber()
  @IsOptional()
  presence_penalty?: number;

  @IsNumber()
  @IsOptional()
  frequency_penalty?: number;

  @IsBoolean()
  @IsOptional()
  stream?: boolean;

  @IsInt()
  @IsOptional()
  internet_search?: number;

  @IsInt()
  @IsOptional()
  past_message?: number;

  @IsOptional()
  size?: string;

  @IsOptional()
  style?: string;

  @IsOptional()
  quality?: string;
  
}
