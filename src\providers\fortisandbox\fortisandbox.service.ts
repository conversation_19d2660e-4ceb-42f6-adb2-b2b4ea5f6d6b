import {
  forwardRef,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  ChatFile,
  ModelFile,
  ScanMalwareStatus,
} from '@prisma/client';
import axios, { AxiosInstance } from 'axios';
import { Configuration } from '../../config/configuration.interface';
import { PrismaService } from '../prisma/prisma.service';
import FormData from 'form-data';
import { LLMBackendService } from '../../providers/llm-backend/llm-backend.service';
import * as fs from 'fs';
import { ChatFilesService } from '../../modules/chat-files/chat-files.service';
import { Readable } from 'stream';
import { UpdateFileMalwareScanResponse } from './fortisandbox.interface';

@Injectable()
export class FortiSanboxService {
  axios?: AxiosInstance;
  private logger = new Logger(FortiSanboxService.name);

  constructor(
    private configService: ConfigService,
    private prisma: PrismaService,
    @Inject(forwardRef (() => LLMBackendService))
    private llmBackendService: LLMBackendService,
    @Inject(forwardRef (() => ChatFilesService))
    private chatFilesService: ChatFilesService,
  ) {}


  async scanMalware(
    docId: string,
    fileRecord: ModelFile,
    env: string,
    callBackUrl: string
  ): Promise<any> {
    try {
      const callBackUrl = this.configService.get<string>('scanMalware.callbackUrl');
      const fileRequest = await this.llmBackendService.getS3ModelFileStream(env, fileRecord.s3Path);

      const data = await this.scan(fileRecord.s3Path, docId, callBackUrl, fileRecord, fileRequest);
      await this.prisma.modelFile.update({
        where: {
          docId,
        },
        data,
      });
      return fileRecord;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async scanChatFileMalware(
    chatFile: ChatFile,
    file: Express.Multer.File | Readable,
  ): Promise<any> {
    const callBackUrl = this.configService.get<string>('scanMalware.chatFileCallbackUrl');
    const s3FileKey = this.chatFilesService.getS3FileKey(
      chatFile.groupId,
      chatFile.userId,
      chatFile.s3Basename,
      chatFile.type,
    );
    this.logger.log({
      Action: 'Going to fortiSandbox to scan malware',
      'file.s3Basename': chatFile.s3Basename,
      groupId: chatFile.groupId,
      userId: chatFile.userId,
    });
    const data = await this.scan(s3FileKey, chatFile.s3Basename, callBackUrl, chatFile, file);
    const chatFileData = await this.prisma.chatFile.findUnique({ where: { id: chatFile.id } });
    if (chatFileData) {
      await this.prisma.chatFile.update({
        where: {
          id: chatFile.id,
        },
        data,
      });
    } else {
      this.logger.log(`chatFile removed  ${chatFile.s3Basename}`);
    }
    return chatFile;
  }

  async scan(
    s3Path: string,
    docId: string,
    callBackUrl: string,
    fileRecord: ModelFile | ChatFile,
    fileContent: Express.Multer.File | Readable,
  ): Promise<UpdateFileMalwareScanResponse> {
    try {
      const jsonData = {
        callBackUrl: callBackUrl,
        docId: docId
      };
      this.logger.log(`s3path: ${s3Path} ,scanMalware jsonData: ${JSON.stringify(jsonData)}`);

      const formData = new FormData();
      const fileRequest = fileContent instanceof Readable ? fileContent : fileContent.buffer;
      formData.append("file", fileRequest, {
        filename: fileRecord.filename,
        // contentType: file.mimetype,
        contentType: fileContent instanceof Readable ? 'application/octet-stream' : fileContent.mimetype,
        knownLength: fileRecord.fileSize,
      });
      formData.append("callBackUrl", callBackUrl);
      formData.append("docId", docId);

      const response = await axios.post(this.configService.get<string>('scanMalware.backendPrefix') + '/submit-file', formData,{
        headers: {
          ...formData.getHeaders(),
        },
        maxBodyLength: Infinity
      });
      this.logger.log(`scanMalware response: ${JSON.stringify(response.data)}`);

      const data: UpdateFileMalwareScanResponse = {
        scanMalwareStatus: ScanMalwareStatus.SCANNING,
      }

      if (!response.data || response.data['status'] !== 0) {
        data.scanMalwareStatus = ScanMalwareStatus.SCANNING_FAILED;
        data.errCode = fileRecord.errCode ? `${fileRecord.errCode}, MS0001` : 'MS0001';
      }
      return data;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

}
