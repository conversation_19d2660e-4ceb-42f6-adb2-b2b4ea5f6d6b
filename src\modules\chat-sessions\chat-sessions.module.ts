import { forwardRef, Module } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { ChatSessionsService } from './chat-sessions.service';
import { ChatSessionsController } from './chat-sessions.controller';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';
import { BotSecurityModule } from '../bot-security/bot-security.module';
import { GroupsModule } from '../groups/groups.module';
import { LlmEnginesModule } from '../llm-engines/llm-engines.module';
import { ChatFilesModule } from '../chat-files/chat-files.module';
import { LLMModelsModule } from '../llm-models/llm-models.module';
import { PublicChatSessionController } from './chat-session.public.controller';
@Module({
  imports: [
    PrismaModule,
    FeatureFlagModule,
    BotSecurityModule,
    forwardRef(() => GroupsModule),
    LlmEnginesModule,
    forwardRef(() => ChatFilesModule),
    forwardRef(() => LLMModelsModule),
    FeatureFlagModule,
  ],
  controllers: [ChatSessionsController, PublicChatSessionController],
  providers: [ChatSessionsService],
  exports: [ChatSessionsService],
})
export class ChatSessionsModule {}
