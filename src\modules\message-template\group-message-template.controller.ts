import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
} from '@nestjs/common';
import { MessageTemplate, TemplateAccessLevel } from '@prisma/client';
import { OptionalIntPipe } from '../../pipes/optional-int.pipe';
import { OrderByPipe } from '../../pipes/order-by.pipe';
import { WherePipe } from '../../pipes/where.pipe';
import { CursorPipe } from '../../pipes/cursor.pipe';
import { Expose } from '../../providers/prisma/prisma.interface';
import { UserRequest } from '../auth/auth.interface';
import { Scopes } from '../auth/scope.decorator';
import { CreateMessageTemplateDto, UpdateMessageTemplateDto } from './message-template.dto';
import { MessageTemplateService } from './message-template.service';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@Controller('groups/:groupId/message-templates')
@ApiBearerAuth('bearer-auth')
@ApiTags('Group Message Template')
export class GroupMessageTemplateController {
  constructor(private messageTemplateService: MessageTemplateService) {}

  /** Create message templates */
  @Post()
  @AuditLog('create-message-template')
  @Scopes('group-{groupId}:write-message-template')
  async create(
    @Body() data: CreateMessageTemplateDto,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Req() request: UserRequest,
  ): Promise<MessageTemplate> {
    data.accessLevel = TemplateAccessLevel.GROUP;
    data.groupId = groupId;
    return await this.messageTemplateService.createMessageTemplate(data, request);
  }

  /** Get group message templates */
  @Get()
  @Scopes('group-{groupId}:read-message-template')
  async getAll(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<{ list: Expose<MessageTemplate[]>; count: number }> {
    const list = await this.messageTemplateService.getMessageTemplates(
      { skip, take, orderBy, where },
      undefined,
      groupId,
    );
    const count = await this.messageTemplateService.getMessageTemplatesCount(
      where,
      undefined,
      groupId,
    );
    return {
      list,
      count,
    };
  }

  /** Get group and system message templates */
  @Get('group-and-system')
  @Scopes('group-{groupId}:read-message-template', 'group-{groupId}:read-playground')
  async getGroupAndSystemAll(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<Expose<MessageTemplate>[]> {
    return await this.messageTemplateService.getGroupAndSystemMessageTemplates(
      groupId,
      where,
      orderBy,
    );
  }

  /** Get a message template */
  @Get(':messageTemplateId')
  @Scopes('group-{groupId}:read-message-template')
  async get(
    @Param('messageTemplateId', ParseIntPipe) id: number,
  ): Promise<Expose<MessageTemplate>> {
    return await this.messageTemplateService.getMessageTemplate(id);
  }

  /** Update a message template */
  @Patch(':messageTemplateId')
  @AuditLog('update-message-template')
  @Scopes('group-{groupId}:write-message-template')
  async update(
    @Param('messageTemplateId', ParseIntPipe) id: number,
    @Body() data: UpdateMessageTemplateDto,
    @Req() request: UserRequest,
  ): Promise<Expose<MessageTemplate>> {
    return await this.messageTemplateService.updateMessageTemplate(id, data, request);
  }

  /** Share a message template */
  @Patch('/share/:messageTemplateId')
  @AuditLog('share-message-template')
  @Scopes('group-{groupId}:write-message-template')
  async share(
    @Param('messageTemplateId', ParseIntPipe) id: number,
  ): Promise<Expose<MessageTemplate>> {
    return await this.messageTemplateService.shareMessageTemplate(id);
  }

  /** Unshare a message template */
  @Patch('/unshare/:messageTemplateId')
  @AuditLog('unshare-message-template')
  @Scopes('group-{groupId}:write-message-template')
  async unshare(
    @Param('messageTemplateId', ParseIntPipe) id: number,
  ): Promise<Expose<MessageTemplate>> {
    return await this.messageTemplateService.unshareMessageTemplate(id);
  }

  /** Delete a message template */
  @Delete(':messageTemplateId')
  @AuditLog('delete-message-template')
  @Scopes('group-{groupId}:delete-message-template')
  async remove(@Param('messageTemplateId', ParseIntPipe) id: number) {
    return await this.messageTemplateService.deleteMessageTemplate(id);
  }
}
