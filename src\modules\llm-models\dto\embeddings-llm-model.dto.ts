import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsDefined,
  IsEnum,
} from 'class-validator';
import { EMBEDDINGS_MODEL,} from '../../../providers/llm-backend/llm-backend.interface';

export class EmbeddingsOverrides {
    @IsEnum(EMBEDDINGS_MODEL)
    @ApiProperty({
      enum: EMBEDDINGS_MODEL,
      description:
        'Embeddings model',
      default: EMBEDDINGS_MODEL.TEXT_EMBEDDING_ADA_002,
    })
    model: EMBEDDINGS_MODEL;
  }
  
  export class EmbeddingsDto {
    @IsArray()
    @ArrayMinSize(1)
    @IsDefined({ each: true })
    @ApiProperty({
      type: [String],
      example: ["This is the first document to be embedded.", "This is the second document to be embedded."]
    })
    texts: String[];
  
    @ApiProperty({
      type: EmbeddingsOverrides,
    })
    overrides: EmbeddingsOverrides;
  }