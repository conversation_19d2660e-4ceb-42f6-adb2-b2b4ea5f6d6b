import { <PERSON>, Controller, Delete, Get, Logger, Param, ParseInt<PERSON>ip<PERSON>, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Scopes } from '../auth/scope.decorator';
import {
  CreateWikijsMembershipDTO,
  DeleteWikiMembershipDTO,
  EnableWikijsDTO,
  WikiPeerDTO,
} from './wikijs.interface';
import { WikijsService } from './wikijs.service';

@Controller('wikijs')
@ApiBearerAuth('bearer-auth')
@ApiTags('wikijs')
export class WikijsController {
  private logger = new Logger(WikijsController.name);
  constructor(private wikijsService: WikijsService) {}

  @Get('syncStatus')
  @Scopes('system:read-wikijs-data')
  async getSyncStatus(): Promise<any> {
    return await this.wikijsService.getSyncStatus();
  }

  @Post('sync')
  @Scopes('system:sync-wikijs-data')
  sync() {
    this.wikijsService.sync().catch((err: Error) => {
      this.logger.error(`wikijsService sync ${err.message}`, err.stack);
    });
    return { acknowledged: true };
  }

  @Post('unlink')
  @Scopes('system:sync-wikijs-data')
  unlink() {
    this.wikijsService.unlink().catch((err: Error) => {
      this.logger.error(`wikijsService unlink  ${err.message}`, err.stack);
    });
    return { acknowledged: true };
  }

  @Get('group/:groupId/status')
  @Scopes('group-{groupId}:read-gen-kb')
  async getStatus(@Param('groupId', ParseIntPipe) groupId: number) {
    const isEnabled = await this.wikijsService.getEnabledStatus(groupId);
    return { enabled: isEnabled };
  }

  @Post('group/:groupId/enable')
  @Scopes('group-{groupId}:write-gen-kb')
  async changeEnableStatus(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: EnableWikijsDTO,
  ) {
    if (data.enabled) {
      await this.wikijsService.enableWikiJS(groupId);
      return { enabled: true };
    } else {
      await this.wikijsService.disableWikiJS(groupId);
      return { enabled: false };
    }
  }

  @Get('group/:groupId/peer-kbs')
  @Scopes('group-{groupId}:write-gen-kb')
  async listPeerKb(@Param('groupId', ParseIntPipe) groupId: number) {
    return await this.wikijsService.listWikiPeer(groupId);
  }

  @Post('group/:groupId/peer-kb')
  @Scopes('group-{groupId}:write-gen-kb')
  async appendPeerKb(@Param('groupId', ParseIntPipe) groupId: number, @Body() data: WikiPeerDTO) {
    return await this.wikijsService.appendWikiPeer(groupId, data);
  }

  @Delete('group/:groupId/peer-kb/:kbId')
  @Scopes('group-{groupId}:write-gen-kb')
  async removePeerKb(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('kbId', ParseIntPipe) kbId: number,
  ) {
    return await this.wikijsService.removeWikiPeer(groupId, kbId);
  }

  @Post('group/:groupId/user')
  @Scopes('group-{groupId}:write-gen-kb')
  async createUser(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: CreateWikijsMembershipDTO,
  ) {
    return await this.wikijsService.createMembership(data, groupId);
  }

  @Delete('group/:groupId/user')
  @Scopes('group-{groupId}:write-gen-kb')
  async deleteUser(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: DeleteWikiMembershipDTO,
  ) {
    return await this.wikijsService.deleteMembership(data, groupId);
  }

  @Get('group/:groupId/users')
  @Scopes('group-{groupId}:read-gen-kb')
  async getUsers(@Param('groupId', ParseIntPipe) groupId: number) {
    const list = await this.wikijsService.getUsersInGroupById(groupId);
    return { list, count: list.length };
  }
}
