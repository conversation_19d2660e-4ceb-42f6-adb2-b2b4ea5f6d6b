import { Injectable, Logger } from '@nestjs/common';
import {
  MessageTemplate,
  Prisma,
  TemplateAccessLevel,
  UserBookmarkEntityType,
} from '@prisma/client';
import { isAfter, subWeeks } from 'date-fns';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { Expose } from '../../providers/prisma/prisma.interface';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { UserRequest } from '../auth/auth.interface';
import { LabelsService } from '../labels/labels.service';
import { LlmEnginesService } from '../llm-engines/llm-engines.service';
import {
  BatchUploadDto,
  CreateMessageTemplateDto,
  UpdateMessageTemplateDto,
} from './message-template.dto';
import { GroupsService } from '../groups/groups.service';

@Injectable()
export class MessageTemplateService {
  private logger = new Logger(MessageTemplateService.name);
  constructor(
    private prisma: PrismaService,
    private readonly labelsService: LabelsService,
    private readonly llmEnginesService: LlmEnginesService,
    private groupService: GroupsService,
  ) {}

  async getMessageTemplate(id: number): Promise<Expose<MessageTemplate>> {
    const messageTemplate = await this.prisma.messageTemplate.findUnique({
      include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
      where: { id },
    });
    if (!messageTemplate) throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_NOT_FOUND);
    const messageTemplateFormatted =
      this.labelsService.formatLabelsAndCategoriesData(messageTemplate);
    return messageTemplateFormatted;
  }

  async getMessageTemplates(
    params: {
      skip?: number;
      take?: number;
      cursor?: Prisma.MessageTemplateWhereUniqueInput;
      where?: Prisma.MessageTemplateWhereInput;
      orderBy?:
        | Prisma.MessageTemplateOrderByWithRelationInput[]
        | Prisma.MessageTemplateOrderByWithRelationInput;
    },
    templateAccessLevel?: TemplateAccessLevel,
    groupId?: number,
    type?: 'GROUP' | 'SYSTEM' | 'ALL',
  ): Promise<Expose<MessageTemplate>[]> {
    const { skip, take, cursor, where, orderBy } = params;
    let condition: Prisma.MessageTemplateWhereInput =
      this.labelsService.formatLabelsAndCategoriesFilter('MESSAGE_TEMPLATE', where);
    condition = this.getMessageCondition(condition, templateAccessLevel, groupId, type);

    const messageTemplates = await this.prisma.messageTemplate.findMany({
      include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
      skip,
      take,
      cursor,
      where: {
        ...condition,
        OR: [
          {
            group: {
              isDeprecated: false,
            },
          },
          {
            groupId: null,
          },
        ],
      },
      orderBy,
    });
    const groupIds = messageTemplates.map((item) => item.groupId).filter((item) => item);
    const groupNames =
      groupIds.length != 0
        ? await this.prisma.group.findMany({
            select: { name: true, id: true },
            where: { id: { in: groupIds } },
          })
        : [];
    return messageTemplates.map((messageTemplate) => ({
      ...messageTemplate,
      ...this.labelsService.formatLabelsAndCategoriesData(messageTemplate),
      groupName: messageTemplate.groupId
        ? groupNames.find((item) => item.id === messageTemplate.groupId)?.name ?? '(delete bot)'
        : undefined,
    }));
  }

  public getMessageCondition(
    condition: Prisma.MessageTemplateWhereInput,
    templateAccessLevel?: TemplateAccessLevel,
    groupId?: number,
    type?: 'GROUP' | 'SYSTEM' | 'ALL',
  ) {
    if (type && type == 'SYSTEM') {
      condition.groupId = null;
    }
    if (type && type == 'GROUP') {
      condition.NOT = {
        groupId: null,
      };
      condition.group = {
        isDeprecated: false,
      };
    }
    if (templateAccessLevel) {
      condition.accessLevel = templateAccessLevel;
    }
    if (groupId) {
      condition.groupId = groupId;
    }
    return condition;
  }

  async getMessageTemplatesCount(
    where?: Prisma.MessageTemplateWhereInput,
    templateAccessLevel?: TemplateAccessLevel,
    groupId?: number,
    type?: 'GROUP' | 'SYSTEM' | 'ALL',
  ): Promise<number> {
    let condition: Prisma.MessageTemplateWhereInput =
      this.labelsService.formatLabelsAndCategoriesFilter('MESSAGE_TEMPLATE', where);
    condition = this.getMessageCondition(condition, templateAccessLevel, groupId, type);

    const count = await this.prisma.messageTemplate.count({
      where: {
        ...condition,
        OR: [
          {
            group: {
              isDeprecated: false,
            },
          },
          {
            groupId: null,
          },
        ],
      },
    });
    return count;
  }

  async getGroupAndSystemMessageTemplates(
    groupId: number,
    where?: Prisma.MessageTemplateWhereInput & {
      labels?: { in: string[] };
      categories?: { in: string[] };
      isMostlyUsed?: boolean;
      isNew?: boolean;
    },
    orderBy?: Prisma.MessageTemplateOrderByWithRelationInput,
  ): Promise<Expose<MessageTemplate>[]> {
    const _condition = this.labelsService.formatLabelsAndCategoriesFilter(
      'MESSAGE_TEMPLATE',
      where,
    );
    const { where: condition, mostlyUsedMessageTemplateIds } =
      await this.getCalculateQueryCondition(_condition);
    let messageTemplates = [];
    if (orderBy) {
      messageTemplates = await this.prisma.messageTemplate.findMany({
        include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
        where: {
          OR: [
            {
              accessLevel: TemplateAccessLevel.SYSTEM,
              OR: [
                {
                  group: {
                    isDeprecated: false,
                  },
                },
                {
                  groupId: null,
                },
              ],
            },
            {
              groupId: groupId,
            },
            ...(condition?.OR ?? []),
          ],
          AND: { ...{ ...condition, OR: undefined }, active: true },
        },
        orderBy,
      });
    } else {
      const groupMessageTemplates = await this.prisma.messageTemplate.findMany({
        include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
        where: {
          groupId: groupId,
          ...condition,
          active: true,
        },
        orderBy: {
          groupSequence: 'asc',
        },
      });
      const systemMessageTemplates = await this.prisma.messageTemplate.findMany({
        include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
        where: {
          accessLevel: TemplateAccessLevel.SYSTEM,
          active: true,
          ...condition,
          OR: [
            {
              groupId: null,
            },
            {
              group: { isDeprecated: false },
            },
          ],
        },
        orderBy: {
          systemSequence: 'asc',
        },
      });
      const _messageTemplates = systemMessageTemplates.filter((item) => item.groupId != groupId);
      messageTemplates = [...groupMessageTemplates, ..._messageTemplates];
    }

    return messageTemplates.map((messageTemplate) => ({
      ...messageTemplate,
      isMostlyUsed: mostlyUsedMessageTemplateIds.includes(messageTemplate.id),
      isNew: isAfter(messageTemplate.createdAt, subWeeks(new Date(), 2)),
      ...this.labelsService.formatLabelsAndCategoriesData(messageTemplate),
    }));
  }

  async updateMessageTemplate(
    id: number,
    updateMessageTemplateDto: UpdateMessageTemplateDto,
    userReq: UserRequest,
  ): Promise<Expose<MessageTemplate>> {
    const dbMessageTemplate = await this.prisma.messageTemplate.findUnique({
      where: { id },
    });
    if (!dbMessageTemplate) throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_NOT_FOUND);
    const updateMessageTemplateInput = { ...updateMessageTemplateDto, entityLabels: undefined };

    const { needRemoveIds, createEntityLabels, createLabels } =
      this.labelsService.formatPatchLabelsDto(
        updateMessageTemplateDto as any,
        userReq,
        'MESSAGE_TEMPLATE',
        id,
      );
    const messageTemplate = await this.prisma.$transaction(async (tx) => {
      await this.labelsService.patchUpdateWithTransaction(
        { needRemoveIds, createEntityLabels, createLabels },
        tx,
        'MESSAGE_TEMPLATE',
        id,
      );
      const messageTemplate = await tx.messageTemplate.update({
        include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
        where: { id },
        data: updateMessageTemplateInput as Prisma.MessageTemplateUpdateInput,
      });
      const updatedResult = this.labelsService.formatLabelsAndCategoriesData(messageTemplate);
      return updatedResult;
    });
    return messageTemplate;
  }

  async shareMessageTemplate(id: number): Promise<Expose<MessageTemplate>> {
    const dbMessageTemplate = await this.prisma.messageTemplate.findUnique({
      where: { id },
    });
    if (!dbMessageTemplate) throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_NOT_FOUND);
    const messageTemplate = await this.prisma.messageTemplate.update({
      include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
      where: { id },
      data: {
        accessLevel: TemplateAccessLevel.SYSTEM,
      },
    });
    return this.prisma.expose<MessageTemplate>(messageTemplate);
  }

  async unshareMessageTemplate(id: number): Promise<Expose<MessageTemplate>> {
    const dbMessageTemplate = await this.prisma.messageTemplate.findUnique({
      where: { id },
    });
    if (!dbMessageTemplate) throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_NOT_FOUND);
    const messageTemplate = await this.prisma.messageTemplate.update({
      include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
      where: { id },
      data: {
        accessLevel: TemplateAccessLevel.GROUP,
      },
    });
    const updatedResult = this.labelsService.formatLabelsAndCategoriesData(messageTemplate);
    return updatedResult;
  }

  public async getPublicMessageTemplate(
    where?: Prisma.MessageTemplateWhereInput & {
      labels?: { in: string[] };
      categories?: { in: string[] };
      isMostlyUsed?: boolean;
      isNew?: boolean;
    },
    skip?: number,
    take?: number,
    orderBy?:
      | Prisma.MessageTemplateOrderByWithRelationInput[]
      | Prisma.MessageTemplateOrderByWithRelationInput,
  ) {
    const _condition = this.labelsService.formatLabelsAndCategoriesFilter(
      'MESSAGE_TEMPLATE',
      where,
    );
    const { where: condition, mostlyUsedMessageTemplateIds } =
      await this.getCalculateQueryCondition(_condition);
    const publicSystemMessageTemplatesWhere = {
      ...condition,
      active: true,
      accessLevel: TemplateAccessLevel.SYSTEM,
      OR: [
        {
          group: {
            isDeprecated: false,
          },
        },
        {
          group: null,
        },
      ],
    };
    const systemMessageTemplates = await this.prisma.messageTemplate.findMany({
      include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
      where: publicSystemMessageTemplatesWhere,
      skip,
      take,
      ...(orderBy
        ? { orderBy }
        : {
            orderBy: {
              systemSequence: 'asc',
            },
          }),
    });
    const count = await this.prisma.messageTemplate.count({
      where: publicSystemMessageTemplatesWhere,
    });
    const messageTemplateIds = systemMessageTemplates.map((item) => item.id);
    const messageTemplateBookmarkCount = await this.prisma.userBookmark.groupBy({
      by: ['entityType', 'entityId'],
      where: {
        entityType: UserBookmarkEntityType.MESSAGE_TEMPLATE,
        entityId: { in: messageTemplateIds },
      },
      _count: { id: true },
    });
    const publicMessageTemplates = systemMessageTemplates.map((item) => ({
      ...this.labelsService.formatLabelsAndCategoriesData(item),
      isMostlyUsed: mostlyUsedMessageTemplateIds.includes(item.id),
      isNew: isAfter(item.createdAt, subWeeks(new Date(), 2)),
      bookmarkedNo:
        messageTemplateBookmarkCount.find((_item) => _item.entityId == item.id)?._count?.id ?? 0,
    }));
    return {
      list: publicMessageTemplates,
      count,
    };
  }

  async deleteMessageTemplate(id: number) {
    const dbMessageTemplate = await this.prisma.messageTemplate.findUnique({
      where: { id },
    });
    if (!dbMessageTemplate) throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_NOT_FOUND);
    await this.prisma.$transaction(async (tx) => {
      await this.labelsService.removeEntityLabelsWithEntity(tx, id, 'MESSAGE_TEMPLATE');
      await tx.messageTemplate.delete({ where: { id } });
    });
    return dbMessageTemplate;
  }

  async createMessageTemplate(
    params: CreateMessageTemplateDto,
    userReq: UserRequest,
  ): Promise<MessageTemplate> {
    const createMessageTemplateInput = { ...params, entityLabels: undefined };
    const messageTemplate = await this.prisma.$transaction(async (tx) => {
      const createdMessageTemplate = await tx.messageTemplate.create({
        data: {
          ...createMessageTemplateInput,
          createdAt: new Date(),
        },
      });
      const { needRemoveIds, createEntityLabels, createLabels } =
        this.labelsService.formatPatchLabelsDto(
          params as any,
          userReq,
          'MESSAGE_TEMPLATE',
          createdMessageTemplate.id,
        );
      await this.labelsService.patchUpdateWithTransaction(
        { needRemoveIds, createEntityLabels, createLabels },
        tx,
        'MESSAGE_TEMPLATE',
        createdMessageTemplate.id,
      );
      const messageTemplateInfo = await tx.messageTemplate.findUnique({
        include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
        where: { id: createdMessageTemplate.id },
      });
      return this.labelsService.formatLabelsAndCategoriesData(messageTemplateInfo);
    });
    return messageTemplate;
  }

  /**
   * @description  convert the calculated from where new will convert [isNew]
   * @param where
   * @returns
   */
  private async getCalculateQueryCondition(where: any) {
    if (where.isNew) {
      where.createdAt = {
        gte: subWeeks(new Date(), 2),
      };
      delete where.isNew;
    }
    const mostlyUsedMessageTemplate = await this.prisma.messageTemplate.findMany({
      select: {
        id: true,
      },
      where: {
        active: true,
        accessLevel: 'SYSTEM',
      },
      orderBy: {
        used: 'desc',
      },
      take: 5,
    });
    const mostlyUsedMessageTemplateIds = mostlyUsedMessageTemplate.map((item) => item.id);
    if (where?.isMostlyUsed) {
      where.id = {
        in: mostlyUsedMessageTemplateIds,
      };
      delete where.isMostlyUsed;
    }
    let _where = this.llmEnginesService.orConditionCheck(where);
    if (_where?.OR) {
      const newOrWhere = [];
      for (const whereItem of _where?.OR as Array<any>) {
        if (whereItem?.['isNew']) {
          newOrWhere.push({
            createdAt: {
              gte: subWeeks(new Date(), 2),
            },
          });
          delete whereItem.isNew;
        } else if (whereItem?.['isMostlyUsed']) {
          newOrWhere.push({
            id: {
              in: mostlyUsedMessageTemplateIds,
            },
          });
          delete whereItem.isMostlyUsed;
        } else {
          newOrWhere.push(whereItem);
        }
      }
      _where = {
        ..._where,
        OR: newOrWhere,
      };
    }
    return { where: _where, mostlyUsedMessageTemplateIds };
  }

  async createBatch(data: BatchUploadDto, userReq: UserRequest) {
    const { messageTemplateList } = data;
    const successList = [];
    const failureList = [];
    for (const dto of messageTemplateList) {
      try {
        // disposal data
        const messageTemplate = {
          title: dto?.templateName ?? undefined,
          botInstruction: dto?.templateInstruction ?? undefined,
          isRecommended: dto?.isRecommended ?? undefined,
          active: dto.isActive ?? undefined,
          entityLabels: [],
          groupId: undefined,
          messageContent: undefined,
          accessLevel: undefined,
          templateGuideDetails: undefined,
          systemSequence: undefined,
          groupSequence: undefined,
        };
        // get all labels, labelType = LABELS/CATEGORIES
        if (dto.action && dto.action.toUpperCase() !== 'REMOVE') {
          // category
          if (dto.categories) {
            const categoryObj = await this.labelsService.formatLabelsAndCategoriesByCondition(
              dto.categories,
              'CATEGORIES',
              dto.categoriesActionType,
              dto.id,
            );
            messageTemplate.entityLabels.push(categoryObj);
          }
          // label
          if (dto.labelsName) {
            const labelObj = await this.labelsService.formatLabelsAndCategoriesByCondition(
              dto.labelsName,
              'LABELS',
              dto.labelActionType,
              dto.id,
              dto.labelColor,
            );
            messageTemplate.entityLabels.push(labelObj);
          }
        }

        if (dto.action && dto.action.toUpperCase() === 'CREATE') {
          // default value
          messageTemplate.active = dto.isActive ?? false;
          messageTemplate.isRecommended = dto.isRecommended ?? false;
          // if name == null, then throw
          if (!dto.templateName) {
            throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_NAME_REQUIRED);
          }
          // action = create, create message template
          // group message template
          if (dto.botId) {
            messageTemplate.groupId = parseInt(dto.botId);
            // check groupId is exist
            await this.groupService.getGroup(messageTemplate.groupId, {});
            messageTemplate.accessLevel = TemplateAccessLevel.GROUP;
            messageTemplate.groupSequence = dto.ordering ?? undefined;
            const mt = await this.createMessageTemplate(messageTemplate, userReq);
            if (dto.public) {
              // share message template
              await this.shareMessageTemplate(mt.id);
            }
          } else {
            // system message template
            messageTemplate.accessLevel = TemplateAccessLevel.SYSTEM;
            messageTemplate.systemSequence = dto.ordering ?? undefined;
            await this.createMessageTemplate(messageTemplate, userReq);
          }
        } else if (dto.action && dto.action.toUpperCase() === 'UPDATE') {
          // if id == null, then throw
          if (!dto.id) {
            throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_ID_REQUIRED);
          }
          // action = update, update message template
          // group message template
          if (dto.botId) {
            messageTemplate.groupId = parseInt(dto.botId);
            // check groupId is exist
            await this.groupService.getGroup(messageTemplate.groupId, {});
            messageTemplate.accessLevel = TemplateAccessLevel.GROUP;
            messageTemplate.groupSequence = dto.ordering ?? undefined;
            const mt = await this.updateMessageTemplate(dto.id, messageTemplate, userReq);
            if (dto.public) {
              // share message template
              await this.shareMessageTemplate(mt.id);
            } else {
              // unshare message template
              await this.unshareMessageTemplate(mt.id);
            }
          } else {
            // system message template
            messageTemplate.accessLevel = TemplateAccessLevel.SYSTEM;
            messageTemplate.systemSequence = dto.ordering ?? undefined;
            await this.updateMessageTemplate(dto.id, messageTemplate, userReq);
          }
        } else if (dto.action && dto.action.toUpperCase() === 'REMOVE') {
          // if id = null, then throw
          if (!dto.id) {
            throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_ID_REQUIRED);
          }
          // delete message template
          await this.deleteMessageTemplate(dto.id);
        } else {
          throw new ApiException(ErrorCode.INVALID_UPLOAD_ACTION);
        }
        successList.push(dto);
      } catch (err) {
        this.logger.error(err, `Failed to`);
        failureList.push({
          ...dto,
          errMsg:
            (err as any)?.response?.error?.message ??
            'Internal Server Error. Please try to fix input and re-upload again.',
        });
      }
    }
    return {
      successList,
      failureList,
    };
  }

  getBatchUploadFileTemplate() {
    return [
      {
        id: null,
        ordering: 1,
        templateName: 'message group template',
        templateInstruction: 'message group template instruction',
        botId: 77777777,
        public: true,
        isActive: true,
        isRecommended: true,
        categories: 'message group template category',
        categoriesActionType: 'CREATE',
        labelsName: 'message group template label',
        labelColor: 'orange',
        labelActionType: 'CREATE',
        action: 'CREATE',
      },
      {
        id: 2,
        ordering: 2,
        templateName: 'message group template_1',
        templateInstruction: 'message group template instruction_1',
        botId: 77777777,
        public: true,
        isActive: true,
        isRecommended: true,
        categories: 'message group template category_1',
        categoriesActionType: 'CREATE',
        labelsName: 'message group template label_1',
        labelColor: 'orange',
        labelActionType: 'CREATE',
        action: 'UPDATE',
      },
      {
        id: 2,
        ordering: 2,
        templateName: 'message group template_1',
        templateInstruction: 'message group template instruction_1',
        botId: 77777777,
        public: true,
        isActive: true,
        isRecommended: true,
        categories: 'message group template category_2',
        categoriesActionType: 'CREATE',
        labelsName: 'message group template label_1',
        labelColor: 'orange',
        labelActionType: 'REMOVE',
        action: 'UPDATE',
      },
      {
        id: 4,
        action: 'REMOVE',
      },
      {
        id: null,
        ordering: 1,
        templateName: 'message system template',
        templateInstruction: 'message system template instruction',
        botId: null,
        public: null,
        isActive: true,
        isRecommended: true,
        categories: 'message system template category',
        categoriesActionType: 'CREATE',
        labelsName: 'message system template label',
        labelColor: 'orange',
        labelActionType: 'CREATE',
        action: 'CREATE',
      },
      {
        id: 2,
        ordering: 2,
        templateName: 'message system template_1',
        templateInstruction: 'message system template instruction_1',
        botId: null,
        public: null,
        isActive: true,
        isRecommended: true,
        categories: 'message system template category_1',
        categoriesActionType: 'CREATE',
        labelsName: 'message system template label_1',
        labelColor: 'orange',
        labelActionType: 'CREATE',
        action: 'UPDATE',
      },
      {
        id: 2,
        ordering: 2,
        templateName: 'message system template_1',
        templateInstruction: 'message system template instruction_1',
        botId: null,
        public: null,
        isActive: true,
        isRecommended: true,
        categories: 'message system template category_2',
        categoriesActionType: 'CREATE',
        labelsName: 'message system template label_1',
        labelColor: 'orange',
        labelActionType: 'REMOVE',
        action: 'UPDATE',
      },
      {
        id: 4,
        action: 'REMOVE',
      },
    ];
  }
}
