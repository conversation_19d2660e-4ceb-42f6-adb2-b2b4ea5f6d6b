
update "FeatureFlag" as f
SET "metaData" = case
	WHEN f."metaData"->>'value' IS NOT NULL THEN JSONB_SET(f."metaData"::jsonb, '{value}', (f."metaData"->>'value')::jsonb || '["ADMIN.CONFIG_BOT_FEATURE_FLAGS_FOR_GROUP_ADMIN"]'::jsonb, true)
    WHEN f."metaData"->>'value' IS NULL THEN jsonb_build_object('value', jsonb_build_array('ADMIN.CONFIG_BOT_FEATURE_FLAGS_FOR_GROUP_ADMIN'))
end
where key = 'ADMIN.CONFIG_BOT_FEATURE_FLAGS_FOR_GROUP_ADMIN';



insert into "FeatureFlag"("key","value","metaData","description","isEnabled","isForClientSide","updatedAt")
SELECT 'CONNECT_API.ALLOWED_HOST_URL_MAP', '', '{}',
'connect API Host Map', true, true, now()
WHERE NOT EXISTS (SELECT id FROM "FeatureFlag" WHERE "key" = 'CONNECT_API.ALLOWED_HOST_URL_MAP');
