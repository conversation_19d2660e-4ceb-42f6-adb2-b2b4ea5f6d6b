do
$$
declare needPatchData RECORD;

declare needPatchSummaryData RECORD;

declare needPatchSummaryAllData RECORD;

declare groupId INTEGER;

declare updatedJson jsonb;

declare dateStr text;

begin  
    for needPatchData in
		select
			x.*,
			cs."groupId", 
			 to_char(x."createdAt" ,
			'YYYY-MM-DD"T"HH24:00:00') as "dateStr",
			x."responseUsage"
from
			public."ChatHistory" x
left join "ChatSession" cs on
			cs.id = x."chatSessionId"
where
			x."responseUsage" is not null
	and ((x."responseUsage"::json->>'promptTokens')::int + (x."responseUsage"::json->>'completionTokens')::int) <> (x."responseUsage"::json->>'totalCompletionTokens')::int
		    loop
		      groupId := needPatchData."groupId";

dateStr := needPatchData."dateStr";

raise notice 'data patch date : %,  groupId: %',
				dateStr,
				groupId;

for needPatchSummaryData in
				select
			s."engineSlug",
			s."callingBy" ,
			s."startDate",
			(
	select
				s1.value
	from
				"Summary" as s1
	where
				"groupId" = groupId
		and 
				s1."startDate" = s."startDate"
		and s1.key = 'PROMPT_TOKENS_TOTAL'
		and s1."engineSlug" = s."engineSlug"
		and s1."callingType" = 'USER_PLAYGROUND'
		and s1."callingBy" = s."callingBy") as "promptTokens",
			(
	select
				s1.value
	from
				"Summary" as s1
	where
				"groupId" = groupId
		and 
				s1."startDate" = s."startDate"
		and s1.key = 'COMPLETION_TOKENS_TOTAL'
		and s1."engineSlug" = s."engineSlug"
		and s1."callingType" = 'USER_PLAYGROUND'
		and s1."callingBy" = s."callingBy") as "completionTokens",
						(
	select
				s1.value
	from
				"Summary" as s1
	where
					"groupId" = groupId
		and 
					s1."startDate" = s."startDate"
		and s1.key = 'TOTAL_COMPLETION_TOKENS_TOTAL'
		and s1."engineSlug" = s."engineSlug"
		and s1."callingType" = 'USER_PLAYGROUND'
		and s1."callingBy" = s."callingBy") as "totalCompletionTokens",
								(
	select
				s1.id
	from
				"Summary" as s1
	where
					"groupId" = groupId
		and 
					s1."startDate" = s."startDate"
		and s1.key = 'TOTAL_COMPLETION_TOKENS_TOTAL'
		and s1."engineSlug" = s."engineSlug"
		and s1."callingType" = 'USER_PLAYGROUND'
		and s1."callingBy" = s."callingBy") as "totalCompletionTokensID"
from
				"Summary" as s
where
				"groupId" = groupId
	and to_char(s."startDate",
					'YYYY-MM-DD"T"HH24:00:00') = dateStr
	and "callingType" = 'USER_PLAYGROUND'
group by
				s."callingBy",
				s."engineSlug",
				s."startDate"
			loop 
				if (needPatchSummaryData."promptTokens" + needPatchSummaryData."completionTokens") <> needPatchSummaryData."totalCompletionTokens" then 
				    update
	"Summary"
set
	value = (needPatchSummaryData."promptTokens" + needPatchSummaryData."completionTokens")
where
	id = needPatchSummaryData."totalCompletionTokensID";

select
	id,
	value
into
	needPatchSummaryAllData
from
	"SummaryAll" sa
where
	sa."callingBy" = needPatchSummaryData."callingBy"
	and key = 'TOTAL_COMPLETION_TOKENS_TOTAL'
	and "callingType" = 'USER_PLAYGROUND'
	and "groupId" = groupId
	and "engineSlug" = needPatchSummaryData."engineSlug";

update
	"SummaryAll"
set
	value = needPatchSummaryAllData.value + (needPatchSummaryData."totalCompletionTokens" - ((needPatchSummaryData."promptTokens" + needPatchSummaryData."completionTokens") * -1))
where
	id = needPatchSummaryAllData.id;

raise notice 'need to patch id %, promptTokens % ,completionTokens % ,totalCompletionTokens % ',
needPatchSummaryData."totalCompletionTokensID",
needPatchSummaryData."promptTokens",
needPatchSummaryData."completionTokens",
needPatchSummaryData."totalCompletionTokens";
end if;
end loop;

updatedJson := jsonb_set(needPatchData."responseUsage"::jsonb,
'{totalCompletionTokens}',
to_jsonb((needPatchData."responseUsage"->>'completionTokens')::int + (needPatchData."responseUsage"->>'promptTokens')::int) );

raise notice 'ss %',
updatedJson;

update
	"ChatHistory"
set
	"responseUsage" = updatedJson
where
	id = needPatchData.id;
end loop;
end;

$$