import { Type } from 'class-transformer';
import { IsObject, IsOptional, ValidateNested } from 'class-validator';
import { BotSettingDto } from './bot-setting.dto';
import { StableDiffusionParamsDto } from './stable-diffusion-params.dto';

export class ChatSettingDto {
  @ValidateNested()
  @Type(() => BotSettingDto)
  @IsObject()
  botSetting: BotSettingDto;

  @IsObject()
  @IsOptional()
  llmParams?: Record<string, any>;

  @IsOptional()
  @IsObject()
  dataSource?: Record<string, any>;

  @ValidateNested()
  @Type(() => StableDiffusionParamsDto)
  @IsObject()
  @IsOptional()
  stableDiffusionParams?: StableDiffusionParamsDto;
}
