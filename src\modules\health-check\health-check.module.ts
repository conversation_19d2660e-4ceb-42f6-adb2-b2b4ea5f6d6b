import { Module } from '@nestjs/common';
import { GraviteeModule } from 'src/providers/gravitee/gravitee.module';
import { LLMBackendModule } from 'src/providers/llm-backend/llm-backend.module';
import { SsoModule } from 'src/providers/sso/sso.module';
import { HealthCheckController } from './health-check.controller';
import { HealthCheckService } from './health-check.service';

@Module({
  imports: [GraviteeModule, LLMBackendModule, SsoModule],
  controllers: [HealthCheckController],
  providers: [HealthCheckService],
})
export class HealthCheckModule {}
