import { SystemName } from '@prisma/client';

// the allowed editing/creating membership for each role, ref: https://theclub.atlassian.net/browse/AI-3330
export const userRoleAndAllowedMembershipMapping = new Map<SystemName, SystemName[]>([
  [
    SystemName.GROUP_OWNER,
    [
      SystemName.GROUP_OWNER,
      SystemName.GROUP_ADMIN,
      SystemName.GROUP_CONTRIBUTOR,
      SystemName.GROUP_MEMBER,
      SystemName.GROUP_VISITOR,
      SystemName.GROUP_CUSTOM,
    ],
  ],
  [SystemName.GROUP_ADMIN, [SystemName.GROUP_MEMBER, SystemName.GROUP_VISITOR]],
  [SystemName.GROUP_CONTRIBUTOR, [SystemName.GROUP_VISITOR]],
]);
