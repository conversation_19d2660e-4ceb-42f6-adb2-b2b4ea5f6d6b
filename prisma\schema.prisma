//fieldReference  https://github.com/prisma/prisma/releases/tag/4.3.0 to use
generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["tracing"]
}

generator erd {
  provider = "prisma-erd-generator"
  output   = "./ERD.md"
}

datasource db {
  provider     = "postgresql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

model User {
  checkLocationOnLogin        <PERSON>                   @default(false)
  countryCode                 String                    @default("hk")
  createdAt                   DateTime                  @default(now())
  gender                      Gender                    @default(UNKNOWN)
  id                          Int                       @id @default(autoincrement())
  name                        String
  notificationEmail           NotificationEmail         @default(ACCOUNT)
  password                    String?
  ccc                         String?
  department                  String?
  businessUnit                String?
  prefersLanguage             String                    @default("en-us")
  prefersColorScheme          PrefersColorScheme        @default(NO_PREFERENCE)
  prefersReducedMotion        PrefersReducedMotion      @default(NO_PREFERENCE)
  prefersEmailId              Int?
  roleId                      Int
  timezone                    String                    @default("America/Los_Angeles")
  twoFactorMethod             MfaMethod                 @default(NONE)
  twoFactorPhone              String?
  twoFactorSecret             String?
  attributes                  Json?
  updatedAt                   DateTime                  @updatedAt
  active                      Boolean                   @default(true)
  isStaffIdInitBySso          Boolean                   @default(false)
  apiKeys                     ApiKey[]                  @relation("userApiKey")
  approvedSubnets             ApprovedSubnet[]          @relation("userApprovedSubnet")
  auditLogs                   AuditLog[]                @relation("userAuditLog")
  backupCodes                 BackupCode[]              @relation("userBackupCode")
  emails                      Email[]                   @relation("userEmail")
  identities                  Identity[]                @relation("userIdentity")
  memberships                 Membership[]              @relation("userMembership")
  uploadedFiles               ModelFile[]               @relation("userUploadedFiles")
  sessions                    Session[]                 @relation("userSession")
  fileHistories               FileHistory[]             @relation("userFileHistories")
  prefersEmail                Email?                    @relation("userPrefersEmail", fields: [prefersEmailId], references: [id], onDelete: Restrict, onUpdate: Restrict)
  userRole                    Role                      @relation("userRole", fields: [roleId], references: [id], onDelete: Restrict, onUpdate: Restrict)
  accessibleModels            UserAccessOnLLMModels[]
  loginType                   LoginType?
  llmModelModified            LLMModel[]                @relation("llmModelLastUpdatedBy")
  remarks                     String?
  staffId                     String?
  wikijsId                    Int?                      @unique
  requestedDataPromotions     DataPromotionRequest[]    @relation("dataPromotionRequester")
  operatedDataPromotions      DataPromotionRequest[]    @relation("dataPromotionOperator")
  createdEntitySnapshots      EntitySnapshot[]          @relation("snapshotCreator")
  chatSessions                ChatSession[]
  flowBotRequesterRequests    FlowBotRequest[]          @relation("userFlowBotRequestRequester")
  flowBotOperatorRequests     FlowBotRequest[]          @relation("userFlowBotRequestOperator")
  featureFlagModified         FeatureFlag[]             @relation("FeatureFlagUpdatedBy")
  FeatureFlagOverrideModified FeatureFlagOverride[]     @relation("FeatureFlagOverrideUpdatedBy")
  FeatureFlagOverrideCreated  FeatureFlagOverride[]     @relation("FeatureFlagOverrideCreatedBy")
  createdGroups               Group[]                   @relation("botCreator")
  ApiResource                 ApiResource[]
  lastLoginDate               DateTime?
  lastActiveDate              DateTime?
  chatFiles                   ChatFile[]
  ssoId                       String?                   @unique
  loginProviderUniqueKey      String?
  requestedPlanRequests       PlanSubscriptionRequest[] @relation("resourcePlanSubscriptionRequester")
  operatedPlanRequests        PlanSubscriptionRequest[] @relation("resourcePlanSubscriptionOperator")
  UserGroupModified           UserGroup[]               @relation("UserGroupUpdatedBy")
  UserGroupCreated            UserGroup[]               @relation("UserGroupCreatedBy")
  CreateLabels                Labels[]                  @relation(name: "updateLabelsUser")
  UpdateLabels                Labels[]                  @relation(name: "createLabelsUser")
  modelPriceEvents            ModelPriceEvent[]
  Bookmark                    UserBookmark[]
  profilePictureS3Path        String?
  ShareChat                   ShareChat[]

  @@unique([loginProviderUniqueKey, loginType])
  @@index([prefersEmailId], map: "prefersEmailId")
}

model Group {
  autoJoinDomain           Boolean                   @default(false)
  createdAt                DateTime                  @default(now())
  forceTwoFactor           Boolean                   @default(false)
  id                       Int                       @id
  ipRestrictions           String?
  name                     String
  env                      Environment               @default(TEST)
  onlyAllowDomain          Boolean                   @default(false)
  profilePictureUrl        String                    @default("https://unavatar.now.sh/fallback.png")
  attributes               Json?
  ccc                      String?
  department               String?
  businessUnit             String?
  description              String?
  updatedAt                DateTime                  @updatedAt
  parentId                 Int?
  isDeprecated             Boolean                   @default(false)
  apikeys                  ApiKey[]                  @relation("groupApiKey")
  auditLogs                AuditLog[]                @relation("groupAuditLog")
  domains                  Domain[]                  @relation("groupDomain")
  parent                   Group?                    @relation("groupSubgroups", fields: [parentId], references: [id], onDelete: Restrict, onUpdate: Restrict)
  subgroups                Group[]                   @relation("groupSubgroups")
  llmModel                 LLMModel?
  memberships              Membership[]              @relation("groupMembership")
  // TODO: use camelCase variable name, "modelFiles"
  ModelFiles               ModelFile[]               @relation("groupUploadedFiles")
  // TODO: use camelCase variable name, "roles"
  Role                     Role[]                    @relation("groupRole")
  webhooks                 Webhook[]                 @relation("groupWebhook")
  pairId                   Int?
  aiResource               AiResource[]
  messageTemplate          MessageTemplate[]
  deletedAt                DateTime?
  dataPromotionRequests    DataPromotionRequest[]
  entitySnapshots          EntitySnapshot[]          @relation("snapshotGroup")
  chatSessions             ChatSession[]
  flow                     Flow?
  flowBotFlows             FlowBot[]                 @relation("groupFlowBotFlows")
  flowBotBots              FlowBot[]                 @relation("groupFlowBotBots")
  groupType                GroupType                 @default(BOT)
  ApiResource              ApiResource?
  flowBotRequestFlows      FlowBotRequest[]          @relation("groupFlowBotRequestFlows")
  flowBotRequestBots       FlowBotRequest[]          @relation("groupFlowBotRequestBots")
  createdBy                User?                     @relation("botCreator", fields: [createdByUserId], references: [id])
  createdByUserId          Int?
  Summary                  Summary[]
  SummaryAll               SummaryAll[]
  chatFiles                ChatFile[]
  subscribedPlanRequests   PlanSubscriptionRequest[] @relation("resourcePlanSubscriptionRequestSubscribedGroup")
  subscribedPlan           PlanSubscription[]        @relation("resourcePlanSubscriptionSubscribedGroup")
  modelPriceProcess        ModelPriceProcess[]
  modelPriceSummaries      ModelPriceSummary[]
  UserBookmark             UserBookmark[]
  groupNotificationConfigs GroupNotificationConfig[]
  ShareChat                ShareChat[]

  @@index([name], map: "nameIndex")
  @@index([env], map: "envIndex")
  @@index([createdByUserId], map: "createdByIndex")
  @@index([createdAt], map: "createdAtIndex")
  @@index([parentId], map: "parentId")
  @@index([groupType], map: "groupTypeIndex")
  @@index([businessUnit], map: "businessUnitIndex")
  @@index([isDeprecated, groupType, name])
}

model Email {
  createdAt  DateTime @default(now())
  email      String   @unique(map: "Email.email_unique")
  emailSafe  String
  id         Int      @id @default(autoincrement())
  isVerified Boolean  @default(false)
  updatedAt  DateTime @updatedAt
  userId     Int
  user       User     @relation("userEmail", fields: [userId], references: [id], onDelete: Cascade)
  users      User[]   @relation("userPrefersEmail")

  @@index([userId], map: "Email.userId_index")
}

model ApiKey {
  createdAt             DateTime   @default(now())
  description           String?
  id                    Int        @id @default(autoincrement())
  ipRestrictions        Json?
  apiKey                String     @unique(map: "ApiKey.apiKey_unique")
  name                  String?
  groupId               Int?
  referrerRestrictions  Json?
  scopes                Json?
  updatedAt             DateTime   @updatedAt
  userId                Int?
  group                 Group?     @relation("groupApiKey", fields: [groupId], references: [id])
  user                  User?      @relation("userApiKey", fields: [userId], references: [id])
  auditLogs             AuditLog[] @relation("apiKeyAuditLog")
  gatewayApplicationId  String?
  gatewaySubscriptionId String?
  rateLimitPlanId       String?
  apiPlan               ApiPlan?   @relation("apiPlanOfApiKey", fields: [rateLimitPlanId], references: [planId])
  promoted              Boolean    @default(false)

  @@index([groupId], map: "ApiKey.groupId_index")
  @@index([userId], map: "ApiKey.userId_index")
}

model ApiPlan {
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt
  planId     String       @unique(map: "ApiPlan.apiPlan_unique")
  flowId     String       @default("")
  env        Environment? @default(TEST)
  id         Int          @id @default(autoincrement())
  apiKeys    ApiKey[]     @relation("apiPlanOfApiKey")
  precedence Int          @default(3)
}

model ApprovedSubnet {
  createdAt   DateTime @default(now())
  id          Int      @id @default(autoincrement())
  subnet      String
  city        String?
  region      String?
  timezone    String?
  countryCode String?
  updatedAt   DateTime @updatedAt
  userId      Int
  user        User     @relation("userApprovedSubnet", fields: [userId], references: [id])

  @@index([userId], map: "ApprovedSubnet.userId_index")
}

// TODO: to remove
model BackupCode {
  id        Int      @id @default(autoincrement())
  code      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  isUsed    Boolean  @default(false)
  userId    Int
  user      User     @relation("userBackupCode", fields: [userId], references: [id])

  @@index([userId], map: "BackupCode.userId_index")
}

// TODO: to remove
model CouponCode {
  id               Int       @id @default(autoincrement())
  code             String
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  expiresAt        DateTime?
  maxUses          Int       @default(1000)
  usedCount        Int       @default(0)
  teamRestrictions String?
  amount           Decimal   @default(0.00)
  currency         String
  description      String?
}

// TODO: to remove
model Domain {
  createdAt        DateTime @default(now())
  domain           String
  id               Int      @id @default(autoincrement())
  isVerified       Boolean  @default(false)
  groupId          Int
  updatedAt        DateTime @updatedAt
  verificationCode String
  group            Group    @relation("groupDomain", fields: [groupId], references: [id])

  @@index([groupId], map: "Domain.groupId_index")
}

// TODO: to remove
model Identity {
  createdAt DateTime     @default(now())
  id        Int          @id @default(autoincrement())
  loginName String
  type      IdentityType
  updatedAt DateTime     @updatedAt
  userId    Int
  user      User         @relation("userIdentity", fields: [userId], references: [id])

  @@index([userId], map: "Identity.userId_index")
}

model Membership {
  createdAt           DateTime              @default(now())
  id                  Int                   @id @default(autoincrement())
  groupId             Int
  updatedAt           DateTime              @updatedAt
  userId              Int
  roleId              Int
  group               Group                 @relation("groupMembership", fields: [groupId], references: [id])
  user                User                  @relation("userMembership", fields: [userId], references: [id])
  // TODO: use camelCase variable name, "role"
  Role                Role?                 @relation(fields: [roleId], references: [id])
  BotReviewNomination BotReviewNomination[]

  @@index([groupId], map: "Membership.groupId_index")
  @@index([userId], map: "Membership.userId_index")
  @@index([roleId])
}

model Session {
  createdAt       DateTime   @default(now())
  id              Int        @id @default(autoincrement())
  ipAddress       String
  token           String
  updatedAt       DateTime   @updatedAt
  userAgent       String?
  city            String?
  region          String?
  timezone        String?
  countryCode     String?
  browser         String?
  operatingSystem String?
  userId          Int
  user            User       @relation("userSession", fields: [userId], references: [id], onDelete: Cascade)
  loginType       LoginType?
}

model Webhook {
  id          Int       @id @default(autoincrement())
  groupId     Int
  group       Group     @relation("groupWebhook", fields: [groupId], references: [id])
  event       String
  url         String
  headers     String?
  isActive    Boolean   @default(false)
  lastFiredAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([groupId], map: "Webhook.groupId_index")
}

model AuditLog {
  createdAt       DateTime @default(now())
  event           String
  rawEvent        String
  eventMetadata   Json?
  id              Int      @id @default(autoincrement())
  groupId         Int?
  updatedAt       DateTime @updatedAt
  userId          Int?
  apiKeyId        Int?
  ipAddress       String?
  userAgent       String?
  city            String?
  region          String?
  timezone        String?
  countryCode     String?
  browser         String?
  operatingSystem String?
  apiKey          ApiKey?  @relation("apiKeyAuditLog", fields: [apiKeyId], references: [id])
  group           Group?   @relation("groupAuditLog", fields: [groupId], references: [id])
  user            User?    @relation("userAuditLog", fields: [userId], references: [id])
  eventDetails    String?

  @@index([apiKeyId], map: "apiKeyId")
  @@index([groupId], map: "groupId")
  @@index([userId], map: "userId")
  @@index([event], map: "event")
}

// TODO: rename as "LlmModel"
model LLMModel {
  id                    Int                     @id @default(autoincrement())
  modelId               String                  @unique(map: "LLMModel.modelId_unique") @db.VarChar(100)
  name                  String                  @db.VarChar(200)
  tone                  String?
  typeDefinition        String?
  startupMessage        String?
  groupId               Int
  makeLiveToPublic      Boolean                 @default(false) // this just for the live groups 
  publicOrder           Int? // TODO::if more info need set to public need remove this and create new table for public bot 
  active                Boolean                 @default(true)
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @updatedAt
  description           String?
  modelEngine           String?                 @default("gpt-35-turbo") @db.VarChar(200)
  group                 Group                   @relation(fields: [groupId], references: [id])
  files                 ModelFile[]             @relation("modelFiles")
  userAccesses          UserAccessOnLLMModels[]
  llmEngineId           Int                     @default(1)
  llmEngine             LlmEngine               @relation("llmModelUsedLlmEngine", fields: [llmEngineId], references: [id])
  lastModUserId         Int?
  lastModifiedBy        User?                   @relation("llmModelLastUpdatedBy", fields: [lastModUserId], references: [id])
  parameters            Json?
  lastPromotedAt        DateTime?
  showInTeams           Boolean?                @default(false)
  showRefInTeams        Boolean?                @default(false)
  showReference         Boolean?                @default(true)
  canShareChat          Boolean?                @default(false)
  publicBotGuideDetails String?
  official              Boolean?                @default(false)
  EntityLabels          EntityLabels[]          @relation(name: "LLMModelLabels")
  UserBookmark          UserBookmark[]          @relation(name: "LLMModelRef")

  @@unique([groupId])
  @@index([modelId], map: "LLMModel.modelId_index")
}

model ShareChat {
  id                   Int             @id @default(autoincrement())
  shareId              String
  name                 String
  chatHistories        Json[]
  chatSetting          Json
  groupId              Int
  createdBy            Int
  createdShareChatUser User            @relation(fields: [createdBy], references: [id])
  group                Group           @relation(fields: [groupId], references: [id])
  createdAt            DateTime?       @default(now())
  shareChatSessionType ChatSessionType
  isPersonalChatShare  Boolean         @default(false)

  @@unique([shareId])
  @@index([createdBy])
  @@index([groupId])
}

model UserAccessOnLLMModels {
  modelId     Int
  userId      Int
  accessLevel AccessLevel @default(USER)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @default(now()) @updatedAt
  model       LLMModel    @relation(fields: [modelId], references: [id])
  user        User        @relation(fields: [userId], references: [id])

  @@id([modelId, userId, accessLevel])
  @@index([modelId, userId, accessLevel], map: "UserAccessOnLLMModels.modelId_userId_accessLevel_index")
}

model ModelFile {
  id                      Int                 @id @default(autoincrement())
  filename                String
  filetype                String?
  fileExt                 String?
  fileSize                Int?
  s3Path                  String?
  summary                 String?             @db.Text
  description             String?             @db.Text
  modelId                 Int?
  groupId                 Int?
  uploaderId              Int?
  createdAt               DateTime            @default(now())
  updatedAt               DateTime            @updatedAt
  docId                   String              @unique(map: "ModelFile.docId_unique")
  status                  FileStatus?         @default(UPLOADED)
  group                   Group?              @relation("groupUploadedFiles", fields: [groupId], references: [id])
  model                   LLMModel?           @relation("modelFiles", fields: [modelId], references: [id])
  uploader                User?               @relation("userUploadedFiles", fields: [uploaderId], references: [id], onDelete: Cascade)
  errorMsg                String?             @db.Text
  isApproved              Boolean             @default(false)
  fileClassification      FileClassification
  fullScanReportPath      String?
  fullScanReportCreatedAt DateTime?
  fullScanReportUpdatedAt DateTime?
  errCode                 String?
  hasPii                  String?
  hasPromptInjection      HasPromptInjection?
  detectedPii             String?
  piiFileStatus           PiiFileStatus?
  scanMalwareStatus       ScanMalwareStatus?
  malwareRating           String?
  scanMalwareVersion      String?
  scanMalwareErrorMsg     String?
  deletedAt               DateTime?
  deletedBy               Int?
  fullScanReportVersion   Int?
  rescanPiiErrorMsg       String?
  jobId                   String?

  tags                          EntityLabels[]                 @relation(name: "DocumentTags")
  historicalFileMalwareScan     HistoricalFileMalwareScan[]    @relation(name: "file_malware_hisotry")
  indexingStartDate             DateTime?
  historicalFileSecurityReports HistoricalFileSecurityReport[] @relation(name: "HistoricalFileSecurityReportTags")
  verifyStatus                  FileVerifyStatus?
  verifyErrCode                 String?
  verifyErrorMsg                String?
  autoIndex                     Boolean?                       @default(true)

  @@index([filename], map: "files_filename")
  @@index([docId], map: "doc_id")
  @@index([groupId], map: "files_groupId")
}

model WhiteList {
  id        Int       @id @default(autoincrement())
  email     String    @unique(map: "WhiteList_email_idx") @db.VarChar(300)
  createdAt DateTime  @default(now())
  deletedAt DateTime?
}

model Permission {
  id                     Int                      @id @default(autoincrement())
  createdAt              DateTime?                @default(now()) @db.Timestamp(6)
  updatedAt              DateTime?                @updatedAt
  description            String?                  @db.VarChar(200)
  permissionKey          String                   @unique @db.VarChar(200)
  permissionType         PermissionType           @default(SYSTEM)
  envs                   Environment[]
  roles                  RolePermission[]
  permissionGroupSetting PermissionGroupSetting[] @relation("permissionGroupSetting")
  planPermissions        PlanPermission[]
  resources              Resource[]

  @@index([permissionKey])
  @@index([permissionType, envs])
}

model PermissionGroupSetting {
  permissionId        Int
  groupType           GroupType               @default(BOT)
  createdAt           DateTime                @default(now()) @db.Timestamp(6)
  updatedAt           DateTime                @updatedAt
  isCustomRoleAllowed Boolean                 @default(true)
  isApiKeyAllowed     Boolean                 @default(false)
  isActiveOnly        Boolean                 @default(true)
  featureId           Int?
  permission          Permission              @relation("permissionGroupSetting", fields: [permissionId], references: [id])
  feature             PermissionGroupFeature? @relation("permissionGroupSetting", fields: [featureId], references: [id], onDelete: Cascade)

  @@id([permissionId, groupType])
  @@index([featureId])
  @@index([permissionId, groupType, isCustomRoleAllowed, featureId])
  @@index([permissionId, groupType, isApiKeyAllowed, featureId])
}

model PermissionGroupFeature {
  id                     Int                      @id @default(autoincrement())
  groupTypes             GroupType[]              @default([])
  featureName            String
  featureKey             String                   @unique()
  createdAt              DateTime                 @default(now()) @db.Timestamp(6)
  permissionGroupSetting PermissionGroupSetting[] @relation("permissionGroupSetting")
  isBasic                Boolean                  @default(false)
}

model Role {
  id                          Int              @id @default(autoincrement())
  createdAt                   DateTime         @default(now()) @db.Timestamp(6)
  updatedAt                   DateTime         @updatedAt
  roleType                    RoleType         @default(SYSTEM_DEFAULT)
  name                        String           @db.VarChar(100)
  groupId                     Int              @default(0)
  systemName                  SystemName       @default(USER)
  memberships                 Membership[]
  group                       Group?           @relation("groupRole", fields: [groupId], references: [id])
  permissions                 RolePermission[]
  user                        User[]           @relation("userRole")
  order                       Int?
  isCustomRoleTemplateAllowed Boolean?         @default(false)

  @@unique([name, groupId, systemName])
  @@index([roleType, groupId, isCustomRoleTemplateAllowed])
}

model RolePermission {
  role         Role       @relation(fields: [roleId], references: [id])
  roleId       Int
  permission   Permission @relation(fields: [permissionId], references: [id])
  permissionId Int
  createdAt    DateTime?  @default(now()) @db.Timestamp(6)
  updatedAt    DateTime?  @db.Timestamp(6)

  @@id([roleId, permissionId])
}

model LlmEngine {
  id                 Int                 @id @default(autoincrement())
  name               String              @db.VarChar(255)
  slug               String              @db.VarChar(255)
  sequence           Int?
  reportSlug         String?             @db.VarChar(255) // for generating the name of tab of report (not more than 30 characters)
  platform           LlmServiceProvider? @default(AZURE)
  isActive           Boolean             @default(false)
  config             Json?               @default("\"{}\"")
  description        String?             @default("")
  isRecommended      Boolean             @default(false)
  bots               LLMModel[]          @relation("llmModelUsedLlmEngine")
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  aiResource         AiResource[]
  isClientDeprecated Boolean?            @default(false)
  EntityLabels       EntityLabels[]      @relation(name: "LlmEngine")
  iconPictureUrl     String?
  learningMaterial   String?             @default("")

  @@unique([slug])
}

model FileHistory {
  id          Int                @id @default(autoincrement())
  fileId      String             @unique()
  requesterId Int
  fileType    FileType
  entityType  FileEntityType?
  entityId    Int?
  requester   User?              @relation("userFileHistories", fields: [requesterId], references: [id], onDelete: Cascade)
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  s3FilePath  String?            @db.VarChar(255)
  status      FileHistoryStatus? @default(PROCESSING)
  errorMsg    String?            @db.VarChar(255)
  dateFrom    DateTime?
  dateTo      DateTime

  @@index([fileId], map: "fileHistory.search_index")
  @@index([fileType, entityType, entityId], map: "fileHistory.list_search_index")
}

model AiResource {
  id          Int              @id @default(autoincrement())
  llmEngineId Int
  llmEngine   LlmEngine        @relation(fields: [llmEngineId], references: [id])
  groupId     Int
  group       Group            @relation(fields: [groupId], references: [id], onDelete: Cascade)
  prompt      String?
  files       String[]         @db.VarChar(1024)
  status      AiResourceStatus
  createdAt   DateTime         @default(now())
  generatedAt DateTime?
}

model KYCVerification {
  id          Int                @id @default(autoincrement())
  createdAt   DateTime           @default(now())
  expiredAt   DateTime
  verifiedAt  DateTime?
  code        String
  requestorId Int?
  userId      Int
  type        VerificationType
  status      VerificationStatus @default(PENDING)

  @@unique([code], map: "KYCVerification.code")
  @@unique([userId, type], map: "KYCVerification.userId_type_unique")
  @@index([code, userId, type], map: "KYCVerification.search_index")
}

model FeatureFlag {
  id                   Int                   @id @default(autoincrement())
  description          String?
  key                  String                @unique(map: "FeatureFlag_key_constraint")
  value                String?
  metaData             Json?                 @default("{}")
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @default(now())
  updatedByUserId      Int?
  updatedBy            User?                 @relation("FeatureFlagUpdatedBy", fields: [updatedByUserId], references: [id])
  featureFlagOverrides FeatureFlagOverride[] @relation("FeatureFlagOverrideFlagId")
  isEnabled            Boolean               @default(false)
  isForClientSide      Boolean               @default(true)
  isPublic             Boolean?              @default(false)

  @@index([key], map: "FeatureFlag_key_index")
  @@index([isForClientSide])
  @@index([isPublic])
}

model FeatureFlagOverride {
  id              Int                   @id @default(autoincrement())
  value           String?
  targetType      FeatureFlagTargetType
  targetValue     String
  level           Int
  featureFlagId   Int
  featureFlag     FeatureFlag           @relation("FeatureFlagOverrideFlagId", fields: [featureFlagId], references: [id])
  createdByUserId Int?
  createdBy       User?                 @relation("FeatureFlagOverrideCreatedBy", fields: [createdByUserId], references: [id])
  createdAt       DateTime              @default(now())
  updatedAt       DateTime              @default(now())
  updatedByUserId Int?
  updatedBy       User?                 @relation("FeatureFlagOverrideUpdatedBy", fields: [updatedByUserId], references: [id])
  metaData        Json?
  isEnabled       Boolean

  @@unique([targetType, targetValue, featureFlagId])
  @@index([targetType, targetValue], map: "FeatureFlagOverride_targetType_targetValue_index")
}

model GroupWikijs {
  id       Int             @id @default(autoincrement())
  groupId  Int
  wikijsId Int
  role     WikiJSGroupRole @default(VIEWER)

  @@index([groupId, wikijsId], map: "GroupWikiJS.groupId_wikijsId_index")
}

model Internationalization {
  key      String
  content  String
  language Language

  @@id([key, language])
  @@index([language], map: "Internationalization_language")
}

model EntitySnapshot {
  id                    Int                    @id @default(autoincrement())
  /// snapshot name
  name                  String
  /// entity type enum
  entityType            SnapshotEntityType
  /// entity identifier
  entityId              String
  /// bot builder group
  group                 Group                  @relation("snapshotGroup", fields: [groupId], references: [id])
  groupId               Int
  /// the snapshot data in json format
  entityData            Json
  /// when the history data is created or updated
  versionDate           DateTime
  /// bot builder user who created this snapshot
  createdBy             User                   @relation("snapshotCreator", fields: [createdByUserId], references: [id])
  createdByUserId       Int
  /// related data promotion requests
  dataPromotionRequests DataPromotionRequest[]
}

/// Table storing data promotion request
model DataPromotionRequest {
  id               Int                            @id @default(autoincrement())
  actionType       DataPromotionRequestActionType
  /// the group to promote, depends on the env in api request
  targetGroup      Group                          @relation(fields: [targetGroupId], references: [id])
  targetGroupId    Int
  /// the id of the promoted entity
  promotedEntityId String?
  /// related snapshot record used for promotion
  entitySnapshot   EntitySnapshot                 @relation(fields: [entitySnapshotId], references: [id])
  entitySnapshotId Int
  /// promotion status
  status           DataPromotionRequestStatus
  /// the user who created the request
  requester        User                           @relation("dataPromotionRequester", fields: [requesterId], references: [id])
  requesterId      Int
  requestedDate    DateTime                       @default(now())
  requesterComment String?
  /// the user who approved or rejected the request
  operator         User?                          @relation("dataPromotionOperator", fields: [operatorId], references: [id])
  operatorId       Int?
  operatedDate     DateTime?
  operatorComment  String?
}

model MessageTemplate {
  id             Int                 @id @default(autoincrement())
  title          String              @db.VarChar(50)
  botInstruction String?             @db.Text
  messageContent String?             @db.Text
  active         Boolean             @default(true)
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  accessLevel    TemplateAccessLevel
  groupId        Int?
  systemSequence Int                 @default(autoincrement())
  groupSequence  Int                 @default(autoincrement())
  description    String?             @default("")
  isRecommended  Boolean             @default(false)
  used           Int                 @default(0)
  group          Group?              @relation(fields: [groupId], references: [id])
  templateGuideDetails String?

  EntityLabels EntityLabels[] @relation(name: "MessageTemplate")

  UserBookmark UserBookmark[] @relation(name: "messageTemplateRef")
}

model Summary {
  id                Int                @id @default(autoincrement())
  groupId           Int
  group             Group?             @relation(fields: [groupId], references: [id])
  key               SummaryKeyType
  value             Int
  engineSlug        String?
  flowId            Int                @default(0)
  callingType       SummaryCallingType
  callingBy         Int                @default(0)
  startDate         DateTime           @db.Timestamp(6)
  endDate           DateTime           @db.Timestamp(6)
  callingAttributes String?
  feature           Feature            @default(CHAT)

  @@index([groupId], map: "summary_groupId_index")
  @@index([key, startDate], map: "summary_key_date_index")
  @@index([callingBy, callingType, groupId, startDate], map: "summary_callingBy_callingType_groupId_date_index")
  @@index([callingBy, callingType, groupId, startDate, callingAttributes], map: "summary_callingBy_callingType_groupId_date_callingAtt_index")
  @@index([startDate, endDate, groupId], map: "summary_modelPrice_record_index")
  @@index([flowId, groupId, startDate], map: "summary_flowId_groupId_date_index")
}

model SummaryAll {
  id                Int                @id @default(autoincrement())
  groupId           Int
  group             Group?             @relation(fields: [groupId], references: [id])
  key               SummaryKeyType
  value             BigInt
  engineSlug        String?
  flowId            Int                @default(0)
  callingType       SummaryCallingType
  callingBy         Int                @default(0)
  callingAttributes String?
  feature           Feature            @default(CHAT)

  @@index([groupId], map: "summary_all_groupId_index")
  @@index([key], map: "summary_all_key_date_index")
  @@index([callingBy, callingType, groupId], map: "summary_all_callingBy_callingType_groupId_date_index")
  @@index([callingBy, callingType, callingAttributes, groupId], map: "summary_all_callingBy_callingType_groupId_date_callingAtt_index")
  @@index([flowId, groupId], map: "summary_all_flowId_groupId_date_index")
}

enum Feature {
  CHAT
  EMBEDDING
  TTS
  STT
  AI_TAGGING
  CHAT_WITH_FILE
  CHAT_WITH_DATA
  INSIGHT_GENERATOR
  TEXT_TO_IMAGE
  OPENAI_COMPATIBLE
  BATCH_PROCESS
}

enum SummaryCallingType {
  USER_PLAYGROUND
  API_KEY
  TEAMS
  AUTO_TEST
  GEN_KB
}

enum SummaryKeyType {
  CALL_TOTAL
  COMPLETION_TOKENS_TOTAL
  TOTAL_COMPLETION_TOKENS_TOTAL
  PROMPT_TOKENS_TOTAL
  EMBEDDING_TOKENS_TOTAL
  TOOLS_USAGE_TOTAL
  DURATION_IN_MS_AVG
  IMAGE_STANDARD_1024_1024
  IMAGE_STANDARD_1024_1792
  IMAGE_STANDARD_1792_1024
  IMAGE_HD_1024_1024
  IMAGE_HD_1024_1792
  IMAGE_HD_1792_1024
  MODEL_COST
  MODEL_PRICE
  MODEL_USAGE
}

enum Language {
  EN
  ZH_HANT
  ZH_HANS
}

enum DataPromotionRequestActionType {
  PROMOTE
  DELETE
}

/// snapshot entity type enum, add new type when needed
enum SnapshotEntityType {
  API_KEY
  LLM_MODEL
  FLOW
  API_RESOURCE
  BOT_SECURITY
  LLM_MODEL_SETTING
}

enum DataPromotionRequestStatus {
  PENDING
  CANCELED
  APPROVED
  REJECTED
}

model Flow {
  id             Int       @id @default(autoincrement())
  groupId        Int       @unique // Refers to the id of Group table (Group.id), the record's Group.groupType should be 'FLOW' type
  group          Group     @relation(fields: [groupId], references: [id])
  flowUuid       String    @db.Uuid
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  lastPromotedAt DateTime?
}

model FlowBot {
  id          Int           @id @default(autoincrement())
  flowGroupId Int // Storing Group.id value. Refers to the id of Group table (Group.id), the record's Group.groupType should be 'FLOW' type
  botGroupId  Int // Storing Group.id value. Refers to the id of Group table (Group.id), the record's Group.groupType should be 'BOT' type   
  flowGroup   Group         @relation("groupFlowBotFlows", fields: [flowGroupId], references: [id])
  botGroup    Group         @relation("groupFlowBotBots", fields: [botGroupId], references: [id])
  status      FlowBotStatus
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  @@unique([flowGroupId, botGroupId])
}

model FlowBotRequest {
  id             Int                  @id @unique @default(autoincrement())
  flowGroupId    Int
  botGroupId     Int
  flowGroup      Group                @relation("groupFlowBotRequestFlows", fields: [flowGroupId], references: [id])
  botGroup       Group                @relation("groupFlowBotRequestBots", fields: [botGroupId], references: [id])
  requestType    FlowBotRequestType
  // dataSnapshot storing the model current data when creating request. Just for reference, will operate by the requestType
  dataSnapshot   Json?
  requesterId    Int
  requesterUser  User                 @relation("userFlowBotRequestRequester", fields: [requesterId], references: [id])
  requestNotes   String?
  operatorId     Int?
  operatorUser   User?                @relation("userFlowBotRequestOperator", fields: [operatorId], references: [id])
  operationNotes String?
  status         FlowBotRequestStatus
  createdAt      DateTime             @default(now())
  updatedAt      DateTime?            @updatedAt
}

model ApiResource {
  id              Int               @id @unique @default(autoincrement())
  groupId         Int               @unique
  group           Group             @relation(fields: [groupId], references: [id])
  docPath         String?
  fileName        String?
  fileBytesSize   Int?
  status          ApiResourceStatus @default(UNKNOWN)
  apiResourceType ApiResourceType
  hostUrl         String?
  customParam     String?           @default("{}") @db.Text
  enable          Boolean           @default(false)
  createdAt       DateTime          @default(now())
  updatedAt       DateTime?         @updatedAt
  generatedAt     DateTime?
  user            User?             @relation(fields: [uploadBy], references: [id])
  uploadBy        Int?
  supportCalls    Json?

  @@index([uploadBy])
}

model BusinessUnit {
  id        Int       @id @unique @default(autoincrement())
  name      String    @unique
  createdAt DateTime? @default(now())
  updatedAt DateTime? @updatedAt

  @@index([name])
}

model Department {
  id        Int       @id @unique @default(autoincrement())
  name      String    @unique
  createdAt DateTime? @default(now())
  updatedAt DateTime? @updatedAt

  @@index([name])
}

model CostCentreCode {
  id          Int       @id @unique @default(autoincrement())
  companyCode String
  code        String
  description String
  enable      Boolean
  createdBy   String?
  amendedBy   String?
  createdAt   DateTime? @default(now())
  updatedAt   DateTime? @updatedAt

  @@index([enable, code])
}

enum ApiResourceType {
  RESTAPI
  GRAPHQL
}

enum ApiResourceStatus {
  PROCESSING
  GENERATED
  FAILED
  UNKNOWN
}

model ChatSession {
  id              Int             @id @default(autoincrement())
  name            String
  group           Group           @relation(fields: [groupId], references: [id])
  groupId         Int
  user            User            @relation(fields: [userId], references: [id])
  userId          Int
  /// any data frontend want to persist in db for the chat session
  chatSetting     Json?
  chatSessionType ChatSessionType
  createdAt       DateTime        @default(now())
  chatHistories   ChatHistory[]
  /// for sorting chat sessions
  lastActionDate  DateTime        @default(now())
  isDefault       Boolean         @default(false)

  @@index([groupId, userId], map: "ChatSession.groupId_userId_index")
  @@index([chatSessionType], map: "ChatSession.chatSessionType_index")
}

model ChatHistory {
  id              Int                    @id @default(autoincrement())
  chatSession     ChatSession            @relation(fields: [chatSessionId], references: [id], onDelete: Cascade)
  chatSessionId   Int
  message         Json?
  contentType     ChatMessageContentType
  responseUsage   Json?
  createdAt       DateTime               @default(now())
  chatwithdata    Json?
  contentPoints   Json?
  chatFileId      Int?
  chatFile        ChatFile?              @relation(fields: [chatFileId], references: [id], onDelete: SetNull)
  file_expired_at DateTime?              @map("fileExpiredAt")
  rating          Int?
  showReference   Boolean?
  comment         Json?

  @@index([chatSessionId], map: "ChatHistory.chatSessionId_index")
  @@index([chatFileId])
}

model AlertHistory {
  id         Int                    @id @default(autoincrement())
  entityType AlertHistoryEntityType
  entityId   Int
  channel    AlertHistoryChannel
  alertType  String
  createdAt  DateTime               @default(now())
  sourceId   String?
  source     String?

  @@index([entityType, entityId, alertType], map: "AlertHistory_alertType_entity_index")
}

model UserBookmark {
  id              Int                    @id @default(autoincrement())
  userId          Int
  entityId        Int
  entityType      UserBookmarkEntityType
  group           Group                  @relation(fields: [entityId], references: [id], map: "UserBookmarkgroup_entityId_fkey")
  messageTemplate MessageTemplate        @relation(fields: [entityId], references: [id], name: "messageTemplateRef", map: "UserBookmark_messageTemplate_entityId_fkey")
  user            User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  LLMModel        LLMModel               @relation(fields: [entityId], references: [id], name: "LLMModelRef", map: "UserBookmark_LLMModel_entityId_fkey")
  createdAt       DateTime               @default(now())

  @@index([userId, entityId, entityType], map: "UserBookmark_user_entity_index")
  @@index([id], map: "UserBookmark_index")
  @@index([entityId])
}

enum UserBookmarkEntityType {
  GROUP
  MESSAGE_TEMPLATE
  PUBLIC_BOT
}

enum AlertHistoryChannel {
  EMAIL
}

enum AlertHistoryEntityType {
  GROUP
}

enum AlertHistoryStatus {
  PENDING
  COMPLETED
  FAILED
}

enum ChatMessageContentType {
  TEXT
  CONTEXT_BREAK
}

enum ChatSessionType {
  PLAYGROUND
  GEN_KB
  PUBLIC
}

enum ChatFileType {
  QUESTION
  RESPONSE
}

model ChatFile {
  id                      Int                 @id @default(autoincrement())
  group                   Group               @relation(fields: [groupId], references: [id])
  groupId                 Int
  user                    User                @relation(fields: [userId], references: [id])
  userId                  Int
  /// the original uploaded filename with extension
  filename                String
  /// the path basename in s3 bucket
  s3Basename              String
  type                    ChatFileType        @default(QUESTION)
  fileSize                Int?
  expiresAt               DateTime?
  ChatHistory             ChatHistory[]
  fullScanReportPath      String?
  fullScanReportCreatedAt DateTime?
  fullScanReportUpdatedAt DateTime?
  errCode                 String?
  hasPii                  String?
  hasPromptInjection      HasPromptInjection?
  detectedPii             String?
  piiFileStatus           PiiFileStatus?
  scanMalwareStatus       ScanMalwareStatus?
  malwareRating           String?
  verifyStatus            FileVerifyStatus?
  verifyErrCode           String?
  verifyErrorMsg          String?
  errorMsg                String?             @db.Text
  hasPiiScanSkip          Boolean?            @default(false)
  fullScanReportVersion   Int?
  scanMalwareVersion      String?
  jobId                   String?

  @@index([groupId, userId], map: "ChatFile.groupId_userId_index")
}

model ModelFilePermissionButton {
  id                         Int                 @id @default(autoincrement())
  isApproved                 Boolean?
  status                     FileStatus?
  isRequireSecondaryApproval Boolean?
  fileClassification         FileClassification?
  permissionKey              String
  buttonList                 String[]
  hasMalware                 HasMalware?

  @@unique([isApproved, status, isRequireSecondaryApproval, fileClassification, permissionKey, hasMalware])
}

model BotSecurity {
  id             Int         @id @default(autoincrement())
  securityId     String      @unique(map: "BotSecurity.securityId.unique") @db.VarChar(100)
  groupId        Int         @unique
  env            Environment
  inputScanners  Json
  outputScanners Json
  /// when the history data is created or updated
  createdAt      DateTime?   @default(now()) @db.Timestamp(6)
  updatedAt      DateTime    @default(now()) @updatedAt
}

model PromptOutputSecurityDetection {
  id              Int                   @id @default(autoincrement())
  groupId         Int
  user_prompt     String
  output          String?
  masked_prompt   String?
  scanners        String[]
  type            SecurityDetectionType
  createdByUserId Int
  createdAt       DateTime              @default(now()) @db.Timestamp(6)
}

enum SecurityDetectionType {
  PROMPT
  OUTPUT
}

model ResourceCategory {
  id              Int                     @id @default(autoincrement())
  name            String
  subscriberTypes ResourceSubsciberType[] @default([])
  key             String                  @unique
  resources       Resource[]

  @@index([subscriberTypes])
}

model Resource {
  id                 Int                     @id @default(autoincrement())
  subscriberTypes    ResourceSubsciberType[] @default([])
  resourceKey        String                  @unique
  resourceName       String
  description        String
  resourceEntityType ResourceEntityType?
  resourceEntityKey  String?
  resourceCategoryId Int?
  resourceCategory   ResourceCategory?       @relation(fields: [resourceCategoryId], references: [id], onDelete: Cascade)
  plans              Plan[]
  permissionId       Int?
  quotaRuleId        Int?
  permission         Permission?             @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  quotaRule          ResourceQuotaRule?      @relation(fields: [quotaRuleId], references: [id], onDelete: Cascade)

  @@index([subscriberTypes])
  @@index([resourceCategoryId])
  @@index([permissionId])
  @@index([quotaRuleId])
}

model Plan {
  id                  Int                    @id @default(autoincrement())
  resourceId          Int
  resource            Resource               @relation(fields: [resourceId], references: [id], onDelete: Cascade)
  planKey             String                 @unique
  planName            String
  description         String
  planRoleIdsRequired Int[]                  @default([])
  isDefault           Boolean                @default(false)
  isDisabledPlan      Boolean                @default(false)
  groupEnv            Environment?
  entityType          ResourceSubsciberType?
  entityId            Int?
  isActive            Boolean                @default(true)
  subsciptions        PlanSubscription[]
  planPermissions     PlanPermission[]
  planQuotas          PlanQuota[]

  @@index([resourceId])
  @@index([resourceId, isDefault])
  @@index([resourceId, planRoleIdsRequired])
  @@index([resourceId, entityType, entityId])
  @@index([resourceId, entityType, entityId, planRoleIdsRequired, groupEnv])
}

model PlanSubscription {
  id                   Int                   @id @default(autoincrement())
  planId               Int
  subscriberEntityType ResourceSubsciberType
  plan                 Plan                  @relation(fields: [planId], references: [id], onDelete: Cascade)
  subscriberEntityId   Int
  subscribeStartDate   DateTime
  subscribeEndDate     DateTime?
  subscribedGroup      Group?                @relation("resourcePlanSubscriptionSubscribedGroup", fields: [subscriberEntityId], references: [id])

  @@index([subscriberEntityId, subscriberEntityType, subscribeEndDate], map: "plan_subscription_index")
  @@index([subscriberEntityType], map: "plan_subscription_index2")
  @@index([planId], map: "plan_subscription_index3")
}

model PlanPermission {
  planId       Int
  permissionId Int
  plan         Plan       @relation(fields: [planId], references: [id], onDelete: Cascade)
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@id([planId, permissionId])
  @@index([permissionId], map: "plan_permission_index")
}

model ResourceQuotaRule {
  id          Int                  @id @default(autoincrement())
  ruleKey     String               @unique
  description String
  quotaType   String
  values      ResourceQuotaValue[]
  resource    Resource[]
}

model ResourceQuotaValue {
  id          Int               @id @default(autoincrement())
  quotaKey    String            @unique
  ruleId      Int
  description String?
  value       Float
  rule        ResourceQuotaRule @relation(fields: [ruleId], references: [id], onDelete: Cascade)
  planQuotas  PlanQuota[]

  @@index([ruleId])
}

model PlanQuota {
  planId       Int
  quotaValueId Int
  plan         Plan               @relation(fields: [planId], references: [id], onDelete: Cascade)
  quotaValue   ResourceQuotaValue @relation(fields: [quotaValueId], references: [id], onDelete: Cascade)

  @@id([planId, quotaValueId])
  @@index([quotaValueId], map: "plan_quota_index")
}

model PlanSubscriptionRequest {
  id                   Int                           @id @default(autoincrement())
  subscriberEntityType ResourceSubsciberType
  subscriberEntityId   Int
  crNum                String?
  status               PlanSubscriptionRequestStatus
  createdDate          DateTime                      @default(now())
  createdBy            Int
  operatedDate         DateTime?
  operatedBy           Int?
  subscribedPlanIds    Int[]
  customPlans          Json[]
  creator              User                          @relation("resourcePlanSubscriptionRequester", fields: [createdBy], references: [id])
  operator             User?                         @relation("resourcePlanSubscriptionOperator", fields: [operatedBy], references: [id])
  subscribedGroup      Group?                        @relation("resourcePlanSubscriptionRequestSubscribedGroup", fields: [subscriberEntityId], references: [id])
  rejectReason         String?

  @@index([subscriberEntityType, subscriberEntityId], map: "plan_subscription_request_index")
  @@index([subscriberEntityType, status], map: "plan_subscription_request_index2")
  @@index([createdBy])
  @@index([operatedBy])
  @@index([subscriberEntityId])
}

model BotReviewNomination {
  id           Int        @id @default(autoincrement())
  membershipId Int        @unique
  startDate    DateTime
  endDate      DateTime
  membership   Membership @relation(fields: [membershipId], references: [id], onDelete: Cascade)

  @@index([membershipId, startDate, endDate])
}

model Labels {
  id                Int            @id @default(autoincrement())
  name              String
  color             String?
  desc              String?
  seq               Int            @default(autoincrement())
  labelType         LabelType
  updatedBy         Int
  createdBy         Int
  createdLabelsUser User           @relation(name: "createLabelsUser", fields: [createdBy], references: [id], onDelete: Cascade)
  updatedAt         DateTime
  createdAt         DateTime
  updateLabelsUser  User           @relation(name: "updateLabelsUser", fields: [updatedBy], references: [id], onDelete: Cascade)
  EntityLabel       EntityLabels[] @relation(name: "Labels")

  @@index([updatedBy], map: "updateLabelsUser")
  @@index([createdBy], map: "createLabelsUser")
}

model EntityLabels {
  id              Int              @id @default(autoincrement())
  labelsId        Int
  seq             Int              @default(autoincrement())
  Labels          Labels           @relation(name: "Labels", fields: [labelsId], references: [id], onDelete: Cascade)
  LabelEntityType LabelEntityType
  entityId        Int
  llmEngine       LlmEngine?       @relation(name: "LlmEngine", fields: [entityId], references: [id], map: "entity_label_llm_engine_index")
  messageTemplate MessageTemplate? @relation(name: "MessageTemplate", fields: [entityId], references: [id], map: "entity_label_message_template_index")
  modelFile       ModelFile?       @relation(name: "DocumentTags", fields: [entityId], references: [id], map: "entity_label_model_file_index")
  LLMModelLabels  LLMModel?        @relation(name: "LLMModelLabels", fields: [entityId], references: [id], map: "entity_label_llm_index")

  @@index([labelsId])
  @@index([entityId])
}

model ModelFileLabelHistory {
  id          Int      @id @default(autoincrement())
  groupId     Int
  modelFileId Int
  fileHash    String
  model       String
  prompt      String
  value       String
  version     Int
  createdBy   Int
  createdAt   DateTime @default(now())

  @@index([groupId, createdAt(sort: Desc)])
  @@index([groupId, modelFileId, fileHash, model, prompt])
}

model HistoricalFileMalwareScan {
  id                 Int       @id @default(autoincrement())
  errorCode          String?
  docId              String
  malwareRating      String?
  scanMalwareVersion String?
  modelFile          ModelFile @relation(name: "file_malware_hisotry", fields: [docId], references: [docId], onDelete: Cascade)

  @@index([docId])
}

enum LabelEntityType {
  LLM_ENGINE
  MESSAGE_TEMPLATE
  MODEL_FILE
  LLM_MODEL
}

enum LabelType {
  CATEGORIES
  LABELS
  TAG
}

enum HasPii {
  YES
  NO
  PENDING
  ERROR
}

enum HasPromptInjection {
  YES
  NO
}

enum TemplateAccessLevel {
  GROUP
  SYSTEM
}

enum AiResourceStatus {
  PROCESSING
  GENERATED
  FAILED
}

enum Environment {
  TEST
  PROD
}

enum LlmServiceProvider {
  OPENAI
  AZURE
  AWS
  HUGGING_FACE
  VERTEX_AI
  SENSENOVA
  ALIBABA
}

enum FileStatus {
  UPLOADED
  VERIFY_SUCCESS
  VERIFY_FAILED
  PROCESSING
  COMPLETED
  APPROVED
}

enum PiiFileStatus {
  SCANNING
  SCANNING_FAILED
  COMPLETED
}

enum AccessLevel {
  ADMIN
  USER
}

enum Gender {
  MALE
  FEMALE
  NONBINARY
  UNKNOWN
}

enum NotificationEmail {
  ACCOUNT
  UPDATES
  PROMOTIONS
}

enum PrefersColorScheme {
  NO_PREFERENCE
  LIGHT
  DARK
}

enum PrefersReducedMotion {
  NO_PREFERENCE
  REDUCE
}

enum SystemName {
  SUDO
  OPERATION_TEAM
  SECURITY_TEAM
  ACCOUNT_MANAGEMENT
  PRODUCT_TEAM
  BOT_REVIEWER
  BOT_CREATOR
  USER
  GROUP_OWNER
  GROUP_ADMIN
  GROUP_CONTRIBUTOR
  GROUP_MEMBER
  GROUP_VISITOR
  GROUP_CUSTOM
}

enum MfaMethod {
  NONE
  SMS
  TOTP
  EMAIL
}

// TODO: remove
enum MembershipRole {
  OWNER
  ADMIN
  MEMBER
}

enum IdentityType {
  GOOGLE
  APPLE
  SLACK
}

enum FileHistoryStatus {
  PROCESSING
  COMPLETED
  EXPIRED
  ERROR
}

enum FileType {
  SUMMARY
  BOT_SUMMARY
  USER_SUMMARY
  FLOW_SUMMARY
  LLM_ENGINE_SUMMARY
  QUERY_LOG
  AUDIT_LOG
  SYSTEM_ROLE_PERMISSION_SUMMARY
  PLAN_SUBSCRIPTION_HISTORY
  SECURITY_DETECTION_REPORT
  LEADER_BOARD
  LLM_ENGINE_MEMBER_SUMMARY
  MONTHLY_PRICE_REPORT_PER_GROUP
  MONTHLY_PRICE_REPORT
  MODEL_PRICE_EVENTS
  MEMBERSHIP_SUMMARY
  SCORING_RATING
  INSIGHT_GENERATOR_SUMMARY
}

enum FileEntityType {
  BOT
  FLOW
  USER
  LLM
  INSIGHT
}

enum LoginType {
  HKT
  LOCAL
  Azure
}

enum VerificationStatus {
  PENDING
  VERIFY_SUCCESS
}

enum VerificationType {
  ACTIVATE_USER
  RESET_PASSWORD
}

enum WikiJSGroupRole {
  VIEWER
  EDITOR
}

enum GroupType {
  BOT
  FLOW
  INSIGHT
}

enum FlowBotStatus {
  ACTIVE
  DELETED
}

enum FlowBotRequestType {
  REQUEST_BOT
  DELETE_BOT
  LEAVE_FLOW
}

enum FlowBotRequestStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
}

enum FileClassification {
  STRICTLY_CONFIDENTIAL
  CONFIDENTIAL
  INTERNAL_USE
  PUBLIC_DOMAIN
}

enum FeatureFlagTargetType {
  ENV
  BOT
  FLOW
}

enum PermissionType {
  SYSTEM
  USER
  GROUP
}

enum RoleType {
  SYSTEM_DEFAULT
  GROUP_DEFAULT
  GROUP_CUSTOM
}

enum ResourceSubsciberType {
  USER
  BOT
  FLOW
  INSIGHT
}

enum PlanSubscriptionRequestStatus {
  PENDING
  APPROVED
  REJECTED
}

enum ResourceEntityType {
  LLM_ENGINE
}

enum ScanMalwareStatus {
  PENDING
  SCANNING
  SCANNING_FAILED
  COMPLETED
}

model UserGroup {
  id               Int              @id @default(autoincrement())
  name             String
  userGroupFilter  UserGroupFilter?
  additionalEmails String[]         @default([])
  createdByUserId  Int
  createdBy        User             @relation("UserGroupCreatedBy", fields: [createdByUserId], references: [id])
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt
  updatedByUserId  Int
  updatedBy        User             @relation("UserGroupUpdatedBy", fields: [updatedByUserId], references: [id])

  @@index([name], map: "user_group_index")
}

model UserGroupFilter {
  id            Int        @id @default(autoincrement())
  groupIds      Int[]      @default([])
  systemRoleIds Int[]      @default([])
  groupRoleIds  Int[]      @default([])
  cccList       String[]   @default([])
  businessUnits String[]   @default([])
  groupType     GroupType?
  userGroupId   Int        @unique
  userGroup     UserGroup  @relation(fields: [userGroupId], references: [id])
}

enum HasMalware {
  YES
  NO
  ERROR
}

enum ModelPriceType {
  COST_SUGGESTION
  COST
  PRICE
}

enum ModelPriceSource {
  ADMIN
  LITELLM
  AZURE
  SENSENOVA
}

enum ModelPriceEventType {
  CREATE
  UPDATE
  DELETE
  SNAPSHOT
  RESET
}

model ModelPriceUnit {
  id               Int              @id @default(autoincrement())
  slug             String           @unique
  llmEngineSlug    String
  modelPriceSource ModelPriceSource
  metadata         Json
  order            Int              @default(99)

  createdBy Int
  createdAt DateTime @default(now())
  updatedBy Int
  updatedAt DateTime @updatedAt

  // Relation
  modelPriceEvents  ModelPriceEvent[]
  modelPrice        ModelPrice[]
  modelPriceSummary ModelPriceSummary[]
}

model ModelPriceEvent {
  id Int @id @default(autoincrement())

  modelPriceEventType ModelPriceEventType
  modelPriceType      ModelPriceType

  modelPriceUnitId Int
  modelPriceUnit   ModelPriceUnit @relation(fields: [modelPriceUnitId], references: [id])

  value Float?

  metadata  Json
  createdBy Int
  createdAt DateTime @default(now())

  // Relation
  creator User? @relation(fields: [createdBy], references: [id])
}

enum ModelPriceProcessStatus {
  PENDING
  PROCESSING
  COMPLETED
  ERROR
}

model ModelPriceSummary {
  id               Int                @id @default(autoincrement())
  startDate        DateTime           @db.Timestamp(6)
  endDate          DateTime           @db.Timestamp(6)
  groupId          Int
  modelPriceUnitId Int
  engineSlug       String
  key              SummaryKeyType
  feature          Feature
  channel          SummaryCallingType
  value            Decimal

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  // Relation
  group          Group          @relation(fields: [groupId], references: [id])
  modelPriceUnit ModelPriceUnit @relation(fields: [modelPriceUnitId], references: [id])

  @@unique([startDate, endDate, groupId, modelPriceUnitId, engineSlug, key, feature, channel], name: "priceSummaryIdentifier")
  @@index([groupId])
  @@index([modelPriceUnitId])
}

model ModelPriceProcess {
  id Int @id @default(autoincrement())

  year    Int
  month   Int
  groupId Int

  status ModelPriceProcessStatus

  metadata Json

  createdBy Int
  createdAt DateTime @default(now())
  updatedBy Int
  updatedAt DateTime @updatedAt

  // Relation
  group Group @relation(fields: [groupId], references: [id])

  @@index([year, month])
  @@index([year, month, groupId])
}

model ModelPrice {
  id Int @id @default(autoincrement())

  year  Int
  month Int

  modelPriceType ModelPriceType

  modelPriceUnitId Int
  modelPriceUnit   ModelPriceUnit @relation(fields: [modelPriceUnitId], references: [id])

  value Float

  metadata  Json
  createdAt DateTime @default(now())
  createdBy Int
  updatedAt DateTime @updatedAt
  updatedBy Int

  @@unique([year, month, modelPriceType, modelPriceUnitId], name: "modelPriceIdentifier")
  @@index([year, month])
}

enum FileVerifyStatus {
  VERIFYING
  VERIFY_FAILED
  VERIFY_SUCCESS
}

model HistoricalFileSecurityReport {
  id                      Int                 @id @default(autoincrement())
  docId                   String?
  fullScanReportPath      String?
  fullScanReportCreatedAt DateTime?
  fullScanReportUpdatedAt DateTime?
  hasPii                  HasPii?
  hasPromptInjection      HasPromptInjection?
  detectedPii             String?
  piiFileStatus           PiiFileStatus?
  fullScanReportVersion   Int?
  errorMsg                String?             @db.Text
  errCode                 String?
  modelFile               ModelFile?          @relation(name: "HistoricalFileSecurityReportTags", fields: [docId], references: [docId], map: "Historical_file_security_report_docId_index")

  @@unique([docId, fullScanReportVersion])
}

model LastUsedGroups {
  userId          Int
  groupType       GroupType
  lastUsedHistory Json[]

  @@id([userId, groupType])
  @@unique([userId, groupType])
  @@index([userId, groupType])
}

model GroupNotification {
  id                      Int                              @id @default(autoincrement())
  title                   String
  groupNotificationName   String
  channel                 GroupNotificationChannel
  type                    GroupNotificationType
  description             String
  interval                String
  defaultSilentPeriod     Int?
  defaultRecipientRoles   GroupNotificationRecipientRole[]
  groupNotificationConfig GroupNotificationConfig[]
  enabledOverride         Boolean?                         @default(true)
  defaultEnabledNotify    Boolean?                         @default(true)

  @@unique([groupNotificationName, channel])
}

model GroupNotificationConfig {
  id                  Int               @id @default(autoincrement())
  groupNotificationId Int
  groupNotification   GroupNotification @relation(fields: [groupNotificationId], references: [id])
  groupId             Int
  group               Group             @relation(fields: [groupId], references: [id])
  recipients          String[]          @default([])
  silentPeriod        Int?
  enabledNotify       Boolean?

  @@unique([groupNotificationId, groupId])
}

enum GroupNotificationChannel {
  EMAIL
  IN_APP
}

enum GroupNotificationType {
  REGULAR_ALERT
  IMMEDIATE_ALERT
  REPORT
}

// mapping of group notification default recipient not actual role
enum GroupNotificationRecipientRole {
  OWNER
  ADMIN
  REVIEWER_N_APPROVER
}
