import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { Configuration } from '../../config/configuration.interface';
import { ConfigService } from '@nestjs/config';

export interface SSOUserResponse {
  name: string;
  email: string;
  error: string;
  sub: string;
  preferred_username: string;
}

@Injectable()
export class SsoService {
  private axios?: AxiosInstance;
  private logger = new Logger(SsoService.name);
  private readonly ssoConfig: Configuration['ssoHkt'];

  constructor(private configService: ConfigService) {
    this.ssoConfig = this.configService.get<Configuration['ssoHkt']>('ssoHkt');
    this.axios = axios.create({
      timeout: 30000,
    });
  }

  async extractUserFromToken(token: string): Promise<SSOUserResponse> {
    try {
      const resp = await fetch(this.ssoConfig.userInfo, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: 'application/json',
        },
      });
      const tokenResponse = await resp.json();
      this.logger.log(tokenResponse, 'sso login token response');
      return tokenResponse;
    } catch (error) {
      this.logger.error(error, 'failed to get sso login token response');
      throw error;
    }
  }

  async verifyUserToken(token: string, email: string): Promise<boolean> {
    try {
      const resp = await fetch(this.ssoConfig.userInfo, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: 'application/json',
        },
      });

      const data = await resp.json();
      this.logger.log(data);
      if (data.email === email) {
        return true;
      }
    } catch (error) {
      this.logger.error(error);
    }

    return false;
  }

  async healthCheck() {
    return this.axios.get(this.ssoConfig.healthCheck);
  }
}
