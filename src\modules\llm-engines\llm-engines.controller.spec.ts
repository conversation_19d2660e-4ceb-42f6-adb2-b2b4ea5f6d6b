import { ModuleMocker, MockFunctionMetadata } from 'jest-mock';
import { mockDeep, DeepMockProxy } from 'jest-mock-extended';
import { Test, TestingModule } from '@nestjs/testing';
import { LlmEngine } from '@prisma/client';
import { LlmEnginesService } from './llm-engines.service';
import { LlmEnginesController } from './llm-engines.controller';
import { CreateLlmEngineDto, UpdateLlmEngineDto } from './llm-engines.dto';
import { UserRequest } from '../auth/auth.interface';
const moduleMocker = new ModuleMocker(global);

describe('LlmEnginesService', () => {
  let llmEnginesService: DeepMockProxy<LlmEnginesService>;

  let llmEnginesController: LlmEnginesController;
  beforeEach(async () => {
    llmEnginesService = mockDeep<LlmEnginesService>();
    const module: TestingModule = await Test.createTestingModule({
      controllers: [LlmEnginesController],
      providers: [{ provide: LlmEnginesService, useValue: llmEnginesService }],
    })
      .useMocker((token) => {
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>;
          const Mock = moduleMocker.generateFromMetadata(mockMetadata);
          return new Mock();
        }
      })
      .compile();
    llmEnginesController = module.get(LlmEnginesController);
  });

  describe('findAll', () => {
    it('should be return llm-engine list', async () => {
      const llmEngineList = [
        {
          id: 1,
          name: 'ChatGPT 4 128K@0125',
          slug: 'gpt-4-0125',
        },
        {
          id: 2,
          name: 'Chat Bison 32K@Latest',
          slug: 'vertexai-chat-bison-32k',
        },
      ] as LlmEngine[];
      const count = {
        count: 2,
      };
      const check = {
        list: [
          {
            id: 1,
            name: 'ChatGPT 4 128K@0125',
            slug: 'gpt-4-0125',
          },
          {
            id: 2,
            name: 'Chat Bison 32K@Latest',
            slug: 'vertexai-chat-bison-32k',
          },
        ],
        ...count,
      };
      llmEnginesService.findAll.mockResolvedValue(llmEngineList);
      llmEnginesService.count.mockResolvedValue(count.count);
      const res = await llmEnginesController.findAll();
      expect(res).toEqual(check);
    });
  });

  describe('create', () => {
    it('should be return llm-engine', async () => {
      const llmEngine = {
        id: 1,
        name: 'ChatGPT 4 128K@0125',
        slug: 'gpt-4-0125',
      } as LlmEngine;
      const req = {
        name: 'test',
        slug: 'slug-test',
        reportSlug: 'reportSlug-test',
      } as CreateLlmEngineDto;
      llmEnginesService.create.mockResolvedValue(llmEngine);
      const res = await llmEnginesController.create(req);
      expect(res).toEqual(llmEngine);
    });
  });

  describe('update', () => {
    it('should be return llm-engine', async () => {
      const llmEngine = {
        id: 1,
        name: 'ChatGPT 4 128K@0125',
        slug: 'gpt-4-0125',
      } as LlmEngine;
      const userReq = {
        user: { id: 1 },
      };
      const req = {
        name: 'test',
        slug: 'slug-test',
        reportSlug: 'reportSlug-test',
      } as UpdateLlmEngineDto;
      llmEnginesService.update.mockResolvedValue(llmEngine);
      const res = await llmEnginesController.update(null, req, 1, userReq as UserRequest);
      expect(res).toEqual(llmEngine);
    });
  });
});
