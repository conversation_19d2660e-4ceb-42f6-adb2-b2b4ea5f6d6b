export interface ResourceQuotaRuleSeed {
  ruleKey: string;
  description: string;
  quotaType: string;
  link?: 'LLM_ENGINE' | 'CHAT_CHANNEL';
}

export const resourceQuotaRules: ResourceQuotaRuleSeed[] = [
  {
    ruleKey: 'group-{groupId}:llm-engine-{{engine}}-quota',
    description: 'To declare maximum token usage of {{engine}} per month',
    quotaType: 'TokenLimitPerMonth',
    link: 'LLM_ENGINE',
  },
  {
    ruleKey: 'group-{groupId}:rate-limit-{{channel}}-quota',
    description: 'To declare maximum chat number per hour through {{channel}}',
    quotaType: 'RateLimit',
    link: 'CHAT_CHANNEL',
  },
];
