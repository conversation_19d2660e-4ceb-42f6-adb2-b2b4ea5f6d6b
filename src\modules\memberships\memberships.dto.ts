import { Type } from 'class-transformer';
import {
  IsArray,
  IsEmail,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  MinLength,
  ValidateNested,
  IsNumber,
} from 'class-validator';

export class BatchImportMembershipDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ImportGroupMembershipDto)
  members: ImportGroupMembershipDto[];
}

export class ImportGroupMembershipDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsInt()
  roleId: number;
}

export class UpdateMembershipDto {
  @IsInt()
  roleId: number;
}

export class CreateGroupMembershipDto {
  @IsEmail()
  @IsNotEmpty()
  email!: string;

  @IsString()
  @IsOptional()
  @MinLength(3)
  name?: string;

  // TODO: use number instead
  @IsInt()
  @IsOptional()
  roleId?: number;
}

export class CreateReviewNorminateDto {
  @IsInt()
  membershipId: number;
  @IsString()
  startDate: string;
  @IsString()
  endDate: string;
}

export class UpdateReviewNorminateDto {
  @IsString()
  startDate: string;
  @IsString()
  endDate: string;
}

export class LastUsedGroupsDto {
  @IsOptional()
  @IsNumber()
  BOT?: number;

  @IsOptional()
  @IsNumber()
  FLOW?: number;

  @IsOptional()
  @IsNumber()
  INSIGHT?: number;

  @IsArray()
  @IsNumber({}, { each: true })
  rankedList: number[];
}
