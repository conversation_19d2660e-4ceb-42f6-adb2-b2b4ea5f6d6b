import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  ParseArrayPipe,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { Language } from '@prisma/client';
import { Expose } from '../../providers/prisma/prisma.interface';
import { Scopes } from '../auth/scope.decorator';
import { WriteI18nDto } from './internationalization.dto';
import { InternationalizationService } from './internationalization.service';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { GenericResponse, WriteI18nResponse } from './internationalization.interface';
import { Public } from '../auth/public.decorator';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@Controller('/internationalization')
@ApiBearerAuth('bearer-auth')
@ApiTags('internationalization')
export class InternationalizationController {
  private readonly logger = new Logger(InternationalizationController.name);
  constructor(private internationalizationService: InternationalizationService) {}

  /** Batch create i18n */
  @Post('/create')
  @AuditLog('create-i18n')
  @Scopes('system:write-i18n')
  async create(
    @Body(new ParseArrayPipe({ items: WriteI18nDto })) data: WriteI18nDto[],
  ): Promise<WriteI18nResponse[]> {
    return await this.internationalizationService.createI18n(data);
  }

  /** Get i18n by language */
  @Get('/list')
  @Public()
  async getAll(@Query('language') language?: Language, @Body() body?: any) {
    this.logger.log(`body: ${JSON.stringify(body)}`);
    return await this.internationalizationService.getManyByLanguage(language);
  }

  /** Batch update i18n */
  @Put('/update')
  @AuditLog('update-i18n')
  @Scopes('system:write-i18n')
  async update(
    @Body(new ParseArrayPipe({ items: WriteI18nDto })) data: WriteI18nDto[],
  ): Promise<WriteI18nResponse[]> {
    return await this.internationalizationService.updateI18n(data);
  }

  /** Delete a i18n */
  @Delete('/delete/:key')
  @AuditLog('delete-i18n')
  @Scopes('system:delete-i18n')
  async remove(@Param('key') key: string): Promise<Expose<GenericResponse>> {
    return await this.internationalizationService.deleteByKey(key);
  }
}
