import { Controller, Get, Param, Query } from '@nestjs/common';

import { Role, RoleType } from '@prisma/client';
import { Scopes } from '../auth/scope.decorator';
import { RolesService } from './roles.service';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { RoleQueryData } from './roles.dto';

@Controller('roles')
@ApiBearerAuth('bearer-auth')
@ApiTags('System Role')
export class RolesUsersController {
  constructor(private rolesService: RolesService) {}

  /** Get roles */
  @Get('system')
  @Scopes('system:read-role')
  async getRoles(): Promise<Role[]> {
    return this.rolesService.getSystemRolesForDisplay();
  }

  /** Get group roles */
  @Get('group')
  @Scopes('system:read-role')
  async getGroupRoles(@Query('groupType') groupType: string): Promise<RoleQueryData[]> {
    return this.rolesService.getGroupRolesForDisplay(groupType);
  }

  @Get(':roleType')
  @Scopes('system:read-role')
  async getRolesByType(@Param('roleType') roleType: RoleType): Promise<{ list: Role[] }> {
    const list = await this.rolesService.getRolesByType(roleType);
    return { list };
  }
}
