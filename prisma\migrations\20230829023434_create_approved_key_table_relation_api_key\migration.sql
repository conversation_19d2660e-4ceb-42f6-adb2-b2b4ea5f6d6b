-- CreateEnum
CREATE TYPE "ApprovedKeyStatus" AS ENUM ('PASS', 'PEDNDING');

-- CreateTable
CREATE TABLE "ApprovedKey" (
    "id" SERIAL NOT NULL,
    "apiKeyId" INTEGER NOT NULL,
    "status" "ApprovedKeyStatus" NOT NULL DEFAULT 'PASS',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ApprovedKey_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "approvedKey.apiKey_unique" ON "ApprovedKey"("apiKeyId");

-- AddForeignKey
ALTER TABLE "ApprovedKey" ADD CONSTRAINT "ApprovedKey_apiKeyId_fkey" FOREIGN KEY ("apiKeyId") REFERENCES "ApiKey"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
