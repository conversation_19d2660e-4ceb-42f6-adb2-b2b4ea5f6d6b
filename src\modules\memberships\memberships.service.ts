import { Injectable, Logger } from '@nestjs/common';
import { Membership, User, Group, SystemName, Environment } from '@prisma/client';
import type { Prisma, Role } from '@prisma/client';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { Expose } from '../../providers/prisma/prisma.interface';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { GroupsService } from '../groups/groups.service';
import { CreateMembershipInput } from './memberships.interface';
import { TokensService } from '../../providers/tokens/tokens.service';
import { LLMModelsService } from '../llm-models/llm-models.service';
import { LlmEnginesService } from '../llm-engines/llm-engines.service';
import { LLMBackendService } from '../../providers/llm-backend/llm-backend.service';
import {
  CreateReviewNorminateDto,
  ImportGroupMembershipDto,
  UpdateMembershipDto,
  UpdateReviewNorminateDto,
} from './memberships.dto';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { AccessTokenParsed } from '../auth/auth.interface';
import { FlowsService } from '../flows/flows.service';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { GROUP_MEMBERSHIP_SCOPE_KEY } from 'src/providers/redis/redis.constants';
import { RedisService } from 'src/providers/redis/redis.service';
import { BotSecurityService } from '../bot-security/bot-security.service';
import { RolesService } from '../roles/roles.service';
import moment from 'moment';
import { LastUsedGroupsService } from '../../providers/last-used-groups/last-used-groups.service';
import { ConfigService } from '@nestjs/config';

export type ExtendedMembership = Membership & {
  group: Group;
  user: User;
};
@Injectable()
export class MembershipsService {
  private logger = new Logger(MembershipsService.name);
  constructor(
    private redis: RedisService,
    private prisma: PrismaService,
    private groupsService: GroupsService,
    private tokensService: TokensService,
    private llmBackendService: LLMBackendService,
    private llmModelService: LLMModelsService,
    private featureFlagService: FeatureFlagService,
    private llmEnginesService: LlmEnginesService,
    private flowsService: FlowsService,
    private botSecurityService: BotSecurityService,
    private rolesService: RolesService,
    private lastUsedGroupsService: LastUsedGroupsService,
    private configService: ConfigService,
  ) {}

  getBatchUploadFileTemplate() {
    return [
      { email: '<EMAIL>', role: 'GROUP_OWNER', action: 'add' },
      {
        email: '<EMAIL>',
        role: 'GROUP_ADMIN',
        action: 'update',
      },
      {
        email: '<EMAIL>',
        role: 'GROUP_MEMBER',
        action: 'remove',
      },
      {
        email: '<EMAIL>',
        role: 'GROUP_CUSTOM:Custom Role Name',
        action: 'add',
      },
    ];
  }

  async createBotAndUserMembership(
    userId: number,
    defaultLlmEngineFeatureFlagKey:
      | FeatureFlagKey.BOT_CREATE_DEFAULT_LLM_ENGINE_SLUG_PROD
      | FeatureFlagKey.BOT_CREATE_DEFAULT_LLM_ENGINE_SLUG_TEST,
    defaultBotInstructionFeatureFlagKey:
      | FeatureFlagKey.BOT_CREATE_DEFAULT_LLM_ENGINE_INSTRUCTION_TEST
      | FeatureFlagKey.BOT_CREATE_DEFAULT_LLM_ENGINE_INSTRUCTION_PROD,
    data: Omit<Prisma.GroupCreateInput, 'id'>,
  ) {
    // Default groupType should be 'BOT'

    let id: number | undefined = undefined;

    // Generate unique groupId and check db
    while (!id) {
      id = Number(`10${await this.tokensService.generateRandomString(6, 'numeric')}`);
      const isGroupExist = await this.prisma.group.findFirst({
        where: { id },
      });
      if (isGroupExist) id = undefined;
    }
    if (!data.pairId) {
      data.pairId = id;
    }

    return await this.prisma.$transaction(async (tx) => {
      const createdGroup = await this.groupsService.createGroup(
        userId,
        {
          ...data,
          groupType: 'BOT',
          updatedAt: new Date(),
          id,
        },
        tx,
      );
      if (!createdGroup) {
        throw new Error('Failed to create bot');
      }

      const defaultLlmEngineFeatureFlag = await this.featureFlagService.getOne(
        defaultLlmEngineFeatureFlagKey,
      );

      if (!defaultLlmEngineFeatureFlag) {
        throw new ApiException(ErrorCode.FEATURE_FLAG_NOT_FOUND);
      }
      const defaultBotInstruction = await this.featureFlagService.getOne(
        defaultBotInstructionFeatureFlagKey,
      );

      const llmEngines = await this.llmEnginesService.findDefaulLlmEngine(
        defaultLlmEngineFeatureFlag.metaData['value'],
      );

      if (!llmEngines) {
        throw new ApiException(ErrorCode.LLM_ENGINE_NOT_FOUND);
      }
      const defaultTone =
        defaultBotInstruction &&
        defaultBotInstruction.isEnabled &&
        defaultBotInstruction?.metaData?.['value']
          ? defaultBotInstruction?.metaData['value']
          : '';

      const llmBackendModel = await this.llmBackendService.createModel(data.env, data.name);
      if (!llmBackendModel) {
        throw new ApiException(ErrorCode.CREATE_LLM_MODEL_FAILED);
      }
      const defaultBotInstructionMessage =
        defaultTone.length !== 0 ? defaultTone : llmBackendModel.tone;

      // create new LLM Model assoicated witht this group
      const llmModel = await this.llmModelService.createModel(
        {
          name: data.name,
          modelId: llmBackendModel.id,
          tone: defaultBotInstructionMessage,
          startupMessage: llmBackendModel.startup_message,
          groupId: createdGroup.id,
          llmEngineId: llmEngines.id,
          modelEngine: llmEngines.slug,
          active: data.env === Environment.PROD ? false : true,
        },
        tx,
      );

      //create new BotSecurity assoicated witht this group
      await this.botSecurityService.createDefaultBotSecurity(createdGroup.id, data.env, tx);

      if (!llmModel) {
        throw new ApiException(ErrorCode.FAIL_TO_UPDATE_DB_LLM_MODEL);
      }

      return {
        ...createdGroup.memberships[0],
        group: {
          ...createdGroup.memberships[0].group,
          // return to frontend for displaying the status of bot
          llmModel,
        },
      };
    });
  }

  async createFlowAndUserMembership(
    userId: number,
    defaultLlmEngineFeatureFlagKey: FeatureFlagKey,
    data: Omit<Prisma.GroupCreateInput, 'id'>,
  ) {
    let id: number | undefined = undefined;

    // Generate unique groupId and check db
    while (!id) {
      id = Number(`10${await this.tokensService.generateRandomString(6, 'numeric')}`);
      const isGroupExist = await this.prisma.group.findFirst({
        where: { id },
      });
      if (isGroupExist) id = undefined;
    }
    if (!data.pairId) {
      data.pairId = id;
    }

    return await this.prisma.$transaction(async (tx) => {
      const createdGroup = await this.groupsService.createGroup(
        userId,
        {
          ...data,
          groupType: 'FLOW',
          updatedAt: new Date(),
          id,
        },
        tx,
      );
      let llmModel;
      if (!createdGroup) {
        throw new Error('Failed to create flow');
      }

      await this.flowsService.createGroupFlow(createdGroup.id, { name: data.name }, tx);

      return {
        ...createdGroup.memberships[0],
        group: {
          ...createdGroup.memberships[0].group,
          // return to frontend for displaying the status of bot
          llmModel,
        },
      };
    });
  }

  async createInsightGeneratorAndUserMembership(
    userId: number,
    defaultLlmEngineFeatureFlagKey: FeatureFlagKey,
    data: Omit<Prisma.GroupCreateInput, 'id'>,
  ) {
    let id: number | undefined = undefined;
    // Generate unique groupId and check db
    while (!id) {
      id = Number(`10${await this.tokensService.generateRandomString(6, 'numeric')}`);
      const isGroupExist = await this.prisma.group.findFirst({
        where: { id },
      });
      if (isGroupExist) id = undefined;
    }
    if (!data.pairId) {
      data.pairId = id;
    }
    return await this.prisma.$transaction(async (tx) => {
      const createdGroup = await this.groupsService.createGroup(
        userId,
        {
          ...data,
          groupType: 'INSIGHT',
          updatedAt: new Date(),
          id,
        },
        tx,
      );
      if (!createdGroup) {
        throw new Error('Failed to create insight generator');
      }

      const defaultLlmEngineFeatureFlag = await this.featureFlagService.getOne(
        defaultLlmEngineFeatureFlagKey,
      );

      if (!defaultLlmEngineFeatureFlag) {
        throw new ApiException(ErrorCode.FEATURE_FLAG_NOT_FOUND);
      }

      const llmEngines = await this.llmEnginesService.findDefaulLlmEngine(
        defaultLlmEngineFeatureFlag.metaData['value'],
      );

      if (!llmEngines) {
        throw new ApiException(ErrorCode.LLM_ENGINE_NOT_FOUND);
      }
      const defaultLlmBackendModel = await this.llmBackendService.createModel(data.env, data.name);
      if (!defaultLlmBackendModel) {
        throw new ApiException(ErrorCode.CREATE_LLM_MODEL_FAILED);
      }
      // create new LLM Model assoicated witht this group
      const llmModel = await this.llmModelService.createModel(
        {
          name: data.name,
          modelId: defaultLlmBackendModel.id,
          tone: defaultLlmBackendModel.tone,
          startupMessage: defaultLlmBackendModel.startup_message,
          groupId: createdGroup.id,
          llmEngineId: llmEngines.id,
          modelEngine: llmEngines.slug,
          active: data.env === Environment.PROD ? false : true,
        },
        tx,
      );

      if (!llmModel) {
        throw new ApiException(ErrorCode.FAIL_TO_UPDATE_DB_LLM_MODEL);
      }

      return {
        ...createdGroup.memberships[0],
        group: {
          ...createdGroup.memberships[0].group,
          // return to frontend for displaying the status of bot
          llmModel,
        },
      };
    });
  }

  async updateGroupPairId(testGroupId: number, prodGroupId: number) {
    await this.prisma
      .$executeRaw`UPDATE "Group" SET "pairId" = ${testGroupId} WHERE id in (${testGroupId}, ${prodGroupId})`;
  }

  async getMemberships(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.MembershipWhereUniqueInput;
    where?: Prisma.MembershipWhereInput;
    orderBy?: Prisma.MembershipOrderByWithRelationInput;
    // orderBy?: Prisma.MembershipOrderByInput;
  }): Promise<Expose<ExtendedMembership>[]> {
    const { skip, take, cursor, where, orderBy } = params;
    try {
      const memberships = await this.prisma.membership.findMany({
        skip,
        take,
        cursor,
        where,
        orderBy,
        include: {
          group: {
            include: {
              llmModel: {
                select: {
                  active: true,
                  showInTeams: true,
                  llmEngine: {
                    select: {
                      slug: true,
                      name: true,
                      platform: true,
                    },
                  },
                },
              },
              createdBy: {
                select: {
                  id: true,
                  name: true,
                },
              },
              _count: {
                select: {
                  memberships: true,
                },
              },
            },
          },
          user: { include: { userRole: true } },
          Role: true,
          BotReviewNomination: { take: 1 },
        },
      });
      return await Promise.all(
        memberships.map(async (membership) => {
          const { email } = await this.prisma.email.findFirst({
            where: { userId: membership.userId },
          });
          let leaveBotFlag = true;
          const memberCurrentDayUploadedFile = await this.llmModelService.getModelFilesCount(
            {
              groupId: membership.groupId,
              uploaderId: membership.userId,
              createdAt: {
                gte: moment().startOf('day').utcOffset(8).toDate(),
                lte: moment().endOf('day').utcOffset(8).toDate(),
              },
            },
            true,
          );

          if (membership.Role.systemName === SystemName.GROUP_OWNER) {
            leaveBotFlag =
              (await this.prisma.membership.count({
                where: {
                  groupId: membership.groupId,
                  Role: { systemName: SystemName.GROUP_OWNER },
                },
              })) > 1;
          }

          // return the group completeness flag
          const completeness = [
            membership.group.businessUnit,
            membership.group.ccc,
            membership.group.department,
            membership.group.name,
          ].every((value) => !!value?.trim());

          return {
            email,
            completeness,
            leaveBotFlag,
            memberCurrentDayUploadedFile,
            ...membership,
          };
        }),
      );
    } catch (error) {
      this.logger.error(error, `failed to get groups of user - ${where?.user?.id}`);
      throw new ApiException(ErrorCode.GET_MEMBERSHIP_FAILED);
    }
  }

  async getMembershipsCount(where?: Prisma.MembershipWhereInput) {
    return await this.prisma.membership.count({ where });
  }

  async getUserMembership(userId: number, id: number): Promise<Expose<Membership>> {
    const membership = await this.prisma.membership.findUnique({
      where: { id },
      include: { group: true, user: true, Role: true },
    });
    if (!membership) throw new ApiException(ErrorCode.MEMBERSHIP_NOT_FOUND);
    if (membership.userId !== userId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    return this.prisma.expose<Membership>(membership);
  }

  async getGroupOwnerMembership(groupId: number): Promise<Expose<Membership>> {
    const membership = await this.prisma.membership.findFirst({
      where: { groupId, Role: { systemName: SystemName.GROUP_OWNER } },
      include: { group: true, user: true, Role: true },
    });
    if (!membership) throw new ApiException(ErrorCode.MEMBERSHIP_NOT_FOUND);
    return this.prisma.expose<Membership>(membership);
  }

  async getGroupMembership(groupId: number, id: number) {
    const membership = await this.prisma.membership.findUnique({
      where: { id },
      include: { group: true, user: true, Role: true },
    });
    if (!membership) throw new ApiException(ErrorCode.MEMBERSHIP_NOT_FOUND);

    if (membership.groupId !== groupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    const { email } = await this.prisma.email.findFirst({ where: { userId: membership.userId } });
    return { email, ...membership };
  }

  async getMembershipRoleOptions(groupId: number, userReq: AccessTokenParsed) {
    const isRequestBySuperRole = userReq.scopes.some((userScope) =>
      [`group-${groupId}:role-*`, `group-*:role-*`].includes(userScope),
    );

    let allowedEditMembershipRoleList: Role[] = [];
    if (isRequestBySuperRole) {
      allowedEditMembershipRoleList = await this.rolesService.getGroupRoleList(groupId);
    } else {
      allowedEditMembershipRoleList = await this.rolesService.getEditableMembershipRoleList(
        groupId,
        userReq.scopes,
      );
    }
    return allowedEditMembershipRoleList;
  }

  async deleteUserMembership(userId: number, id: number): Promise<Expose<Membership>> {
    const membership = await this.prisma.membership.findUnique({
      where: { id },
    });
    if (!membership) throw new ApiException(ErrorCode.MEMBERSHIP_NOT_FOUND);
    if (membership.userId !== userId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    await this.verifyDeleteMembership(membership.groupId, id);
    await this.prisma.membership.delete({
      where: { id },
    });
    return this.prisma.expose<Membership>(membership);
  }

  async updateGroupMembershipByEmail(
    groupId: number,
    data: ImportGroupMembershipDto,
    userReq: AccessTokenParsed,
  ) {
    // # Step 1: Find the membership by email
    const user = await this.prisma.user.findFirst({
      where: { emails: { some: { email: data.email.toLowerCase() } } },
    });
    if (!user) throw new ApiException(ErrorCode.USER_NOT_FOUND);

    const membership = await this.prisma.membership.findFirst({
      where: { groupId, userId: user.id },
    });
    if (!membership) {
      throw new ApiException(ErrorCode.MEMBERSHIP_NOT_FOUND);
    }
    // # Step 2: Check requester permission
    // not allow to edit myself except account management
    if (membership.userId === userReq.id && userReq.role !== SystemName.ACCOUNT_MANAGEMENT) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    const editableRoleList = await this.getEditableRoleList(groupId, userReq);

    // checking editable role list
    if (
      !editableRoleList.some((role) => role.id === data.roleId) ||
      !editableRoleList.some((role) => role.id === membership.roleId)
    ) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    // # Step 3: Update the membership
    await this.prisma.membership.update({
      where: { id: membership.id },
      data: { roleId: data.roleId },
      include: { group: true, user: true, Role: true },
    });
    await this.redis.batchClearCache(
      GROUP_MEMBERSHIP_SCOPE_KEY.replace('{USER_ID}', membership.userId.toString()).replace(
        '{GROUP_ID}',
        groupId.toString(),
      ),
    );
    return { email: data.email, ...membership };
  }

  async updateGroupMembership(
    groupId: number,
    id: number,
    data: UpdateMembershipDto,
    userReq: AccessTokenParsed,
  ) {
    const membership = await this.prisma.membership.findUnique({
      include: { Role: true },
      where: { id },
    });
    if (!membership) throw new ApiException(ErrorCode.MEMBERSHIP_NOT_FOUND);
    if (membership.groupId !== groupId) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    // not allow to edit myself except account management
    if (membership.userId === userReq.id && userReq.role !== SystemName.ACCOUNT_MANAGEMENT) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    const editableRoleList = await this.getEditableRoleList(groupId, userReq);

    // checking editable role list
    if (
      !editableRoleList.some((role) => role.id === data.roleId) ||
      !editableRoleList.some((role) => role.id === membership.roleId)
    ) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    // update membership and clear bot review nomination
    await this.prisma.$transaction(async (tx) => {
      const assignedRole = editableRoleList.find((role) => role.id === data.roleId);
      if (
        !(
          [
            SystemName.GROUP_OWNER,
            SystemName.GROUP_ADMIN,
            SystemName.GROUP_CONTRIBUTOR,
            SystemName.GROUP_MEMBER,
          ] as SystemName[]
        ).includes((assignedRole?.systemName ?? '') as SystemName)
      ) {
        await tx.botReviewNomination.deleteMany({
          where: { membershipId: id },
        });
      }
      await tx.membership.update({
        where: { id },
        data: { roleId: data.roleId },
        include: { group: true, user: true, Role: true },
      });
    });

    await this.redis.batchClearCache(
      GROUP_MEMBERSHIP_SCOPE_KEY.replace('{USER_ID}', membership.userId.toString()).replace(
        '{GROUP_ID}',
        groupId.toString(),
      ),
    );
    const { email } = await this.prisma.email.findFirst({ where: { userId: membership.userId } });

    return { email, ...membership };
  }

  async deleteGroupMembershipByEmail(
    groupId: number,
    data: ImportGroupMembershipDto,
    userReq: AccessTokenParsed,
  ) {
    // # Step 1: Find the membership by email
    const user = await this.prisma.user.findFirst({
      where: { emails: { some: { email: data.email.toLowerCase() } } },
    });
    if (!user) throw new ApiException(ErrorCode.USER_NOT_FOUND);

    const membership = await this.prisma.membership.findFirst({
      where: { groupId, userId: user.id },
    });
    if (!membership) {
      throw new ApiException(ErrorCode.MEMBERSHIP_NOT_FOUND);
    }
    // # Step 2: Check requester permission
    // not allow to delete user himself except ACCOUNT_MANAGEMENT
    if (userReq.id === membership.userId && userReq.role !== SystemName.ACCOUNT_MANAGEMENT) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    const editableRoleList = await this.getEditableRoleList(groupId, userReq);

    // checking editable role list
    if (!editableRoleList.some((role) => role.id === membership.roleId)) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    // # Step 3: Update the membership
    await this.verifyDeleteMembership(membership.groupId, membership.id);
    const deletedMembership = await this.prisma.membership.delete({ where: { id: membership.id } });
    await this.redis.batchClearCache(
      GROUP_MEMBERSHIP_SCOPE_KEY.replace('{USER_ID}', deletedMembership.userId.toString()).replace(
        '{GROUP_ID}',
        groupId.toString(),
      ),
    );
    return { email: data.email, ...membership };
  }

  async deleteGroupMembership(groupId: number, groupMemberId: number, userReq: AccessTokenParsed) {
    const membership = await this.prisma.membership.findUnique({
      include: { Role: true },
      where: { id: groupMemberId },
    });
    if (!membership) throw new ApiException(ErrorCode.MEMBERSHIP_NOT_FOUND);
    if (membership.groupId !== groupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    // not allow to delete user himself except ACCOUNT_MANAGEMENT
    if (userReq.id === membership.userId && userReq.role !== SystemName.ACCOUNT_MANAGEMENT) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    const editableRoleList = await this.getEditableRoleList(groupId, userReq);

    // checking editable role list
    if (!editableRoleList.some((role) => role.id === membership.roleId)) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    await this.verifyDeleteMembership(membership.groupId, groupMemberId);
    const deletedMembership = await this.prisma.membership.delete({ where: { id: groupMemberId } });
    await this.redis.batchClearCache(
      GROUP_MEMBERSHIP_SCOPE_KEY.replace('{USER_ID}', deletedMembership.userId.toString()).replace(
        '{GROUP_ID}',
        groupId.toString(),
      ),
    );
    return deletedMembership;
  }

  async createGroupMembership(
    groupId: number,
    data: CreateMembershipInput,
    userReq: AccessTokenParsed,
  ) {
    const user = await this.prisma.user.findFirst({
      where: { emails: { some: { email: data.email.toLowerCase() } } },
    });

    if (!user) throw new ApiException(ErrorCode.USER_NOT_FOUND);

    const existedMembership = await this.prisma.membership.findFirst({
      where: { groupId, userId: user.id },
    });
    if (existedMembership) {
      throw new ApiException(ErrorCode.MEMBERSHIP_ALREADY_EXIST);
    }

    const editableRoleList = await this.getEditableRoleList(groupId, userReq);
    // checking editable role list
    if (!editableRoleList.some((role) => role.id === data.roleId)) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    const result = await this.prisma.membership.create({
      data: {
        Role: { connect: { id: data.roleId } },
        group: { connect: { id: groupId } },
        user: { connect: { id: user.id } },
      },
      include: { group: { select: { name: true } }, user: true, Role: true },
    });
    return { email: data.email, ...result };
  }

  async createReviewNomination(data: CreateReviewNorminateDto) {
    // validate review nomination of membership
    const botReviewerNominationCount = await this.prisma.botReviewNomination.count({
      where: {
        membershipId: data.membershipId,
      },
    });
    if (botReviewerNominationCount > 0) {
      throw new ApiException(ErrorCode.BOT_REVIEW_NOMINATION_EXISTS);
    }
    const membership = await this.prisma.membership.findUnique({
      select: {
        Role: {
          select: {
            systemName: true,
          },
        },
      },
      where: { id: data.membershipId },
    });
    if (
      !membership ||
      !(
        [
          SystemName.GROUP_OWNER,
          SystemName.GROUP_ADMIN,
          SystemName.GROUP_CONTRIBUTOR,
          SystemName.GROUP_MEMBER,
        ] as SystemName[]
      ).includes(membership.Role.systemName)
    ) {
      throw new ApiException(ErrorCode.BOT_REVIEW_NOMINATION_INVALID);
    }
    return await this.prisma.botReviewNomination.create({
      data: { ...data, startDate: new Date(data.startDate), endDate: new Date(data.endDate) },
    });
  }

  async updateReviewNomination(nominationId: number, data: UpdateReviewNorminateDto) {
    return await this.prisma.botReviewNomination.update({
      where: {
        id: nominationId,
      },
      data: { ...data, startDate: new Date(data.startDate), endDate: new Date(data.endDate) },
    });
  }
  async deleteReviewNomination(nominationId: number) {
    return await this.prisma.botReviewNomination.delete({
      where: {
        id: nominationId,
      },
    });
  }
  /** Verify whether a group membership can be deleted */
  private async verifyDeleteMembership(groupId: number, membershipId: number): Promise<void> {
    const memberships = await this.prisma.membership.findMany({
      include: { Role: true },
      where: { group: { id: groupId } },
    });
    if (memberships.length === 1) throw new ApiException(ErrorCode.CANNOT_DELETE_SOLE_MEMBER);
    const membership = await this.prisma.membership.findUnique({
      include: { Role: true },
      where: { id: membershipId },
    });
    if (!membership) throw new ApiException(ErrorCode.MEMBERSHIP_NOT_FOUND);
    if (
      membership.Role.systemName === SystemName.GROUP_OWNER &&
      memberships.filter((i) => i.Role.systemName === SystemName.GROUP_OWNER).length === 1
    )
      throw new ApiException(ErrorCode.CANNOT_DELETE_SOLE_OWNER);
  }

  private async getEditableRoleList(groupId: number, userReq: AccessTokenParsed): Promise<Role[]> {
    const isRequestBySuperRole = userReq.scopes.some((userScope) =>
      [`group-${groupId}:role-*`, `group-*:role-*`].includes(userScope),
    );
    let editableMembershipRoleList: Role[] = [];
    if (isRequestBySuperRole) {
      editableMembershipRoleList = await this.rolesService.getGroupRoleList(groupId);
    } else {
      editableMembershipRoleList = await this.rolesService.getEditableMembershipRoleList(
        groupId,
        userReq.scopes,
      );
    }
    return editableMembershipRoleList;
  }
  async getLastUsedGroups(userId: number) {
    // get all groupType
    const lastUsedGroups = await this.lastUsedGroupsService.getLastUsedGroups(userId);
    // list all used groups, then sort by lastAccessTime
    //lastUsedGroups: {
    //  "BOT": 10001000,
    //  "FLOW": 10001001,
    //  "INSIGHT": 10001010,
    //  "rankedList": [10001010, 10001000, 10001002, 10001003, 10001004]
    //}
    const groups = {};
    const list = [];
    lastUsedGroups.forEach((item) => {
      groups[item.groupType] = item.lastUsedHistory[0]['groupId'];
      list.push(...item.lastUsedHistory);
    });
    this.logger.log(
      `[MembershipsService][getLastUsedGroups]lastUsedGroups: ${JSON.stringify(list)}`,
    );
    const rankedList = list
      .sort((a, b) => new Date(b.lastAccessTime).getTime() - new Date(a.lastAccessTime).getTime())
      .slice(0, this.configService.get<number>('lastUsedGroups.maxNum'))
      .map((item) => item.groupId);
    return { ...groups, rankedList };
  }
}
