-- AlterTable
ALTER TABLE "FeatureFlag" ADD COLUMN     "isPublic" BOOLEAN DEFAULT false;

-- CreateIndex
CREATE INDEX "FeatureFlag_isForClientSide_idx" ON "FeatureFlag"("isForClientSide");

-- CreateIndex
CREATE INDEX "FeatureFlag_isPublic_idx" ON "FeatureFlag"("isPublic");


UPDATE "FeatureFlag" SET "isPublic" = true WHERE "key" in (
	'ADMIN.CONFIG_UI_THEME_RESOURCES',
	'LOGIN.ENABLE_LOGIN_PAGE_CHECKBOX',
	'AUTH.ENABLE_HKT_SSO_LOGIN',
	'AUTH.ENABLE_USERNAME_PASSWORD_LOGIN'
);