{"name": "hkt-gpt-saas", "template": "staart/api", "version": "4.0.9", "description": "SaaS backend framework with users, payments, APIs, and more", "repository": "**************:staart/api.git", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"preload": "node -e \"import('geolite2-redist').then(geolite => geolite.downloadDbs())\"", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:errors": "eslint . --quiet", "lint:changed": "eslint $(git diff --name-only HEAD | grep -E '\\.(js|jsx|ts)$' | xargs)", "test": "npm run test:unit", "test:watch": "jest --watch --testTimeout 30000", "test:cov": "jest --coverage --forceExit --testTimeout 30000", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand --testTimeout 30000", "test:unit": "jest --forceExit --testTimeout 30000 --testPathPattern=src/ --detectOpenHandles", "test:e2e": "export TEST=true;npx ts-node@10.8.1 tests/test-before.ts && jest --runInBand --config ./tests/jest-e2e.json --forceExit --testTimeout 30000", "prepare": "husky install", "data-migration:local-development": "ts-node prisma/local-development.ts"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@aws-sdk/client-iam": "^3.826.0", "@aws-sdk/client-s3": "^3.828.0", "@aws-sdk/client-sqs": "^3.826.0", "@aws-sdk/lib-storage": "^3.826.0", "@aws-sdk/s3-presigned-post": "^3.826.0", "@aws-sdk/s3-request-presigner": "^3.828.0", "@babel/traverse": "^7.23.2", "@elastic/elasticsearch": "7.13.0", "@golevelup/ts-jest": "^0.5.0", "@googlemaps/google-maps-services-js": "^3.1.14", "@maxmind/geoip2-node": "^3.5.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^3.0.0", "@nestjs/swagger": "^7.0.0", "@octokit/rest": "^18.0.12", "@opentelemetry/api": "^1.6.0", "@opentelemetry/instrumentation": "^0.44.0", "@opentelemetry/instrumentation-express": "^0.33.2", "@opentelemetry/instrumentation-http": "^0.44.0", "@opentelemetry/sdk-trace-node": "^1.17.1", "@prisma/instrumentation": "^5.5.2", "@sentry/node": "^6.0.0", "@sindresorhus/slugify": "^1.1.0", "@slack/web-api": "^6.0.0", "@smithy/smithy-client": "^4.4.3", "@staart/mustache-markdown": "^3.0.3", "@types/bcryptjs": "^2.4.2", "@types/papaparse": "^5.3.7", "aws-elasticsearch-connector": "^9.0.1", "axios": "^0.21.4", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0-rc.5", "circularbuffer": "^0.1.1", "class-transformer": "^0.3.1", "class-validator": "^0.14.0", "cloudinary": "^1.23.0", "crypto-js": "^4.2.0", "crypto-random-string": "^3.3.0", "date-fns": "^2.30.0", "deep-equal": "^2.0.5", "dot-object": "^2.1.4", "elastic-apm-node": "^4.1.0", "exceljs": "^4.4.0", "express-basic-auth": "^1.2.1", "form-data": "^4.0.0", "formdata-node": "^2.4.0", "fs-extra": "^9.0.1", "geolite2-redist": "^3.0.4", "graphql": "^16.8.0", "graphql-request": "^6.1.0", "helmet": "^4.6.0", "hibp": "^10.0.1", "http-aws-es": "^6.0.0", "ioredis": "^5.3.2", "ip-anonymize": "^0.1.0", "ip-range-check": "^0.2.0", "json-schema": "^0.4.0", "jsonpath-plus": "^7.2.0", "jsonwebtoken": "^8.5.1", "maxmind": "^4.3.1", "mem": "^8.0.0", "minimatch": "^3.0.4", "minimist": "^1.2.6", "nestjs-pino": "^3.3.0", "nestjs-redox": "^1.2.2", "nodemailer": "^6.4.17", "normalize-email": "^1.1.1", "nunjucks": "^3.2.2", "openai": "^4.86.1", "openapi-schema-validator": "^12.1.3", "otplib": "^12.0.1", "p-queue": "^6.6.2", "p-retry": "^4.2.0", "papaparse": "^5.4.1", "passport": "^0.4.1", "pidusage": "^2.0.21", "pino-pretty": "^10.2.0", "puppeteer": "^18.1.0", "puppeteer-core": "^19.11.1", "qrcode": "^1.4.4", "quick-lru": "^5.1.1", "randomcolor": "^0.6.2", "rate-limiter-flexible": "^2.4.2", "redoc": "^2.3.0", "reflect-metadata": "^0.1.13", "request-ip": "^2.1.3", "response-time": "^2.3.2", "responselike": "^3.0.0", "rimraf": "^3.0.2", "rotating-file-stream": "^3.1.1", "rxjs": "^7.8.1", "sharp": "^0.33.5", "source-map-support": "^0.5.21", "stripe": "^8.130.0", "swagger-ui-express": "^4.1.6", "twilio": "^3.54.2", "ua-parser-js": "^0.7.23", "uuid": "^8.3.2", "xlsx": "^0.18.5"}, "devDependencies": {"@automock/adapters.nestjs": "^2.1.0", "@automock/jest": "^2.1.0", "@mermaid-js/mermaid-cli": "^10.3.1", "@nestjs/cli": "^9.3.0", "@nestjs/schematics": "^7.2.6", "@nestjs/testing": "^10.0.0", "@prisma/client": "^5.18.0", "@types/bcrypt": "^3.0.0", "@types/cheerio": "^0.22.23", "@types/deep-equal": "^1.0.1", "@types/dot-object": "^2.1.2", "@types/express": "^4.17.9", "@types/fs-extra": "^9.0.6", "@types/jest": "^26.0.20", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^8.5.0", "@types/minimatch": "^3.0.3", "@types/multer": "^1.4.7", "@types/node": "^18.18.7", "@types/nodemailer": "^6.4.0", "@types/nunjucks": "^3.1.3", "@types/passport-local": "^1.0.33", "@types/pidusage": "^2.0.1", "@types/puppeteer": "^5.4.2", "@types/qrcode": "^1.3.5", "@types/randomcolor": "^0.5.5", "@types/request-ip": "0.0.35", "@types/response-time": "^2.3.4", "@types/responselike": "^3.0.0", "@types/supertest": "^2.0.12", "@types/ua-parser-js": "^0.7.35", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "aws-sdk-js-codemod": "^2.4.5", "dotenv": "^8.6.0", "dotenv-expand": "^5.1.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-diff": "^2.0.3", "eslint-plugin-import": "^2.29.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^8.0.3", "jest": "^26.6.3", "jest-mock-extended": "^2.0.4", "lint-staged": "^15.2.0", "moment": "^2.29.4", "prettier": "3.1.1", "prisma": "^5.18.0", "prisma-erd-generator": "^1.11.0", "supertest": "^6.0.1", "ts-jest": "26.5.0", "ts-loader": "^8.0.14", "ts-node": "^10.9.1", "tsconfig-paths": "^3.9.0", "typescript": "^4.7.4", "utility-types": "^3.10.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}}