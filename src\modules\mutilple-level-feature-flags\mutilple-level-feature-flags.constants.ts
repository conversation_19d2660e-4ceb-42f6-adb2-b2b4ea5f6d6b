import { ApiResourceService } from '../api-resource/api-resource.service';
import { MutilpleLevelFeatureFlagsModelDto } from './mutilple-level-feature-flags-model.dto';

export enum FeatureFlagLevels {
  ENV = 1,
  FLOW = 2,
  BOT = 2,
}
type BotFeatureFlagFormatValidatorMapFunc = (
  FeatureFlagsModelDto: MutilpleLevelFeatureFlagsModelDto,
) => void;

export const BotFeatureFlagFormatValidatorMap: {
  [key: string]: BotFeatureFlagFormatValidatorMapFunc;
} = {
  [ApiResourceService.HOST_MAP_FEATURE_FLAGS]:
    ApiResourceService.featureFlagsHostListFormatValidator,
};
