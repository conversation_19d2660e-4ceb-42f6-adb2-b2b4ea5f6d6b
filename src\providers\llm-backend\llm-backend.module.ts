import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ChatSessionsModule } from 'src/modules/chat-sessions/chat-sessions.module';
import { ElasticSearchModule } from '../elasticsearch/elasticsearch.module';
import { S3Module } from '../s3/s3.module';
import { LLMBackendService } from './llm-backend.service';
import { BotSecurityModule } from 'src/modules/bot-security/bot-security.module';
import { GroupsModule } from 'src/modules/groups/groups.module';
import { UsersModule } from 'src/modules/users/users.module';
import { PrismaModule } from '../prisma/prisma.module';
import { LLMModelsModule } from 'src/modules/llm-models/llm-models.module';
import { ChatFilesModule } from 'src/modules/chat-files/chat-files.module';

@Module({
  imports: [
    ConfigModule,
    S3Module,
    forwardRef(() => ElasticSearchModule),
    ChatSessionsModule,
    forwardRef(() => LLMModelsModule),
    BotSecurityModule,
    forwardRef(() => GroupsModule),
    UsersModule,
    PrismaModule,
    ConfigModule,
    S3Module,
    forwardRef(() => ElasticSearchModule),
    ChatSessionsModule,
    forwardRef(() => LLMModelsModule),
    ChatFilesModule,
    forwardRef(() => ChatFilesModule),
  ],
  providers: [LLMBackendService],
  exports: [LLMBackendService],
})
export class LLMBackendModule {}
