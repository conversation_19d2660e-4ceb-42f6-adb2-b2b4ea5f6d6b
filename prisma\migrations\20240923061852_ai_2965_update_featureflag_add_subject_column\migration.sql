-- AlterTable
ALTER TABLE "AlertHistory" ADD COLUMN "subject" TEXT;

-- update feature flag, add subject
update "FeatureFlag" set "metaData" = (jsonb_build_object('percentage',jsonb_build_array('75','90','100'),'emailSubject','[{groupName}] LLM Monthly Token Limit Alert in [{env}]','emailContent','Dear <PERSON><PERSON> Owner and Admin,<br/><br/>I hope this email captures your attention. We have monitored that the Actual Usage Ratio of your Bot [bot id:{groupId}, {groupName}] Monthly LLM Token in [{env}] is high as follows:<br/><br/> <table border="1" width="100%" cellspacing="0"><tr style="text-align: center"><th>LLM Model</th><th>Env</th><th>Actual Usage Ratio</th><th>Model Plan Quota</th></tr>{tr}</table>If it continues, it may cause some chat sessions or some functions to not respond normally. For more monthly token limit info, please see: <a href="https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/Bot-Plan-and-Subscription.aspx?xsdata=MDV8MDJ8fDg0OTY5ZmE5NzBjZDQwMWYyNGU4MDhkY2NiZjY2Y2YxfGM1OTI0ZGE2ZGViMzQyMWJhYTk4NTdiY2JhMGJhMDUwfDB8MHw2Mzg2MDk1MDY0Mjk5MDQxMDR8VW5rbm93bnxWR1ZoYlhOVFpXTjFjbWwwZVZObGNuWnBZMlY4ZXlKV0lqb2lNQzR3TGpBd01EQWlMQ0pRSWpvaVYybHVNeklpTENKQlRpSTZJazkwYUdWeUlpd2lWMVFpT2pFeGZRPT18MXxMMk5vWVhSekx6RTVPamN3Wm1RM1lXRTNPRGs1TURRM09UZzRZMkl5T0RReE5UYzFNR1l5T1RVNFFIUm9jbVZoWkM1Mk1pOXRaWE56WVdkbGN5OHhOekkxTXpVek9EUXlOREU1fDIzMWI5NzcxNzBkYjQwNzkyNGU4MDhkY2NiZjY2Y2YxfDIxNzVlNTc0OTczOTQyODg4YzVlNDU2MzE5YmFlMmU1&sdata=TTA0ays2L25uS1JTa3JhM2VwZXZtMDh1NndZZkpKZUpWR0sxMDZkZHV4ST0%3D&ovuser=c5924da6-deb3-421b-aa98-57bcba0ba050%2C82007479%40corphq.hk.pccw.com&OR=Teams-HL&CT=1725354982326&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiI0OS8yNDA3MTEyODgyNSIsIkhhc0ZlZGVyYXRlZFVzZXIiOmZhbHNlfQ%3D%3D#how-to-upgrade-bot-plan"> Bot’s Monthly Token Limit </a><br/><br/><br/><h3>If it exceeds, </h3><p style="text-indent: 2em">Error Message: The system returns " [400-994-1] Bot monthly token limit exceeded." error message in playground.</p><h3>Recommended Action after exceeding: </h3><p style="text-indent: 2em">Promptly follow our <a href="https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/Bot%20Token%20&%20Rate%20Limit.aspx#bot%E2%80%99s-monthly-token-limit"> user guide </a> to apply for monthly token increasing and solve this issue.</p><br/>Thank you for your attention and understanding!<br/><br/>Best regards,<br/><br/>Gen AI Application Support <br/><br/>'))
WHERE "key" = 'ALERT.CONFIG_MONTHLY_TOKEN_LIMIT_SEND_ALERT_CONDITION';
