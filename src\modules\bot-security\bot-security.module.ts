import { Module, forwardRef } from '@nestjs/common';
import { BotSecurityService } from './bot-security.service';
import { BotSecurityController } from './bot-security.controller';
import { PrismaModule } from 'src/providers/prisma/prisma.module';
import { GroupsModule } from '../groups/groups.module';
import { ConfigModule } from '@nestjs/config';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';

@Module({
  //maybe group and botSecurity will Circular dependency,so use forwardRef
  imports: [PrismaModule, forwardRef(() => GroupsModule), ConfigModule,FeatureFlagModule],
  controllers: [BotSecurityController],
  providers: [BotSecurityService],
  exports: [BotSecurityService],
})
export class BotSecurityModule {}
