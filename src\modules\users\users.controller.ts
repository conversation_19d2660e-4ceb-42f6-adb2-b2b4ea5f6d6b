import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UploadedFiles,
  UseInterceptors,
  ValidationPipe,
  UsePipes,
  Logger,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { User, UserBookmarkEntityType, VerificationType } from '@prisma/client';
import { OptionalIntPipe } from '../../pipes/optional-int.pipe';
import { OrderByPipe } from '../../pipes/order-by.pipe';
import { WherePipe } from '../../pipes/where.pipe';
import { Expose } from '../../providers/prisma/prisma.interface';
import { UserRequest } from '../auth/auth.interface';
import { Scopes } from '../auth/scope.decorator';
import {
  ActivateAccountRecordBatchDto,
  ActivateUserDTO,
  ChangePasswordDto,
  CreateUserDTO,
  CreateUserRecordBatchDto,
  DeleteUserRecordBatchDto,
  PublicUserWhereDto,
  ResendActivateCodeDTO,
  SubscribeUserResourcePlanDto,
  UpdateUserDto,
  UpdateUserProfleDto,
  UpdateUserRecordBatchDto,
  UserProfileCompletenessRes,
} from './users.dto';
import { UsersService } from './users.service';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { Readable } from 'stream';
import papa from 'papaparse';
import { Response } from 'express';
import { Public } from '../auth/public.decorator';
import { FeatureFlag } from '../feature-flags/feature-flags.decorator';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { CronJobApiKeyScopes } from 'src/constants/cron-job';
import { UserBookmarkService } from '../userBookmark/user-bookmark.service';
import { UserBookmarkDto } from '../userBookmark/user-bookmark.dto';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { NewWherePipe } from 'src/pipes/new-where.pipe';

@Controller('users')
@ApiBearerAuth('bearer-auth')
@ApiTags('Users')
export class UserController {
  private logger = new Logger(UserController.name);
  constructor(
    private usersService: UsersService,
    private readonly userBookmarkService: UserBookmarkService,
  ) {}

  @Get('activate')
  @Public()
  async validateActivateCode(@Query('code') activateCode: string) {
    const isValid = await this.usersService.validateActivateCode(
      activateCode,
      VerificationType.ACTIVATE_USER,
    );
    return {
      isValid,
    };
  }

  @Post('activate')
  @Public()
  async activateUser(@Body() data: ActivateUserDTO) {
    return await this.usersService.activateUser(data);
  }

  @Patch('activateCode/resend')
  @Scopes('user-*:write-info')
  async resendActivateCode(@Req() request: UserRequest, @Body() data: ResendActivateCodeDTO) {
    return await this.usersService.resendActivateCode(request, data);
  }

  /** Get users */
  @Get()
  @Scopes('user-*:read-info')
  async getAll(
    @Query('from') dateFrom: string,
    @Query('to') dateTo: string,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', NewWherePipe) where?: PublicUserWhereDto,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<{ list: User[]; count: number }> {
    const list = await this.usersService.getUsers({
      dateFrom,
      dateTo,
      skip,
      take,
      orderBy,
      where,
    });
    const count = await this.usersService.getUserCount(where);
    return { list, count };
  }

  @Post('batch')
  @Scopes('user-*:write-info')
  @AuditLog('create-users-batch')
  async createBatch(@Req() request: UserRequest, @Body() data: CreateUserRecordBatchDto) {
    const { users } = data;
    const successList = [];
    const failureList = [];
    for (const user of users) {
      try {
        this.usersService.checkBatchCreateUserInput(user);
        await this.usersService.createUser(user, request);
        successList.push(user);
      } catch (err) {
        this.logger.error(err, `Failed to insert user: ', ${user.email} `);
        failureList.push({
          ...user,
          errMsg:
            (err as any)?.response?.error?.message ??
            'Internal Server Error. Please try to fix input and re-upload again.',
        });
      }
    }
    return {
      successList,
      failureList,
    };
  }

  @Patch('batch')
  @Scopes('user-*:write-info')
  @AuditLog('update-users-batch')
  async updateBatch(@Body() data: UpdateUserRecordBatchDto) {
    const { users } = data;
    const successList = [];
    const failureList = [];
    for (const user of users) {
      try {
        this.usersService.checkBatchCreateUserInput(user);
        await this.usersService.updateUserByEmailAndLoginType(user);
        successList.push(user);
      } catch (err) {
        this.logger.error(err, `Failed to update user by batch: ', ${user.email} `);
        failureList.push({
          ...user,
          errMsg:
            (err as any)?.response?.error?.message ??
            'Internal Server Error. Please try to fix input and re-upload again.',
        });
      }
    }
    return {
      successList,
      failureList,
    };
  }

  @Patch('batch/activate')
  @Scopes('user-*:write-info')
  @AuditLog('update-users-batch')
  async activateBatch(@Body() data: ActivateAccountRecordBatchDto) {
    const { users } = data;
    const successList = [];
    const failureList = [];
    for (const user of users) {
      try {
        await this.usersService.activateUserByEmailAndLoginType(user);
        successList.push(user);
      } catch (err) {
        this.logger.error(err, `Failed to activate/deactivate user by batch: ', ${user.email} `);
        failureList.push({
          ...user,
          errMsg:
            (err as any)?.response?.error?.message ??
            'Internal Server Error. Please try to fix input and re-upload again.',
        });
      }
    }
    return {
      successList,
      failureList,
    };
  }

  @Delete('batch')
  @Scopes('user-*:write-info')
  @FeatureFlag(FeatureFlagKey.ENABLE_TRUE_DELETE_USER)
  @AuditLog('delete-users-batch')
  async deleteBatch(@Body() data: DeleteUserRecordBatchDto) {
    const { users } = data;
    const successList = [];
    const failureList = [];
    for (const user of users) {
      try {
        await this.usersService.deleteUserByEmailAndNameAndLoginType(
          user.email,
          user.name,
          user.loginType,
        );
        successList.push(user);
      } catch (err) {
        this.logger.error(err, `Failed to delete user by batch: ', ${user.email} `);
        failureList.push({
          ...user,
          errMsg: (err as any)?.response?.error?.message ?? 'Internal Server Error',
        });
      }
    }
    return {
      successList,
      failureList,
    };
  }

  /** Get a user */
  @Get(':userId')
  @Scopes('user-{userId}:read-info')
  async get(@Param('userId', ParseIntPipe) id: number): Promise<Expose<User>> {
    return this.usersService.getUser(id);
  }

  @Get(':userId/profile/completeness')
  @Scopes('user-{userId}:read-info')
  async getCompletenessStatus(
    @Param('userId', ParseIntPipe) id: number,
  ): Promise<UserProfileCompletenessRes> {
    const completeness = await this.usersService.getUserCompleteness(id);
    return { completeness };
  }

  /** Insert a user */
  @Post()
  @AuditLog('insert-user')
  @Scopes('user-*:write-info')
  async insert(@Req() request: UserRequest, @Body() data: CreateUserDTO): Promise<Expose<User>> {
    return this.usersService.createUser(data, request);
  }

  /** Update profile of a user */
  @Patch(':userId/profile')
  @AuditLog('update-user')
  @Scopes('user-{userId}:write-info')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  @UseInterceptors(FilesInterceptor('files'))
  async updateUserInfo(
    @UploadedFiles() files: Array<Express.Multer.File>,
    @Param('userId', ParseIntPipe) id: number,
    @Body() data: UpdateUserProfleDto,
  ): Promise<Expose<User>> {
    return this.usersService.updateUserProfile(id, data, files);
  }

  /** Update a user */
  @Patch(':userId')
  @AuditLog('update-user')
  @Scopes('user-*:write-info')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async update(
    @Param('userId', ParseIntPipe) id: number,
    @Body() data: UpdateUserDto,
  ): Promise<Expose<User>> {
    return this.usersService.updateUser(id, data);
  }

  /** Delete a user */
  @Delete(':userId')
  @Scopes('user-*:write-info')
  @FeatureFlag(FeatureFlagKey.ENABLE_TRUE_DELETE_USER)
  async remove(@Param('userId', ParseIntPipe) id: number): Promise<Expose<User>> {
    return this.usersService.deleteUser(id);
  }

  @Get('batch/template')
  @Scopes('user-*:read-info')
  async getTemplate(@Res() res: Response) {
    const csvData = await this.usersService.getBatchUploadFileTemplate();
    const csvString = papa.unparse(csvData, { header: true });
    const stream = Readable.from(csvString);
    stream.pipe(res);
  }

  /** Force clear user cache */
  @Patch(':userId/clear-cache')
  @Scopes('user-{userId}:clear-cache')
  async clearCache(@Param('userId', ParseIntPipe) id: number) {
    return await this.usersService.clearUserCache(id);
  }

  @Post(':userId/change-password')
  @Scopes('user-{userId}:write-info')
  @FeatureFlag(FeatureFlagKey.ENABLE_USERNAME_PASSWORD_LOGIN)
  async changePassword(
    @Param('userId', ParseIntPipe) userId: number,
    @Body() data: ChangePasswordDto,
  ) {
    return await this.usersService.changePassword(data, userId);
  }

  @Get(':userId/resources/category')
  @Scopes('system:read-resource-plan')
  async getUserResourceCategory() {
    return await this.usersService.getUserResourceCategory();
  }

  @Get(':userId/resources')
  @Scopes('system:read-resource-plan')
  async getUserResource(@Param('userId', ParseIntPipe) userId: number) {
    return await this.usersService.getUserResources(userId);
  }

  @Get(':userId/resources/plans')
  @Scopes('system:read-resource-plan')
  async getUserResourcePlan(@Param('userId', ParseIntPipe) id: number) {
    return await this.usersService.getUserResourcePlans(id);
  }

  @Patch(':userId/resources/plans/subscribe')
  @AuditLog('subscribe-user-resource-plan')
  @Scopes('system:operate-resource-plan')
  async subscribeUserResourcePlan(
    @Param('userId', ParseIntPipe) id: number,
    @Body() data: SubscribeUserResourcePlanDto,
  ) {
    return await this.usersService.subscribeUserResourcePlans(id, data.subscribedPlanIds);
  }

  @Get(':userId/bookmark')
  @Scopes('user-{userId}:read-bookmark')
  async getUserBookmarked(@Param('userId', ParseIntPipe) userId: number) {
    return await this.userBookmarkService.getUserBookmarkedList(userId);
  }

  @Get(':userId/bookmark-page')
  @Scopes('user-{userId}:read-bookmark')
  async getBookmarkedGroup(
    @Param('userId', ParseIntPipe) userId: number,
    @Query('type') type: UserBookmarkEntityType,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    return await this.userBookmarkService.getUserBookmarkedWithType(
      userId,
      type,
      skip,
      take,
      orderBy,
    );
  }

  @Post(':userId/bookmark')
  @Scopes('user-{userId}:write-bookmark')
  async userBookmarkEntity(
    @Param('userId', ParseIntPipe) userId: number,
    @Body() data: UserBookmarkDto,
  ) {
    const bookmarked = await this.userBookmarkService.bookmarkedEntity(
      userId,
      data.entityId,
      data.entityType,
    );
    return bookmarked;
  }

  @Delete(':userId/bookmark/:bookmarkId')
  @Scopes('user-{userId}:write-bookmark')
  async removeBookmarkedGroup(
    @Param('userId', ParseIntPipe) userId: number,
    @Param('bookmarkId', ParseIntPipe) bookmarkId: number,
  ) {
    const removedBookmark = this.userBookmarkService.removeBookmark(bookmarkId, userId);
    return removedBookmark;
  }

  @Post('/deactivateEmailReminder')
  @Scopes(CronJobApiKeyScopes.SEND_DEACTIVATION_EMAIL_REMINDER)
  public async sendDeactivateEmail(@Body() body: { reminders }) {
    return await this.usersService.sendDeactivateEmailReminder(body.reminders);
  }

  /** get user avatar */
  @Get(':userId/avatar/:avatarUserId')
  @Scopes('user-{userId}:read-info')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async getUserAvatar(
    @Res() response: Response,
    @Param('avatarUserId', ParseIntPipe) avatarUserId: number,
  ) {
    const s3Response = await this.usersService.getUserAvatar(avatarUserId);
    // fileRequest
    //   .on('httpHeaders', (statusCode, headers) => {
    //     const filename = Buffer.from(headers['x-amz-meta-filename'], 'base64').toString('utf-8');

    //     response.setHeader(
    //       'Content-Disposition',
    //       `attachment; filename*=UTF-8''${encodeURIComponent(filename)}`,
    //     );
    //     response.setHeader('Content-Type', headers['content-type']);
    //     response.setHeader('Content-Length', headers['content-length']);
    //   })
    //   .on('error', (err) => {
    //     this.logger.error(err, '[user][getUserAvatar]Failed to get user avatar');
    //     response.status(404).json(new ApiException(ErrorCode.FIlE_NOT_FOUND).getResponse());
    //   });

    // fileRequest.createReadStream().pipe(response);

    // 设置响应头
      if (s3Response.Metadata?.filename) {
        const filename = Buffer.from(s3Response.Metadata.filename, 'base64').toString('utf-8');
        response.setHeader(
          'Content-Disposition',
          `attachment; filename*=UTF-8''${encodeURIComponent(filename)}`,
        );
      }
      
      if (s3Response.ContentType) {
        response.setHeader('Content-Type', s3Response.ContentType);
      }
      
      if (s3Response.ContentLength) {
        response.setHeader('Content-Length', s3Response.ContentLength);
      }
      
      // 在 AWS SDK v3 中，Body 已经是一个 Readable 流
      if (s3Response.Body) {
        // 直接将 Body 流管道连接到响应
        (s3Response.Body as Readable).pipe(response);
      } else {
        response.status(404).json(new ApiException(ErrorCode.FIlE_NOT_FOUND).getResponse());
      }
    } catch (err) {
      this.logger.error(err, '[user][getUserAvatar]Failed to get user avatar');
      response.status(404).json(new ApiException(ErrorCode.FIlE_NOT_FOUND).getResponse());
    }
  }
}
