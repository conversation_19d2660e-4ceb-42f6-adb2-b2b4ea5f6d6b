import { Injectable, Logger } from '@nestjs/common';
import { FeatureFlagOverride, FeatureFlagTargetType, GroupType, Prisma } from '@prisma/client';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { FEATURE_FLAG_TYPE } from 'src/providers/redis/redis.constants';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { UserRequest } from '../auth/auth.interface';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { GroupsService } from '../groups/groups.service';
import { MutilpleLevelFeatureFlagsModelDto } from './mutilple-level-feature-flags-model.dto';
import {
  BotFeatureFlagFormatValidatorMap,
  FeatureFlagLevels,
} from './mutilple-level-feature-flags.constants';
import { UpsertBotFeatFlagsDto } from './upsertBotFeatFlags.dto';
@Injectable()
export class MutilpleLevelFeatureFlagsService {
  private logger = new Logger(MutilpleLevelFeatureFlagsService.name);
  constructor(
    private prisma: PrismaService,
    private featureFlagService: FeatureFlagService,
    private groupsService: GroupsService,
  ) {}

  async getAll(
    skip?: number,
    take?: number,
    where?: Record<string, number | string>,
    orderBy?: Record<string, 'asc' | 'desc'>,
    keys?: string[],
  ) {
    let newWhere: Prisma.FeatureFlagOverrideWhereInput = this.ORConditionCheck(where);
    if (keys) {
      newWhere = {
        ...newWhere,
        featureFlag: {
          key: {
            in: keys,
          },
        },
      };
    }
    const featureFlagOverrideList = await this.prisma.featureFlagOverride.findMany({
      skip,
      take,
      where: newWhere,
      orderBy,
      include: {
        featureFlag: {
          select: { key: true },
        },
        updatedBy: {
          select: { name: true },
        },
        createdBy: {
          select: { name: true },
        },
      },
    });
    return featureFlagOverrideList.map((featureFlagOverride) =>
      this.convertMetaDataToString(featureFlagOverride),
    );
  }

  //(will not affect existing where format)if want to trigger fuzzy search on multiple fields
  // where field needs to be in this format: where={fieldA}|{fieldB}: contains {fuzzy search content}
  ORConditionCheck(where: Record<string, number | string>) {
    // if it does not exist ,returns an empty object
    if (!where) {
      return {};
    }
    //List all Keys
    const whereKeys = Object.keys(where);
    const orWhereList = [];

    whereKeys.forEach((key) => {
      if (key.includes('|')) {
        // first isolate the detected Key from other Keys
        const { [key]: keyValue, ...otherWhere } = where;
        //split keys and combine conditions
        const fieldList = key.split('|');
        fieldList.forEach((field) => {
          if (field == 'key') {
            orWhereList.push({ featureFlag: { [field]: keyValue } });
          } else if (field && keyValue) {
            orWhereList.push({ [field]: keyValue });
          }
        });
        //remove the detected Key
        where = otherWhere;
      }
    });

    let newWhere: Prisma.FeatureFlagOverrideWhereInput;
    if (orWhereList.length > 0) {
      newWhere = {
        ...where,
        OR: orWhereList,
      };
    } else {
      newWhere = where;
    }

    return newWhere;
  }

  async existedFeatureFlagOverride(
    targetType: FeatureFlagTargetType,
    targetValue: string,
    featureFlagId: number,
  ): Promise<boolean> {
    const exist = await this.prisma.featureFlagOverride.findFirst({
      select: {
        id: true,
      },
      where: {
        targetType,
        targetValue,
        featureFlagId,
      },
    });
    return exist ? true : false;
  }

  async getCount(where?: Record<string, number | string>, keys?: string[]) {
    let newWhere: Prisma.FeatureFlagOverrideWhereInput = this.ORConditionCheck(where);
    if (keys) {
      newWhere = {
        ...newWhere,
        featureFlag: {
          key: {
            in: keys,
          },
        },
      };
    }
    return await this.prisma.featureFlagOverride.count({ where: newWhere });
  }

  async create(data: MutilpleLevelFeatureFlagsModelDto) {
    const exist = await this.existedFeatureFlagOverride(
      data.targetType,
      data.targetValue,
      data.featureFlagId,
    );
    if (exist) {
      throw new ApiException(ErrorCode.FEATURE_FLAGS_DUPLICATE);
    }
    const featureFlagOverride = await this.prisma.featureFlagOverride.create({
      data: {
        isEnabled: data.isEnabled,
        metaData: data.metaData,
        value: data.value,
        targetType: data.targetType,
        targetValue: data.targetValue,
        featureFlagId: data.featureFlagId,
        updatedByUserId: data.updatedByUserId,
        level: data.level,
        createdByUserId: data.createdByUserId,
        updatedAt: new Date(),
      },
      include: {
        featureFlag: {
          select: {
            key: true,
            description: true,
          },
        },
        updatedBy: {
          select: { name: true },
        },
        createdBy: {
          select: { name: true },
        },
      },
    });
    if (featureFlagOverride) {
      await this.deleteOverrideFeatureFlagCache(
        featureFlagOverride.targetType,
        featureFlagOverride.targetValue,
        featureFlagOverride.featureFlag.key,
      );
    }
    return featureFlagOverride;
  }

  async update(id: number, data: MutilpleLevelFeatureFlagsModelDto) {
    const featureFlagOverride = await this.prisma.featureFlagOverride.update({
      where: { id },
      data: {
        targetType: data.targetType,
        targetValue: data.targetValue,
        value: data.value,
        metaData: data.metaData,
        isEnabled: data.isEnabled,
        featureFlagId: data.featureFlagId,
        updatedByUserId: data.updatedByUserId,
        updatedAt: new Date(),
      },
      include: {
        featureFlag: {
          select: {
            key: true,
            description: true,
          },
        },
        updatedBy: {
          select: { name: true },
        },
        createdBy: {
          select: { name: true },
        },
      },
    });
    if (featureFlagOverride) {
      await this.deleteOverrideFeatureFlagCache(
        featureFlagOverride.targetType,
        featureFlagOverride.targetValue,
        featureFlagOverride.featureFlag.key,
      );
    }
    return featureFlagOverride;
  }

  //delete override feature flag cache
  //cache key , **:KEYS and **:KEY:{KEY} (decide by featureFlagKey if available)
  async deleteOverrideFeatureFlagCache(
    targetType: FeatureFlagTargetType,
    targetValue: string,
    featureFlagKey?: string,
  ) {
    let featureFlagTargetType = null;
    if (targetType === FeatureFlagTargetType.ENV) {
      featureFlagTargetType = FEATURE_FLAG_TYPE.ENV;
    } else {
      featureFlagTargetType = FEATURE_FLAG_TYPE.GROUP;
    }
    await this.featureFlagService.deleteFeatureFlagCache(featureFlagTargetType, null, targetValue);
    if (featureFlagKey) {
      await this.featureFlagService.deleteFeatureFlagCache(
        featureFlagTargetType,
        featureFlagKey,
        targetValue,
      );
    }
  }

  async delete(id: number) {
    const featureFlagOverride = await this.prisma.featureFlagOverride.delete({
      where: { id },
      include: {
        featureFlag: {
          select: {
            key: true,
            description: true,
          },
        },
      },
    });
    if (featureFlagOverride) {
      await this.deleteOverrideFeatureFlagCache(
        featureFlagOverride.targetType,
        featureFlagOverride.targetValue,
        featureFlagOverride.featureFlag.key,
      );
    }
    return featureFlagOverride;
  }

  async getAllFeatureFlagKeys(keys?: string[]) {
    let where = undefined;
    if (keys) {
      where = {
        key: {
          in: keys,
        },
      };
    }
    return await this.prisma.featureFlag.findMany({
      select: {
        id: true,
        key: true,
      },
      orderBy: {
        key: 'asc',
      },
      where,
    });
  }

  async getById(id: number, includeFeatureFlag?: boolean) {
    const featureFlagOverride = await this.prisma.featureFlagOverride.findUnique({
      where: {
        id,
      },
      include: {
        featureFlag: includeFeatureFlag ? true : undefined,
      },
    });
    return this.convertMetaDataToString(featureFlagOverride);
  }

  async getGroupLevelFeatureKeys(groupId: number) {
    const group = await this.groupsService.getGroup(groupId, {});
    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    const flag = await this.featureFlagService.getOne(
      group.groupType === GroupType.BOT
        ? FeatureFlagKey.BOT_LEVEL_CONTROL_KEY
        : FeatureFlagKey.FLOW_LEVEL_CONTROL_KEY,
    );
    const metaData = flag.metaData ? flag.metaData : undefined;
    return metaData['value'] as Array<string>;
  }

  private convertMetaDataToString(featureFlag: FeatureFlagOverride): FeatureFlagOverride {
    featureFlag.metaData = JSON.stringify(featureFlag.metaData);
    return featureFlag;
  }

  async upsertGroupLevelFeature(
    userReq: UserRequest,
    groupId: number,
    upsertBotFeatFlagsDto: UpsertBotFeatFlagsDto,
  ) {
    const group = await this.groupsService.getGroup(groupId, {});
    const keys = await this.getGroupLevelFeatureKeys(groupId);
    const checkGroupLevelFeatureCanEdit = keys.includes(upsertBotFeatFlagsDto.key);
    if (!checkGroupLevelFeatureCanEdit) {
      throw new ApiException(ErrorCode.FEATURE_FLAGS_NOT_OVERRIDE);
    }

    let targetType: FeatureFlagTargetType = FeatureFlagTargetType.BOT;
    if (group.groupType === GroupType.FLOW) {
      targetType = FeatureFlagTargetType.FLOW;
    }
    const mutilpleLevelFeatureFlagsModelDto: MutilpleLevelFeatureFlagsModelDto = {
      ...upsertBotFeatFlagsDto,
      targetType,
      targetValue: groupId.toString(),
      level: FeatureFlagLevels[targetType],
      value: '',
      featureFlagId: 0,
      updatedAt: new Date(),
      updatedByUserId: userReq.user.id,
    };
    const featureFlag = await this.prisma.featureFlag.findFirst({
      include: {
        featureFlagOverrides: {
          where: {
            targetValue: groupId.toString(),
            targetType,
          },
        },
      },
      where: {
        key: upsertBotFeatFlagsDto.key,
      },
    });
    if (!featureFlag) {
      throw new ApiException(ErrorCode.FEATURE_FLAG_NOT_FOUND);
    }
    if (BotFeatureFlagFormatValidatorMap[upsertBotFeatFlagsDto.key]) {
      BotFeatureFlagFormatValidatorMap[upsertBotFeatFlagsDto.key](
        mutilpleLevelFeatureFlagsModelDto,
      );
    }
    mutilpleLevelFeatureFlagsModelDto.featureFlagId = featureFlag.id;
    if (featureFlag.featureFlagOverrides.length > 0) {
      return await this.update(
        featureFlag.featureFlagOverrides[0].id,
        mutilpleLevelFeatureFlagsModelDto,
      );
    }
    mutilpleLevelFeatureFlagsModelDto.createdByUserId = userReq.user.id;
    return await this.create(mutilpleLevelFeatureFlagsModelDto);
  }
}
