import { CanActivate, ExecutionContext, Injectable, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { FEATURE_FLAG } from './feature-flags.constants';
import { FeatureFlagService } from './feature-flags.service';
import { UserRequest } from '../auth/auth.interface';

@Injectable()
export class FeatureFlagGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private featureFlagService: FeatureFlagService,
  ) {}

  private readonly logger = new Logger(FeatureFlagGuard.name);

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const featureFlags = this.reflector.get<string[]>(FEATURE_FLAG, context.getHandler());
      if (!featureFlags) return true;
      const request = context.switchToHttp().getRequest<UserRequest>();
      const groupId = request.user.groupId;
      for (const featureFlag of featureFlags) {
        const isEnabled = (
          await this.featureFlagService.getOverrideFeatureFlagOrDefault(groupId, featureFlag)
        )?.isEnabled;
        if (isEnabled) return true;
      }
      return false;
    } catch (err) {
      this.logger.error(err, 'failed to validate feature flag');
      return false;
    }
  }
}
