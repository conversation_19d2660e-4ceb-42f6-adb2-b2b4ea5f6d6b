-- This is an empty migration.
DELETE FROM public."FeatureFlag"
WHERE "key" in ('ENABLE_LOCAL_LOGIN',
                'ENABLE_DELETE_USER',
                'ENABLE_DELETE_BOT',
                'REQUIRE_SECONDARY_FILE_APPROVAL_ENVS',
                'MAX_CHAT_HISTORY_PER_CHAT_SESSION');

UPDATE public."FeatureFlag"
SET "metaData" = jsonb_build_object('value', "value"), "value" = ''
WHERE "FeatureFlag"."key" in ('BOT.BOT_CREATE_DEFAULT_LLM_ENGINE_SLUG_TEST',
                              'BOT.BOT_CREATE_DEFAULT_LLM_ENGINE_SLUG_PROD');
