import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { FlowBackendService } from './flow-backend.service';
import { BotSecurityModule } from '../../modules/bot-security/bot-security.module';

@Module({
  imports: [
    ConfigModule, 
    forwardRef(() => BotSecurityModule),
  ],
  providers: [FlowBackendService],
  exports: [FlowBackendService],
})
export class FlowBackendModule {}
