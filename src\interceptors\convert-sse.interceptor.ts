import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, Logger, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
@Injectable()
export class ConvertSseInterceptor implements NestInterceptor {
  private readonly logger = new Logger(ConvertSseInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler<any>): Observable<any> {
    const response = context.switchToHttp().getResponse();
    const request = context.switchToHttp().getRequest();
    const isStream = request.body?.overrides?.stream || request.body?.stream || false;
    if (isStream) {
      response.set({
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        'Content-Type': 'text/event-stream',
      });

      response.on('close', () => {
        response.destroy();
      });
    }
    return next.handle();
  }
}
