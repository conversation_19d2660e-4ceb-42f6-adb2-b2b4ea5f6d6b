-- INSERT INTO "User" ("name", "updatedAt", "active") 
-- SELECT "WhiteList"."email", CURRENT_TIMESTAMP, true FROM "WhiteList" LEFT JOIN 
-- "Email" ON "Email"."emailSafe" = "WhiteList"."email" WHERE "emailSafe" IS NULL;


-- INSERT INTO "Email" ("email", "emailSafe", "updatedAt", "userId", "isVerified") 
-- SELECT "User".name, "User".name, CURRENT_TIMESTAMP, "User".id , true
-- FROM "User" LEFT JOIN "Email" ON "Email"."userId" = "User".id
-- WHERE "userId" IS NULL; 
