import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { LLMBackendModule } from 'src/providers/llm-backend/llm-backend.module';
import { FlowBackendModule } from '../../providers/flow-backend/flow-backend.module';
import { MailModule } from '../../providers/mail/mail.module';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { S3Module } from '../../providers/s3/s3.module';
import { TokensModule } from '../../providers/tokens/tokens.module';
import { ApiKeysModule } from '../api-keys/api-keys.module';
import { AuthModule } from '../auth/auth.module';
import { FlowsModule } from '../flows/flows.module';
import { GroupsModule } from '../groups/groups.module';
import { LLMModelsModule } from '../llm-models/llm-models.module';
import { WikijsModule } from '../wikijs/wikijs.module';
import { GroupMembershipController } from './memberships-group.controller';
import { UserMembershipController } from './memberships-user.controller';
import { MembershipsService } from './memberships.service';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';
import { RedisModule } from 'src/providers/redis/redis.module';
import { BotSecurityModule } from '../bot-security/bot-security.module';
import { ScopeModule } from '../scope/scope.module';
import { LlmEnginesModule } from '../llm-engines/llm-engines.module';
import { RolesModule } from '../roles/roles.module';
import { LastUsedGroupsModule } from '../../providers/last-used-groups/last-used-groups.module';

@Module({
  imports: [
    ScopeModule,
    PrismaModule,
    MailModule,
    ConfigModule,
    AuthModule,
    GroupsModule,
    ApiKeysModule,
    TokensModule,
    S3Module,
    WikijsModule,
    FlowBackendModule,
    FlowsModule,
    LLMBackendModule,
    LLMModelsModule,
    FeatureFlagModule,
    RedisModule,
    BotSecurityModule,
    LlmEnginesModule,
    forwardRef(() => RolesModule),
    LastUsedGroupsModule,
  ],
  controllers: [UserMembershipController, GroupMembershipController],
  providers: [MembershipsService],
})
export class MembershipsModule {}
