-- AlterEnum
ALTER TYPE "LabelEntityType" ADD VALUE 'MODEL_FILE';

-- AlterEnum
ALTER TYPE "LabelType" ADD VALUE 'TAG';

-- CreateTable
CREATE TABLE "ModelFileLabelHistory" (
    "id" SERIAL NOT NULL,
    "groupId" INTEGER NOT NULL,
    "modelFileId" INTEGER NOT NULL,
    "fileHash" TEXT NOT NULL,
    "model" TEXT NOT NULL,
    "prompt" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "createdBy" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ModelFileLabelHistory_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ModelFileLabelHistory_groupId_modelFileId_fileHash_prompt_idx" ON "ModelFileLabelHistory"("groupId", "modelFileId", "fileHash", "prompt");
