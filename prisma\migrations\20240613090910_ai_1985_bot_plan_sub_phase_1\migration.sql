-- CreateTable
CREATE TABLE "ResourceQuota" (
    "id" SERIAL NOT NULL,
    "quotaKey" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "quotaType" TEXT NOT NULL,
    "quotaValue" INTEGER NOT NULL,

    CONSTRAINT "ResourceQuota_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PlanQuota" (
    "planId" INTEGER NOT NULL,
    "quotaId" INTEGER NOT NULL,

    CONSTRAINT "PlanQuota_pkey" PRIMARY KEY ("planId","quotaId")
);

-- CreateIndex
CREATE UNIQUE INDEX "ResourceQuota_quotaKey_key" ON "ResourceQuota"("quotaKey");

-- CreateIndex
CREATE INDEX "plan_quota_index" ON "PlanQuota"("quotaId");


UPDATE "FeatureFlag" f SET "metaData" = '{"flow":"disable-flow","llm-engine-gpt-35-turbo-16k":"llm-engine-gpt-35-turbo-16k-plan-1","llm-engine-gpt-4-32k":"llm-engine-gpt-4-32k-plan-1","llm-engine-vertexai-chat-bison-32k":"llm-engine-vertexai-chat-bison-32k-plan-1","llm-engine-vertexai-codechat-bison-latest":"llm-engine-vertexai-codechat-bison-latest-plan-1","llm-engine-gpt-4-0125":"llm-engine-gpt-4-0125-plan-1","llm-engine-vertexai-gemini-1.5-pro-preview-0409":"llm-engine-vertexai-gemini-1.5-pro-preview-0409-plan-1","llm-engine-vertexai-gemini-1.5-flash-preview-0514":"llm-engine-vertexai-gemini-1.5-flash-preview-0514-plan-1","llm-engine-vertexai-gemini-1.5-pro-001":"llm-engine-vertexai-gemini-1.5-pro-001-plan-1","llm-engine-vertexai-gemini-1.5-flash-001":"llm-engine-vertexai-gemini-1.5-flash-001-plan-1","llm-engine-nova-sensechat-5-cantonese":"llm-engine-nova-sensechat-5-cantonese-plan-1","llm-engine-nova-sensechat-5":"llm-engine-nova-sensechat-5-plan-1","llm-engine-nova-sensechat-128K":"llm-engine-nova-sensechat-128K-plan-1","llm-engine-nova-ptc-xl-v1":"llm-engine-nova-ptc-xl-v1-plan-1","llm-engine-nova-ptc-xs-v1":"llm-engine-nova-ptc-xs-v1-plan-1","llm-engine-nova-ptc-yue-xl-v1":"llm-engine-nova-ptc-yue-xl-v1-plan-1","llm-engine-gpt-4":"llm-engine-gpt-4-plan-1","llm-engine-gpt-4-turbo":"llm-engine-gpt-4-turbo-plan-1","llm-engine-vertexai-chat-bison":"llm-engine-vertexai-chat-bison-plan-1","llm-engine-vertexai-chat-bison-001":"llm-engine-vertexai-chat-bison-001-plan-1","llm-engine-vertexai-codechat-bison-001":"llm-engine-vertexai-codechat-bison-001-plan-1","llm-engine-gpt-35-turbo-0613":"llm-engine-gpt-35-turbo-0613-plan-1","llm-engine-gpt-35-turbo-1106":"llm-engine-gpt-35-turbo-1106-plan-1","llm-engine-gpt-35-turbo":"llm-engine-gpt-35-turbo-plan-1","llm-engine-sd-txt2img":"llm-engine-sd-txt2img-plan-1","llm-engine-metagpt":"llm-engine-metagpt-plan-1"}' WHERE f."key" = 'ADMIN.CONFIG_DEFAULT_PLAN_SUBSCRIPTION';

-- CreateEnum
CREATE TYPE "PlanSubscriptionRequestStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- CreateTable
CREATE TABLE "PlanSubscriptionRequest" (
    "id" SERIAL NOT NULL,
    "subscriberEntityType" "ResourceSubsciberType" NOT NULL,
    "subscriberEntityId" INTEGER NOT NULL,
    "crNum" TEXT,
    "status" "PlanSubscriptionRequestStatus" NOT NULL,
    "createdDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" INTEGER NOT NULL,
    "operatedDate" TIMESTAMP(3),
    "operatedBy" INTEGER,
    "subscribedPlanIds" INTEGER[],

    CONSTRAINT "PlanSubscriptionRequest_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "plan_subscription_request_index" ON "PlanSubscriptionRequest"("subscriberEntityType", "subscriberEntityId");

-- CreateIndex
CREATE INDEX "plan_subscription_request_index2" ON "PlanSubscriptionRequest"("subscriberEntityType", "status");
