import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { BroadcastService } from './broadcast.service';
import { Scopes } from '../auth/scope.decorator';
import { OptionalIntPipe } from '../../pipes/optional-int.pipe';
import { WherePipe } from '../../pipes/where.pipe';
import { OrderByPipe } from '../../pipes/order-by.pipe';
import { UserRequest } from '../auth/auth.interface';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { BroadcastDto } from './broadcast.dto';
import { Public } from '../auth/public.decorator';
import { BroadcastResponse } from '../../providers/notification-backend/notification-backend.interface';
import { NotificationApiKeyScopes } from '../../constants/notification';

@Controller('broadcast')
@ApiBearerAuth('bearer-auth')
@ApiTags('Broadcast center')
export class BroadcastController {
  private logger = new Logger(BroadcastController.name);

  constructor(private broadcastService: BroadcastService) {}

  @Get()
  @Scopes('system:read-broadcast')
  async getAll(
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    if (where && 'id' in where) {
      where['id'] = parseInt(where['id'] + '');
    }
    return await this.broadcastService.getAll(
      skip,
      take,
      where,
      orderBy,
    );
  }

  @Get('users-notification-data')
  @Scopes(NotificationApiKeyScopes.GET_USER_NOTIFICATIONS_DATA)
  async getUserNotificationsData(
    @Query('includeAllUser') includeAllUser: boolean,
    @Query('userGroupId', OptionalIntPipe) userGroupId?: number,
  ) {
    return await this.broadcastService.getUserNotificationsData(includeAllUser, userGroupId);
  }

  @Post()
  @AuditLog('create-broadcast')
  @Scopes('system:write-broadcast')
  @UsePipes(new ValidationPipe({ transform: true }))
  async create(
    @Req() request: UserRequest,
    @Body() body: BroadcastDto,
  ): Promise<BroadcastResponse> {
    return await this.broadcastService.create(body, request.user.id);
  }

  @Put(':id')
  @AuditLog('update-broadcast')
  @Scopes('system:write-broadcast')
  async update(
    @Req() request: UserRequest,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: BroadcastDto,
  ): Promise<BroadcastResponse> {
    return await this.broadcastService.update(id, body, 10986164);
  }

  @Delete(':id')
  @AuditLog('delete-broadcast')
  @Scopes('system:write-broadcast')
  async delete(
    @Req() request: UserRequest,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ broadcastId: number }> {
    return await this.broadcastService.delete(id, 10986164);
  }

  @Get('system-text')
  @Scopes('system:read-broadcast')
  async getSystemText(
    @Query('userGroupId', OptionalIntPipe) userGroupId?: number,
  ): Promise<{ list: string[] }> {
    const list = await this.broadcastService.getSupportSystemTexts(userGroupId);
    return { list };
  }

}
