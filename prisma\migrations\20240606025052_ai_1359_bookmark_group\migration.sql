-- CreateTable
CREATE TABLE "BookmarkedGroup" (
    "id" SERIAL NOT NULL,
    "groupId" INTEGER NOT NULL,
    "userId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "unbookmarkedAt" TIMESTAMP(3),

    CONSTRAINT "BookmarkedGroup_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "BookmarkedGroup_groupId_userId_unbookmarkedAt_idx" ON "BookmarkedGroup"("groupId", "userId", "unbookmarkedAt");

-- CreateIndex
CREATE INDEX "BookmarkedGroup_userId_unbookmarkedAt_idx" ON "BookmarkedGroup"("userId", "unbookmarkedAt");
