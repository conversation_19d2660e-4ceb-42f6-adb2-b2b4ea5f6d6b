/*
  Warnings:

  - A unique constraint covering the columns `[groupId]` on the table `LLMModel` will be added. If there are existing duplicate values, this will fail.
  - Made the column `groupId` on table `LLMModel` required. This step will fail if there are existing NULL values in that column.
  - Made the column `llmEngineId` on table `LLMModel` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "Flow" ADD COLUMN     "lastPromotedAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "LLMModel" ADD COLUMN     "lastPromotedAt" TIMESTAMP(3),
ALTER COLUMN "groupId" SET NOT NULL,
ALTER COLUMN "llmEngineId" SET NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "LLMModel_groupId_key" ON "LLMModel"("groupId");
