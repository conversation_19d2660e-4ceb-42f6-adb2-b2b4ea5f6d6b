import { Modu<PERSON> } from '@nestjs/common';
import { TestCaseController } from './test-case.controller';
import { TestCaseService } from './test-case.service';
import { GroupsModule } from '../groups/groups.module';
import { AutoTestModule } from 'src/providers/test-case/auto-test.module';
import { BotSecurityModule } from '../bot-security/bot-security.module';
import { ExcelModel } from 'src/providers/excel/excel.model';
import { UsersModule } from '../users/users.module';
import { LlmEnginesModule } from '../llm-engines/llm-engines.module';

@Module({
  imports: [AutoTestModule, GroupsModule, ExcelModel, BotSecurityModule, UsersModule, LlmEnginesModule],
  controllers: [TestCaseController],
  providers: [TestCaseService],
})
export class TestCaseModule {}
