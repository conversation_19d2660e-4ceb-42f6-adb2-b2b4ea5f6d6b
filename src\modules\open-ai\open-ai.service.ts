import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config'; // Corrected import
import apm from 'elastic-apm-node'; // Corrected import
import { Response } from 'express';
import moment from 'moment';
import OpenAI from 'openai';
import { ChatCompletionChunk, CompletionUsage } from 'openai/resources';
import { Stream } from 'openai/streaming';
import { Configuration } from 'src/config/configuration.interface';
import { ElasticSearchService } from 'src/providers/elasticsearch/elasticsearch.service';
import { UserRequest } from '../auth/auth.interface';
import { GroupsService } from '../groups/groups.service';
import { ChannelType } from '../llm-models/dto/chat-llm-model.dto';
import { PlansService } from '../plans/plans.service';
import { PrefilledLog } from './open-ai.interface';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { ChatCompletionCreateParamsDto } from './open-ai.dto';
import { Feature } from '@prisma/client';

@Injectable()
export class OpenAIService {
  private logger = new Logger(OpenAIService.name);
  private openai: OpenAI;

  constructor(
    private readonly plansService: PlansService,
    private readonly groupService: GroupsService,
    private readonly elasticSearchService: ElasticSearchService,
    private readonly configService: ConfigService,
  ) {
    const config = configService.get<Configuration['litellm']>('litellm');
    this.openai = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseUrl,
    });
  }

  async createCompletion(
    groupId: number,
    createCompletion: ChatCompletionCreateParamsDto,
    request: UserRequest,
    res: Response,
  ): Promise<OpenAI.ChatCompletion | void> {
    const models = await this.getGroupsSupportModels(groupId);
    if (!models.includes(createCompletion.model)) {
      throw new ApiException(ErrorCode.LLM_ENGINE_NOT_FOUND);
    }
    try {
      let completion = await this.openai.chat.completions.create({
        ...createCompletion,
        ...(createCompletion.stream
          ? {
              stream_options: {
                include_usage: true,
              },
            }
          : {}),
      });
      const prefilledLog = await this.generatePrefilledChatLog(request, createCompletion);
      if (!createCompletion.stream) {
        completion = completion as OpenAI.ChatCompletion;
        const answer = completion.choices?.[0].message.content;
        this.logChatResponse(prefilledLog, completion.usage, answer);

        return completion;
      }
      let answer = '';
      for await (const part of completion as unknown as Stream<ChatCompletionChunk>) {
        const data = JSON.stringify(part);
        answer += part?.choices?.[0]?.delta?.content ?? '';
        if (part?.usage) {
          this.logChatResponse(prefilledLog, part.usage, answer);
        }
        res.write(`data: ${data}\n\n`);
      }
      res.end();
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async generatePrefilledChatLog(
    request: UserRequest,
    createCompletion: ChatCompletionCreateParamsDto,
  ): Promise<PrefilledLog> {
    const requester = {
      requesterId: request?.user.id?.toString(),
      requesterName: '',
      requesterStaffId: '',
      requesterEmail: '',
    };
    const group = await this.groupService.getGroup(request.user.groupId, {});
    let modelKey = createCompletion.model;
    if (modelKey.includes('gemini')) {
      modelKey = 'vertexai-' + request.body.model;
    }
    const prefilledLog: PrefilledLog = {
      date: moment(),
      botId: group.id,
      botEnv: group.env,
      botName: group.name,
      channel: ChannelType.API_KEY,
      ...requester,
      engine: modelKey,
      engineConfig: { ...createCompletion, messages: undefined } as any,
      query: createCompletion?.messages?.[0].content as string,
      chatSessionName: '',
      isChatSessionDefault: true,
      feature: Feature.OPENAI_COMPATIBLE,
    };
    return prefilledLog;
  }

  logChatResponse(prefilledLog: PrefilledLog, completionUsage: CompletionUsage, answer: string) {
    const usage = {
      promptTokens: completionUsage?.prompt_tokens || 0,
      completionTokens: completionUsage?.completion_tokens || 0,
      totalCompletionTokens: completionUsage?.total_tokens || 0,
      embeddingTokens: 0,
    };
    prefilledLog.durationInMS = moment().diff(prefilledLog.date, 'milliseconds');
    prefilledLog.usage = usage;
    prefilledLog.answer = answer;
    prefilledLog.traceId = apm?.currentTraceIds?.['trace.id'];
    this.logger.debug(prefilledLog);
    const trackingConfig = this.configService.get<Configuration['tracking']>('tracking');
    this.elasticSearchService.logChatRecord(
      `${trackingConfig.index}-${moment(prefilledLog.date).format('YYYY-MM-DD')}`,
      prefilledLog,
    );
  }

  async getActiveModels(groupId: number): Promise<OpenAI.Models.ModelsPage> {
    const modellist = await this.getGroupsSupportModels(groupId);
    const models = await this.openai.models.list();
    const activeModels = models.data.filter((model) => modellist.includes(model.id));
    return {
      object: 'list',
      data: activeModels,
    } as any;
  }

  public async getGroupsSupportModels(groupId: number) {
    const groupsPlans = await this.plansService.getResources('BOT', groupId);
    const models = groupsPlans
      .filter((item) => item.resourceEntityType == 'LLM_ENGINE')
      .map((item) => item.resourceEntityKey.replace('vertexai-', ''));
    return models;
  }
}
