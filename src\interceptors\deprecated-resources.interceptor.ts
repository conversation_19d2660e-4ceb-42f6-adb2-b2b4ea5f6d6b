import { Injectable, Logger, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { ApiException, ErrorCode } from '../errors/errors.constants';

@Injectable()
export class DeprecatedResourcesInterceptor implements NestInterceptor {
  private logger = new Logger(DeprecatedResourcesInterceptor.name);
  constructor() {}

  intercept(): Observable<unknown> {
    throw new ApiException(ErrorCode.TO_BE_REMOVED);
  }
}
