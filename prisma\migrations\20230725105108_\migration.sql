-- CreateEnum
CREATE TYPE "LogFileStatus" AS ENUM ('PROCESSING', 'COMPLETED', 'EXPIRED');

-- CreateTable
CREATE TABLE "LogFileHistory" (
    "id" SERIAL NOT NULL,
    "botId" INTEGER NOT NULL,
    "requesterId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "s3FilePath" VARCHAR(255) NOT NULL,
    "status" "LogFileStatus" NOT NULL DEFAULT 'PROCESSING',

    CONSTRAINT "LogFileHistory_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "LogFileHistory" ADD CONSTRAINT "LogFileHistory_requesterId_fkey" FOREIGN KEY ("requesterId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
