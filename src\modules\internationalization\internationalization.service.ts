import { BadRequestException, Injectable } from '@nestjs/common';
import { Internationalization, Language } from '@prisma/client';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { WriteI18nDto } from './internationalization.dto';
import { WriteI18nResponse, GenericResponse } from './internationalization.interface';
import { ApiException, ErrorCode } from '../../errors/errors.constants';

@Injectable()
export class InternationalizationService {
  constructor(private prisma: PrismaService) {}

  async getManyByLanguage(language?: Language) {
    const languageEnumValues = Object.values(Language);

    if (language && !languageEnumValues.includes(language)) {
      throw new BadRequestException('Invalid input');
    }

    const i18nArr = await this.prisma.internationalization.findMany({
      where: {
        language: language,
      },
    });

    const response = {};

    if (language) {
      response[language] = {};
    } else {
      languageEnumValues.forEach((value) => {
        response[value] = {};
      });
    }

    i18nArr.forEach((i18n) => {
      response[i18n.language][i18n.key] = i18n.content;
    });

    return response;
  }

  async createI18n(params: WriteI18nDto[]): Promise<WriteI18nResponse[]> {
    const response: WriteI18nResponse[] = [];
    const validI18n: string[] = [];
    const languageEnumLength = Object.values(Language).length;
    const keyMap = new Map();

    for (let index = 0; index < params.length; index++) {
      const { key, language } = params[index];

      const keySet = keyMap.get(key);

      if (!keySet) {
        const initKeySet = new Set();
        initKeySet.add(language);
        keyMap.set(key, initKeySet);
      } else {
        keySet.add(language);
      }
    }

    keyMap.forEach((value, key) => {
      if (value.size == languageEnumLength) {
        validI18n.push(key);
      }
    });

    for (let index = 0; index < params.length; index++) {
      const { key, language, content } = params[index];

      if (validI18n.includes(key)) {
        const dbRecord = await this.findUniqueIn18n(key, language);
        if (!dbRecord) {
          await this.prisma.internationalization.create({
            data: {
              key,
              content,
              language,
            },
          });

          const currentResponse: WriteI18nResponse = {
            key: key,
            language: language,
            statusCode: 200,
            message: 'success',
          };
          response.push(currentResponse);
        } else {
          const currentResponse: WriteI18nResponse = {
            key: key,
            language: language,
            statusCode: 300,
            message: 'Record already existed',
          };
          response.push(currentResponse);
        }
      } else {
        const currentResponse: WriteI18nResponse = {
          key: key,
          language: language,
          statusCode: 400,
          message: 'Need to provide content for all language types',
        };
        response.push(currentResponse);
      }
    }
    return response;
  }

  async findUniqueIn18n(key: string, language: Language): Promise<Internationalization> {
    return await this.prisma.internationalization.findUnique({
      where: { key_language: { key: key, language: language } },
    });
  }

  async updateI18n(params: WriteI18nDto[]): Promise<WriteI18nResponse[]> {
    const response: WriteI18nResponse[] = [];

    for (let index = 0; index < params.length; index++) {
      const { key, language, content } = params[index];

      const dbRecord = await this.findUniqueIn18n(key, language);
      if (dbRecord) {
        await this.prisma.internationalization.update({
          where: { key_language: { key: key, language: language } },
          data: {
            content,
          },
        });

        const currentResponse: WriteI18nResponse = {
          key: key,
          language: language,
          statusCode: 200,
          message: 'success',
        };
        response.push(currentResponse);
      } else {
        const currentResponse: WriteI18nResponse = {
          key: key,
          language: language,
          statusCode: 400,
          message: 'Record does not existed',
        };
        response.push(currentResponse);
      }
    }
    return response;
  }

  async deleteByKey(key: string): Promise<GenericResponse> {
    const dbRecords = await this.prisma.internationalization.count({
      where: { key: key },
    });
    if (!dbRecords || dbRecords <= 0) throw new ApiException(ErrorCode.I18N_NOT_FOUND);
    await this.prisma.internationalization.deleteMany({
      where: { key: key },
    });

    return this.prisma.expose<GenericResponse>({ statusCode: 200, message: 'success' });
  }
}
