import { Injectable, Logger } from '@nestjs/common';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { GetGroupFlowBotsResponse } from './flow-bots.dto';
import { Prisma, GroupType, FlowBotStatus, FlowBot, Environment } from '@prisma/client';
import { GroupsService } from '../groups/groups.service';
import { FlowBotRequestsService } from '../flow-bot-requests/flow-bot-requests.service';

@Injectable()
export class FlowBotsService {
  private logger = new Logger(FlowBotsService.name);

  constructor(
    private prisma: PrismaService,
    private groupsService: GroupsService,
    private flowBotRequestsService: FlowBotRequestsService,
  ) {}

  async getFlowBotsForGroup(
    groupId: number,
    skip?: number,
    take?: number,
    where?: Record<string, number | string>,
    orderBy?: Record<string, number | string>,
  ): Promise<{ list: GetGroupFlowBotsResponse[]; count: number }> {
    const groupType = await this.groupsService.getGroupTypeByGroupId(groupId);

    const include: Prisma.FlowBotInclude = {};
    let whereInput: Prisma.FlowBotWhereInput = {};

    whereInput = {
      NOT: {
        status: FlowBotStatus.DELETED,
      },
    };

    if (GroupType.BOT === GroupType[groupType]) {
      // Find the group of flows which belongs to that bot
      include.flowGroup = true;
      whereInput.botGroupId = groupId;
      whereInput.flowGroup = { ...where };
    } else if (GroupType.FLOW === GroupType[groupType]) {
      // Find the group of bots which belongs to that flow
      include.botGroup = true;
      whereInput.flowGroupId = groupId;
      whereInput.botGroup = { ...where };
    } else {
      throw new ApiException(ErrorCode.FLOW_BOT_NOT_FOUND);
    }

    const flowBots = await this.prisma.flowBot.findMany({
      include: include,
      skip,
      take,
      where: whereInput,
      orderBy,
    });

    if (flowBots.length === 0) return { list: [], count: 0 };

    // Find the corresponding entity lastPromotedAt
    const targetGroups = await this.prisma.group.findMany({
      where: {
        pairId: {
          in: flowBots.map((flowBot) =>
            GroupType.BOT === GroupType[groupType] ? flowBot.flowGroup.id : flowBot.botGroup.id,
          ),
        },
        AND: {
          env: Environment.PROD,
        },
      },
    });

    // targetEntityies can be LLMModel or Flow
    let targetEntities;
    if (GroupType.BOT === GroupType[groupType]) {
      targetEntities = await this.prisma.flow.findMany({
        where: {
          groupId: {
            in: targetGroups.map((targetGroup) => targetGroup.id),
          },
        },
      });
    } else {
      targetEntities = await this.prisma.lLMModel.findMany({
        where: {
          groupId: {
            in: targetGroups.map((targetGroup) => targetGroup.id),
          },
        },
      });
    }

    const list = flowBots.map((flowBot) => {
      const group = GroupType.BOT === GroupType[groupType] ? flowBot.flowGroup : flowBot.botGroup;
      // Find the target group first, then find the target entity
      const targetEntity = targetEntities.find(
        (targetEntity) =>
          targetEntity.groupId ===
          targetGroups.find((targetGroup) => targetGroup.pairId === group.pairId).id,
      );
      return {
        id: flowBot.id,
        groupId: group.id,
        name: group.name,
        status: flowBot.status,
        createdAt: flowBot.createdAt,
        updatedAt: flowBot.updatedAt,
        lastPromotedAt: targetEntity.lastPromotedAt,
      } as GetGroupFlowBotsResponse;
    });

    const count = await this.prisma.flowBot.count({ where: whereInput });

    return { list, count };
  }

  async deleteFlowBot(
    userId: number,
    groupId: number,
    flowBotId: number,
  ): Promise<{ flowBotId: number }> {
    const flowBot = await this.prisma.flowBot.findFirst({
      include: {
        flowGroup: true,
        botGroup: true,
      },
      where: {
        id: flowBotId,
        status: FlowBotStatus.ACTIVE,
      },
    });

    if (!flowBot) throw new ApiException(ErrorCode.FLOW_BOT_NOT_FOUND);

    // flowBot record not belongs to the group
    if (flowBot.flowGroupId !== groupId && flowBot.botGroupId !== groupId)
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);

    const requestGroup = flowBot.flowGroupId === groupId ? flowBot.flowGroup : flowBot.botGroup;
    const targetGroup = flowBot.flowGroupId === groupId ? flowBot.botGroup : flowBot.flowGroup; // the oppsite group = target group

    let deletedFlowBot: FlowBot;
    await this.prisma.$transaction(async (tx) => {
      deletedFlowBot = await tx.flowBot.update({
        data: {
          status: FlowBotStatus.DELETED,
        },
        where: {
          id: flowBotId,
        },
      });

      await this.flowBotRequestsService.handleCreateDeleteFlowBot(
        tx,
        userId,
        requestGroup,
        targetGroup,
      );
    });

    return { flowBotId: deletedFlowBot.id };
  }

  async validateFlowHasBotAccess(botId: number, flowId: number) {
    const flowAccess = await this.prisma.flowBot.findFirst({
      where: {
        flowGroupId: flowId,
        botGroupId: botId,
        status: FlowBotStatus.ACTIVE,
      },
    });
    if (!flowAccess) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
  }
}
