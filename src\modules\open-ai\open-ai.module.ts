import { Module } from '@nestjs/common';
import { OpenAIController } from './open-ai.controller';
import { OpenAIService } from './open-ai.service';
import { GroupsModule } from '../groups/groups.module';
import { PlansModule } from '../plans/plans.module';
import { ElasticSearchModule } from 'src/providers/elasticsearch/elasticsearch.module';
import { ConfigModule } from '@nestjs/config';
import { LlmEnginesModule } from '../llm-engines/llm-engines.module';
import { RateLimitModule } from 'src/providers/rate-limit/rate-limit.module';
import { QuotaModule } from '../quotas/quota.module';

@Module({
  imports: [
    GroupsModule,
    PlansModule,
    ElasticSearchModule,
    ConfigModule,
    LlmEnginesModule,
    RateLimitModule,
    QuotaModule,
  ],
  controllers: [OpenAIController],
  providers: [OpenAIService],
  exports: [OpenAIService],
})
export class OpenAIModule {}
