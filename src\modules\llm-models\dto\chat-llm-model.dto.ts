import { ApiHideProperty, ApiProperty, ApiPropertyOptional, getSchemaPath, ApiExtraModels } from '@nestjs/swagger';
import { ChatSession, ChatSessionType, LLMModel } from '@prisma/client';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDefined,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import {
  CHAT_APPROACH,
  MODEL,
  ROLE,
  SOURCE,
} from '../../../providers/llm-backend/llm-backend.interface';
import { FunctionCall, ToolCall } from '../entities/llm-model.entity';
import { InputScannersResultDTO } from 'src/modules/bot-security/dto/input-scanners-result.DTO';
import { Type } from 'class-transformer';
import { LlmModelParamsDto } from './llm-model-params.dto';


export enum ContentType {
  TEXT = 'text',
  IMAGE_URL = 'image_url',
}


export class ImageContent {
  @ApiProperty({
    example: 'data:image/jpeg;base64,{encoded_image}',
    description:
      'Base64 encoded image data. or image url',
  })
  url: string;
}


export class Content {
  @ApiProperty({
    enum: ContentType,
    description:
      'The two contentType used is "text" and "image_url"',
  })
  @IsEnum(ContentType)
  type: ContentType;

  @ApiPropertyOptional({
    example: 'what is chatgpt?',
    description:
      'The chat history content from each role. The chat history is used for GPT to answer follow-up questions.	',
  })
  @IsOptional()
  text?: string;

  @ApiPropertyOptional({
    example: 'image url or base64 image',
    description:
      'Image content',
    type: ImageContent,
  })
  @IsOptional()
  image_url?: ImageContent

}

@ApiExtraModels(Content)
export class HistoryMessage {
  @ApiProperty({
    enum: ROLE,
    description:
      'The two roles used is "assistant" and "user", which represents the chatbot and the user respectively.	',
  })
  @IsDefined()
  @IsEnum(ROLE)
  role: ROLE;

  @ApiProperty({
    example: 'what is chatgpt?',
    description:
      'The chat history content from each role. The chat history is used for GPT to answer follow-up questions.	',
    oneOf: [
      { type: 'string' },
      {
        type: 'array',
        items: {
          $ref: getSchemaPath(Content)
        }
      },
    ],
  })
  @IsOptional()
  content?: string | Content[];

  @ApiProperty({
    required: false,
    deprecated: true,
    description:
      'this function call will not be supported on April, currently will auto Convert to tools call',
  })
  @IsOptional()
  function_call?: FunctionCall;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContentPoint)
  content_points?: ContentPoint[];

  @ApiProperty({ required: false })
  @IsOptional()
  tool_calls?: ToolCall;
}

export enum SecurityScanType {
  ON = 'ON',
  OFF = 'OFF',
  OUTPUT = 'OUTPUT',
  PROMPT_INJECTION = 'PROMPT_INJECTION',
  ANONYMIZE = 'ANONYMIZE',
}

/**
 * @description when the channel is CHAT_WITH_DATA will not store the log in the opensearch
 */
export enum ChannelType {
  API_KEY = 'API_KEY',
  PLAYGROUND = 'PLAYGROUND',
  TEAMS = 'TEAMS',
  CHAT_WITH_DATA = 'CHAT_WITH_DATA',
  AUTO_TEST = 'AUTO_TEST',
  GEN_KB = 'GEN_KB',
  OUTLOOK = 'OUTLOOK',
}

export class FlowCallChatQuery {
  @ApiHideProperty()
  flowId?: string;
}

export class FunctionDefinition {
  @ApiProperty({ description: 'The name of the function to be called.' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description:
      'A description of what the function does. The model will use this description when selecting the function and interpreting its parameters.',
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({
    description: 'The parameters the functions accepts, described as a JSON Schema object',
  })
  @IsNotEmpty()
  @IsString()
  parameters: string;
}

export enum ToolType {
  function = 'function',
}

export class ToolDefinition {
  @ApiProperty({ description: 'The type of the tool', enum: ToolType })
  type: ToolType;
  @ApiProperty()
  function: FunctionDefinition;
}


export enum GenKbDataType {
  PAGE = 'PAGE',
  FILE = 'FILE',
  TAG = 'TAG',
}

export class GenkbSearchFilter {
  @ApiPropertyOptional({ 
    description: 'GenKB content group id',
    type: Number,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  content_group_ids?: number[];

  @ApiPropertyOptional({
    description: 'The type of the genkb data type',
    enum: GenKbDataType,
    isArray: true,
  })
  @ApiPropertyOptional()
  @IsArray()
  types?: GenKbDataType[];

  @ApiPropertyOptional({ 
    description: 'The type of the genkb data type',
    type: String,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  tags?: String[];

  @ApiProperty({ 
    description: 'GenKB tagging',
    type: String,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  mandatory_tags?: String[];

  @ApiProperty({ 
    description: 'GenKB page path, example: home/{kbId}/your-page',
    type: String,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  pages?: String[];
}

export class GenkbScoringFactor {
  @ApiPropertyOptional({
    type: 'number', 
    description: 'GenKB embedding content scoring factor',
    default: 0.7,
    minimum: 0,
    maximum: 1.0,
  })
  @IsOptional()
  embedding?: Number;

  @ApiPropertyOptional({ 
    description: 'GenKB embedding tagging scoring factor',
    type: 'number',
    default: 0.3,
    minimum: 0,
    maximum: 1.0,
  })
  @IsOptional()
  embedding_tag?: Number;

  @ApiPropertyOptional({ 
    description: 'GenKB content publish datetime scoring factor; higher score for latest content',
    type: 'number', 
    default: 0.3,
    minimum: 0,
    maximum: 1.0,
  })
  @IsOptional()
  publish_start_datetime?: Number;
}

export class GenkbParam {
  @ApiProperty({ description: 'The group id or kb id', })
  @IsNotEmpty()
  @IsNumber()
  kb_id: number;

  @IsOptional()
  @ApiPropertyOptional({    
    description: 'Genkb search filters',
    type: GenkbSearchFilter,
    required: false,
  })
  filter?: GenkbSearchFilter;


  @ApiProperty({ 
    description: 'The ai search threshold',
    default: 0.4,
    minimum: 0,
    maximum: 1.0,
  })
  @IsOptional()
  @IsNumber()
  search_threshold?: number;

  @ApiProperty({ 
    description: 'The search threshold', 
    type: GenkbScoringFactor
  })
  @IsOptional()
  scoring_factor?: GenkbScoringFactor;
}

export class Overrides {
  @ApiProperty({
    enum: MODEL,
    description: 'LLM model selection',
    default: MODEL.GPT_35_TURBO_0613,
  })
  @IsString()
  @IsOptional()
  model?: MODEL;

  @IsOptional()
  @ApiHideProperty()
  prompt_template?: string;

  @IsOptional()
  @ApiPropertyOptional({
    type: 'integer',
    default: 3,
    minimum: 0,
    maximum: 20,
    description:
      'What sampling temperature to use. Higher values means the model will take more risks. Try 0.9 for more creative applications, and 0 (argmax sampling) for ones with a well-defined answer.\nWe generally recommend altering this or top_p but not both.',
  })
  top?: number;

  @IsOptional()
  @ApiPropertyOptional({
    type: 'integer',
    default: 1000,
    minimum: 0,
    description:
      "The token count of your prompt plus max_tokens cannot exceed the model's context length. GPT-35-turbo: 4096, GPT-35-turbo-16K: 16384, GPT-4: 8192, GPT-4-32K: 32768",
  })
  max_tokens?: number;

  @IsOptional()
  @ApiPropertyOptional({
    default: 1,
    minimum: 0,
    maximum: 1,
    description:
      'An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\nWe generally recommend altering this or temperature but not both.',
  })
  top_p?: number;

  @IsOptional()
  @IsNumber()
  @ApiPropertyOptional({
    default: 0,
    minimum: 0,
    maximum: 2,
    description:
      'What sampling temperature to use. Higher values means the model will take more risks. Try 0.9 for more creative applications, and 0 (argmax sampling) for ones with a well-defined answer.\nWe generally recommend altering this or top_p but not both.',
  })
  temperature?: number;

  @IsOptional()
  @IsNumber()
  @ApiPropertyOptional({
    default: 0,
    minimum: -2.0,
    maximum: 2.0,
    description:
      "Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model's likelihood to talk about new topics.",
  })
  presence_penalty?: number;

  @IsOptional()
  @IsNumber()
  @ApiPropertyOptional({
    default: 0,
    minimum: -2.0,
    maximum: 2.0,
    description:
      "Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model's likelihood to repeat the same line verbatim.",
  })
  frequency_penalty?: number;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @ApiProperty({
    type: [FunctionDefinition],
    description:
      '*** Important: the function call will not be supported . *** \n A list of functions the model may generate JSON inputs for. Remark: this param is available in gpt-35-turbo-0613, gpt-35-turbo-16k, gpt-4, gpt-4-32k only',
    required: false,
  })
  functions?: FunctionDefinition[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @ApiProperty({
    type: [ToolDefinition],
    description:
      'A list of tools the model may generate JSON inputs for. Remark: this param is available in gpt-35-turbo-0613, gpt-35-turbo-16k, gpt-4, gpt-4-32k only',
    required: false,
    examples: {
      tools_call: {
        value: [
          {
            type: 'function',
            function: {
              name: 'complaint_ticket',
              description: 'Raise a complaint ticket to follow up',
              parameters: {
                type: 'object',
                properties: {
                  complaint_content: {
                    type: 'string',
                    description: 'The content of complaint',
                  },
                },
                required: ['complaint_content'],
              },
            },
          },
          {
            type: 'function',
            function: {
              name: '1083_hotline',
              description: 'extract parameters based on prompt',
              parameters: {
                type: 'object',
                properties: {
                  address: {
                    type: 'string',
                    description: 'The address',
                  },
                  biz_name: {
                    type: 'string',
                    description: 'The business name',
                  },
                  area: {
                    type: 'string',
                    enum: [
                      'ALL 全部',
                      'Hong Kong 香港',
                      'Kowloon 九龍',
                      'New Territories 新界',
                      'Lantau 大嶼山',
                    ],
                  },
                  district: {
                    type: 'string',
                    enum: [
                      'ALL 全部',
                      'Aberdeen 香港仔',
                      'Airport 機場',
                      'Ap Lei Chau 鴨脷洲',
                      'Yuen Long Pat Heung 元朗八鄉',
                      'Yuen Long San Tin 元朗新田',
                    ],
                  },
                },
                required: ['biz_name', 'district'],
              },
            },
          },
        ],
        summary: 'Tools call example',
        description: "Tools call will be selected by LLM based on user's prompt",
      },
    },
  })
  tools?: ToolDefinition[];

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    enum: SecurityScanType,
    required: false,
    description: 'Enable prompt PII check',
    default: SecurityScanType.OFF,
  })
  security_scan?: SecurityScanType;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    type: 'boolean',
    required: false,
    description: 'Enable streaming response (SSE)',
  })
  stream?: boolean;

  @ApiHideProperty()
  api_resource: any;

  @IsOptional()
  @IsNumber()
  @ApiPropertyOptional({
    type: 'integer',
    default: 0,
    minimum: 0,
    description: 'Internet search. Supported models: gemini-flash / gemini-pro',
  })
  internet_search?: number;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    type: 'boolean',
    required: false,
    description: 'content show references',
  })
  show_reference?: boolean;

  @IsOptional()
  @ApiPropertyOptional({
    type: 'boolean',
    required: false,
    description: 'enable genkb data source',
  })
  genkb?: boolean;

  @IsOptional()
  @IsNumber()
  @ApiPropertyOptional({
    type: 'integer',
    required: false,
    description: 'genkb top x content',
  })
  genkb_top?: number;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @ApiProperty({
    type: [GenkbParam],
    description:
      'genkb search parameters',
    required: false,
  })
  genkb_params?: GenkbParam[];
}

export class RefMetaData {
  @IsOptional()
  index_id?: string;
  @IsOptional()
  document_id?: string;
}

export class ContentPoint {
  filename?: string;
  file_id?: string;
  page?: number;
  data_source?: SOURCE;
  cited?: boolean;
  ref_metadata?: RefMetaData | null;
  type?: string;
}
export class File {
  @IsEnum(SOURCE)
  @IsNotEmpty()
  @ApiProperty({
    enum: SOURCE,
    description: 'source of file',
    default: SOURCE.VECTOR_STORE,
    examples: {
      vector_store: {
        value: SOURCE.VECTOR_STORE,
        summary: 'Vector store',
        description: 'file uploaded from Upload File page (persistent storage)',
      },
      upload: {
        value: SOURCE.UPLOAD,
        summary: 'Upload',
        description: 'file directly uploaded from playground page (temporary storage)',
      },
    },
  })
  data_source: SOURCE;

  @IsNotEmpty()
  @ApiProperty({
    example: '1234',
    description: 'file id',
    examples: {
      vector_store: {
        value: '1234',
        summary: 'Vector store',
        description: 'file_id = docId',
      },
      upload: {
        value: '1234',
        summary: 'Upload',
        description: 'file_id = s3Basename',
      },
    },
  })
  file_id: string;

  @ApiProperty({
    required: false,
    example: '1-5,10,20-50',
    description: 'page range, complete file will be used if not specified',
  })
  range?: string;
}

export class ChatLlmModelDto {
  @IsEnum(CHAT_APPROACH)
  @IsNotEmpty()
  @ApiProperty({
    default: CHAT_APPROACH.RRR,
    enum: [CHAT_APPROACH.CWF, CHAT_APPROACH.RRR, CHAT_APPROACH.CWD],
    description:
      'Chat approach. Possible values: \n- rrr: General chat application. Available features: upload file, GenKB, Connect API, and tools_call. \n- cwf: Chat with file, i.e. direct file chat. \n- cwd: Chat with data, i.e. direct data chat.',
    example: CHAT_APPROACH.CWF,
  })
  approach: CHAT_APPROACH;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => HistoryMessage)
  @ArrayMinSize(1)
  @IsDefined({ each: true })
  @ApiProperty({
    type: [HistoryMessage],
    description:
      'An array of objects, each representing a message in the conversation history, with role and content fields. ',
    examples: {
      simple_chat: {
        value: [
          { role: 'user', content: 'what is chatgpt?' },
          {
            role: 'assistant',
            content:
              'ChatGPT is an AI chatbot that was initially built on a family of large language models (LLMs) collectively known as GPT-3.',
          },
          { role: 'user', content: 'Show me CSL shops' },
        ],
        summary: 'Simple conversation',
        description: 'Simple conversation between user and assistant, must start with user',
      },
      tools_call: {
        value: [
          {
            role: 'user',
            content: "What's CSL phone number",
          },
          {
            role: 'assistant',
            tool_calls: [
              {
                function: {
                  name: '1083_hotline',
                  arguments: '{\n  "biz_name": "CSL",\n  "district": "ALL 全部"\n}',
                },
                id: 'call_FsKI2Dk0qeQvxvNyBXlQ0S5t',
                type: 'function',
              },
            ],
          },
          {
            role: 'tool',
            tool_call_id: 'call_FsKI2Dk0qeQvxvNyBXlQ0S5t',
            name: '1083_hotline',
            content:
              '{"success": true, "data": {"id": 1, "data": "Csl Ltd: Blk R Telford Gdn 電話: 2405 1010, HK Telecom CSL Tower 電話: 2381 1010, PCCW Tower Taikoo Place 電話: 2888 2111 ,PCCW Tower Taikoo Place 傳真: 2250 5611", "createdAt": "2020-11-01" }}',
          },
        ],
        summary: 'Conversation with tool call',
        description: 'Conversation with tool call',
      },
      chat_with_file: {
        value: [
          {
            role: 'user',
            content: 'What is the content of the file',
          },
        ],
        summary: 'Conversation with file',
        description: 'Chat with file example',
      },
    },
  })
  history: HistoryMessage[];

  @ApiProperty({
    description:
      'An object containing any additional model parameters or overrides, such as the model field. 	',
  })
  @Type(() => Overrides)
  @ValidateNested()
  overrides: Overrides;

  @ApiHideProperty()
  llmModel: LLMModel;

  // This param is only needed by Teams bot.
  // When this API is called without specifying channel, it will be determined as API_KEY with presence of api-key.
  // Otherwise, it will be determined as PLAYGROUND.
  @ApiHideProperty()
  @IsEnum(ChannelType)
  @IsOptional()
  channel?: ChannelType;

  @IsOptional()
  @ApiProperty({
    description:
      'if you use the Connect API. can past you need the variables into the custom Param Config ',
    required: false,
    type: [Map],
    example: { userId: '1' },
  })
  appendVariables?: Map<string, string>;

  // chat session is only available for playground, not supposed to be called from external chat API
  @ApiHideProperty()
  @IsEnum(ChatSessionType)
  @IsOptional()
  chatSessionType?: ChatSessionType;

  // chat session is only available for playground, not supposed to be called from external chat API
  @ApiHideProperty()
  @IsInt()
  @IsOptional()
  chatSessionId?: number;

  // chat session is only available for playground, not supposed to be called from external chat API
  @ApiHideProperty()
  @IsOptional()
  chatSession?: ChatSession;

  @IsOptional()
  @ApiPropertyOptional({
    type: [ContentPoint],
    description: 'the reference of data used ',
    examples: {
      embedding: {
        value: [
          {
            id: 1,
            filename: 'AI_%E5%95%86%E5%93%81%E9%A0%98%E5%8F%96%E7%AB%99.pdf',
            file_id: '5a3b0b0c-8e2e-4d04-a3e6-fff78affa5a9',
            page: 1,
            data_source: 'vector-store',
            cited: true,
            ref_metadata: {
              index_id: 'rzdw_5ib1sxqtnug9bwk',
              document_id: '0bf0c8b5-63e0-4f9e-8bb2-ea8951c86561',
            },
            type: 'embedding',
          },
        ],
        summary: 'Embedding content point example',
        description: 'Embedding content point example',
      },
    },
  })
  @IsOptional()
  @ApiPropertyOptional({
    type: [File],
    description: 'applicable only if approach is cwf and cwd',
    examples: {
      chat_with_file: {
        value: [
          {
            data_source: 'vector-store',
            file_id: '1234',
            range: '1-5,10,20-50',
          },
          {
            data_source: 'upload',
            file_id: '1234',
          },
        ],
        summary: 'Chat with file example',
        description: 'Chat with file example',
      },
    },
  })
  files?: File[];

  // PII Input Scan Result: this param is NOT used by API integration
  @ApiHideProperty()
  @IsOptional()
  inputScannersResult?: InputScannersResultDTO;

  //Using the chatWithModel API
  @ApiHideProperty()
  groupId?: number;

  //Using when the teams call is apikey call . log the teams user email
  @ApiHideProperty()
  callingAttributes?: string;

  // this is NOT used for calling from external chat API, but just for passing the whole object to gpt-service
  @ApiHideProperty()
  userId?: number;

  @ApiHideProperty()
  @IsOptional()
  userPromptHistoryId?: number;
}

export type ChatRequester = {
  requesterId: string;
  requesterName: string;
  requesterStaffId?: string;
  requesterEmail?: string;
};
export interface LlmModelBasicInfoResponse {
  id: number;
  name: string;
  modelEngine: string;
  llmEngineId: number;
  startupMessage: string;
  parameters: LlmModelParamsDto;
  modelEngineIsActive: boolean;
  showRefInTeams: boolean;
  profilePictureUrl: string;
  makeLiveToPublic: boolean;
  canShareChat: boolean;
}
export class InternalChatLlmModelDto extends ChatLlmModelDto {
  internalCallRequesterId?: number;
  isTokenLimitCheckNeed?: boolean;
  needIncludeDataSource?: boolean;
}
