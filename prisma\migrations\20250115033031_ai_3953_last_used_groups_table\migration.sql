-- CreateTable
CREATE TABLE "LastUsedGroups" (
    "userId" INTEGER NOT NULL,
    "groupType" "GroupType" NOT NULL,
    "lastUsedHistory" JSONB[],

    CONSTRAINT "LastUsedGroups_pkey" PRIMARY KEY ("userId","groupType")
);

-- CreateIndex
CREATE INDEX "LastUsedGroups_userId_groupType_idx" ON "LastUsedGroups"("userId", "groupType");

-- CreateIndex
CREATE UNIQUE INDEX "LastUsedGroups_userId_groupType_key" ON "LastUsedGroups"("userId", "groupType");
