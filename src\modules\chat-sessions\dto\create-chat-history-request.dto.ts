import { ChatMessageContentType, ChatSessionType } from '@prisma/client';
import { IsEnum, IsInt, IsOptional, ValidateNested } from 'class-validator';
import { HistoryMessage } from 'src/modules/llm-models/dto/chat-llm-model.dto';

export class CreateChatHistoryRequestDto {
  @IsInt()
  @IsOptional()
  chatSessionId?: number;

  @IsEnum(ChatSessionType)
  @IsOptional()
  chatSessionType?: ChatSessionType;

  @ValidateNested()
  @IsOptional()
  message?: HistoryMessage;

  @IsEnum(ChatMessageContentType)
  contentType: ChatMessageContentType;

  @IsInt()
  @IsOptional()
  rating?: number;

  @IsOptional()
  comment?: string[];
}
