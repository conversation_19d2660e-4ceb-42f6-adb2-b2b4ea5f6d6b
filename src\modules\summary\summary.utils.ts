import { GroupType, SummaryCallingType, SummaryKeyType } from '@prisma/client';
import {
  DashboardData,
  DashboardKey,
  LeaderboardData,
  LeaderboardSummaryQueryData,
} from './summary.interface';
/**
 * @description to solved Prisma Do not know how to serialize a BigInt issue.
 * #ref: https://github.com/prisma/studio/issues/614
 * #scope: line 9 to 19
 */

BigInt.prototype.toJSON = function (): string | number {
  const value = Number.parseInt(this.toString());
  return value ?? this.toString();
};

BigInt.prototype.toNumber = function (): number {
  const value = Number.parseInt(this.toString());
  return value;
};

const CommonKey = [
  SummaryKeyType.CALL_TOTAL,
  SummaryKeyType.COMPLETION_TOKENS_TOTAL,
  SummaryKeyType.DURATION_IN_MS_AVG,
  SummaryKeyType.TOTAL_COMPLETION_TOKENS_TOTAL,
  SummaryKeyType.PROMPT_TOKENS_TOTAL,
  SummaryKeyType.EMBEDDING_TOKENS_TOTAL,
] as DashboardK<PERSON>[];

const flowSummaryKey: DashboardKey[] = [
  SummaryKeyType.CALL_TOTAL,
  SummaryKeyType.TOOLS_USAGE_TOTAL,
];

const botSummaryKey: DashboardKey[] = [...CommonKey];

const isValidDate = (date: string): boolean => {
  return Date.parse(date) > 0 ? true : false;
};

const offsetMonth = (date: Date, offset: number): Date => {
  const tmp = new Date(date);
  tmp.setMonth(tmp.getMonth() + offset);
  return tmp;
};

const formatDateRange = (from: string, to: string) => {
  const now = new Date();
  const fromDate = isValidDate(from) ? new Date(from) : offsetMonth(now, -1);
  const toDate = isValidDate(to) ? new Date(to) : now;
  return { fromDate, toDate };
};

const appendDefaultValueByDateRangeAtSummaryKey = (
  dateRange: string[],
  dashboardData: DashboardData[],
  summaryKey: SummaryKeyType,
) => {
  if (!dashboardData || dashboardData?.length <= 0) {
    return dateRange.map((item) => ({
      key: summaryKey,
      value: 0,
      label: item,
      engineSlug: '',
    }));
  }
  return dashboardData;
};

const formatQueryResponseToMap = (
  queryResponse: DashboardData[],
  groupType: GroupType,
): Map<DashboardKey, DashboardData[]> => {
  const summaryKey: DashboardKey[] = getSummaryKeysGroupType(groupType);
  const dashboardMap = new Map<DashboardKey, DashboardData[]>();
  if (!queryResponse || queryResponse?.length <= 0) {
    summaryKey.forEach((item: DashboardKey) => {
      dashboardMap.set(item, []);
    });
    return dashboardMap;
  }
  queryResponse.forEach((item) => {
    if (dashboardMap.has(item.key)) {
      dashboardMap.get(item.key).push(item);
    } else {
      dashboardMap.set(item.key, [item]);
    }
  });
  return dashboardMap;
};

const membersDefaultDashboardData: DashboardData = {
  label: '',
  value: 0,
  engineSlug: '',
  callingType: SummaryCallingType.USER_PLAYGROUND,
  key: 'CALL_TOTAL',
};

const formatQueryResponseToDashboardData = (
  data: DashboardData[],
  key: DashboardKey,
): DashboardData[] => {
  const dashboardData = data.map((item) => ({ ...membersDefaultDashboardData, ...item, key }));
  return dashboardData;
};

const getSummaryKeysGroupType = (entityType: GroupType): DashboardKey[] => {
  if (entityType === GroupType.FLOW) {
    return flowSummaryKey;
  } else {
    return botSummaryKey;
  }
};

const defaultLeaderboardData = {
  ...Object.keys(SummaryCallingType).reduce((result, callingTypeKeys) => {
    result[callingTypeKeys] = 0;
    return result;
  }, {}),
  sum: 0,
};

const formatLeaderboardSummaryData = (
  leaderboarSummaryQueryData: LeaderboardSummaryQueryData[],
) => {
  const leaderboardData = leaderboarSummaryQueryData.reduce((result, item) => {
    const sum = Number(item._sum);
    if (result[item.labelId]) {
      result[item.labelId].sum += sum;
      result[item.labelId] = { ...result[item.labelId], [item.callingType]: item._sum };
    } else {
      result[item.labelId] = {
        ...defaultLeaderboardData,
        sum: sum,
        [item.callingType]: sum,
        label: item.labelName ? item.labelName : item.labelId,
      };
    }
    return result;
  }, {} as LeaderboardData);

  return Object.values(leaderboardData).sort((a, b) => b.sum - a.sum);
};

export {
  flowSummaryKey,
  botSummaryKey,
  getSummaryKeysGroupType,
  formatDateRange,
  formatQueryResponseToDashboardData,
  formatQueryResponseToMap,
  appendDefaultValueByDateRangeAtSummaryKey,
  formatLeaderboardSummaryData,
};
