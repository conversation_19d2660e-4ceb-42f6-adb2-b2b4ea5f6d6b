import { Environment, GroupType, SummaryCallingType, SummaryKeyType } from '@prisma/client';

export interface DashboardData {
  label: string;
  value: number;
  key: DashboardKey;
  engineSlug: string;
  callingType: SummaryCallingType;
}

export enum CALENDAR_INTERVAL {
  minute = 'minute',
  hour = 'hour',
  day = 'day',
  week = 'week',
  month = 'month',
  year = 'year',
}
export enum SearchType {
  FLOW_DETAIL = 'FLOW_DETAIL',
  NORMAL = 'NORMAL',
}

export interface GroupDashboardDataRsponse {
  groupType: GroupType;
  dashboardRecord: Map<SummaryKeyType, DashboardData>;
  totleDashboardRecord?: Map<SummaryKeyType, DashboardData>;
}

export type DashboardKeyExtendKey = 'MEMBERS' | 'FLOW_CALLS_TOTAL';

export type DashboardKey = SummaryKeyType | DashboardKeyExtendKey | 'BOT_CREATE';

export enum LeaderboardType {
  GROUP = 'GROUP',
  ACTIVE_USER = 'ACTIVE_USER',
  USER = 'USER',
  LLM = 'LLM',
  TOP_TEAMS_BOT = 'TOP_TEAMS_BOT',
}
export enum SortDataType {
  TIEM_RANGE = 'duringTimeRang',
  ACCUMULATED = 'accumulated',
}
export type LeaderboardSummaryData = { [key in SummaryCallingType]?: number } & {
  sum: number;
  label: string;
};

export type LeaderboardData = {
  [key: number]: LeaderboardSummaryData;
};

export type LeaderboardSummaryQueryData = {
  callingType: SummaryCallingType;
  labelId: string;
  labelName?: string;
  _sum: number;
};
export type LeaderboardSoryType = SummaryKeyType | 'FILE_STORAGE';

interface ActiveMemberItem {
  userId: number;
  userName: string;
  lastLoginAt: Date;
  email: string;
  role: string;
}

type ActiveMemberSummaryItem = {
  [key in SummaryKeyType]: number;
};

export type ActiveMember = Partial<ActiveMemberItem & ActiveMemberSummaryItem>;

export interface SummaryData {
  totalMember: number;
  activeMemberNum: number;
  totalApiCalls: number;
  apiCalls: number;
  totalTotalCompletion: number;
  totalCompletion: number;
  totalPrompt: number;
  prompt: number;
  currentTotalCompletion: number;
  completion: number;
  totalEmbedding: number;
  embedding: number;
  totalFlowConnected: number;
  totalBotConnected: number;
  totalUploadFile: number;
  totalDataStorage: number;
  monthlyTokenLimit?: number;
  currentUsedMonthlyToken: number;
  teamsUserTotal: number;
  teamsActiveUser: number;
}

export interface GroupFilter {
  groupType?: GroupType;
  env?: Environment;
  groupId?: number;
  searchType?: SearchType;
}

export type TotalGroupInfo = {
  env: Environment;
  isActive: boolean;
  groupType: GroupType;
  value: number;
};

export interface AdminSummaryData {
  totalGroupInfo: TotalGroupInfo[];
  activeBotNum: number;
  activeFlowNum: number;
  totalUser: number;
  activeUserNum: number;
  totalApiBotCalls?: number;
  totalApiFlowCalls?: number;
  botApiCalls?: number;
  flowApiCalls?: number;
  totalTotalCompletion: number;
  totalCompletion: number;
  totalPrompt: number;
  prompt: number;
  currentTotalCompletion: number;
  completion: number;
  totalEmbedding: number;
  embedding: number;
  totalUploadFile: number;
  totalDataStorage: number;
  teamsUserTotal: number;
  teamsActiveUser: number;
}

export type SummaryLLmList = {
  llmEngineName: string;
  timeRangeChatNum: number;
  accumulatedChatNum: number;
  timeRangeTokenNum: number;
  accumulatedTokenNum: number;
  timeRangeEmbeddingNum: number;
  accumulatedEmbeddingNum: number;
};

export enum DepartmentBoardSortType {
  ACTIVE_USER = ' ACTIVE_USER',
  ACTIVE_GROUP = 'ACTIVE_GROUP',
}

export type DepartmentBoardData = {
  sum: number;
  label: string;
};
