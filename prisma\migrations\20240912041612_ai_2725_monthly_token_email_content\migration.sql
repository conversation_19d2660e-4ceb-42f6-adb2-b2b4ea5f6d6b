-- This is an empty migration.
update "FeatureFlag" set "metaData" = (jsonb_build_object('percentage',jsonb_build_array('75','90','100'),'emailContent','Subject: [{groupName}] LLM Monthly Token Limit Exceeded Maximum in [{env}];Dear <PERSON><PERSON> Owner and <PERSON><PERSON>,<br/><br/>I hope this email captures your attention. We have monitored that the Actual Usage Ratio of your Bot [bot id:{groupId}, {groupName}] Monthly LLM Token in [{env}] is high as follows:<br/><br/> <table border="1" width="100%" cellspacing="0"><tr style="text-align: center"><th>LLM Model</th><th>Env</th><th>Actual Usage Ratio</th><th>Model Plan Quota</th></tr>{tr}</table>If it continues, it may cause some chat sessions or some functions to not respond normally. For more monthly token limit info, please see: <a href="{monthlyTokenLimitGuideUrl}"> Bot’s Monthly Token Limit </a><br/><br/><br/><h3>If it exceeds, </h3><p style="text-indent: 2em">Error Message: The system returns " [400-994-1] Bot monthly token limit exceeded." error message in playground.</p><h3>Recommended Action after exceeding: </h3><p style="text-indent: 2em">Promptly follow our <a href="{userGuideUrl}"> user guide </a> to apply for monthly token increasing and solve this issue.</p><br/>Thank you for your attention and understanding!<br/><br/>Best regards,<br/><br/>Gen AI Application Support <br/><br/>'))
                     WHERE "key" = 'ALERT.CONFIG_MONTHLY_TOKEN_LIMIT_SEND_ALERT_CONDITION';

