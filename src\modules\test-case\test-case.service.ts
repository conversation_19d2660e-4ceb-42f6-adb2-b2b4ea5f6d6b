import { Injectable, Logger } from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import {
  AutoTestCreateTestCaseDto,
  AutoTestUpdateTestCaseDto,
} from 'src/providers/test-case/auto-test.interface';
import { AutoTestService } from 'src/providers/test-case/auto-test.service';
import { UserRequest } from '../auth/auth.interface';
import { BotSecurityService } from '../bot-security/bot-security.service';
import { GroupsService } from '../groups/groups.service';
import { CreateTestCaseDto } from './dto/create-auto-test.dto';
import { ByPassPiiType, ExcelHeaderKey, TestCaseConfig } from './dto/test-case.dto';
import { UpdateTestCaseDto } from './dto/update-auto-test.dto';
import { ExcelService } from 'src/providers/excel/excel.service';
import { SupportFileExt } from 'src/providers/excel/excel.interface';
import { UsersService } from '../users/users.service';
import { formatDateRange } from '../summary/summary.utils';
import { LlmEnginesService } from '../llm-engines/llm-engines.service';
import { SecurityScanType } from '../llm-models/dto/chat-llm-model.dto';

@Injectable()
export class TestCaseService {
  private logger: Logger = new Logger(TestCaseService.name);
  private readonly downloadResultDefaultFetchSize = 30;
  private readonly piiScanTypeMapping: { [key: string]: SecurityScanType } = {
    PromptInjection: SecurityScanType.PROMPT_INJECTION,
    Anonymize: SecurityScanType.ANONYMIZE,
  };
  private readonly downloadResultHeaderKeys = [
    'id',
    'iterationsRound',
    'question',
    'response',
    'validationPrompt',
    'validationResponse',
    'testCaseExecutionResultStatus',
    'tokenUsage',
    'responseTime',
  ];
  private readonly downloadTestCaseHeaderKeys = [
    'name',
    'testCaseStatus',
    'latestExecution',
    'updatedBy',
    'createdBy',
  ];

  private readonly downloadTestCaseExecutionHeaderKeys = [
    'id',
    'triggerAt',
    'triggerBy',
    'completeAt',
    'testCaseExecutionStatus',
    'testCaseExecutionResultInfo',
  ];

  constructor(
    private readonly autoTestService: AutoTestService,
    private readonly groupsService: GroupsService,
    private readonly excelService: ExcelService,
    private readonly botSecurityService: BotSecurityService,
    private readonly usersService: UsersService,
    private readonly llmEnginesService: LlmEnginesService,
  ) {}

  async create(groupId: number, createTestCaseDto: CreateTestCaseDto, userReq: UserRequest) {
    await this.groupsService.isExist(groupId);
    const autoTestCreateTestCaseDto: AutoTestCreateTestCaseDto = {
      ...createTestCaseDto,
      updatedBy: userReq.user.id,
      createdBy: userReq.user.id,
      groupId,
    };
    await this.checkTestCasePiiScan(createTestCaseDto.config, groupId, createTestCaseDto);
    await this.checkLLmTokenLimit(groupId, createTestCaseDto);
    const testCaseCreated = await this.autoTestService.createTestCase(autoTestCreateTestCaseDto);
    return testCaseCreated;
  }

  async createAndRun(groupId: number, createTestCaseDto: CreateTestCaseDto, userReq: UserRequest) {
    await this.groupsService.isExist(groupId);
    const autoTestCreateTestCaseDto: AutoTestCreateTestCaseDto = {
      ...createTestCaseDto,
      updatedBy: userReq.user.id,
      createdBy: userReq.user.id,
      groupId,
    };
    await this.checkTestCasePiiScan(createTestCaseDto.config, groupId, createTestCaseDto);
    await this.checkLLmTokenLimit(groupId, createTestCaseDto);
    return await this.autoTestService.createTestCaseAndRun(autoTestCreateTestCaseDto);
  }

  async checkExcelContentAndReturn(file: Express.Multer.File, fileExtname: string) {
    const headerKeys = Object.values(ExcelHeaderKey);
    const fileContent = await this.excelService.readExcel(
      file.buffer,
      headerKeys,
      fileExtname === 'xlsx' ? SupportFileExt.XLSX : SupportFileExt.CSV,
    );

    if ((fileContent?.length && fileContent.length === 0) || !fileContent) {
      throw new ApiException(ErrorCode.TEST_CASE_QUESTION_NOT_NULL);
    }
    const testCaseConfig = plainToClass<TestCaseConfig, any[]>(TestCaseConfig, fileContent);
    const checkQuestion = testCaseConfig.filter(
      (item) => item?.question?.trim()?.length && item?.question?.trim()?.length !== 0,
    );
    if (checkQuestion.length === 0) {
      throw new ApiException(ErrorCode.TEST_CASE_QUESTION_NOT_NULL);
    }
    return checkQuestion;
  }

  /**
   * @description when tile test config have pii will throw the APiExceptions
   * @param testCaseConfigs
   * @param groupId
   * @returns {Boolean}
   */
  async checkTestCasePiiScan(
    testCaseConfigs: TestCaseConfig[],
    groupId: number,
    inputDto: { byPassPii?: ByPassPiiType },
  ) {
    const needInputScanType = await this.getNeedInputScanType(groupId, inputDto.byPassPii);
    const isEnabledPii = await this.botSecurityService.EnablePromptPIICheckFLAG();
    if (!isEnabledPii || needInputScanType == SecurityScanType.OFF) {
      return true;
    }
    for (const testCaseConfig of testCaseConfigs) {
      await this.botSecurityService.PromptDetect(
        testCaseConfig.question,
        groupId,
        null,
        needInputScanType,
      );
    }
    return true;
  }

  private async getNeedInputScanType(
    groupId: number,
    byPassPii?: ByPassPiiType,
  ): Promise<SecurityScanType> {
    let needInputScanType = SecurityScanType.ON;
    if (!byPassPii) {
      return needInputScanType;
    }
    const groupSecuritySetting = await this.botSecurityService.findByGroupId(groupId);
    const inputScanners = Object.values(groupSecuritySetting?.PIIScanners?.inputScanners);
    const hardScan = inputScanners
      .filter((item: any) => item?.securityLevel && item?.securityLevel == 'HARD' && item?.type)
      .map((item) => this.piiScanTypeMapping[(item as any).type as string]);
    if (hardScan.length >= inputScanners.length) {
      return needInputScanType;
    }
    // if bot botSecurityService inputScanners have change need update below logic
    switch (byPassPii) {
      case ByPassPiiType.PROMPT_INJECTION:
        needInputScanType = SecurityScanType.ANONYMIZE;
        break;
      case ByPassPiiType.ANONYMIZE:
        needInputScanType = SecurityScanType.PROMPT_INJECTION;
        break;
      default:
        needInputScanType = SecurityScanType.OFF;
    }
    if (hardScan.length == 0) {
      return needInputScanType;
    }
    if (needInputScanType != hardScan[0]) {
      SecurityScanType.ON;
    }
  }

  async findAll(
    groupId: number,
    take: number,
    skip: number,
    where: Record<string, string | number>,
    orderBy: Record<string, 'asc' | 'desc'>,
  ) {
    const testCasePage = await this.autoTestService.findAllTestCase(
      groupId,
      take,
      skip,
      where,
      orderBy,
    );
    const testCasePageList = await this.injectUserName(testCasePage.list, [
      'createdBy',
      'updatedBy',
    ]);
    testCasePage.list = testCasePageList;
    return testCasePage;
  }

  async findOne(id: number, groupId: number): Promise<AutoTestCreateTestCaseDto> {
    await this.groupsService.isExist(groupId);
    const testCase = await this.autoTestService.findOneTestCaseById(id);
    if (testCase.groupId !== groupId) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    const testCaseResult = await this.injectUserName(testCase, ['createdBy', 'updatedBy']);
    return testCaseResult;
  }

  async findExecutionOne(executionId: number, groupId: number) {
    await this.groupsService.isExist(groupId);
    const TestCaseExecution = await this.autoTestService.findOneTestCaseExecution(executionId);
    const TestCaseExecutionResult = await this.injectUserName(TestCaseExecution, ['triggerBy']);
    return TestCaseExecutionResult;
  }

  async findExecutionById(
    groupId: number,
    testCaseId: number,
    take: number,
    skip: number,
    where: Record<string, string | number>,
    orderBy: Record<string, 'asc' | 'desc'>,
  ) {
    await this.groupsService.isExist(groupId);
    const testCaseExecutionPage = await this.autoTestService.findAllTestCaseExecution(
      groupId,
      testCaseId,
      take,
      skip,
      where,
      orderBy,
    );
    const testCaseExecutionList = testCaseExecutionPage.list;
    testCaseExecutionPage.list = await this.injectUserName(testCaseExecutionList, ['triggerBy']);
    return testCaseExecutionPage;
  }

  async findExecutionResultById(
    groupId: number,
    executionId: number,
    take: number,
    skip: number,
    where: Record<string, string | number>,
    orderBy: Record<string, 'asc' | 'desc'>,
  ) {
    await this.groupsService.isExist(groupId);
    return await this.autoTestService.findAllTestCaseExecutionResult(
      groupId,
      executionId,
      take,
      skip,
      where,
      orderBy,
    );
  }

  async update(
    id: number,
    groupId: number,
    updateTestCaseDto: UpdateTestCaseDto,
    userReq: UserRequest,
  ) {
    const testCase = await this.findOne(id, groupId);
    const updateDto: AutoTestUpdateTestCaseDto = {
      ...updateTestCaseDto,
      updatedBy: userReq.user.id,
    };
    await this.checkLLmTokenLimit(groupId, updateTestCaseDto);
    await this.checkTestCasePiiScan(
      updateTestCaseDto.config ?? testCase.config,
      groupId,
      updateTestCaseDto,
    );
    const updatedTestCase = await this.autoTestService.updateTestCase(id, updateDto);
    return updatedTestCase;
  }

  async downloadTestCasePrompt(groupId: number, testCaseId: number) {
    const testCase = await this.findOne(testCaseId, groupId);
    const result = await this.excelService.downloadExcel(
      Object.values(ExcelHeaderKey),
      testCase.config,
    );
    return result;
  }

  async downloadTestCaseExecutions(
    groupId: number,
    testCaseId: number,
    from?: string,
    to?: string,
  ) {
    const { fromDate, toDate } = formatDateRange(from, to);
    await this.groupsService.isExist(groupId);
    const result = await this.downloadPageExcel(
      [groupId, testCaseId],
      'findAllTestCaseExecution',
      this.downloadTestCaseExecutionHeaderKeys,
      ['triggerBy'],
      {
        triggerAt: {
          gte: fromDate,
          lte: toDate,
        },
      },
    );
    return result;
  }

  async downloadTestCaste(groupId: number) {
    await this.groupsService.isExist(groupId);
    const result = await this.downloadPageExcel(
      [groupId],
      'findAllTestCase',
      this.downloadTestCaseHeaderKeys,
      ['createdBy', 'updatedBy'],
    );
    return result;
  }

  async downloadTestCaseExecutionResult(groupId: number, executionId: number) {
    await this.groupsService.isExist(groupId);
    const result = await this.downloadPageExcel(
      [groupId, executionId],
      'findAllTestCaseExecutionResult',
      this.downloadResultHeaderKeys,
      [],
    );
    return result;
  }

  async updateAndRun(
    id: number,
    groupId: number,
    updateTestCaseDto: UpdateTestCaseDto,
    userReq: UserRequest,
  ) {
    const testCase = await this.findOne(id, groupId);
    const updateDto: AutoTestUpdateTestCaseDto = {
      ...updateTestCaseDto,
      updatedBy: userReq.user.id,
    };
    await this.checkLLmTokenLimit(groupId, updateTestCaseDto);
    await this.checkTestCasePiiScan(
      updateTestCaseDto.config ?? testCase.config,
      groupId,
      updateTestCaseDto,
    );
    const updatedTestCase = await this.autoTestService.updateTestCaseAndRun(id, updateDto);
    return updatedTestCase;
  }

  async remove(id: number, groupId: number) {
    const testCase = await this.findOne(id, groupId);
    await this.autoTestService.removeTestCase(id);
    return testCase;
  }

  private async injectUserName(item: any | any[], replaceKeys: string[]) {
    if (item instanceof Array) {
      const userIds = Array.from(
        new Set<number>(
          item
            .map((_item) => [...replaceKeys.map((key) => _item[key])])
            .join(',')
            .split(',')
            .filter((_item) => _item)
            .map((__item) => parseInt(__item)),
        ),
      );
      if (userIds.length > 0) {
        const userMap = await this.fetchUserNameMap(userIds);
        return item.map((_item) => ({
          ..._item,
          ...replaceKeys.reduce((result, key) => {
            result[key] = userMap[_item[key]];
            return result;
          }, {}),
        }));
      }
      return item;
    }

    const userIds = [...replaceKeys.map((key) => item[key])].filter((_item) => _item);
    if (userIds.length > 0) {
      const userMap = await this.fetchUserNameMap(userIds);
      replaceKeys.forEach((key) => (item[key] = userMap[item[key]]));
    }
    return item;
  }

  private async checkLLmTokenLimit(
    groupId: number,
    testCaseInputDto: UpdateTestCaseDto | CreateTestCaseDto,
  ) {
    if (
      (testCaseInputDto?.llmParams ?? '' == '') ||
      (testCaseInputDto?.llmParams?.model ?? '' == '')
    ) {
      return;
    }
    const needCheckLLmKey: string[] = [testCaseInputDto.llmParams.model];
    if (testCaseInputDto.llmParams.model != testCaseInputDto.validationModel) {
      needCheckLLmKey.push(testCaseInputDto.validationModel);
    }
    await this.llmEnginesService.checkChatTokenLimit(groupId, needCheckLLmKey);
  }

  private async fetchUserNameMap(userIds: number[]): Promise<{ [key: number]: string }> {
    const userInfo = await this.usersService.getUsersByIds(userIds);
    return userInfo.reduce((result, currentValue) => {
      result[currentValue.id] = currentValue.name;
      return result;
    }, {});
  }

  private async downloadPageExcel(
    entityIds: number[],
    pageFunctionName: string,
    headerKeys: string[],
    injectUserNameKes: string[],
    where?: any,
  ) {
    const whereInput = where ? where : {};
    const orderBy = { id: 'asc' } as Record<string, 'asc' | 'desc'>;
    const take = this.downloadResultDefaultFetchSize;
    const skip = 0;
    const data = await this.autoTestService[pageFunctionName](
      ...entityIds,
      take,
      skip,
      whereInput,
      orderBy,
    );
    const testCaseExecutionResult = data.list;
    if (data.count > data.list.length) {
      for (let i = 1; i < data.count / take; i++) {
        const pageData = await this.autoTestService[pageFunctionName](
          ...entityIds,
          take,
          i * take,
          whereInput,
          orderBy,
        );
        testCaseExecutionResult.push(...pageData.list);
      }
    }
    const downloadList = await this.injectUserName(testCaseExecutionResult, injectUserNameKes);
    return await this.excelService.downloadExcel(headerKeys, downloadList);
  }
}
