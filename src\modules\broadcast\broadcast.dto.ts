import {
  <PERSON><PERSON>num,
  <PERSON>NotEmpty,
  IsO<PERSON>al,
  IsString,
  IsN<PERSON>ber,
  IsDate,
} from 'class-validator';
import { Type } from 'class-transformer';

export class BroadcastDto {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsNotEmpty()
  @IsString()
  htmlContent: string;

  @IsNotEmpty()
  @IsString()
  textContent: string;

  channel: string;

  @IsNumber()
  @IsOptional()
  userGroupId?: number;

  @IsNotEmpty()
  @IsString()
  targetType: string;

  @IsNotEmpty()
  @IsString()
  scheduleType: string;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  scheduleTime?: Date;
}

export class UserNotificationDto{
  userId: number;
  userName: string;
  email: string;
  ccc: string;
  businessUnit: string;
  roleName: string;
  groupNames?: string[];
}

export class UserNotificationResponse {
  userNotifications: UserNotificationDto[];
  groupNames?: string[];
}

