import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { TokensModule } from '../../providers/tokens/tokens.module';
import { PermissionsGroupController } from './permissions-group.controller';
import { PermissionsService } from './permissions.service';
import { GroupsModule } from '../groups/groups.module';

@Module({
  imports: [PrismaModule, ConfigModule, TokensModule, forwardRef(() => GroupsModule)],
  controllers: [PermissionsGroupController],
  providers: [PermissionsService],
  exports: [PermissionsService],
})
export class PermissionsModule {}
