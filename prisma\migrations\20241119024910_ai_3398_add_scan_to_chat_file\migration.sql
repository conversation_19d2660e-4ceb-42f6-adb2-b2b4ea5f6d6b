-- CreateEnum
CREATE TYPE "ChatFileVerifyStatus" AS ENUM ('VERIFYING', 'VERIFY_FAILED', 'VERIFY_SUCCESS');

-- AlterTable
ALTER TABLE "ChatFile" ADD COLUMN     "detectedPii" TEXT,
ADD COLUMN     "errCode" TEXT,
ADD COLUMN     "errorMsg" TEXT,
ADD COLUMN     "fullScanReportCreatedAt" TIMESTAMP(3),
ADD COLUMN     "fullScanReportPath" TEXT,
ADD COLUMN     "fullScanReportUpdatedAt" TIMESTAMP(3),
ADD COLUMN     "hasPii" TEXT,
ADD COLUMN     "hasPromptInjection" "HasPromptInjection",
ADD COLUMN     "malwareRating" TEXT,
ADD COLUMN     "piiFileStatus" "PiiFileStatus",
ADD COLUMN     "priority" INTEGER,
ADD COLUMN     "scanMalwareStatus" "ScanMalwareStatus",
ADD COLUMN     "verifyErrCode" TEXT,
ADD COLUMN     "verifyErrorMsg" TEXT,
ADD COLUMN     "verifyStatus" "ChatFileVerifyStatus";
