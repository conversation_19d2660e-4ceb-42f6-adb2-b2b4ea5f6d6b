/*
  Warnings:

  - The values [MARGIN] on the enum `ModelPriceType` will be removed. If these variants are still used in the database, this will fail.
  - A unique constraint covering the columns `[startDate,endDate,groupId,modelPriceUnitId,engineSlug,key,feature,channel]` on the table `ModelPriceSummary` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "FileType" ADD VALUE 'MONTHLY_PRICE_REPORT_PER_GROUP';
ALTER TYPE "FileType" ADD VALUE 'MONTHLY_PRICE_REPORT';

-- AlterEnum
ALTER TYPE "ModelPriceEventType" ADD VALUE 'CREATE';

-- AlterEnum
BEGIN;
CREATE TYPE "ModelPriceType_new" AS ENUM ('COST_SUGGESTION', 'COST', 'PRICE');
ALTER TABLE "ModelPriceEvent" ALTER COLUMN "modelPriceType" TYPE "ModelPriceType_new" USING ("modelPriceType"::text::"ModelPriceType_new");
ALTER TABLE "ModelPrice" ALTER COLUMN "modelPriceType" TYPE "ModelPriceType_new" USING ("modelPriceType"::text::"ModelPriceType_new");
ALTER TYPE "ModelPriceType" RENAME TO "ModelPriceType_old";
ALTER TYPE "ModelPriceType_new" RENAME TO "ModelPriceType";
DROP TYPE "ModelPriceType_old";
COMMIT;

-- CreateIndex
CREATE INDEX "ModelPriceSummary_groupId_idx" ON "ModelPriceSummary"("groupId");

-- CreateIndex
CREATE INDEX "ModelPriceSummary_modelPriceUnitId_idx" ON "ModelPriceSummary"("modelPriceUnitId");

-- CreateIndex
CREATE UNIQUE INDEX "ModelPriceSummary_startDate_endDate_groupId_modelPriceUnitI_key" ON "ModelPriceSummary"("startDate", "endDate", "groupId", "modelPriceUnitId", "engineSlug", "key", "feature", "channel");
