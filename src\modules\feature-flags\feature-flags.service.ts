import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { FeatureFlagTargetType, Prisma, FeatureFlag, Group } from '@prisma/client';

import { FeatureFlagDTO, GetOverrideFeatureFlagsOrDefaultDTO } from './feature-flags-model.dto';

import { FeatureFlagKey } from './feature-flags.constants';
import { RedisService } from 'src/providers/redis/redis.service';
import {
  FEATURE_FLAG_OVERRIDE_ENV_ALL_KEYS,
  FEATURE_FLAG_OVERRIDE_ENV_KEY,
  FEATURE_FLAG_OVERRIDE_GROUP_ALL_KEYS,
  FEATURE_FLAG_OVERRIDE_GROUP_KEY,
  FEATURE_FLAG_SYSTEM_ALL_KEYS,
  FEATURE_FLAG_SYSTEM_KEY,
  FEATURE_FLAG_TYPE,
} from 'src/providers/redis/redis.constants';

@Injectable()
export class FeatureFlagService {
  private logger = new Logger(FeatureFlagService.name);
  private readonly TTL: number = 60 * 60 * 24;
  constructor(
    private prisma: PrismaService,
    private redisService: RedisService,
  ) {}

  async getAll(
    skip?: number,
    take?: number,
    where?: Record<string, number | string | boolean>,
    orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<FeatureFlag[]> {
    const newWhere: Prisma.FeatureFlagWhereInput = this.ORConditionCheck(where);
    const featureFlagList = await this.prisma.featureFlag.findMany({
      skip,
      take,
      where: newWhere,
      orderBy,
    });
    return featureFlagList.map((featureFlag) => this.convertMetaDataToString(featureFlag));
  }

  //(will not affect existing where format)if want to trigger fuzzy search on multiple fields
  // where field needs to be in this format: where={fieldA}|{fieldB}: contains {fuzzy search content}
  ORConditionCheck(where: Record<string, number | string | boolean>) {
    // if it does not exist ,returns an empty object
    if (!where) {
      return {};
    }
    //List all Keys
    const whereKeys = Object.keys(where);
    const orWhereList = [];

    whereKeys.forEach((key) => {
      if (key.includes('|')) {
        // first isolate the detected Key from other Keys
        const { [key]: keyValue, ...otherWhere } = where;
        //split keys and combine conditions
        const fieldList = key.split('|');
        fieldList.forEach((field) => {
          //field and keyvalue must have values
          if (field && keyValue) {
            orWhereList.push({ [field]: keyValue });
          }
        });
        //remove the detected Key
        where = otherWhere;
      }
    });

    let newWhere: Prisma.FeatureFlagWhereInput;
    if (orWhereList.length > 0) {
      newWhere = {
        ...where,
        OR: orWhereList,
      };
    } else {
      newWhere = where;
    }

    return newWhere;
  }

  async getCount(where?: Record<string, number | string | boolean>): Promise<number> {
    const newWhere: Prisma.FeatureFlagWhereInput = this.ORConditionCheck(where);
    return await this.prisma.featureFlag.count({ where: newWhere });
  }

  async create(data: FeatureFlagDTO): Promise<FeatureFlag> {
    const featureFlag = await this.prisma.featureFlag.create({
      data: {
        ...data,
        updatedAt: new Date(),
      },
    });
    if (featureFlag) {
      await this.deleteFeatureFlagCache(FEATURE_FLAG_TYPE.SYSTEM);
    }
    return featureFlag;
  }

  async update(id: number, data: FeatureFlagDTO): Promise<FeatureFlag> {
    const featureFlag = await this.prisma.featureFlag.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    });
    if (featureFlag) {
      await this.deleteFeatureFlagCache(FEATURE_FLAG_TYPE.SYSTEM);
      await this.deleteFeatureFlagCache(FEATURE_FLAG_TYPE.SYSTEM, featureFlag.key);
    }
    return featureFlag;
  }

  async delete(id: number): Promise<FeatureFlag> {
    const featureFlag = await this.prisma.featureFlag.delete({ where: { id } });
    if (featureFlag) {
      await this.deleteFeatureFlagCache(FEATURE_FLAG_TYPE.SYSTEM);
      await this.deleteFeatureFlagCache(FEATURE_FLAG_TYPE.SYSTEM, featureFlag.key);
    }
    return featureFlag;
  }

  async getOne(key: string): Promise<FeatureFlag> {
    const flag = await this.getDefaultFeatureFlagFromCache(key);
    if (flag) {
      return {
        key: flag.key,
        value: flag.value,
        metaData: flag.metaData,
        description: flag.description,
        isEnabled: flag.isEnabled,
      } as FeatureFlag;
    }
    return null;
  }

  async getById(id: number): Promise<FeatureFlag> {
    return await this.prisma.featureFlag.findUnique({ where: { id } });
  }

  /**
   * @description get value from FeatureFlagOverride, if no exist then get from system FeatureFlag
   * @param groupId
   * @param key
   * @returns
   */
  async getOverrideFeatureFlagOrDefault(
    groupId: number | undefined,
    key: string,
  ): Promise<GetOverrideFeatureFlagsOrDefaultDTO> {
    if (groupId) {
      //get override feature flag, priority: GROUP > ENV
      const group: Group = await this.prisma.group.findUnique({ where: { id: groupId } });
      if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
      //get GROUP override feature flag
      let featureFlag: GetOverrideFeatureFlagsOrDefaultDTO =
        await this.getOverrideFeatureFlagByTargetFromCache(
          FEATURE_FLAG_TYPE.GROUP,
          key,
          FeatureFlagTargetType[group.groupType],
          String(groupId),
        );
      if (featureFlag) {
        return featureFlag;
      } else {
        //get ENV override feature flag
        featureFlag = await this.getOverrideFeatureFlagByTargetFromCache(
          FEATURE_FLAG_TYPE.ENV,
          key,
          FeatureFlagTargetType.ENV,
          group.env,
        );
        if (featureFlag) {
          return featureFlag;
        }
      }
    }
    return await this.getDefaultFeatureFlagFromCache(key);
  }

  async getClientSideFeatureFlags(
    groupId?: number,
    isEnabledOnly?: boolean,
  ): Promise<GetOverrideFeatureFlagsOrDefaultDTO[]> {
    this.logger.log(
      `get client side feature flags, groupId: ${groupId || ''}, isEnabledOnly: ${
        typeof isEnabledOnly === 'boolean' ? isEnabledOnly : ''
      }`,
    );
    let systemFlagList: FeatureFlagDTO[] = await this.getAllDefaultFeatureFlagsFromCache();
    systemFlagList = systemFlagList.filter((flag) => flag.isForClientSide === true);
    if (groupId) {
      //get all override feature flags
      const group: Group = await this.prisma.group.findUnique({ where: { id: groupId } });
      if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
      const groupFeatureFlagList: GetOverrideFeatureFlagsOrDefaultDTO[] =
        await this.getAllOverrideFeatureFlagsByTargetFromCache(
          FEATURE_FLAG_TYPE.GROUP,
          FeatureFlagTargetType[group.groupType],
          String(groupId),
        );
      const envFeatureFlagList: GetOverrideFeatureFlagsOrDefaultDTO[] =
        await this.getAllOverrideFeatureFlagsByTargetFromCache(
          FEATURE_FLAG_TYPE.ENV,
          FeatureFlagTargetType.ENV,
          group.env,
        );
      let overrideOrDefaultFeatureFlagList = await this.generateOverrideOrDefaultFeatureFlags(
        systemFlagList,
        groupFeatureFlagList,
        envFeatureFlagList,
      );
      if (isEnabledOnly)
        overrideOrDefaultFeatureFlagList = overrideOrDefaultFeatureFlagList.filter(
          (flag) => flag.isEnabled === true,
        );
      return overrideOrDefaultFeatureFlagList;
    } else {
      //get all default feature flags
      if (isEnabledOnly) systemFlagList = systemFlagList.filter((flag) => flag.isEnabled === true);
      return systemFlagList.map((flag) => {
        return this.transFeatureFlag(flag);
      });
    }
  }

  private async generateOverrideOrDefaultFeatureFlags(
    systemFlagList: FeatureFlagDTO[],
    groupFlagList: GetOverrideFeatureFlagsOrDefaultDTO[],
    envFlagList: GetOverrideFeatureFlagsOrDefaultDTO[],
  ): Promise<GetOverrideFeatureFlagsOrDefaultDTO[]> {
    const featureFlags: GetOverrideFeatureFlagsOrDefaultDTO[] = [];
    systemFlagList.forEach((systemFlag) => {
      let resultFlag: GetOverrideFeatureFlagsOrDefaultDTO = groupFlagList.find(
        (flag) => flag.key === systemFlag.key,
      );
      if (!resultFlag) {
        resultFlag = envFlagList.find((flag) => flag.key === systemFlag.key);
      }
      const flagResponse: GetOverrideFeatureFlagsOrDefaultDTO = {
        value: resultFlag?.value ?? systemFlag.value,
        isEnabled: resultFlag?.isEnabled ?? systemFlag.isEnabled,
        metaData: resultFlag?.metaData ?? systemFlag.metaData,
        key: systemFlag.key,
        description: systemFlag.description,
      };
      featureFlags.push(flagResponse);
    });
    return featureFlags;
  }

  //get value from redis cache, if not exist in cache then get from db and set to redis
  private async getAllDefaultFeatureFlagsFromCache(): Promise<FeatureFlagDTO[]> {
    const ttl = await this.getFeatureFlagTtlFromCache();
    const valueFunction = async () => {
      const featureFlags = await this.getAllDefaultFeatureFlagsFromDb();
      this.logger.log(
        `set value to cache, key: ${FEATURE_FLAG_SYSTEM_ALL_KEYS}, value length: ${featureFlags.length}`,
      );
      return featureFlags;
    };
    return await this.redisService.getOrSet<FeatureFlagDTO[]>(
      FEATURE_FLAG_SYSTEM_ALL_KEYS,
      valueFunction,
      ttl,
    );
  }

  async getAllDefaultFeatureFlagsFromDb(): Promise<FeatureFlagDTO[]> {
    const systemFlags: FeatureFlag[] = await this.getAll();
    return systemFlags.map((flag) => {
      return this.transFeatureFlagDTO(flag);
    });
  }

  async getDefaultFeatureFlagFromCache(
    featureFlagKey: string,
  ): Promise<GetOverrideFeatureFlagsOrDefaultDTO> {
    const redisKey = await this.getFeatureFlagRedisKey(FEATURE_FLAG_TYPE.SYSTEM, featureFlagKey);
    const valueFunction = async () => {
      const featureFlag = await this.getDefaultFeatureFlagFromDb(featureFlagKey);
      if (featureFlag) {
        this.logger.log(
          `set value to cache, key: ${redisKey}, feature flag key: ${featureFlag.key}`,
        );
      }
      return featureFlag;
    };
    const ttl = await this.getFeatureFlagTtlFromCache();
    return await this.redisService.getOrSet<GetOverrideFeatureFlagsOrDefaultDTO>(
      redisKey,
      valueFunction,
      ttl,
    );
  }

  async getDefaultFeatureFlagFromDb(featureFlagKey: string): Promise<FeatureFlagDTO> {
    const flag: FeatureFlag = await this.prisma.featureFlag.findUnique({
      where: { key: featureFlagKey },
    });
    return flag ? this.transFeatureFlagDTO(flag) : null;
  }

  //featureFlagKey not null/empty will get the specify feature flag cache key **:KEY:{KEY}
  // otherwise get the all system/group/env feature flags list cache key **:KEYS
  private async getFeatureFlagRedisKey(
    featureFlagType: FEATURE_FLAG_TYPE,
    featureFlagKey?: string,
    targetValue?: string,
  ): Promise<string> {
    let redisKey = '';
    switch (featureFlagType) {
      case FEATURE_FLAG_TYPE.SYSTEM: {
        redisKey = featureFlagKey
          ? FEATURE_FLAG_SYSTEM_KEY.replace('{KEY}', featureFlagKey)
          : FEATURE_FLAG_SYSTEM_ALL_KEYS;
        break;
      }
      case FEATURE_FLAG_TYPE.GROUP: {
        redisKey = featureFlagKey
          ? FEATURE_FLAG_OVERRIDE_GROUP_KEY.replace('{KEY}', featureFlagKey)
          : FEATURE_FLAG_OVERRIDE_GROUP_ALL_KEYS;
        redisKey = redisKey.replace('{GROUP_ID}', targetValue);
        break;
      }
      case FEATURE_FLAG_TYPE.ENV: {
        redisKey = featureFlagKey
          ? FEATURE_FLAG_OVERRIDE_ENV_KEY.replace('{KEY}', featureFlagKey)
          : FEATURE_FLAG_OVERRIDE_ENV_ALL_KEYS;
        redisKey = redisKey.replace('{ENV}', targetValue);
        break;
      }
      default: {
        throw new ApiException(ErrorCode.FEATURE_FLAG_TYPE_INVALID);
      }
    }
    return redisKey;
  }

  //get value from cache, if not exist then get from db and set to redis
  private async getAllOverrideFeatureFlagsByTargetFromCache(
    featureFlagType: FEATURE_FLAG_TYPE,
    targetType: FeatureFlagTargetType,
    targetValue: string,
  ): Promise<GetOverrideFeatureFlagsOrDefaultDTO[]> {
    const redisKey = await this.getFeatureFlagRedisKey(featureFlagType, null, targetValue);
    const valueFunction = async () => {
      const featureFlags: GetOverrideFeatureFlagsOrDefaultDTO[] =
        await this.getOverrideFeatFlagsByTargetFromDb(targetType, targetValue);
      this.logger.log(
        `set value to cache, key: ${redisKey}, value length: ${featureFlags?.length || 0}`,
      );
      return featureFlags ? featureFlags : [];
    };
    const ttl = await this.getFeatureFlagTtlFromCache();
    return await this.redisService.getOrSet<FeatureFlagDTO[]>(redisKey, valueFunction, ttl);
  }

  private async getOverrideFeatureFlagByTargetFromCache(
    featureFlagType: FEATURE_FLAG_TYPE,
    featureFlagKey: string,
    targetType: FeatureFlagTargetType,
    targetValue: string,
  ): Promise<GetOverrideFeatureFlagsOrDefaultDTO> {
    const redisKey = await this.getFeatureFlagRedisKey(
      featureFlagType,
      featureFlagKey,
      targetValue,
    );
    const valueFunction = async () => {
      const flags: GetOverrideFeatureFlagsOrDefaultDTO[] =
        await this.getOverrideFeatFlagsByTargetFromDb(targetType, targetValue, featureFlagKey);
      this.logger.log(
        `set value to cache, key: ${redisKey}, feature flag key: ${flags?.[0]?.key || ''}`,
      );
      return flags?.[0] ? flags[0] : {};
    };
    const ttl = await this.getFeatureFlagTtlFromCache();
    const featureFlag = await this.redisService.getOrSet<GetOverrideFeatureFlagsOrDefaultDTO>(
      redisKey,
      valueFunction,
      ttl,
    );
    if (featureFlag?.key) {
      return featureFlag;
    }
    return null;
  }

  async getOverrideFeatFlagsByTargetFromDb(
    targetType: FeatureFlagTargetType,
    targetValue: string,
    featureFlagKey?: string,
  ): Promise<GetOverrideFeatureFlagsOrDefaultDTO[]> {
    const where: Prisma.FeatureFlagWhereInput = {};
    if (featureFlagKey) where.key = featureFlagKey;
    where.featureFlagOverrides = {
      some: {
        targetType: targetType,
        targetValue: targetValue,
      },
    };
    const flags = await this.prisma.featureFlag.findMany({
      where,
      include: {
        featureFlagOverrides: {
          where: {
            targetType: targetType,
            targetValue: targetValue,
          },
        },
      },
    });
    return flags?.map((flag) => {
      return {
        key: flag.key,
        value: flag.featureFlagOverrides?.[0]?.value ?? flag.value,
        description: flag.description,
        metaData: flag.featureFlagOverrides?.[0]?.metaData ?? flag.metaData,
        isEnabled: flag.featureFlagOverrides?.[0]?.isEnabled ?? flag.isEnabled,
      } as GetOverrideFeatureFlagsOrDefaultDTO;
    });
  }

  async getGroupControlFeatureFlags(
    groupId: number,
    keys: string[],
  ): Promise<GetOverrideFeatureFlagsOrDefaultDTO[]> {
    const flags: GetOverrideFeatureFlagsOrDefaultDTO[] = [];
    for (const key of keys) {
      const flag: GetOverrideFeatureFlagsOrDefaultDTO = await this.getOverrideFeatureFlagOrDefault(
        groupId,
        key,
      );
      flags.push(flag);
    }
    return flags;
  }

  //get FEATURE_FLAG_CACHE_TTL feature flag from redis,
  //if not exist then get from db and set to redis
  private async getFeatureFlagTtlFromCache(): Promise<number> {
    let ttl = this.TTL;
    const redisKey = await this.getFeatureFlagRedisKey(
      FEATURE_FLAG_TYPE.SYSTEM,
      FeatureFlagKey.CONFIG_FEATURE_FLAG_CACHE_TTL,
    );
    let response;
    let setRedis;
    try {
      response = await this.redisService.getCache(redisKey);
      setRedis = !response;
    } catch (error) {
      response = null;
      setRedis = false;
    }
    let featureFlag;
    if (response) {
      featureFlag = response;
    } else {
      featureFlag = await this.prisma.featureFlag.findUnique({
        where: {
          key: FeatureFlagKey.CONFIG_FEATURE_FLAG_CACHE_TTL,
        },
        select: {
          key: true,
          metaData: true,
          description: true,
          isEnabled: true,
          value: true,
        },
      });
    }
    if (featureFlag?.isEnabled) {
      ttl = featureFlag.metaData.value;
    }
    if (setRedis) {
      await this.redisService.createCache(redisKey, featureFlag ? featureFlag : {}, ttl);
    }
    return ttl;
  }

  //delete system/override feature flag cache
  //cache key , **:KEYS and **:KEY:{KEY} (decide by featureFlagKey if available)
  async deleteFeatureFlagCache(
    featureFlagType: FEATURE_FLAG_TYPE,
    featureFlagKey?: string,
    targetValue?: string,
  ) {
    const redisKey = await this.getFeatureFlagRedisKey(
      featureFlagType,
      featureFlagKey,
      targetValue,
    );
    await this.redisService.clearCache(redisKey);
  }

  private transFeatureFlag(
    flag: FeatureFlag | FeatureFlagDTO,
  ): GetOverrideFeatureFlagsOrDefaultDTO {
    return {
      key: flag.key,
      value: flag.value,
      description: flag.description,
      metaData: flag.metaData,
      isEnabled: flag.isEnabled,
    } as GetOverrideFeatureFlagsOrDefaultDTO;
  }

  private transFeatureFlagDTO(flag: FeatureFlag): FeatureFlagDTO {
    return {
      key: flag.key,
      value: flag.value,
      description: flag.description,
      metaData: flag.metaData,
      isEnabled: flag.isEnabled,
      isForClientSide: flag.isForClientSide,
    } as FeatureFlagDTO;
  }

  private convertMetaDataToString(featureFlag: FeatureFlag): FeatureFlag {
    featureFlag.metaData = JSON.stringify(featureFlag.metaData);
    return featureFlag;
  }

  async getAllGroupOverrideFeatureFlag(
    featureFlagKey: string,
  ): Promise<Map<string, GetOverrideFeatureFlagsOrDefaultDTO>> {
    const groupMap: Map<string, GetOverrideFeatureFlagsOrDefaultDTO> = new Map();
    const list = await this.prisma.featureFlagOverride.findMany({
      where: {
        targetType: FeatureFlagTargetType.BOT,
        featureFlag: {
          key: featureFlagKey,
        },
      },
      include: {
        featureFlag: true,
      },
    });
    if (list.length > 0) {
      list.forEach((item) => {
        groupMap.set(item.targetValue, {
          key: item.featureFlag.key,
          value: item.featureFlag.value,
          description: item.featureFlag.description,
          metaData: item.metaData,
          isEnabled: item.isEnabled,
        } as GetOverrideFeatureFlagsOrDefaultDTO);
      });
    }

    const defaultFeatureFlag = await this.getDefaultFeatureFlagFromCache(featureFlagKey);
    groupMap.set('default', defaultFeatureFlag);

    return groupMap;
  }
}
