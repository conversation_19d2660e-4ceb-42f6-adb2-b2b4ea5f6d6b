import { Logger } from '@nestjs/common';
import { Prisma, PrismaClient } from '@prisma/client';

export class PrismaExtendClient extends PrismaClient<Prisma.PrismaClientOptions, 'query'> {
  private logger = new Logger(PrismaExtendClient.name);
  constructor(options?: ConstructorParameters<typeof PrismaClient>[0]) {
    super(options);
    this.$on('query', (e) => {
      this.logger.debug(`executed ${e.query} ${e.params}`);
    });
    return this.$extends({
      query: {
        group: {
          async $allOperations({ model, operation, args, query }) {
            if (
              operation === 'count' ||
              operation === 'findMany' ||
              operation === 'findFirst' ||
              operation === 'updateMany'
            ) {
              args.where = { isDeprecated: false, ...args.where };
            }
            return query(args);
          },
        },
        modelFile: {
          async $allOperations({ model, operation, args, query }) {
            if (
              operation === 'findMany' ||
              operation === 'findFirst' ||
              operation === 'updateMany' ||
              operation === 'findUnique'
            ) {
              args.where = { ...args.where, deletedAt: null };
            }
            return query(args);
          },
        },
      },
    }) as this;
  }
}
