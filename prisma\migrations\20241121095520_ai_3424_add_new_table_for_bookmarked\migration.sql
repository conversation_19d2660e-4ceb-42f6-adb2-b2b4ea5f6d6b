-- CreateEnum
CREATE TYPE "UserBookmarkEntityType" AS ENUM ('GROUP', 'MESSAGE_TEMPLATE');

-- CreateTable
CREATE TABLE "UserBookmark" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "entityId" INTEGER NOT NULL,
    "entityType" "UserBookmarkEntityType" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserBookmark_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "UserBookmark_user_entity_index" ON "UserBookmark"("userId", "entityId", "entityType");

-- CreateIndex
CREATE INDEX "UserBookmark_index" ON "UserBookmark"("id");

-- CreateIndex
CREATE INDEX "UserBookmark_entityId_idx" ON "UserBookmark"("entityId");
