import { Module } from '@nestjs/common';
import { TasksService } from '../../providers/tasks/tasks.service';
import { <PERSON>chesController } from './batches.controller';

import { ConfigModule } from '@nestjs/config';
import { DomainsModule } from '../../modules/domains/domains.module';
import { ElasticSearchModule } from '../../providers/elasticsearch/elasticsearch.module';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { UsersModule } from '../users/users.module';
import { MetricsModule } from '../metrics/metrics.module';

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
    ElasticSearchModule,
    DomainsModule,
    UsersModule,
    MetricsModule,
  ],
  controllers: [BatchesController],
  providers: [TasksService],
  exports: [TasksService],
})
export class BatchesModule {}
