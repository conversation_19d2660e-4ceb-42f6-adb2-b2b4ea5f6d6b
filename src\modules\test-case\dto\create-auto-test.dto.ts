import {
  IsInt,
  IsDefined,
  IsString,
  IsIn,
  IsArray,
  ValidateNested,
  ArrayMinSize,
  IsNotEmpty,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  TestCaseType,
  TestCaseStatus,
  TestCaseConfig,
  TestCaseLLMParams,
  ByPassPiiType,
} from './test-case.dto';

export class CreateTestCaseDto {
  @IsDefined()
  @IsString()
  @IsNotEmpty()
  name!: string;

  @IsDefined()
  @IsIn(Object.values(TestCaseType))
  testCaseType!: TestCaseType;

  @IsDefined()
  @IsInt()
  @IsNotEmpty()
  iterations!: number;

  @IsIn(Object.values(TestCaseStatus))
  testCaseStatus: TestCaseStatus = TestCaseStatus.ENABLED;

  @IsDefined()
  @IsNotEmpty()
  @IsString()
  defaultValidationPrompt!: string;

  @ValidateNested({ each: true })
  @IsArray()
  @ArrayMinSize(1)
  @Type(() => TestCaseConfig)
  config!: TestCaseConfig[];

  @IsDefined()
  @ValidateNested()
  @Type(() => TestCaseLLMParams)
  llmParams!: TestCaseLLMParams;

  validationModel?: string;

  @IsOptional()
  @IsArray()
  recipients?: string[];

  @IsOptional()
  byPassPii?: ByPassPiiType;

  @IsOptional()
  jobConfig?: string;
}
