--delete duplicates
WITH duplicates AS (
    SELECT MIN(id) as id
    FROM "ModelFilePermissionButton"
    GROUP BY "isApproved", "status", "isRequireSecondaryApproval", "fileClassification", "permissionKey", "hasMalware"
)
DELETE FROM "ModelFilePermissionButton"
WHERE id NOT IN (SELECT id FROM duplicates);

delete from "ModelFilePermissionButton"
WHERE status = 'VERIFY_FAILED'
  AND "buttonList" && ARRAY['APPROVE', 'APPROVEANDPROCESS', 'FINALAPPROVE'];

-- DropIndex
DROP INDEX "ModelFilePermissionButton_isApproved_status_hasPII_isRequir_key";

-- AlterTable
ALTER TABLE "ModelFilePermissionButton" DROP COLUMN "hasPII";

-- CreateIndex
CREATE UNIQUE INDEX "ModelFilePermissionButton_isApproved_status_isRequireSecond_key" ON "ModelFilePermissionButton"("isApproved", "status", "isRequireSecondaryApproval", "fileClassification", "permissionKey", "hasMalware");
