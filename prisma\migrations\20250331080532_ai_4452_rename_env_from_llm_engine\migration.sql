/*
  Warnings:

  - You are about to drop the column `env` on the `LlmEngine` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[envToBeRemoved,slug]` on the table `LlmEngine` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "LlmEngine_env_slug_key";

-- AlterTable
ALTER TABLE "LlmEngine" RENAME COLUMN "env" TO "envToBeRemoved";
ALTER TABLE "LlmEngine" ADD COLUMN  "learningMaterial" TEXT DEFAULT '';

-- CreateIndex
CREATE UNIQUE INDEX "LlmEngine_envToBeRemoved_slug_key" ON "LlmEngine"("envToBeRemoved", "slug");
