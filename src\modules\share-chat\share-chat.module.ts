import { Modu<PERSON> } from '@nestjs/common';
import { ShareChatService } from './share-chat.service';
import { ShareChatController } from './share-chat.controller';
import { ChatSessionsModule } from '../chat-sessions/chat-sessions.module';
import { PrismaModule } from 'src/providers/prisma/prisma.module';
import { GroupsModule } from '../groups/groups.module';
import { TokensModule } from 'src/providers/tokens/tokens.module';
import { LLMModelsModule } from '../llm-models/llm-models.module';

@Module({
  imports: [PrismaModule, ChatSessionsModule, LLMModelsModule, GroupsModule, TokensModule],
  controllers: [ShareChatController],
  providers: [ShareChatService],
})
export class ShareChatModule {}
