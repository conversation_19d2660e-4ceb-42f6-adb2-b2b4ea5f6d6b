import { Is<PERSON><PERSON>y, IsBoolean, IsEnum, IsNotEmpty, <PERSON>N<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class GroupNotificationConfigDto {
  @IsArray()
  @IsOptional()
  recipients: string[];

  @IsNumber()
  @IsOptional()
  silentPeriod?: number;

  @IsOptional()
  @IsBoolean()
  enabledNotify?: boolean;
}

export enum SendGroupNotificationContentType {
  Normal = 'Normal',
  Template = 'Template',
}

export class SendGroupNotificationDto {

  @IsString()
  @IsNotEmpty()
  sourceId: string;

  @IsEnum(SendGroupNotificationContentType)
  @IsOptional()
  contentType: SendGroupNotificationContentType;

  @IsString()
  @IsOptional()
  title?: string;

  @IsString()
  @IsOptional()
  htmlContent?: string;

  @IsString()
  @IsOptional()
  textContent?: string;

  @IsOptional()
  data?: Record<string, any>;

  @IsString()
  @IsOptional()
  templateName?: string;

  @IsString()
  notificationKey: string;

  @IsOptional()
  @IsString()
  attachmentFileBucket?: string;

  @IsOptional()
  @IsArray()
  attachmentFilePaths?: string[];
}