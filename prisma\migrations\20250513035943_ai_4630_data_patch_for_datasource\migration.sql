-- This is an empty migration.
-- It is used to run a data patch for the datasource field in the ChatSession and LLMModel tables.
-- update isAutoSelectAll is true to autoSelect is All
UPDATE "ChatSession"
SET "chatSetting" = jsonb_set(
        "chatSetting" #- '{dataSource,modelFile,isAutoSelectAll}',
        '{dataSource,modelFile,autoSelect}',
        '{"type": "All"}'::jsonb,
        true -- Create missing path segments if needed
                    )
WHERE ("chatSetting"->'dataSource'->'modelFile'->>'isAutoSelectAll')::boolean = true;

-- update isAutoSelectAll is false to autoSelect is Manual
UPDATE "ChatSession"
SET "chatSetting" = jsonb_set(
        "chatSetting" #- '{dataSource,modelFile,isAutoSelectAll}',
        '{dataSource,modelFile,autoSelect}',
        '{"type": "Manual"}'::jsonb,
        true -- Create missing path segments if needed
                    )
WHERE ("chatSetting"->'dataSource'->'modelFile'->>'isAutoSelectAll')::boolean = false;

-- update isAutoSelectAll is true to autoSelect is All
UPDATE "LLMModel"
SET "parameters" = jsonb_set(
        "parameters" #- '{dataSource,modelFile,isAutoSelectAll}',
        '{dataSource,modelFile,autoSelect}',
        '{"type": "All"}'::jsonb,
        true -- Create missing path segments if needed
                   )
WHERE ("parameters"->'dataSource'->'modelFile'->>'isAutoSelectAll')::boolean = true;

-- update isAutoSelectAll is false to autoSelect is Manual
UPDATE "LLMModel"
SET "parameters" = jsonb_set(
        "parameters" #- '{dataSource,modelFile,isAutoSelectAll}',
        '{dataSource,modelFile,autoSelect}',
        '{"type": "Manual"}'::jsonb,
        true -- Create missing path segments if needed
                   )
WHERE ("parameters"->'dataSource'->'modelFile'->>'isAutoSelectAll')::boolean = false;