import { Body, Controller, Patch, Get, Param, ParseIntPipe } from '@nestjs/common';
import { BotSecurityService } from './bot-security.service';
import { BotSecurityEntity } from './entities/bot-security.entity';
import { SaveBotSecurityDTO } from './dto/save-bot-security.dto';
import { Scopes } from '../auth/scope.decorator';

@Controller('botsecurity')
export class BotSecurityController {
  constructor(private botSecurityService: BotSecurityService) {}

  @Get(':groupId')
  @Scopes(
    'group-{groupId}:read-llm-model',
    'group-{groupId}:read-playground',
    'group-*:read-group-security-setting',
  )
  async findBotSecurity(
    @Param('groupId', ParseIntPipe) groupId: number,
  ): Promise<BotSecurityEntity> {
    return this.botSecurityService.findByGroupId(groupId);
  }

  @Patch(':groupId/')
  @Scopes('group-{groupId}:write-llm-model')
  async saveBotSecurity(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: SaveBotSecurityDTO,
  ): Promise<BotSecurityEntity> {
    data.groupId = groupId;
    return await this.botSecurityService.saveBotSecurity(data);
  }
}
