import { ApiProperty } from '@nestjs/swagger';
import { DataPromotionRequestActionType, DataPromotionRequestStatus } from '@prisma/client';
import { UserDto } from './user.dto';
import { EntitySnapshotDto } from './entity-snapshot.dto';

// This DTO currently is for swagger only
// TODO: use inheritance to separate dto variations from difference APIs if necessary
export class DataPromotionRequestDto {
  @ApiProperty()
  id: number;

  @ApiProperty({ enum: DataPromotionRequestActionType })
  actionType: DataPromotionRequestActionType;

  @ApiProperty()
  targetGroupId: number;

  @ApiProperty()
  promotedEntityId: number;

  @ApiProperty({ enum: DataPromotionRequestStatus })
  status: DataPromotionRequestStatus;

  @ApiProperty()
  entitySnapshotId: number;

  @ApiProperty({
    required: false,
    description: 'Only in search API response.',
  })
  entitySnapshot: EntitySnapshotDto;

  @ApiProperty()
  requesterId: number;

  @ApiProperty({
    required: false,
    description: 'Only in search API response.',
  })
  requester: UserDto;

  @ApiProperty()
  requestedDate: Date;

  @ApiProperty()
  requesterComment: string;

  @ApiProperty()
  operatorId: number;

  @ApiProperty({
    required: false,
    description: 'Only in search API response.',
  })
  operator: UserDto;

  @ApiProperty()
  operatedDate: Date;

  @ApiProperty()
  operatorComment: string;
}
