import { LabelEntityType, LabelType } from '@prisma/client';
import {
  IsDefined,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  <PERSON>Length,
  ValidateIf,
} from 'class-validator';

export class CreateLabelsDto {
  @IsDefined()
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @MinLength(3)
  @ValidateIf((o) => o.labelType === LabelType.LABELS)
  color: string | null;

  @IsOptional()
  desc?: string;

  @IsOptional()
  seq?: string;

  @IsEnum(LabelType)
  @IsDefined()
  labelType: LabelType;

  @IsOptional()
  @IsEnum(LabelEntityType)
  entityType?: LabelEntityType;

  @IsOptional()
  @IsNumber()
  entityId?: number;
}

export class CreateEntityLabelsDto {
  @IsNumber()
  entityId: number;

  @IsEnum(LabelEntityType)
  entityType: LabelEntityType;
}
