import { ApiProperty } from '@nestjs/swagger';
import { Environment } from '@prisma/client';
import { IsEnum, IsInt, IsOptional, IsString } from 'class-validator';

export class CreateDataPromotionRequestDto {
  @ApiProperty()
  @IsInt()
  snapshotId: number;

  @ApiProperty({ enum: Environment })
  @IsEnum(Environment)
  env: Environment;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  comment?: string;
}
