import { ApiPropertyOptional } from '@nestjs/swagger'; // Keep swagger for documentation
import { Transform } from 'class-transformer';
import { IsInt, IsOptional, IsString, Max, Min, IsIn } from 'class-validator';

// Define allowed sort fields
const allowedSortFields = [
  'createdAt',
  'updatedAt', // Added updatedAt
  'id',
  'originalFilename',
  'fileSize',
];

export class ListBatchFilesDto {
  @ApiPropertyOptional({
    description: 'Number of records to skip for pagination',
    minimum: 0,
    default: 0,
    type: Number,
  })
  @IsOptional()
  @IsInt({ message: 'Skip must be an integer.' })
  @Min(0, { message: 'Skip must be greater than or equal to 0.' })
  @Transform(({ value }) => parseInt(String(value), 10), { toClassOnly: true })
  skip?: number = 0;

  @ApiPropertyOptional({
    description: 'Maximum number of records to return',
    default: 10,
    maximum: 100,
    type: Number,
  })
  @IsOptional()
  @IsInt({ message: 'Take must be an integer.' })
  @Min(1, { message: 'Take must be greater than or equal to 1.' })
  @Max(100, { message: 'Take must be less than or equal to 100.' }) // Added Max
  @Transform(({ value }) => parseInt(String(value), 10), { toClassOnly: true })
  take?: number = 10;

  @ApiPropertyOptional({
    description: 'Field to sort by',
    enum: allowedSortFields,
    default: 'createdAt',
  })
  @IsOptional()
  @IsString({ message: 'SortBy must be a string.' })
  @IsIn(allowedSortFields, {
    message: `SortBy must be one of the following values: ${allowedSortFields.join(', ')}`,
  })
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['asc', 'desc'],
    default: 'desc',
  })
  @IsOptional()
  @IsString()
  @IsIn(['asc', 'desc'], {
    message: 'SortOrder must be either "asc" or "desc".',
  })
  sortOrder?: 'asc' | 'desc' = 'desc';
}