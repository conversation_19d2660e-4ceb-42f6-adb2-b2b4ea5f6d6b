import { Options } from 'pino-http';
import pino from 'pino';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const apm = require('elastic-apm-node');

const LOG_FILE_BASE_PATH = process.env['LOG_FILE_BASE_PATH'] ?? './tmp/';
const LOG_FILE_ROTATE_SIZE = process.env['LOG_FILE_ROTATE_SIZE'] ?? '50M';
const LOG_FILE_MAX_FILE_HISTORY = process.env['LOG_FILE_MAX_FILE_HISTORY'] ?? 5;

type TransportOptions =
  | pino.TransportSingleOptions<Record<string, any>>
  | pino.TransportMultiOptions<Record<string, any>>
  | pino.TransportPipelineOptions<Record<string, any>>;

const targetOptions = {
  translateTime: 'SYS:yyyy-mm-dd HH:MM:ss.l o',
  ignore: 'res,context,filename',
  singleLine: true,
};

export function pinoOptionConfig(env = 'production'): Options {
  const logLevel = process.env['BOT_BUILDER_BACKEND_LOG_LEVEL'] || 'info';
  return {
    level: logLevel,
    mixin: (mergeObject, level) => {
      return { 'level-label': pino.levels.labels[level].toUpperCase(), ...injectApmTraceId() };
    },
    timestamp: pino.stdTimeFunctions.isoTime,
    autoLogging: true,
    quietReqLogger: false,
    transport: configTransport(logLevel),
    serializers: {
      req: formatRequestLog,
    },
  };
}

const injectApmTraceId = () => {
  const apmTransaction = {};
  if (apm.isStarted()) {
    const apmTx = apm.currentTransaction;
    if (apmTx) {
      const apmCurrentTransaction = {};
      apmCurrentTransaction['trace.id'] = apmTx.traceId;
      apmCurrentTransaction['transaction.id'] = apmTx.id;
      const span = apm.currentSpan;
      if (span) {
        apmCurrentTransaction['span.id'] = span.id;
      }
      apmTransaction['apmTransaction'] = apmCurrentTransaction;
    }
  }
  return apmTransaction;
};

const formatRequestLog = (req: pino.SerializedRequest): Record<string, string> => {
  const requestLog = {
    api: `[${req.id}] [${req.method}] [${req.url}]`,
    gravitee: undefined,
  };
  if (req.headers['x-gravitee-request-id']) {
    requestLog.gravitee = {
      'x-gravitee-request-id': req.headers['x-gravitee-request-id'],
      'x-gravitee-transaction-id': req.headers['x-gravitee-transaction-id'],
    };
  }
  return requestLog;
};

const configTransport = (logLevel: string): TransportOptions => {
  return {
    targets: [
      {
        target: 'pino-pretty',
        level: logLevel,
        options: {
          ...targetOptions,
        },
      },
      {
        target: require.resolve('./pino-transport-file-rotating'),
        level: logLevel,
        options: {
          size: LOG_FILE_ROTATE_SIZE,
          maxFiles: Number(LOG_FILE_MAX_FILE_HISTORY),
          path: LOG_FILE_BASE_PATH,
        },
      },
    ],
  };
};
