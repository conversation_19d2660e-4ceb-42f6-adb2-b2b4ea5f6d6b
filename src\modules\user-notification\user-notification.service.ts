import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { NotificationBackendService } from '../../providers/notification-backend/notification-backend.service';
import { BroadcastDto } from '../broadcast/broadcast.dto';
import { BroadcastResponse } from '../../providers/notification-backend/notification-backend.interface';

@Injectable()
export class UserNotificationService {
  private logger = new Logger(UserNotificationService.name);

  constructor(
    private prisma: PrismaService,
    private notificationBackendService: NotificationBackendService,
  ) {
  }

  async getAll(
    skip?: number,
    take?: number,
    where?: Record<string, number | string>,
    orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    const responseData = await this.notificationBackendService.getUserNotifications(
      skip,
      take,
      where,
      orderBy,
    );
    return responseData;
  }

  async count(
    where?: Record<string, number | string>,
  ) {
    const responseData = await this.notificationBackendService.getUserNotificationsCount(
      where,
    );
    return responseData;
  }

  async updateViewStatus(id: number) {
    return await this.notificationBackendService.updateViewStatus(id);
  }
}