/*
  Warnings:

  - You are about to drop the `MemberGroup` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `MemberGroupFilter` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropTable
DROP TABLE "MemberGroup";

-- DropTable
DROP TABLE "MemberGroupFilter";

-- CreateTable
CREATE TABLE "UserGroup" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "additionalEmails" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "createdByUserId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "updatedByUserId" INTEGER NOT NULL,

    CONSTRAINT "UserGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserGroupFilter" (
    "id" SERIAL NOT NULL,
    "groupIds" INTEGER[] DEFAULT ARRAY[]::INTEGER[],
    "systemRoles" "SystemRole"[] DEFAULT ARRAY[]::"SystemRole"[],
    "groupRoles" "GroupDefaultRole"[] DEFAULT ARRAY[]::"GroupDefaultRole"[],
    "cccList" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "businessUnits" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "groupType" "GroupType",
    "userGroupId" INTEGER NOT NULL,

    CONSTRAINT "UserGroupFilter_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "user_group_index" ON "UserGroup"("name");

-- CreateIndex
CREATE UNIQUE INDEX "UserGroupFilter_userGroupId_key" ON "UserGroupFilter"("userGroupId");
