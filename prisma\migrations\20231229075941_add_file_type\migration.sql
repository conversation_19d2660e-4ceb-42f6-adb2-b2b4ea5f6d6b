/*
  Warnings:

  - Made the column `reportSlug` on table `LlmEngine` required. This step will fail if there are existing NULL values in that column.

*/

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "FileType" ADD VALUE 'FLOW_SUMMARY';
ALTER TYPE "FileType" ADD VALUE 'LLM_ENGINE_SUMMARY';
