-- CreateE<PERSON>
CREATE TYPE "GroupDefaultRole" AS ENUM ('GROUP_OWNER', 'GROUP_ADMIN', 'GROUP_CONTRIBUTOR', 'G<PERSON><PERSON>_MEMBER');

-- CreateEnum
CREATE TYPE "SystemRole" AS ENUM ('SUDO', 'OPERATION_TEAM', 'SECURITY_TEAM', 'ACCOUNT_MANAGEMENT', 'BOT_REVIEWER', 'BOT_CREATOR', 'USER');

-- CreateTable
CREATE TABLE "MemberGroup" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "additionalEmails" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "createdByUserId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "updatedByUserId" INTEGER NOT NULL,

    CONSTRAINT "MemberGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MemberGroupFilter" (
    "id" SERIAL NOT NULL,
    "groupIds" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "systemRoles" "SystemRole"[] DEFAULT ARRAY[]::"SystemRole"[],
    "groupRoles" "GroupDefaultRole"[] DEFAULT ARRAY[]::"GroupDefaultRole"[],
    "cccList" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "businessUnits" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "memberGroupId" INTEGER NOT NULL,

    CONSTRAINT "MemberGroupFilter_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "member_group_index" ON "MemberGroup"("name");

-- CreateIndex
CREATE UNIQUE INDEX "MemberGroupFilter_memberGroupId_key" ON "MemberGroupFilter"("memberGroupId");
