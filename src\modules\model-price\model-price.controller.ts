import {
  Body,
  Controller,
  Get,
  Logger,
  Param,
  ParseArrayPipe,
  ParseBoolPipe,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ModelPriceType } from '@prisma/client';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { UserRequest } from '../auth/auth.interface';
import { Public } from '../auth/public.decorator';
import { Scopes } from '../auth/scope.decorator';
import { BulkCreatePriceItem } from './model-price.dto';
import { ModelPriceService } from './model-price.service';

@Controller(['model-prices'])
@ApiTags('Model Price')
export class ModelPriceController {
  private readonly logger = new Logger(ModelPriceController.name);
  constructor(private readonly modelPriceService: ModelPriceService) {}

  @Get()
  @Scopes('system:read-model-price')
  @ApiOperation({ summary: 'For Price Table' })
  @ApiResponse({
    content: {
      'application/json': {
        example: {
          count: 2,
          lastCostSuggestionAutoUpdateDate: '2024-11-28T11:16:44.178Z',
          lastCostAutoUpdateDate: '2024-11-28T11:06:33.552Z',
          lastPriceAutoUpdateDate: '2024-11-28T11:06:33.552Z',
          lastCurrentPriceAutoUpdateDate: '2024-11-12T14:42:13.667Z',
          price: [
            {
              id: 1,
              slug: 'dalle3-hd-1024x1024-cost-per-image',
              llmEngineSlug: 'dalle-3',
              platform: 'AZURE',
              modelName: 'Dalle 3',
              unitName: 'Image (HD 1024x1024)',
              modelPriceSource: 'LITELLM',
              lastManualUpdateDate: null,
              COST_SUGGESTION: {
                id: 593,
                year: 2024,
                month: 12,
                modelPriceUnitId: 1,
                modelPriceType: 'COST_SUGGESTION',
                value: 0.08,
                updatedAt: '2024-11-12T10:03:22.157Z',
                updatedBy: 0,
              },
              COST: {
                id: 646,
                year: 2024,
                month: 12,
                modelPriceUnitId: 1,
                modelPriceType: 'COST',
                value: 0.08,
                updatedAt: '2024-11-12T10:03:22.157Z',
                updatedBy: 0,
                isManual: true,
                overrideAutoSync: true,
              },
              PRICE: {
                id: 700,
                year: 2024,
                month: 12,
                modelPriceUnitId: 1,
                modelPriceType: 'PRICE',
                value: 0.096,
                updatedAt: '2024-11-12T10:03:22.157Z',
                updatedBy: 0,
                isManual: true,
                overrideAutoSync: true,
              },
              CURRENT_PRICE: {
                id: 267,
                year: 2024,
                month: 11,
                modelPriceUnitId: 1,
                modelPriceType: 'PRICE',
                value: 0.096,
                updatedAt: '2024-11-12T10:03:22.157Z',
                updatedBy: 0,
              },
            },
            {
              id: 2,
              slug: 'dalle3-hd-1024x1792-cost-per-image',
              llmEngineSlug: 'dalle-3',
              platform: 'AZURE',
              modelName: 'Dalle 3',
              unitName: 'Image (HD 1024x1792)',
              modelPriceSource: 'LITELLM',
              lastManualUpdateDate: '2024-12-10T09:53:46.502Z',
              COST_SUGGESTION: {
                id: 568,
                year: 2024,
                month: 12,
                modelPriceUnitId: 2,
                modelPriceType: 'COST_SUGGESTION',
                value: 0.12,
                updatedAt: '2024-11-11T15:53:44.597Z',
                updatedBy: 0,
              },
              COST: {
                id: 601,
                year: 2024,
                month: 12,
                modelPriceUnitId: 2,
                modelPriceType: 'COST',
                value: 0.12,
                updatedAt: '2024-12-10T09:53:46.417Z',
                updatedBy: 504,
                isManual: true,
                overrideAutoSync: false,
              },
              PRICE: {
                id: 675,
                year: 2024,
                month: 12,
                modelPriceUnitId: 2,
                modelPriceType: 'PRICE',
                value: 0.144,
                updatedAt: '2024-12-10T09:53:46.502Z',
                updatedBy: 504,
                isManual: true,
                overrideAutoSync: false,
              },
              CURRENT_PRICE: {
                id: 242,
                year: 2024,
                month: 11,
                modelPriceUnitId: 2,
                modelPriceType: 'PRICE',
                value: 0.144,
                updatedAt: '2024-11-11T15:53:44.597Z',
                updatedBy: 0,
              },
            },
          ],
        },
      },
    },
  })
  @AuditLog('read-model-price')
  async getPrice(
    @Query('enabled', ParseBoolPipe) enabled: boolean,
    @Query('year', OptionalIntPipe) year?: number,
    @Query('month', OptionalIntPipe) month?: number,
  ) {
    // If year or month is not provided, use current year and month
    if (!year || !month) {
      year = new Date().getFullYear();
      month = new Date().getMonth() + 1;
    }
    return this.modelPriceService.getPriceByYearAndMonth(year, month, enabled);
  }

  @Get('ratecard')
  // @Scopes('user-{userId}:read-ratecard')
  @Public()
  @ApiOperation({ summary: 'For Rate Card' })
  @ApiResponse({
    content: {
      'application/json': {
        example: {
          count: 2,
          price: [
            {
              id: 1,
              slug: 'dalle3-hd-1024x1024-cost-per-image',
              llmEngineSlug: 'dalle-3',
              platform: 'AZURE',
              modelName: 'Dalle 3',
              unitName: 'Image (HD 1024x1024)',
              modelPriceSource: 'LITELLM',
              PRICE: {
                id: 700,
                year: 2024,
                month: 12,
                modelPriceType: 'PRICE',
                value: 0.096,
                updatedAt: '2024-11-12T10:03:22.157Z',
                updatedBy: 0,
              },
            },
            {
              id: 2,
              slug: 'dalle3-hd-1024x1792-cost-per-image',
              llmEngineSlug: 'dalle-3',
              platform: 'AZURE',
              modelName: 'Dalle 3',
              unitName: 'Image (HD 1024x1792)',
              modelPriceSource: 'LITELLM',
              PRICE: {
                id: 675,
                year: 2024,
                month: 12,
                modelPriceType: 'PRICE',
                value: 0.144,
                updatedAt: '2024-12-10T09:53:46.502Z',
                updatedBy: 504,
              },
            },
          ],
        },
      },
    },
  })
  @AuditLog('read-model-price')
  async getRateCard(
    @Query('enabled', ParseBoolPipe) enabled: boolean,
    @Query('year', OptionalIntPipe) year?: number,
    @Query('month', OptionalIntPipe) month?: number,
  ) {
    // If year or month is not provided, use current year and month
    if (!year || !month) {
      year = new Date().getFullYear();
      month = new Date().getMonth() + 1;
    }
    return this.modelPriceService.getRateCard(year, month, enabled);
  }

  @Get('internal')
  @Scopes('read-model-price')
  @ApiOperation({ summary: 'For internal use e.g. log-file-processer.' })
  @AuditLog('read-model-price-internal')
  async getPriceInternal(
    @Query('year', ParseIntPipe) year: number,
    @Query('month', ParseIntPipe) month: number,
    @Query('modelPriceType') modelPriceType?: ModelPriceType,
  ) {
    // If year or month is not provided, use current year and month
    if (!year || !month) {
      year = new Date().getFullYear();
      month = new Date().getMonth() + 1;
    }
    return this.modelPriceService.getPriceByYearAndMonthInternal(year, month, {
      modelPriceType,
    });
  }

  @Get('units/:id/events')
  @Scopes('system:read-price-unit-event')
  @ApiOperation({ summary: 'For Change Logs' })
  @ApiResponse({
    content: {
      'application/json': {
        example: {
          count: 10,
          events: [
            {
              id: 129,
              modelPriceEventType: 'UPDATE',
              modelPriceType: 'COST',
              modelPriceUnitId: 2,
              value: 0.13,
              metadata: {
                overrideAutoSync: false,
              },
              createdBy: 504,
              createdAt: '2024-12-10T09:50:19.179Z',
              creator: null,
            },
            {
              id: 128,
              modelPriceEventType: 'UPDATE',
              modelPriceType: 'COST',
              modelPriceUnitId: 2,
              value: 0.13,
              metadata: {
                overrideAutoSync: false,
              },
              createdBy: 10047989,
              createdAt: '2024-12-10T09:39:00.637Z',
              creator: {
                id: 10047989,
                name: 'John Doe',
                ccc: 'C25D',
                department: 'IT/DV',
                businessUnit: 'Group IT',
              },
            },
          ],
        },
      },
    },
  })
  @AuditLog('read-price-unit-event')
  async getModelPriceUnitEvents(
    @Param('id', ParseIntPipe) id?: number,
    @Query('take', ParseIntPipe) take: number = 10,
    @Query('skip', ParseIntPipe) skip: number = 0,
  ) {
    return {
      count: await this.modelPriceService.countModelPriceEvents(id),
      events: await this.modelPriceService.getModelPriceEvents(id, take, skip),
    };
  }

  @Get('reports')
  @Scopes('system:read-system-price-report')
  @ApiOperation({ summary: 'For Listing System Price Reports' })
  @ApiResponse({
    content: {
      'application/json': {
        example: {
          reports: [
            {
              year: 2024,
              month: 11,
              price: 131.6972788919999,
              status: 'COMPLETED',
            },
            {
              year: 2024,
              month: 10,
              price: null,
              status: 'ERROR',
            },
            {
              year: 2024,
              month: 9,
              price: null,
              status: 'PROCESSING',
            },
            {
              year: 2024,
              month: 8,
              price: null,
              status: 'NOT_AVAILABLE',
            },
          ],
        },
      },
    },
  })
  @AuditLog('read-system-price-report')
  async getSystemReports(@Req() req: UserRequest) {
    return await this.modelPriceService.getSystemPriceReports(req);
  }

  @Get('groups/:groupId/reports')
  @Scopes('group-{groupId}:read-group-price-report', 'group-*:read-group-price-report')
  @ApiOperation({ summary: 'For Listing Group Price Reports' })
  @ApiResponse({
    content: {
      'application/json': {
        example: {
          reports: [
            {
              year: 2024,
              month: 11,
              price: 0.3972788,
              status: 'COMPLETED',
            },
            {
              year: 2024,
              month: 10,
              price: null,
              status: 'ERROR',
            },
            {
              year: 2024,
              month: 9,
              price: null,
              status: 'PROCESSING',
            },
            {
              year: 2024,
              month: 8,
              price: null,
              status: 'NOT_AVAILABLE',
            },
          ],
        },
      },
    },
  })
  @AuditLog('read-group-price-report')
  async getGroupReports(@Req() req: UserRequest, @Param('groupId', ParseIntPipe) groupId: number) {
    return await this.modelPriceService.getGroupPriceReports(req, groupId);
  }

  @Get('reports/:year/:month')
  @Scopes('system:download-system-price-report')
  @ApiOperation({ summary: 'For Showing Download Report status and id' })
  @ApiResponse({
    content: {
      'application/json': {
        example: {
          id: 7070,
          fileId: 'ZuXGLw2xBB',
          requesterId: 10047989,
          fileType: 'MONTHLY_PRICE_REPORT',
          entityType: null,
          entityId: null,
          createdAt: '2024-12-05T11:18:23.462Z',
          updatedAt: '2024-12-05T11:18:25.350Z',
          s3FilePath:
            'MONTHLY_PRICE_REPORT/2024-12-05T11:18:23.462Z/ZuXGLw2xBB_2024-10-31T16:00:00.000Z-2024-11-30T15:59:59.999Z.xlsx',
          status: 'COMPLETED',
          errorMsg: '',
          dateFrom: '2024-10-31T16:00:00.000Z',
          dateTo: '2024-11-30T15:59:59.999Z',
        },
      },
    },
  })
  @AuditLog('download-system-price-report')
  async generateAndDownloadReport(
    @Req() req: UserRequest,
    @Param('year', ParseIntPipe) year: number,
    @Param('month', ParseIntPipe) month: number,
    @Query('selectedFields', new ParseArrayPipe({ optional: true })) selectedFields?: string[],
    @Query('forceRegenerate', new ParseBoolPipe({ optional: true })) forceRegenerate?: boolean,
  ) {
    this.logger.log(`Generating report for year: ${year} and month: ${month}`);
    return await this.modelPriceService.generateAndDownloadReport(
      req,
      year,
      month,
      selectedFields,
      forceRegenerate,
    );
  }

  @Get('groups/:groupId/reports/:year/:month')
  @Scopes('group-{groupId}:download-group-price-report', 'group-*:download-group-price-report')
  @ApiOperation({ summary: 'For Showing Download Report status and id' })
  @ApiResponse({
    content: {
      'application/json': {
        example: {
          id: 7277,
          fileId: 'dx7HJgGkSC',
          requesterId: 504,
          fileType: 'MONTHLY_PRICE_REPORT_PER_GROUP',
          entityType: 'BOT',
          entityId: 10452855,
          createdAt: '2024-12-11T04:03:32.797Z',
          updatedAt: '2024-12-11T04:03:32.797Z',
          s3FilePath: null,
          status: 'PROCESSING',
          errorMsg: null,
          dateFrom: '2024-10-31T16:00:00.000Z',
          dateTo: '2024-11-30T15:59:59.999Z',
        },
      },
    },
  })
  @AuditLog('download-group-price-report')
  async generateAndDownloadReportByGroupId(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('year', ParseIntPipe) year: number,
    @Param('month', ParseIntPipe) month: number,
    @Query('forceRegenerate', new ParseBoolPipe({ optional: true })) forceRegenerate?: boolean,
  ) {
    this.logger.log(
      `Generating report for group id: ${groupId}, year: ${year} and month: ${month}`,
    );
    const selectedFields = [
      'referenceInvoiceNumber',
      'dateFrom',
      'dateTo',
      'ccc',
      'department',
      'bu',
      'id',
      'name',
      'stage',
      'feature',
      'channel',
      'env',
      'model',
      'usage',
      'unit',
      'pricePerUnit',
      'price',
      'remarks',
    ];
    return await this.modelPriceService.generateAndDownloadReportByGroupId(
      req,
      year,
      month,
      groupId,
      selectedFields,
      forceRegenerate,
    );
  }

  @Post('process/bulk')
  @Scopes('create-model-price-process', 'system:create-model-price-process')
  @ApiOperation({ summary: 'For internal use. Trigger by cronjob or manual on error.' })
  @AuditLog('create-model-price-process')
  async createProcess(
    @Req() req: UserRequest,
    @Body() body: Parameters<typeof ModelPriceService.prototype.bulkCreateProcess>[1],
  ) {
    this.logger.log(
      `Creating process for mode: ${body.mode}, year: ${body.year}, month: ${body.month}`,
    );
    const createdProcessCount = await this.modelPriceService.bulkCreateProcess(req, body);
    return { createdProcessCount };
  }

  @Patch('/process/:id')
  @Scopes('update-callback')
  @ApiOperation({ summary: 'For internal use. Callback by lambda.' })
  @AuditLog('model-price-process-update-callback')
  async patchProcessStatus(
    @Req() req: UserRequest,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: Parameters<typeof ModelPriceService.prototype.patchProcessStatus>[2],
  ) {
    return this.modelPriceService.patchProcessStatus(req, id, body);
  }

  @Post('bulk')
  @Scopes('system:create-price')
  @ApiOperation({ summary: 'For bulk update cost and price with overrideAutoSync.' })
  @ApiBody({
    description: 'Bulk create model price',
    type: BulkCreatePriceItem,
    isArray: true,
    examples: {
      updateCostAndPrice: {
        value: [
          {
            id: 1,
            year: 2024,
            month: 11,
            modelPriceUnitId: 1,
            modelPriceType: 'COST',
            value: 1,
            overrideAutoSync: false,
          },
          {
            year: 2024,
            month: 11,
            modelPriceUnitId: 1,
            modelPriceType: 'PRICE',
            value: 1.2,
            overrideAutoSync: false,
          },
        ],
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Created',
    content: {
      'application/json': {
        example: [
          {
            id: 1,
            year: 2024,
            month: 11,
            modelPriceUnitId: 1,
            modelPriceType: 'COST',
            value: 1,
            overrideAutoSync: false,
          },
          {
            id: 10,
            year: 2024,
            month: 11,
            modelPriceUnitId: 1,
            modelPriceType: 'PRICE',
            value: 1.2,
            overrideAutoSync: false,
          },
        ],
      },
    },
  })
  @AuditLog('create-price')
  async bulkCreatePrice(
    @Req() req: UserRequest,
    @Body(new ParseArrayPipe({ items: BulkCreatePriceItem })) body: BulkCreatePriceItem[],
  ) {
    this.logger.log(`Bulk createing ModelPrice: User: ${req?.user?.id}, price: ${body}`);
    return this.modelPriceService.bulkCreatePrice(req, body);
  }
}
