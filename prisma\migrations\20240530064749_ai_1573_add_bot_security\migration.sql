-- CreateTable
CREATE TABLE "BotSecurity" (
    "id" SERIAL NOT NULL,
    "securityId" VARCHAR(100) NOT NULL,
    "groupId" INTEGER NOT NULL,
    "env" "Environment" NOT NULL,
    "inputScanners" JSONB NOT NULL,
    "outputScanners" JSONB NOT NULL,
    "createdAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "BotSecurity_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "BotSecurity.securityId.unique" ON "BotSecurity"("securityId");

-- CreateIndex
CREATE UNIQUE INDEX "BotSecurity_groupId_key" ON "BotSecurity"("groupId");
