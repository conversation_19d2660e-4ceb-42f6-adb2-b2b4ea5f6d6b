import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { ConfigService } from '@nestjs/config';
import { Configuration } from '../../config/configuration.interface';
import {
  BroadcastCreateRequest,
  BroadcastResponse,
  BroadcastRequest, NotificationRequest,
} from './notification-backend.interface';
import { ApiException, ErrorCode } from '../../errors/errors.constants';

@Injectable()
export class NotificationBackendService {
  axios?: AxiosInstance;
  private logger = new Logger(NotificationBackendService.name);

  constructor(private configService: ConfigService) {
    const notificationConfig =
      this.configService.get<Configuration['notification']>('notification');
    if (notificationConfig?.clientUrl) {
      this.axios = axios.create({
        baseURL: notificationConfig.clientUrl,
        timeout: notificationConfig.timeout,
      });
    } else {
      this.logger.error('No Notification backend URL set.');
      throw new Error('No Notification backend URL set.');
    }
    this.axios.interceptors.response.use(
      (res) => res,
      (error) => {
        this.logger.error({ error: error, data: error?.response?.data });
        const errorResponse = error?.response?.data;
        if (errorResponse && errorResponse?.error) {
          const errorMsg = `${errorResponse.error?.code}: ${errorResponse.error?.message}`;
          throw new ApiException(errorMsg);
        }
        throw new ApiException(ErrorCode.REQUEST_NOTIFICATION_SERVICE_FAILED);
      },
    );
    this.axios.interceptors.request.use((req) => {
      this.logger.debug({ req });
      return req;
    });
  }

  async getBroadcasts(
    skip?: number,
    take?: number,
    where?: Record<any, any>,
    orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    return this.axios
      .get(`/v1/broadcast`, {
        params: {
          take,
          skip,
          where,
          orderBy,
        },
      })
      .then((re) => {
        return re.data;
      });
  }

  async createBroadcast(data: BroadcastCreateRequest): Promise<BroadcastResponse> {
    const response = await this.axios.post(`/v1/broadcast`, data);
    return response.data;
  }

  async updateBroadcast(id: number, data: BroadcastRequest): Promise<BroadcastResponse> {
    const response = await this.axios.put(`/v1/broadcast/${id}`, data);
    return response.data;
  }

  async deleteBroadcast(id: number): Promise<BroadcastResponse> {
    const response = await this.axios.delete(`/v1/broadcast/${id}`);
    return response.data;
  }

  async getUserNotifications(
    skip?: number,
    take?: number,
    where?: Record<any, any>,
    orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    return this.axios
      .get(`/v1/user-notification`, {
        params: {
          take,
          skip,
          where,
          orderBy,
        },
      })
      .then((re) => {
        return re.data;
      });
  }

  async getUserNotificationsCount(
    where?: Record<any, any>,
  ) {
    return this.axios
      .get(`/v1/user-notification/count`, {
        params: {
          where,
        },
      })
      .then((re) => {
        return re.data;
      });
  }

  async updateViewStatus(id: number){
    const response = await this.axios.patch(`/v1/user-notification/view-status/${id}`);
    return response.data;
  }

  async sendNotification(data: NotificationRequest) {
    this.logger.debug(`[NotificationBackendService][sendNotification] start to send email [${JSON.stringify(data)}]`);
    const response = await this.axios.post('/v1/batch', data);
    this.logger.debug(`[NotificationBackendService][sendNotification] response [${JSON.stringify(data)}]`);
    return response.data;
  }
}
