import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsString } from 'class-validator';

export class CreateBatchProcessDto {
  @ApiProperty({
    description: 'The ID of the previously uploaded batch file.',
    example: 'clxkz1q2b0000q9zjf0a3b1c2',
  })
  @IsInt()
  @IsNotEmpty()
  batchFileId: number;

  @ApiProperty({
    description: 'The provider to use for the batch process.',
    example: 'AZURE_OPENAI', // Or potentially an enum later
  })
  @IsString()
  @IsNotEmpty()
  provider: string;

  @IsString()
  @IsNotEmpty()
  endpoint: '/v1/chat/completions'

  @IsString()
  @IsNotEmpty()
  completionWindow: '24h'
}
