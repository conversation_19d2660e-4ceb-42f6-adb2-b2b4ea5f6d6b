-- CreateEnum
CREATE TYPE "BotUsageEntityType" AS ENUM ('BOT', 'USER', 'FLOW');

-- CreateTable
CREATE TABLE "BotUsage" (
    "id" SERIAL NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "entityType" "BotUsageEntityType" NOT NULL,
    "entityId" INTEGER NOT NULL,
    "key" TEXT NOT NULL,
    "value" INTEGER NOT NULL,
    "llmEngineSlug" TEXT NOT NULL,
    CONSTRAINT "BotUsage_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "entityId_type_index" ON "BotUsage"("entityType", "key", "entityId", "date", "llmEngineSlug");
CREATE INDEX "entityType_key_llmEngineSlug_entityId_index" ON "BotUsage" ("entityType", "key", "llmEngineSlug", "date", "entityId");

CREATE CAST (varchar AS "BotUsageEntityType") WITH INOUT AS IMPLICIT;