import {
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsPositive,
  IsBoolean,
  ValidateNested,
  IsArray,
} from 'class-validator';

import { TemplateAccessLevel } from '@prisma/client';
import { Type } from 'class-transformer';
import { PatchLabelsDto } from '../labels/dto/patch-labels.dto';

export class CreateMessageTemplateDto {
  @IsString()
  @MaxLength(50)
  title: string;

  @IsNumber()
  @IsPositive()
  @IsOptional()
  sequence?: number;

  @IsString()
  @IsOptional()
  botInstruction: string;

  @IsNumber()
  @IsPositive()
  @IsOptional()
  systemSequence?: number;

  @IsNumber()
  @IsPositive()
  @IsOptional()
  groupSequence?: number;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  messageContent: string;

  @IsBoolean()
  @IsOptional()
  isRecommended: boolean;

  @IsOptional()
  accessLevel: TemplateAccessLevel;

  @IsOptional()
  groupId: number;

  @Type(() => PatchLabelsDto)
  @ValidateNested({ each: true })
  @IsOptional()
  entityLabels?: PatchLabelsDto[];

  @IsOptional()
  templateGuideDetails: string;
}

export class UpdateMessageTemplateDto {
  @IsString()
  @MaxLength(50)
  @IsOptional()
  title?: string;

  @IsNumber()
  @IsPositive()
  @IsOptional()
  systemSequence?: number;

  @IsNumber()
  @IsPositive()
  @IsOptional()
  groupSequence?: number;

  @IsString()
  @IsOptional()
  botInstruction?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  messageContent?: string;

  @IsBoolean()
  @IsOptional()
  isRecommended?: boolean;

  @IsOptional()
  accessLevel: TemplateAccessLevel;

  @IsOptional()
  groupId: number;
  @Type(() => PatchLabelsDto)
  @ValidateNested({ each: true })
  @IsOptional()
  entityLabels?: PatchLabelsDto[];

  @IsOptional()
  templateGuideDetails: string;
}

export class BatchUploadMessageTemDto {
  @IsOptional()
  id?: number;

  @IsOptional()
  ordering?: number;

  @IsOptional()
  templateName?: string;

  @IsOptional()
  templateInstruction?: string;

  @IsOptional()
  isActive?: boolean;

  @IsOptional()
  isRecommended?: boolean;

  @IsOptional()
  categories?: string;

  @IsOptional()
  categoriesActionType?: string;

  @IsOptional()
  labelsName?: string;

  @IsOptional()
  labelColor?: string;

  @IsOptional()
  labelActionType?: string;

  @IsOptional()
  action?: string;

  @IsOptional()
  botId?: string;

  @IsOptional()
  public?: boolean;
}
export class BatchUploadDto {
  @IsArray()
  @Type(() => BatchUploadMessageTemDto)
  messageTemplateList: BatchUploadMessageTemDto[];
}
