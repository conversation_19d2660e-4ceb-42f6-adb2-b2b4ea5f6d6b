stages:
- dev_build
- dev_test
- devops_ci

workflow:
  rules:
  - if: $CI_COMMIT_TAG =~ /d\d\d*\.\d\d*\.\d\d*/
  - if: $CI_COMMIT_TAG =~ /v\d\d*\.\d\d*\.\d\d*/
      
default:
  tags:
  - aws-botbuilder-runner
#  - aws-runner
  
variables:
  SNYK_TEST: $SNYK_TEST
  SNYK_TOKEN: $SNYK_TOKEN
  SNYK_CFG_ORG: $SNYK_CFG_ORG
  
job_build:
  stage: dev_build
  script:
  - echo "build stage defined by dev"

job_test:
  stage: dev_test
  script:
  - echo "test stage defined by dev"

job_devops_ci:
  stage: devops_ci
  trigger:
    include:
    - project: 'LDA/CLUB/DevOps/gitlab-common-ci'
      ref: master
      file: '/standard-ci-docker-0.0.2.yml'
#      file: '/standard-ci-docker-0.0.1.yml'
    strategy: depend
