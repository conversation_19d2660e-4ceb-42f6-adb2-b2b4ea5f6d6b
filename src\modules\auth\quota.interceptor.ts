import { CallHandler, ExecutionContext, Injectable, Logger, NestInterceptor } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserRequest } from './auth.interface';
import { ResourceQuotaHandler } from './quota.interface';
import { LlmEnginesService } from '../llm-engines/llm-engines.service';
import { Observable } from 'rxjs';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { ResourceQuotaDto } from '../scope/scope.dto';
import { ChannelType } from '../llm-models/dto/chat-llm-model.dto';
import { chatChannelType } from 'prisma/seedData/botPlan.seedData';
import { RateLimitService } from 'src/providers/rate-limit/rate-limit.service';
import { PlansService } from '../plans/plans.service';
import { QuotaService } from '../quotas/quota.service';
@Injectable()
export class QuotaInterceptor implements NestInterceptor {
  private readonly resourceQuotaHandler: ResourceQuotaHandler;
  private logger = new Logger(QuotaInterceptor.name);

  constructor(
    private reflector: Reflector,
    private llmEnginesService: LlmEnginesService,
    private rateLimitService: RateLimitService,
    private readonly plansService: PlansService,
    private readonly quotaService: QuotaService,
  ) {
    this.resourceQuotaHandler = {
      TokenLimitPerMonth: {
        service: this.llmEnginesService,
      },
      RateLimit: {
        service: this.rateLimitService,
      },
    };
  }

  replaceQuotaPlaceholderByRequest(quotaKey: string, request: UserRequest) {
    let replacedQuotaKey = quotaKey;
    if (quotaKey.includes('{groupId}') && request?.params?.['groupId']) {
      replacedQuotaKey = replacedQuotaKey.replace('{groupId}', request.params['groupId']);
    }
    if (quotaKey.includes('{llmEngine}') && request?.body?.overrides?.model) {
      replacedQuotaKey = replacedQuotaKey.replace('{llmEngine}', request.body.overrides.model);
    }
    if (quotaKey.includes('{llmEngine}') && request?.body?.model) {
      let modelKey = request.body.model;
      if (modelKey.includes('gemini')) {
        modelKey = 'vertexai-' + request.body.model;
      }
      replacedQuotaKey = replacedQuotaKey.replace('{llmEngine}', modelKey);
    }
    if (quotaKey.includes('{channel}')) {
      const channel =
        request.body.channel ??
        (request.headers['x-api-key'] != null ? ChannelType.API_KEY : ChannelType.PLAYGROUND);
      const channelKey = chatChannelType.find((chatChannel) => chatChannel.slug === channel).key;
      replacedQuotaKey = replacedQuotaKey.replace('{channel}', channelKey);
    }
    return replacedQuotaKey;
  }

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<unknown>> {
    const requiredQuotaKeys = this.reflector.get<string[]>('quotas', context.getHandler());
    if (requiredQuotaKeys.length === 0) {
      return next.handle();
    }
    const validateQuotaResult: boolean[] = [];
    const request = context.switchToHttp().getRequest<UserRequest>();
    const response = context.switchToHttp().getResponse();
    // get back the user owned resource quota
    let userResourceQuotas: ResourceQuotaDto[] = [];
    if (request?.params?.['groupId']) {
      userResourceQuotas = await this.quotaService.getGroupResourceQuotas(
        Number(request.params['groupId']),
      );
    }
    // validate quota logic
    for (const requiredQuotaKey of requiredQuotaKeys) {
      // replace the placeholder in the quota key with the actual value
      const replacedRequiredQuotaKey = this.replaceQuotaPlaceholderByRequest(
        requiredQuotaKey,
        request,
      );
      const resourceQuota = userResourceQuotas.find(
        (quota) => quota.ruleKey === replacedRequiredQuotaKey,
      );
      if (!resourceQuota) {
        this.logger.error(`no resource quota are found to match - ${replacedRequiredQuotaKey}`);
        throw new ApiException(ErrorCode.RESOURCE_QUOTA_NOT_FOUND);
      }
      // decide whether the quota is reached
      const isQuotaAllowed = await this.resourceQuotaHandler[
        resourceQuota.quotaType
      ].service.checkQuotaIsAllowed(resourceQuota, response);
      this.logger.log(`validate ${replacedRequiredQuotaKey} result - ${isQuotaAllowed}`);
      validateQuotaResult.push(isQuotaAllowed);
    }
    // if success, allow request to pass to controller
    if (validateQuotaResult.every((result) => result === true)) {
      return next.handle();
    } else {
      throw new ApiException(ErrorCode.RESOURCE_QUOTA_FAILED);
    }
  }
}
