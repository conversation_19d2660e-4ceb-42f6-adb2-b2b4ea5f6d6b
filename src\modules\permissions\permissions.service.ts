import { Injectable } from '@nestjs/common';
import {
  Prisma,
  Permission,
  PermissionType,
  SystemName,
  PermissionGroupFeature,
} from '@prisma/client';
import { GroupType } from '@prisma/client';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { GroupsService } from '../groups/groups.service';

@Injectable()
export class PermissionsService {
  constructor(
    private prisma: PrismaService,
    private groupsService: GroupsService,
  ) {}

  async getGroupPermissionOfFeature(groupId: number): Promise<PermissionGroupFeature[]> {
    try {
      const group = await this.groupsService.getGroup(groupId, {});
      return await this.prisma.permissionGroupFeature.findMany({
        where: {
          groupTypes: {
            has: group.groupType,
          },
        },
      });
    } catch (error) {
      return null;
    }
  }

  async getGroupPermissions(groupId: number): Promise<Permission[]> {
    try {
      const group = await this.groupsService.getGroup(groupId, {});
      const permissions = await this.prisma.permission.findMany({
        include: {
          permissionGroupSetting: {
            select: {
              featureId: true,
            },
            where: {
              groupType: group.groupType,
            },
          },
        },
        where: {
          permissionGroupSetting: {
            some: {
              groupType: group.groupType,
            },
          },
          permissionType: PermissionType.GROUP,
          envs: {
            has: group.env,
          },
        },
      });
      return permissions;
    } catch (error) {
      return null;
    }
  }

  async getGroupPermissionsByRole(groupId: number, roleId: number): Promise<Permission[]> {
    try {
      const group = await this.groupsService.getGroup(groupId, {});

      const rolePermissions = await this.prisma.rolePermission.findMany({
        include: {
          permission: true,
        },
        where: {
          roleId: roleId,
          permission: {
            envs: {
              has: group.env,
            },
            permissionGroupSetting: {
              some: {
                groupType: group.groupType,
              },
            },
          },
        },
      });
      return rolePermissions.map((rolePermission) => rolePermission.permission);
    } catch (error) {
      return null;
    }
  }

  async getGroupPermissionsOfCustomRole(groupId: number): Promise<Permission[]> {
    try {
      const group = await this.groupsService.getGroup(groupId, {});
      const permissions = await this.prisma.permission.findMany({
        include: {
          permissionGroupSetting: {
            select: {
              featureId: true,
            },
            where: {
              groupType: group.groupType,
            },
          },
        },
        where: {
          permissionGroupSetting: {
            some: {
              isCustomRoleAllowed: true,
              groupType: group.groupType,
            },
          },
          permissionType: PermissionType.GROUP,
          envs: {
            has: group.env,
          },
        },
      });

      return permissions;
    } catch (error) {
      return null;
    }
  }

  async getOwnerPermissionsByGroupType(
    groupType: string,
    apiKeyAllowed?: boolean,
  ): Promise<Partial<Permission>[]> {
    const whereInput: Prisma.PermissionWhereInput = {
      permissionType: PermissionType.GROUP,
      roles: {
        some: {
          role: {
            systemName: SystemName.GROUP_OWNER,
          },
        },
      },
      permissionGroupSetting: { some: { groupType: GroupType[groupType] } },
    };

    if (apiKeyAllowed) {
      whereInput.permissionGroupSetting.some.isApiKeyAllowed = apiKeyAllowed;
    }

    const permissions = await this.prisma.permission.findMany({
      select: {
        description: true,
        permissionKey: true,
      },
      where: whereInput,
    });

    return permissions;
  }

  async getUserPermissions() {
    return this.prisma.permission.findMany({
      where: {
        permissionType: PermissionType.USER,
      },
      orderBy: {
        description: 'asc',
      },
    });
  }
}
