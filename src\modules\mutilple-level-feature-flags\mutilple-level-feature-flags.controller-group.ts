import {
  Controller,
  Post,
  Body,
  Param,
  ParseIntPipe,
  Get,
  Put,
  Delete,
  Query,
  Req,
} from '@nestjs/common';
import { Scopes } from '../auth/scope.decorator';
import { MutilpleLevelFeatureFlagsModelDto } from './mutilple-level-feature-flags-model.dto';
import { MutilpleLevelFeatureFlagsService } from './mutilple-level-feature-flags.service';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { WherePipe } from 'src/pipes/where.pipe';
import { OrderByPipe } from 'src/pipes/order-by.pipe';
import { UserRequest } from '../auth/auth.interface';
import {
  BotFeatureFlagFormatValidatorMap,
  FeatureFlagLevels,
} from './mutilple-level-feature-flags.constants';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { FeatureFlagTargetType } from '@prisma/client';
import { GroupsService } from '../groups/groups.service';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { UpsertBotFeatFlagsDto } from './upsertBotFeatFlags.dto';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuditLog } from '../audit-logs/audit-log.decorator';

@Controller('/groups/:groupId')
@ApiTags('Group MutilpleLevel Feature Flags')
@ApiBearerAuth('bearer-auth')
export class MutilpleLevelFeatureFlagsGroupController {
  constructor(
    private mutilpleLevelFeatureFlagService: MutilpleLevelFeatureFlagsService,
    private featureFlagService: FeatureFlagService,
    private groupsService: GroupsService,
  ) {}

  @Get('/feature-flags')
  @Scopes('group-{groupId}:read-feature-flag')
  async getFeatFlags(
    @Param('groupId', OptionalIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    where = { ...where, targetValue: groupId + '' };
    const keys = await this.mutilpleLevelFeatureFlagService.getGroupLevelFeatureKeys(groupId);
    if (keys) {
      const list = await this.mutilpleLevelFeatureFlagService.getAll(
        skip,
        take,
        where,
        orderBy,
        keys,
      );
      const count = await this.mutilpleLevelFeatureFlagService.getCount(where, keys);
      return { list, count };
    }
    return { list: [], count: 0 };
  }

  @Get('/feature-flags/keys')
  @Scopes('group-{groupId}:read-feature-flag')
  async getFeatFlagsKeys(@Param('groupId') groupId: number) {
    const keys = await this.mutilpleLevelFeatureFlagService.getGroupLevelFeatureKeys(groupId);
    if (keys) {
      const list = await this.mutilpleLevelFeatureFlagService.getAllFeatureFlagKeys(keys);
      return { list };
    }
    return { list: [] };
  }

  @Post('/feature-flags')
  @Scopes('group-{groupId}:write-feature-flag')
  async createFeatFlag(
    @Param('groupId') groupId: number,
    @Req() request: UserRequest,
    @Body() body: MutilpleLevelFeatureFlagsModelDto,
  ) {
    const keys = await this.mutilpleLevelFeatureFlagService.getGroupLevelFeatureKeys(groupId);
    if (keys) {
      const flag = await this.featureFlagService.getById(body.featureFlagId);
      if (keys.includes(flag.key)) {
        if (BotFeatureFlagFormatValidatorMap[flag.key]) {
          BotFeatureFlagFormatValidatorMap[flag.key](body);
        }
        body.createdByUserId = request.user.id;
        body.updatedByUserId = request.user.id;
        const group = await this.groupsService.getGroup(+groupId, {});
        body.targetType = FeatureFlagTargetType[group.groupType];
        body.targetValue = groupId + '';
        body.level = FeatureFlagLevels[body.targetType];
        return await this.mutilpleLevelFeatureFlagService.create(body);
      }
    }
    throw new ApiException(ErrorCode.INVALID_FEATURE_FLAG_PARAMETER);
  }

  @Put('/feature-flags/:id')
  @Scopes('group-{groupId}:write-feature-flag')
  async updateFeatFlag(
    @Param('groupId') groupId: number,
    @Req() request: UserRequest,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: MutilpleLevelFeatureFlagsModelDto,
  ) {
    const keys = await this.mutilpleLevelFeatureFlagService.getGroupLevelFeatureKeys(groupId);
    if (keys) {
      const mutilpleFlag = await this.mutilpleLevelFeatureFlagService.getById(id);
      const group = await this.groupsService.getGroup(groupId, {});
      if (
        mutilpleFlag?.targetType === group.groupType &&
        mutilpleFlag.targetValue === groupId + ''
      ) {
        const flag = await this.featureFlagService.getById(body.featureFlagId);
        if (flag && keys.includes(flag.key)) {
          if (BotFeatureFlagFormatValidatorMap[flag.key]) {
            BotFeatureFlagFormatValidatorMap[flag.key](body);
          }
          body.updatedByUserId = request.user.id;
          body.targetType = FeatureFlagTargetType[group.groupType];
          body.targetValue = groupId + '';
          body.level = FeatureFlagLevels[body.targetType];
          return await this.mutilpleLevelFeatureFlagService.update(id, body);
        }
      }
    }
    throw new ApiException(ErrorCode.INVALID_FEATURE_FLAG_PARAMETER);
  }

  @Delete('/feature-flags/:id')
  @Scopes('group-{groupId}:write-feature-flag')
  async deleteFeatFlag(@Param('id', ParseIntPipe) id: number, @Param('groupId') groupId: number) {
    const keys = await this.mutilpleLevelFeatureFlagService.getGroupLevelFeatureKeys(groupId);
    if (keys) {
      const mutilpleFlag = await this.mutilpleLevelFeatureFlagService.getById(id, true);
      const group = await this.groupsService.getGroup(groupId, {});
      if (
        mutilpleFlag?.targetType === group.groupType &&
        mutilpleFlag.targetValue === groupId + '' &&
        mutilpleFlag?.featureFlagId &&
        keys.includes((mutilpleFlag as any).featureFlag.key)
      ) {
        return await this.mutilpleLevelFeatureFlagService.delete(id);
      }
    }
    throw new ApiException(ErrorCode.INVALID_FEATURE_FLAG_PARAMETER);
  }

  @Get('/current-feature-flags')
  @Scopes('group-{groupId}:read-feature-flag')
  async getBotFeatFlags(@Param('groupId') groupId: number) {
    const keys = await this.mutilpleLevelFeatureFlagService.getGroupLevelFeatureKeys(groupId);
    if (keys && keys.length > 0) {
      const list = await this.featureFlagService.getGroupControlFeatureFlags(groupId, keys);
      return { list };
    } else {
      return { list: [] };
    }
  }

  @Post('/upsert-feature-flags')
  @AuditLog('upsert-feature-flags')
  @Scopes('group-{groupId}:write-feature-flag', 'group-*:update-token-limit')
  async upsertGroupFeatFlags(
    @Req() request: UserRequest,
    @Param('groupId') groupId: number,
    @Body() body: UpsertBotFeatFlagsDto,
  ) {
    const groupFeatFlags = await this.mutilpleLevelFeatureFlagService.upsertGroupLevelFeature(
      request,
      groupId,
      body,
    );
    return groupFeatFlags;
  }
}
