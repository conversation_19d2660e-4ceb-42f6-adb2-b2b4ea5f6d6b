-- CreateEnum
CREATE TYPE "GroupNotificationChannel" AS ENUM ('EMAIL', 'IN_APP');

-- Create<PERSON>num
CREATE TYPE "GroupNotificationType" AS ENUM ('REGULAR_ALERT', 'IMMEDIATE_ALERT', 'REPORT');

-- CreateEnum
CREATE TYPE "GroupNotificationRecipientRole" AS ENUM ('OWNER', 'ADMIN', 'REVIEWER_N_APPROVER');

-- CreateTable
CREATE TABLE "GroupNotification" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "groupNotificationName" TEXT NOT NULL,
    "channel" "GroupNotificationChannel" NOT NULL,
    "type" "GroupNotificationType" NOT NULL,
    "description" TEXT NOT NULL,
    "interval" TEXT NOT NULL,
    "defaultSilentPeriod" INTEGER,
    "defaultRecipientRoles" "GroupNotificationRecipientRole"[],

    CONSTRAINT "GroupNotification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "GroupNotificationConfig" (
    "id" SERIAL NOT NULL,
    "groupNotificationId" INTEGER NOT NULL,
    "groupId" INTEGER NOT NULL,
    "recipients" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "silentPeriod" INTEGER,

    CONSTRAINT "GroupNotificationConfig_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "GroupNotification_groupNotificationName_channel_key" ON "GroupNotification"("groupNotificationName", "channel");

-- CreateIndex
CREATE UNIQUE INDEX "GroupNotificationConfig_groupNotificationId_groupId_key" ON "GroupNotificationConfig"("groupNotificationId", "groupId");
