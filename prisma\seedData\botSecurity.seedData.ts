import { Environment } from '@prisma/client';

export const defaultbotSecurityList = [
  {
    groupId: 1,
    securityId: 'DefaultTEST',
    env: Environment.TEST,
    inputScanners: [
      {
        type: 'PromptInjection',
        securityLevel: 'SOFT',
        explanation:
          'A prompt injection scanner based on HuggingFace model. It is used todetect if a prompt is attempting to perform an injection attack.',
        params: [
          {
            name: 'threshold',
            type: 'float',
            value: 0.0,
          },
        ],
      },
      {
        type: 'Anonymize',
        securityLevel: 'SOFT',
        explanation:
          'Anonymize sensitive data in the text using NLP (English only) and predefined regex patterns.Anonymizes detected entities with placeholders like [REDACTED_PERSON_1] and stores the real values in a Vault.Deanonymizer can be used to replace the placeholders back to their original values.',
        params: [
          {
            name: 'use_faker',
            type: 'boolean',
            value: false,
          },
          {
            name: 'threshold',
            type: 'float',
            value: 0.75,
          },
          {
            name: 'allowed_names',
            type: 'Array',
            value: [],
          },
        ],
      },
    ],
    outputScanners: [
      {
        type: 'Sensitive',
        securityLevel: 'SOFT',
        explanation:
          ' A class used to detect sensitive (PII) data in the output of a language model.This class uses the Presidio Analyzer Engine and predefined internally patterns (patterns.py) to analyze the output for specified entity types.If no entity types are specified, it defaults to checking for all entity types.',
        params: [
          {
            name: 'threshold',
            type: 'float',
            value: 0.5,
          },
          {
            name: 'redact',
            type: 'bool',
            value: true,
          },
          {
            name: 'allowed_names',
            type: 'Array',
            value: [],
          },
        ],
      },
    ],
  },
  {
    groupId: 2,
    securityId: 'DefaultPROD',
    env: Environment.PROD,
    inputScanners: [
      {
        type: 'PromptInjection',
        securityLevel: 'SOFT',
        explanation:
          'A prompt injection scanner based on HuggingFace model. It is used todetect if a prompt is attempting to perform an injection attack.',
        params: [
          {
            name: 'threshold',
            type: 'float',
            value: 0.0,
          },
        ],
      },
      {
        type: 'Anonymize',
        securityLevel: 'SOFT',
        explanation:
          'Anonymize sensitive data in the text using NLP (English only) and predefined regex patterns.Anonymizes detected entities with placeholders like [REDACTED_PERSON_1] and stores the real values in a Vault.Deanonymizer can be used to replace the placeholders back to their original values.',
        params: [
          {
            name: 'use_faker',
            type: 'boolean',
            value: false,
          },
          {
            name: 'threshold',
            type: 'float',
            value: 0.75,
          },
          {
            name: 'allowed_names',
            type: 'Array',
            value: [],
          },
        ],
      },
    ],
    outputScanners: [
      {
        type: 'Sensitive',
        securityLevel: 'SOFT',
        explanation:
          ' A class used to detect sensitive (PII) data in the output of a language model.This class uses the Presidio Analyzer Engine and predefined internally patterns (patterns.py) to analyze the output for specified entity types.If no entity types are specified, it defaults to checking for all entity types.',
        params: [
          {
            name: 'threshold',
            type: 'float',
            value: 0.5,
          },
          {
            name: 'redact',
            type: 'bool',
            value: true,
          },
          {
            name: 'allowed_names',
            type: 'Array',
            value: [],
          },
        ],
      },
    ],
  },
];
