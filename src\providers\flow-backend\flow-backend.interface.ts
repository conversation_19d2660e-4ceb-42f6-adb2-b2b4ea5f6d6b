import { ApiProperty } from "@nestjs/swagger";
import { Call<PERSON>hain } from "src/modules/flows/flows.dto";

export interface CreateChatflowResponse {
  id: string;
  name: string;
  flowData: string;
  deployed: boolean;
  isPublic: boolean;
  apikeyid: string;
  chatbotConfig: string;
  apiConfig: string;
  createdDate: string;
  updatedDate: string;
}
export interface GetSpecificChatflowResponse {
  id: string;
  name: string;
  flowData: string;
  deployed: boolean;
  isPublic: boolean;
  apikeyid: string;
  chatbotConfig: string;
  apiConfig: string;
  analytic: string;
  createdDate: string;
  updatedDate: string;
}
export interface GetAllNodesResponse {
  label: string;
  name: string;
  version: number;
  type: string;
  category: string;
  icon: string;
  description: string;
  baseClasses: string[];
  credential: Credential;
  inputs: any;
  filePath: string;
}

type Credential = {
  label: string;
  name: string;
  type: string;
  credentialNames: string[];
};
export interface PredictionResponse {
  answer: string;
  usage: Usage[];
  toolUsage: ToolUsage[];
  callChain?: CallChain;
}

export interface PredictionHistory {
  type: PredictionHistoryType;
  message: string;
}

export enum PredictionHistoryType {
  apiMessage = 'apiMessage',
  userMessage = 'userMessage',
}

export interface PredictionRequest {
  question: string;
  history?: PredictionHistory[];
  appendVariables?: Map<string, string>;
}

// Currently, it's same with llm-backend.interface Usage, but separate it here for flow chat.
export class Usage {
  @ApiProperty({ description: 'bot builder id' })
  botId: number;

  @ApiProperty({ description: 'bot prompt tokens' })
  promptTokens: number;

  @ApiProperty({ description: 'bot completion tokens' })
  completionTokens: number;

  @ApiProperty({ description: 'bot total completion tokens' })
  totalCompletionTokens: number;

  @ApiProperty({ description: 'bot embedding tokens' })
  embeddingTokens: number;
}

export class ToolUsage {
  @ApiProperty({ description: 'tool name' })
  name: string;

  @ApiProperty({ description: 'tool usage of number' })
  usage: number;
}
