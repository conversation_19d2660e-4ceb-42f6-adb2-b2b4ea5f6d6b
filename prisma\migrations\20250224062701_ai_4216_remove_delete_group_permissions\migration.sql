
DELETE FROM "RolePermission" rp WHERE (SELECT "permissionKey" FROM "Permission" p WHERE p.id = rp."permissionId") in (
	'group-{groupId}:delete-api-key',
	'group-{groupId}:delete-membership',
	'group-{groupId}:delete-llm-model-files-tags',
	'group-*:delete-api-key',
	'group-*:delete-membership',
	'group-*:delete-llm-model-files-tags'
);
DELETE FROM "PermissionGroupSetting" pgs WHERE (SELECT "permissionKey" FROM "Permission" p WHERE p.id = pgs."permissionId") in (
	'group-{groupId}:delete-api-key',
	'group-{groupId}:delete-membership',
	'group-{groupId}:delete-llm-model-files-tags'
);   
DELETE FROM "Permission" p WHERE "permissionKey" in (
	'group-{groupId}:delete-api-key',
	'group-{groupId}:delete-membership',
	'group-{groupId}:delete-llm-model-files-tags',
	'group-*:delete-api-key',
	'group-*:delete-membership',
	'group-*:delete-llm-model-files-tags'
);

DELETE FROM "RolePermission" rp WHERE rp."roleId" = (SELECT id FROM "Role" r WHERE r."systemName" = 'SUDO' ) AND rp."permissionId" = (SELECT id FROM "Permission" p WHERE p."permissionKey" = 'group-*:write-membership');

DELETE FROM "RolePermission" rp WHERE rp."roleId" = (SELECT id FROM "Role" r WHERE r."systemName" = 'SUDO' ) AND rp."permissionId" = (SELECT id FROM "Permission" p WHERE p."permissionKey" = 'group-*:read-membership');

DELETE FROM "RolePermission" rp WHERE rp."roleId" = (SELECT id FROM "Role" r WHERE r."systemName" = 'SUDO' ) AND rp."permissionId" = (SELECT id FROM "Permission" p WHERE p."permissionKey" = 'group-*:role-*');

