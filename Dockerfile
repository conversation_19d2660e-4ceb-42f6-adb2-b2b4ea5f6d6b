# build stage
FROM registry.access.redhat.com/ubi8/nodejs-18 as builder

ARG NODE_ENV
ARG SENTRY_DSN
ARG PUPPETEER_SKIP_DOWNLOAD

ENV NODE_ENV=${NODE_ENV}
ENV SENTRY_DSN=${SENTRY_DSN}
ENV PUPPETEER_SKIP_DOWNLOAD=$PUPPETEER_SKIP_DOWNLOAD
ENV PUPPETEER_SKIP_DOWNLOAD=true

ENV PORT=3001

WORKDIR /opt/app-root

USER 0

COPY . .

RUN git config --global --add safe.directory /opt/app-root

RUN echo $(git describe --tags --abbrev=0) $(git rev-parse --short HEAD) > version.txt

RUN ["npm", "ci"]

RUN ["npx", "prisma", "generate"]

RUN ["npm", "run", "build"]

FROM registry.access.redhat.com/ubi8/nodejs-18

WORKDIR /opt/app-root

COPY --from=builder /opt/app-root/dist/ ./dist/

COPY --from=builder /opt/app-root/node_modules/ ./node_modules/

COPY --from=builder /opt/app-root/prisma/ ./prisma/

COPY --from=builder /opt/app-root/package.json .

COPY --from=builder /opt/app-root/startup.sh .

COPY --from=builder /opt/app-root/version.txt ./version.txt

COPY --from=builder /opt/app-root/static/ ./static/

USER 0

RUN /bin/bash -c 'chmod +x startup.sh'

USER 1001

EXPOSE 3001

ENTRYPOINT ["/bin/bash", "-c", "./startup.sh"]

# how to build
# docker build -f Dockerfile -t hkt-gptaas-backend:1.0.21-sit --build-arg NODE_ENV=development .

# docker build -f Dockerfile -t hkt-gptaas-backend:1.0.21-sit --build-arg NODE_ENV=development --build-arg SENTRY_DSN=https://<EMAIL>/4505137504780288 .

# docker run -dp 3001:3001 --env-file .env hkt-gptaas-backend
