import { Logger } from '@nestjs/common';

export const SkipIfNoClient = (): MethodDecorator => {
  const logger = new Logger('SkipIfNoClient');
  return (_, __, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;
    descriptor.value = async function (...args: any[]) {
      if ((this as any)?.graphqlClient == null) {
        logger.warn('Wiki.js ENV is not properly configured. Wiki.js function is skipped.');
        return;
      }
      return await originalMethod.apply(this, args);
    };
  };
};
