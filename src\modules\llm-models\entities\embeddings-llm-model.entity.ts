import { ApiProperty } from "@nestjs/swagger";
import { EmbeddingsData, EmbeddingsResponse } from "src/providers/llm-backend/llm-backend.interface";
import { UsageEntity } from "./llm-model.entity";


export class EmbeddingsDataEntity implements EmbeddingsData{
    @ApiProperty({ description: 'index of the input text' })
    text_index: number;
    @ApiProperty({ description: 'embeddings result' })
    embeddings: number[];
}

export class EmbeddingsResponseEntity implements EmbeddingsResponse {
    @ApiProperty({ description: 'model' })
    model: string;
    @ApiProperty({ description: 'embeddings data' })
    data: EmbeddingsDataEntity[];
    @ApiProperty({ description: 'Token usage' })
    usage: UsageEntity;
}