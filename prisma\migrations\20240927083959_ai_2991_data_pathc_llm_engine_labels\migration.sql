-- This is an empty migration.

do
$$
    declare needAddLabelsLLm RECORD;
    LabelId INTEGER;
    llmEngineId INTEGER;
begin  
    FOR needAddLabelsLLm in select jsonb_array_elements(x.labels) as  label ,x.id  FROM "LlmEngine" x where
    jsonb_array_length( x.labels ) > 0 
    loop
      llmEngineId := needAddLabelsLLm.id;  
      INSERT INTO "Labels"("name", "color","labelType","createdAt","createdBy","updatedAt","updatedBy")
      SELECT needAddLabelsLLm.label->>'name', needAddLabelsLLm.label->>'color','LABELS',now(),1,now(),1
      WHERE NOT EXISTS (
          SELECT id
          FROM "Labels"
          WHERE name = needAddLabelsLLm.label->>'name'
          );
      SELECT id FROM "Labels" WHERE name = needAddLabelsLLm.label->>'name' into LabelId;
      INSERT INTO "EntityLabels"("labelsId","LabelEntityType","entityId")
      SELECT LabelId, 'LLM_ENGINE', llmEngineId
      WHERE NOT EXISTS (
          SELECT id
          FROM "EntityLabels"
          WHERE "labelsId" = LabelId AND "LabelEntityType" = 'LLM_ENGINE' AND "entityId" = llmEngineId
        );
    end loop;
    ALTER TABLE "LlmEngine" DROP COLUMN "labels";
end;
$$
