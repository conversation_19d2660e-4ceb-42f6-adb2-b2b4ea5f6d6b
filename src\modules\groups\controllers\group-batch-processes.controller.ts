import {
  Controller,
  Post,
  Body,
  Param,
  Get,
  Delete,
  Logger,
  Header,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
  Req,
  StreamableFile,
  UseGuards,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiParam,
  ApiOperation,
  ApiBody,
  ApiResponse,
} from '@nestjs/swagger';
import { BatchProcessorService } from 'src/providers/batch-processor/batch-processor.service';
import { CreateBatchProcessDto } from '../dto/create-batch-process.dto';
import { ListBatchProcessesDto } from '../dto/list-batch-processes.dto';
import { AxiosError, AxiosResponse } from 'axios';
import { Readable } from 'stream';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { UserRequest } from 'src/modules/auth/auth.interface';
import { StaartAuthGuard } from 'src/modules/auth/staart-auth.guard';
import { Scopes } from 'src/modules/auth/scope.decorator';
import { ExternalApi } from 'src/swagger-document';

@ApiTags('Groups - Batch API')
@ApiBearerAuth()
@Controller('groups/:groupId/batch-processes')
@UseGuards(StaartAuthGuard)
export class GroupBatchProcessesController {
  private logger = new Logger(GroupBatchProcessesController.name);
  constructor(private readonly batchProcessorService: BatchProcessorService) {}

  @Post()
  @ExternalApi()
  @Scopes('group-{groupId}:create-batch-process')
  @ApiOperation({ summary: 'Create a new batch process' })
  @ApiParam({ name: 'groupId', description: 'ID of the group', type: String })
  @ApiBody({ type: CreateBatchProcessDto })
  @ApiResponse({
    status: 201,
    description: 'Batch process created successfully.',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        groupId: { type: 'number', example: 10452855 },
        groupInfo: {
          type: 'object',
          properties: {
            id: { type: 'number', example: 10452855 },
            env: { type: 'string', example: 'TEST' },
            name: { type: 'string', example: 'Bot Name' },
          },
        },
        batchFileId: { type: 'number', example: 1 },
        provider: { type: 'string', example: 'AZURE_OPENAI' },
        providerMetadata: {
          type: 'object',
          properties: {
            endpoint: { type: 'string', example: '/v1/chat/completions' },
            s3ErrorPath: { type: 'string', nullable: true, example: null },
            azureBatchId: { type: 'string', example: 'batch_19f0d7ca-6cde-4f2e-a7c7-02c72171ef1a' },
            s3OutputPath: { type: 'string', example: '10452855/batchProcesses/1/output.jsonl' },
            azureEndpoint: { type: 'string', example: 'https://hkt-chatgpt-model.openai.azure.com' },
            lastAzureStatus: { type: 'string', example: 'completed' },
            azureInputFileId: { type: 'string', example: 'file-80ce4c44091a4375b7c326b6022fff53' },
            completionWindow: { type: 'string', example: '24h' },
          },
        },
        batchProcessStatus: { type: 'string', example: 'POSTPROCESSING' },
        batchProcessResult: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'batch_19f0d7ca-6cde-4f2e-a7c7-02c72171ef1a' },
            errors: { type: 'object', nullable: true, example: null },
            object: { type: 'string', example: 'batch' },
            status: { type: 'string', example: 'completed' },
            endpoint: { type: 'string', example: '/chat/completions' },
            metadata: { type: 'object', nullable: true, example: null }, // Added colon
            failed_at: { type: 'number', nullable: true, example: null },
            created_at: { type: 'number', example: 1745489695 },
            expired_at: { type: 'number', nullable: true, example: null },
            expires_at: { type: 'number', example: 1745576095 },
            cancelled_at: { type: 'number', nullable: true, example: null },
            completed_at: { type: 'number', example: 1745491811 },
            cancelling_at: { type: 'number', nullable: true, example: null },
            error_file_id: { type: 'string', nullable: true, example: null },
            finalizing_at: { type: 'number', example: 1745491717 },
            input_file_id: { type: 'string', example: 'file-80ce4c44091a4375b7c326b6022fff53' },
            in_progress_at: { type: 'number', example: 1745491491 },
            output_file_id: { type: 'string', example: 'file-7a129983-2994-4f86-a011-5461ae6b5ff3' },
            request_counts: {
              type: 'object',
              properties: {
                total: { type: 'number', example: 2 },
                failed: { type: 'number', example: 0 },
                completed: { type: 'number', example: 2 },
              },
            },
            completion_window: { type: 'string', example: '24h' },
          },
        },
        createdBy: { type: 'number', example: 547 },
        createdAt: { type: 'string', format: 'date-time', example: '2025-04-24T10:14:55.702Z' },
        updatedAt: { type: 'string', format: 'date-time', example: '2025-04-24T10:59:38.812Z' },
      },
    },
  })
  async createBatchProcess(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() createDto: CreateBatchProcessDto,
    @Req() req: UserRequest,
  ): Promise<unknown> {
    this.logger.log(
      `[${groupId}] Creating batch process for file ${createDto.batchFileId} using provider ${createDto.provider}`,
    );
    try {
      const response: AxiosResponse<unknown> = await this.batchProcessorService.createBatchProcess(
        groupId,
        createDto.batchFileId,
        createDto.provider,
        createDto.endpoint,
        createDto.completionWindow,
        req.user,
      );
      return response.data;
    } catch (err: unknown) {
      this.logger.error(`[${groupId}] Error creating batch process: ${String(err)}`);
      this.handleError(err);
    }
  }

  @Get()
  @ExternalApi()
  @Scopes('group-{groupId}:read-batch-process')
  @ApiOperation({ summary: 'List batch processes for a group' })
  @ApiParam({ name: 'groupId', description: 'ID of the group', type: String })
  @ApiResponse({
    status: 200,
    description: 'List of batch processes retrieved successfully.',
    schema: {
      type: 'object',
      properties: {
        batchProcesses: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number', example: 1 },
              groupId: { type: 'number', example: 10452855 },
              groupInfo: {
                type: 'object',
                properties: {
                  id: { type: 'number', example: 10452855 },
                  env: { type: 'string', example: 'TEST' },
                  name: { type: 'string', example: 'Bot Name' },
                },
              },
              batchFileId: { type: 'number', example: 1 },
              provider: { type: 'string', example: 'AZURE_OPENAI' },
              providerMetadata: {
                type: 'object',
                properties: {
                  endpoint: { type: 'string', example: '/v1/chat/completions' },
                  azureBatchId: { type: 'string', example: 'batch_19f0d7ca-6cde-4f2e-a7c7-02c72171ef1a' },
                  azureEndpoint: { type: 'string', example: 'https://hkt-chatgpt-model.openai.azure.com' },
                  azureInputFileId: { type: 'string', example: 'file-80ce4c44091a4375b7c326b6022fff53' },
                  completionWindow: { type: 'string', example: '24h' },
                },
              },
              batchProcessStatus: { type: 'string', example: 'PENDING' },
              batchProcessResult: { type: 'object', example: {}, description: 'Result object structure varies based on provider and status' },
              createdBy: { type: 'number', example: 547 },
              createdAt: { type: 'string', format: 'date-time', example: '2025-04-24T10:14:55.702Z' },
              updatedAt: { type: 'string', format: 'date-time', example: '2025-04-24T10:14:55.702Z' },
            },
          },
        },
        total: { type: 'number', example: 1 },
      },
    },
  })
  async listBatchProcesses(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query() queryParams: ListBatchProcessesDto,
  ): Promise<unknown> {
    this.logger.log(
      `[${groupId}] Listing batch processes with params: ${JSON.stringify(queryParams)}`,
    );
    try {
      const batchProcesses = await this.batchProcessorService.listBatchProcesses(
        groupId,
        queryParams,
      );
      return batchProcesses.data;
    } catch (err: unknown) {
      this.logger.error(`[${groupId}] Error listing batch processes: ${String(err)}`);
      this.handleError(err);
    }
  }

  @Get(':batchProcessId')
  @ExternalApi()
  @Scopes('group-{groupId}:read-batch-process')
  @ApiOperation({ summary: 'Get batch process status and details' })
  @ApiParam({ name: 'groupId', description: 'ID of the group', type: String })
  @ApiParam({ name: 'batchProcessId', description: 'ID of the batch process', type: String })
  @ApiResponse({
    status: 200,
    description: 'Batch process status and details retrieved successfully.',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        groupId: { type: 'number', example: 10452855 },
        groupInfo: {
          type: 'object',
          properties: {
            id: { type: 'number', example: 10452855 },
            env: { type: 'string', example: 'TEST' },
            name: { type: 'string', example: 'Bot Name' },
          },
        },
        batchFileId: { type: 'number', example: 1 },
        provider: { type: 'string', example: 'AZURE_OPENAI' },
        providerMetadata: {
          type: 'object',
          properties: {
            endpoint: { type: 'string', example: '/v1/chat/completions' },
            s3ErrorPath: { type: 'string', nullable: true, example: null },
            azureBatchId: { type: 'string', example: 'batch_19f0d7ca-6cde-4f2e-a7c7-02c72171ef1a' },
            s3OutputPath: { type: 'string', example: '10452855/batchProcesses/1/output.jsonl' },
            azureEndpoint: { type: 'string', example: 'https://hkt-chatgpt-model.openai.azure.com' },
            lastAzureStatus: { type: 'string', example: 'completed' },
            azureInputFileId: { type: 'string', example: 'file-80ce4c44091a4375b7c326b6022fff53' },
            completionWindow: { type: 'string', example: '24h' },
          },
        },
        batchProcessStatus: { type: 'string', example: 'POSTPROCESSING' },
        batchProcessResult: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'batch_19f0d7ca-6cde-4f2e-a7c7-02c72171ef1a' },
            errors: { type: 'object', nullable: true, example: null },
            object: { type: 'string', example: 'batch' },
            status: { type: 'string', example: 'completed' },
            endpoint: { type: 'string', example: '/chat/completions' },
            metadata: { type: 'object', nullable: true, example: null },
            failed_at: { type: 'number', nullable: true, example: null },
            created_at: { type: 'number', example: 1745489695 },
            expired_at: { type: 'number', nullable: true, example: null },
            expires_at: { type: 'number', example: 1745576095 },
            cancelled_at: { type: 'number', nullable: true, example: null },
            completed_at: { type: 'number', example: 1745491811 },
            cancelling_at: { type: 'number', nullable: true, example: null },
            error_file_id: { type: 'string', nullable: true, example: null },
            finalizing_at: { type: 'number', example: 1745491717 },
            input_file_id: { type: 'string', example: 'file-80ce4c44091a4375b7c326b6022fff53' },
            in_progress_at: { type: 'number', example: 1745491491 },
            output_file_id: { type: 'string', example: 'file-7a129983-2994-4f86-a011-5461ae6b5ff3' },
            request_counts: {
              type: 'object',
              properties: {
                total: { type: 'number', example: 2 },
                failed: { type: 'number', example: 0 },
                completed: { type: 'number', example: 2 },
              },
            },
            completion_window: { type: 'string', example: '24h' },
          },
        },
        createdBy: { type: 'number', example: 547 },
        createdAt: { type: 'string', format: 'date-time', example: '2025-04-24T10:14:55.702Z' },
        updatedAt: { type: 'string', format: 'date-time', example: '2025-04-24T10:59:38.812Z' },
      },
    },
  })
  async getBatchProcessStatus(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('batchProcessId', ParseIntPipe) batchProcessId: number,
  ): Promise<unknown> {
    // Added return type
    this.logger.log(`[${groupId}] Getting status for batch process ${batchProcessId}`);
    try {
      const response: AxiosResponse<unknown> = await this.batchProcessorService.getBatchProcess(
        groupId,
        batchProcessId,
      );
      return response.data;
    } catch (err: unknown) {
      this.logger.error(`[${groupId}] Error getting batch process: ${String(err)}`);
      this.handleError(err);
    }
  }

  @Get(':batchProcessId/output.jsonl')
  @ExternalApi()
  @Scopes('group-{groupId}:read-batch-process')
  @ApiOperation({ summary: 'Download batch process output file (.jsonl)' })
  @ApiParam({ name: 'groupId', description: 'ID of the group', type: String })
  @ApiParam({ name: 'batchProcessId', description: 'ID of the batch process', type: String })
  @Header('Content-Type', 'application/jsonl')
  @Header('Content-Disposition', 'attachment; filename="batch-output.jsonl"')
  @ApiResponse({ // Added ApiResponse decorator for 200 OK (File Download)
    status: 200,
    description: 'Batch process output file downloaded successfully.',
    content: {
      'application/jsonl': {
        schema: {
          type: 'string',
          format: 'binary',
          example: '{"custom_id": "request-1", "response": {"body": {"choices": [{"content_filter_results": {"hate": {"filtered": false, "severity": "safe"}, "protected_material_code": {"filtered": false, "detected": false}, "protected_material_text": {"filtered": false, "detected": false}, "self_harm": {"filtered": false, "severity": "safe"}, "sexual": {"filtered": false, "severity": "safe"}, "violence": {"filtered": false, "severity": "safe"}}, "finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "2 + 2 equals 4.", "refusal": null, "role": "assistant"}}], "created": 1745491530, "id": "chatcmpl-BPoN8GGW2e4qFY6N0aZl5Ag56x21L", "model": "gpt-4o-mini-2024-07-18", "object": "chat.completion", "prompt_filter_results": [{"prompt_index": 0, "content_filter_results": {"hate": {"filtered": false, "severity": "safe"}, "jailbreak": {"filtered": false, "detected": false}, "self_harm": {"filtered": false, "severity": "safe"}, "sexual": {"filtered": false, "severity": "safe"}, "violence": {"filtered": false, "severity": "safe"}}}], "system_fingerprint": "fp_ded0d14823", "usage": {"completion_tokens": 9, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 0, "rejected_prediction_tokens": 0}, "prompt_tokens": 24, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 0}, "total_tokens": 33}}, "request_id": "1b335165-da7e-4f34-b29e-7533a79a609c", "status_code": 200}, "error": null}\n{"custom_id": "request-2", "response": {"body": {"choices": [{"content_filter_results": {"hate": {"filtered": false, "severity": "safe"}, "protected_material_code": {"filtered": false, "detected": false}, "protected_material_text": {"filtered": false, "detected": false}, "self_harm": {"filtered": false, "severity": "safe"}, "sexual": {"filtered": false, "severity": "safe"}, "violence": {"filtered": false, "severity": "safe"}}, "finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "Hello in French is \\"Bonjour.\\"", "refusal": null, "role": "assistant"}}], "created": 1745491530, "id": "chatcmpl-BPoN8t9syRUh9usvCyTJlCRK5zATo", "model": "gpt-4o-mini-2024-07-18", "object": "chat.completion", "prompt_filter_results": [{"prompt_index": 0, "content_filter_results": {"hate": {"filtered": false, "severity": "safe"}, "jailbreak": {"filtered": false, "detected": false}, "self_harm": {"filtered": false, "severity": "safe"}, "sexual": {"filtered": false, "severity": "safe"}, "violence": {"filtered": false, "severity": "safe"}}}], "system_fingerprint": "fp_ded0d14823", "usage": {"completion_tokens": 8, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 0, "rejected_prediction_tokens": 0}, "prompt_tokens": 22, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 0}, "total_tokens": 30}}, "request_id": "06187ba6-182b-4cf4-9ac4-d22699647558", "status_code": 200}, "error": null}',
        },
      },
    },
  })
  async downloadBatchOutput(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('batchProcessId', ParseIntPipe) batchProcessId: number,
  ): Promise<StreamableFile> {
    this.logger.log(`[${groupId}] Downloading output for batch process ${batchProcessId}`);
    try {
      const response: AxiosResponse<Readable> =
        await this.batchProcessorService.downloadBatchProcessOutput(groupId, batchProcessId);
      return new StreamableFile(response.data);
    } catch (err: unknown) {
      this.logger.error(`[${groupId}] Error getting batch process output: ${String(err)}`);
      this.handleError(err);
    }
  }

  @Get(':batchProcessId/error.jsonl')
  @ExternalApi()
  @Scopes('group-{groupId}:read-batch-process')
  @ApiOperation({ summary: 'Download batch process error file (.jsonl)' })
  @ApiParam({ name: 'groupId', description: 'ID of the group', type: String })
  @ApiParam({ name: 'batchProcessId', description: 'ID of the batch process', type: String })
  @Header('Content-Type', 'application/jsonl')
  @Header('Content-Disposition', 'attachment; filename="batch-error.jsonl"')
  @ApiResponse({ // Added ApiResponse decorator for 200 OK (File Download)
    status: 200,
    description: 'Batch process error file downloaded successfully.',
    content: {
      'application/jsonl': {
        schema: {
          type: 'string',
          format: 'binary',
          example: '{"custom_id": "task-2", "response": {"request_id": "44b2a0db-eed7-4e67-91c3-3586b80652d6", "status_code": 400}, "error": {"code": null, "message": "Unsupported data type\\n"}}',
        },
      },
    },
  })
  async downloadBatchError(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('batchProcessId', ParseIntPipe) batchProcessId: number,
  ): Promise<StreamableFile> {
    this.logger.log(`[${groupId}] Downloading error file for batch process ${batchProcessId}`);
    try {
      const response: AxiosResponse<Readable> =
        await this.batchProcessorService.downloadBatchProcessError(groupId, batchProcessId);
      return new StreamableFile(response.data);
    } catch (err: unknown) {
      this.logger.error(`[${groupId}] Error getting batch process error: ${String(err)}`);
      this.handleError(err);
    }
  }

  @Delete(':batchProcessId')
  @ExternalApi()
  @Scopes('group-{groupId}:cancel-batch-process')
  @ApiOperation({ summary: 'Cancel/Delete a batch process' })
  @ApiParam({ name: 'groupId', description: 'ID of the group', type: String })
  @ApiParam({ name: 'batchProcessId', description: 'ID of the batch process', type: String })
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    status: 200,
    description: 'Batch process cancellation initiated successfully.',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 7 },
        groupId: { type: 'number', example: 10452855 },
        groupInfo: {
          type: 'object',
          properties: {
            id: { type: 'number', example: 10452855 },
            env: { type: 'string', example: 'TEST' },
            name: { type: 'string', example: 'Bot Name' },
          },
        },
        batchFileId: { type: 'number', example: 1 },
        provider: { type: 'string', example: 'AZURE_OPENAI' },
        providerMetadata: {
          type: 'object',
          properties: {
            endpoint: { type: 'string', example: '/v1/chat/completions' },
            azureBatchId: { type: 'string', example: 'batch_3b43a313-c61a-4d0c-af5e-302c91c10a1c' },
            lastAzureStatus: { type: 'string', example: 'cancelling' },
            azureInputFileId: { type: 'string', example: 'file-53843d2804324e579a66717ab84a5e2e' },
            completionWindow: { type: 'string', example: '24h' },
          },
        },
        batchProcessStatus: { type: 'string', example: 'CANCELLED' },
        batchProcessResult: {
          type: 'object',
          properties: {
            id: { type: 'string', example: 'batch_3b43a313-c61a-4d0c-af5e-302c91c10a1c' },
            errors: { type: 'object', nullable: true, example: null },
            object: { type: 'string', example: 'batch' },
            status: { type: 'string', example: 'cancelling' },
            endpoint: { type: 'string', example: '/chat/completions' },
            metadata: { type: 'object', nullable: true, example: null },
            failed_at: { type: 'number', nullable: true, example: null },
            created_at: { type: 'number', example: 1745317829 },
            expired_at: { type: 'number', nullable: true, example: null },
            expires_at: { type: 'number', example: 1745404229 },
            cancelled_at: { type: 'number', nullable: true, example: null }, // Might be null if still cancelling
            completed_at: { type: 'number', nullable: true, example: null },
            cancelling_at: { type: 'number', example: 1745317859 },
            error_file_id: { type: 'string', example: '' },
            finalizing_at: { type: 'number', nullable: true, example: null },
            input_file_id: { type: 'string', example: 'file-53843d2804324e579a66717ab84a5e2e' },
            in_progress_at: { type: 'number', nullable: true, example: null },
            output_file_id: { type: 'string', example: '' },
            request_counts: {
              type: 'object',
              properties: {
                total: { type: 'number', example: 0 },
                failed: { type: 'number', example: 0 },
                completed: { type: 'number', example: 0 },
              },
            },
            completion_window: { type: 'string', example: '24h' },
          },
        },
        createdBy: { type: 'number', example: 1 },
        createdAt: { type: 'string', format: 'date-time', example: '2025-04-22T10:30:30.026Z' },
        updatedAt: { type: 'string', format: 'date-time', example: '2025-04-22T10:31:00.053Z' },
      },
    },
  })
  async cancelBatchProcess(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('batchProcessId', ParseIntPipe) batchProcessId: number,
  ): Promise<unknown> {
    this.logger.log(`[${groupId}] Cancelling/Deleting batch process ${batchProcessId}`);
    try {
      const result = await this.batchProcessorService.cancelBatchProcess(groupId, batchProcessId);
      this.logger.debug(`Batch process ${batchProcessId} cancelled successfully, result: ${JSON.stringify(result)}`);
      return result;
    } catch (err: unknown) {
      this.logger.error(`[${groupId}] Error cancelling batch process: ${String(err)}`);
      this.handleError(err);
    }
  }

  handleError(err: unknown) {
    if (err instanceof ApiException) throw err;
    if (
      typeof err === 'object' &&
      'response' in err &&
      typeof err.response === 'object' &&
      'status' in err.response
    ) {
      const axiosError = err as AxiosError;
      if (axiosError.response) {
        if (axiosError.response.status === 400) {
          throw new ApiException(ErrorCode.BAD_REQUEST_FROM_BATCH_PROCESSOR, {
            message: axiosError.message,
            error: axiosError.response?.data?.error,
          });
        } else if (axiosError.response.status === 404) {
          throw new ApiException(ErrorCode.NOT_FOUND_FROM_BATCH_PROCESSOR, {
            message: axiosError.message,
            error: axiosError.response?.data?.error,
          });
        } else if (axiosError.response.status === 409) {
          throw new ApiException(ErrorCode.INVALID_STATUS_FROM_BATCH_PROCESSOR, {
            message: axiosError.message,
            error: axiosError.response?.data?.error,
          });
        }
      }
    }
    throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR, {
      error: String(err),
    });
  }
}
