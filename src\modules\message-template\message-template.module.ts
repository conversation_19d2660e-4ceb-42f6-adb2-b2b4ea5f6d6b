import { Module } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { SystemMessageTemplateController } from './system-message-template.controller';
import { MessageTemplateService } from './message-template.service';
import { GroupMessageTemplateController } from './group-message-template.controller';
import { LabelsModule } from '../labels/labels.module';
import { LlmEnginesModule } from '../llm-engines/llm-engines.module';
import { GroupsModule } from '../groups/groups.module';

@Module({
  imports: [PrismaModule, LabelsModule, LlmEnginesModule, GroupsModule],
  controllers: [SystemMessageTemplateController, GroupMessageTemplateController],
  providers: [MessageTemplateService],
  exports: [MessageTemplateService],
})
export class MessageTemplateModule {}
