import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from '../auth/auth.module';
import { MailModule } from '../../providers/mail/mail.module';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { TokensModule } from '../../providers/tokens/tokens.module';
import { UserController } from './users.controller';
import { UsersService } from './users.service';
import { S3Module } from '../../providers/s3/s3.module';
import { ApiKeysModule } from '../api-keys/api-keys.module';
import { WikijsModule } from '../wikijs/wikijs.module';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';
import { RedisModule } from 'src/providers/redis/redis.module';
import { PlansModule } from '../plans/plans.module';
import { RateLimitModule } from 'src/providers/rate-limit/rate-limit.module';
import { UserBookmarkModule } from '../userBookmark/user-bookmark.module';
import { ResizeImageModule } from 'src/providers/resize-image/resize-image.module';
@Module({
  imports: [
    PlansModule,
    PrismaModule,
    forwardRef(() => AuthModule),
    MailModule,
    ConfigModule,
    TokensModule,
    S3Module,
    forwardRef(() => ApiKeysModule),
    WikijsModule,
    FeatureFlagModule,
    MailModule,
    RedisModule,
    RateLimitModule,
    UserBookmarkModule,
    ResizeImageModule,
  ],
  controllers: [UserController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
