-- AlterTable
ALTER TABLE "ApiKey" ADD COLUMN     "rateLimitPlanId" TEXT;

-- CreateTable
CREATE TABLE "ApiPlan" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "planId" TEXT NOT NULL,
    "env" "Environment" DEFAULT 'TEST',
    "id" SERIAL NOT NULL,

    CONSTRAINT "ApiPlan_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ApiPlan.apiPlan_unique" ON "ApiPlan"("planId");
