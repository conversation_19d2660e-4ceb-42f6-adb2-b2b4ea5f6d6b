import { Prisma } from '@prisma/client';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export type Expose<T> = Omit<
  Omit<Omit<Omit<Omit<T, 'password'>, 'twoFactorSecret'>, 'token'>, 'emailSafe'>,
  'subnet'
>;
export class PrismaStringFilterDto {
  @IsString()
  @IsOptional()
  equals?: string;

  @IsString()
  @IsOptional()
  contains?: string;

  @IsString()
  @IsOptional()
  startsWith?: string;

  @IsString()
  @IsOptional()
  endsWith?: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  in?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  notIn?: string[];

  @ValidateNested()
  @Type(() => PrismaStringFilterDto)
  @IsOptional()
  not?: PrismaStringFilterDto;

  @IsEnum(Prisma.QueryMode)
  @IsOptional()
  mode: Prisma.QueryMode = 'insensitive';
}

export class PrismaPaginationDto<T> {
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  skip?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  take?: number;

  @IsOptional()
  orderBy?: Record<keyof T, Prisma.SortOrder>;
}
