# Default values for bot-builder-backend.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 2

image:
  repository: ************.dkr.ecr.ap-east-1.amazonaws.com/gbot/bot-builder-backend
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
# need to set "fullnameOverride", otherwise the app name will be "bot-builder-backend-bot-builder-backend"
# may need to think if needed to fix
fullnameOverride: &appFullName "bot-builder-backend"

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role-bot-builder-backend
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "bot-builder-backend"

podAnnotations: {}
podLabels: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: NodePort
  port: 80
  targetPort: 3001

ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  limits:
    cpu: '1'
    memory: 2Gi
  requests:
    cpu: 500m
    memory: 1Gi
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes:
  - name: bot-builder-backend-configmap-volume
    configMap:
      name: bot-builder-backend-configmap
  - name: bot-builder-backend-secret-volume
    secret:
      secretName: bot-builder-backend-secret
  - name: file-log-volume
    emptyDir: {}
  - name: promtail-config-volume
    configMap:
      name: bot-builder-backend-promtail-sidecar-configmap
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts:
  - name: bot-builder-backend-configmap-volume
    mountPath: /my/files/mountPath/dir
  - name: bot-builder-backend-secret-volume
    readOnly: true
    mountPath: /opt/app-root/cert
  - name: file-log-volume
    mountPath: /tmp
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

livenessProbe:
  httpGet:
    path: /v1/version/versionNumber
    port: http
    scheme: HTTP
  initialDelaySeconds: 10
  timeoutSeconds: 1
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /v1/version/versionNumber
    port: http
    scheme: HTTP
  initialDelaySeconds: 10
  timeoutSeconds: 1
  periodSeconds: 30
  successThreshold: 1
  failureThreshold: 3

#configs: {}
#secrets: {}
#
#env: {}
enableEnvFromConfigMap: true
enableEnvFromSecret: false

secondaryContainers:
  - name: promtail
    spec:
      image: grafana/promtail:2.9.1
      args:
        - '-config.file=/etc/promtail/promtail.yaml'
        - '-log.level=debug'
        - '-config.expand-env=true'
      env:
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.nodeName
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        - name: DEPLOYMENT_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.labels['app.kubernetes.io/instance']
        - name: LOKI_PUSH_URL
          value: http://loki-gateway/loki/api/v1/push
      imagePullPolicy: IfNotPresent
      volumeMounts:
        - name: promtail-config-volume
          mountPath: /etc/promtail
        - name: file-log-volume
          mountPath: /tmp

    config:
      name: promtail-sidecar
      data:
        promtail.yaml: |
          server:
            http_listen_port: 9080
            grpc_listen_port: 0
            log_level: "debug"
          positions:
            filename: /tmp/positions.yaml
          clients: # Specify target
            - url: ${LOKI_PUSH_URL}
          scrape_configs:
            - job_name: promtail-sidecar-${DEPLOYMENT_NAME}
              pipeline_stages:
                - cri: {}
                - replace:
                    expression: '(?i)"?(?:authorization|flowise-integration-shared-secret)"?\s*:\s*"?([^"\\\\]*)"?'  # (?i) for case-insensitive match 
                    replace: "__masked__"
              static_configs:
                - targets:
                    - localhost
                  labels:
                    app: ${DEPLOYMENT_NAME}
                    job: ${NAMESPACE}/${DEPLOYMENT_NAME}
                    pod: ${POD_NAME}
                    node_name: ${NODE_NAME}
                    namespace: ${NAMESPACE}
                    __path__: /tmp/*.log # Any file .log in the EmptyDir Volume.

configs:
  RATE_LIMIT_API_KEY_DURATION: '1'
  RATE_LIMIT_AUTHENTICATED_DURATION: '1'
  RATE_LIMIT_PUBLIC_DURATION: '1'
  ACCESS_TOKEN_EXPIRY: 15m
  ALLOW_DISPOSABLE_EMAILS: 'true'
  API_KEY_APPROVE_SKIP: 'true'
  API_PATH_PATTERN: /(.*?)/(.*?)/(.*?)
  APP_NAME: HKT GPT AI Bot Builder - UAT
  AWS_S3_ENCRYPTION_KEY_ARN: arn:aws:kms:ap-east-1:************:alias/aws/s3
  AWS_S3_LOG_FILES_BUCKET_PROD: bot-builder-logs-uat-live
  AWS_S3_LOG_FILES_BUCKET_TEST: bot-builder-logs-uat-test
  AWS_S3_PROFILE_PICTURE_BUCKET: gpt-saas-dev
  AWS_S3_REGION: ap-east-1
  AWS_S3_STATIC_FILES_BUCKET_PROD: bot-builder-files-uat-live
  AWS_S3_STATIC_FILES_BUCKET_TEST: bot-builder-files-uat-test
  AWS_SQS_GEN_RES_LIVE_QUEUE_URL: https://sqs.ap-east-1.amazonaws.com/432797229265/imaging-stable-dffision-live
  AWS_SQS_GEN_RES_TEST_QUEUE_URL: https://sqs.ap-east-1.amazonaws.com/432797229265/imaging-stable-dffision-test
  AWS_SQS_LOG_FILES_QUEUE_URL: https://sqs.ap-east-1.amazonaws.com/************/logfile_process_queue_uat
  AWS_SQS_REGION: ap-east-1
  BOT_API_ID: f0db7266-de44-327a-b257-298fd0eff7a4
  BRUTE_FREE_RETRIES: '50'
  BRUTE_LIFETIME: '300000'
  CACHE_CHECK_PERIOD: '1000'
  CACHE_TTL: '600'
  DB_HOST: rds-hkt-bot-builder-uat.cluster-coeyecamxqju.ap-east-1.rds.amazonaws.com
  DB_NAME: botbuilderdb
  DB_PORT: '5432'
  DB_PROVIDER: postgresql
  DB_USER: botbuilder
  DISALLOW_OPEN_CORS: 'false'
  ELASTICSEARCH_AUTH_USERNAME: botbuilder
  ELASTICSEARCH_FAIL_RETRIES: '1'
  ELASTICSEARCH_NODES: https://vpc-os-gbot-uat-api-logs-erj3r2jp4dwzdmlsm265wuet7u.ap-east-1.es.amazonaws.com
  ELASTIC_APM_SECRET_TOKEN:
  ELASTIC_APM_SERVER_URL: http://elastic-apm.gbot-uat:8200
  ELASTIC_APM_SERVICE_NAME: bot-builder-backend
  EMAIL_FROM: <EMAIL>
  EMAIL_HOST: smtp.gmail.com
  EMAIL_NAME: <EMAIL>
  EMAIL_PORT: '465'
  EMAIL_SECURE: 'true'
  FLOWISE_BACKEND_URL: http://localhost:8080
  FRONTEND_URL: https://uat.bot-builder.pccw.com
  GITHUB_CLIENT_ID: github-oauth2-client-id
  GITHUB_CLIENT_SECRET: oauth2-client-secret
  GOOGLE_CLIENT_ID: google-oauth2-client-id
  GOOGLE_CLIENT_SECRET: oauth2-client-secret
  GRAVITEE_ENV_ID: DEFAULT
  GRAVITEE_HOST: http://graviteeio-apim3x-api.gravitee-apim:83
  GRAVITEE_ORG_ID: DEFAULT
  HKT_EMAIL_SUFFIX: pccw.com
  MAXMIND_ACCOUNT_ID: '857445'
  MICROSOFT_CLIENT_ID: microsoft-oauth2-client-id
  MICROSOFT_CLIENT_SECRET: oauth2-client-secret
  NODE_ENV: development
  OPENAI_BACKEND_URL: http://bot-builder-gpt-service/
  PORT: '3001'
  PUBLIC_RATE_LIMIT_MAX: '60'
  PUBLIC_RATE_LIMIT_TIME: '60000'
  PUPPETEER_SKIP_DOWNLOAD: 'true'
  RATE_LIMIT_MAX: '1000'
  RATE_LIMIT_TIME: '60000'
  REDIS_URL: redis://127.0.0.1:6379
  REFRESH_TOKEN_EXPIRY: 10h
  SENTRY_DSN: https://<EMAIL>/****************
  SPEED_LIMIT_COUNT: '500'
  SPEED_LIMIT_DELAY: '100'
  SPEED_LIMIT_TIME: '600000'
  SSO_HKT_HEALTH_CHECK_ENDPOINT: /certs
  SSO_HKT_URL: https://uiauth.hkt.com/auth/realms/HKT_LDAP/protocol/openid-connect
  SSO_HKT_USER_INFO: https://uiauth.hkt.com/auth/realms/HKT_LDAP/protocol/openid-connect/userinfo
  SSO_HKT_USER_INFO_ENDPOINT: /userinfo
  STRIPE_PRODUCT_ID: stripe-product-id
  STRIPE_SECRET_KEY: stripe-test-api-key
  TOKEN_EXPIRY_API_KEY_MAX: '**************'
  TOKEN_EXPIRY_APPROVE_LOCATION: 10m
  TOKEN_EXPIRY_EMAIL_VERIFICATION: 7d
  TOKEN_EXPIRY_LOGIN: 15m
  TOKEN_EXPIRY_PASSWORD_RESET: 1d
  TOKEN_EXPIRY_REFRESH: 30d
  TRACKING_DELETE_OLD_LOGS: 'true'
  TRACKING_DELETE_OLD_LOGS_DAYS: 'true'
  TRACKING_INDEX: bot-builder-logs
  TRACKING_MODE: all
  VERIFICATION_CODE_EXPIRY_DAY: '1'
  WIKIJS_GENKB_CHAT_HOST_CONTEXT: https://api.uat.bot-builder.pccw.com/genkb
  WIKIJS_GUEST_GROUP_ID: '3'
  WIKIJS_HOST: https://genkb.uat.bot-builder.pccw.com
  WIKIJS_TOKEN_ISSUER: urn:wiki.js
  RATE_LIMIT_EMAIL_NOTIFICATION_API_URL: http://bot-builder-notification/v1
  RATE_LIMIT_EXPIRE_TIME: '3600'
  RATE_LIMIT_TIME_OUT: '30000'
  LAST_USED_GROUPS_MAX_NUMBER: '5'
