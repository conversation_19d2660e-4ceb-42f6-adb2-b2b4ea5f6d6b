import { Body, Controller, Headers, HttpCode, Ip, Post, Query } from '@nestjs/common';
import { VerificationType } from '@prisma/client';
import {
  ForgetPasswordDto,
  LoginDto,
  RefreshTokenDto,
  ResetPasswordDto,
} from './auth.dto';
import { TokenResponse } from './auth.interface';
import { AuthService } from './auth.service';
import { Public } from './public.decorator';
import { RateLimit } from '../../decorators/rate-limit.decorator';
import { UsersService } from '../users/users.service';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { ApiTags } from '@nestjs/swagger';

@Controller('auth')
@Public()
@ApiTags('Auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private usersService: UsersService,
  ) {}

  /** Login to an account */
  @Post('login')
  @HttpCode(200)
  @RateLimit(10)
  async login(
    @Body() data: LoginDto,
    @Ip() ip: string,
    @Headers('User-Agent') userAgent: string,
  ): Promise<TokenResponse> {
    return this.authService.login(
      ip,
      userAgent,
      data.email,
      data.loginType,
      data.password,
      data.hktToken,
      data.azureToken,
    );
  }

  /** Login to an account */
  @Post('login-teams')
  @HttpCode(200)
  @RateLimit(10)
  async loginByTeamsBot(
    @Body() data: LoginDto,
    @Ip() ip: string,
    @Headers('User-Agent') userAgent: string,
  ): Promise<TokenResponse> {
    return this.authService.loginByTeamsBot(ip, userAgent, data.email);
  }

  /** 
  @Post('login/test')
  async loginTest(@Ip() ip: string, @Headers('User-Agent') userAgent: string) {
    return this.authService.loginTest(ip, userAgent);
  }
  */

  @Post('forget-password')
  async forgetPassword(@Body() data: ForgetPasswordDto) {
    await this.authService.forgetPassword(data.email);
    return { email: data.email };
  }

  @Post('validate-code')
  async validateCode(@Query('code') activateCode: string) {
    const isValid = await this.usersService.validateActivateCode(
      activateCode,
      VerificationType.RESET_PASSWORD,
    );
    return {
      isValid,
    };
  }

  @Post('reset-password')
  async resetPassword(@Body() data: ResetPasswordDto) {
    const isValid = await this.usersService.validateActivateCode(
      data.token,
      VerificationType.RESET_PASSWORD,
    );
    if (!isValid) {
      throw new ApiException(ErrorCode.INVALID_ACTIVATION_CODE);
    }
    return await this.authService.resetPassword(data);
  }

  /** Get a new access token using a refresh token */
  @Post('refresh')
  @HttpCode(200)
  @RateLimit(1)
  async refresh(@Ip() ip: string, @Body() data: RefreshTokenDto): Promise<TokenResponse> {
    return this.authService.refresh(data.token, data.groupId);
  }

  /** Logout from a session */
  @Post('logout')
  @HttpCode(200)
  @RateLimit(5)
  async logout(@Body('token') refreshToken: string): Promise<{ success: true }> {
    await this.authService.logout(refreshToken);
    return { success: true };
  }

  @Post('verify/email')
  async verifyUserEmail(@Body('userEmail') userEmail: string) {
    const userExist = await this.authService.userEmailExist(userEmail);
    return { userExist };
  }
}
