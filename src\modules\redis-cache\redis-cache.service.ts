import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from 'src/providers/redis/redis.service';

@Injectable()
export class RedisCacheService {
  constructor(private redisService: RedisService) {}
  private readonly logger = new Logger(RedisCacheService.name);

  async deleteCache(key: string) {
    return { message: 'Delete Cache Successfully', count: await this.redisService.clearCache(key) };
  }

  async batchDeleteCache(keyPattern: string) {
    return {
      message: 'Batch Delete Cache Successfully',
      count: await this.redisService.batchClearCache(keyPattern),
    };
  }
}