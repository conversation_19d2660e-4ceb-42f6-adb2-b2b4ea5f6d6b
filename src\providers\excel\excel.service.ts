import { Injectable, Logger } from '@nestjs/common';
import Excel, { Cell, Workbook } from 'exceljs';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { Readable } from 'stream';
import * as tmp from 'tmp';
import { SupportFileExt } from './excel.interface';

@Injectable()
export class ExcelService {
  private readonly logger = new Logger(ExcelService.name);
  constructor() {}

  async downloadExcel(header: string[] = [], data: object[] = [], merges: string[] = []) {
    if (!data.length) {
      throw new ApiException(ErrorCode.INVALID_PROMOTE_FLOW_REQUEST);
    }
    const rows = [];
    rows.push(header);
    data.forEach((doc) => {
      const rowsData = Object.values(header).map((key) => doc[key] ?? '');
      rows.push(rowsData);
    });
    const book = new Workbook();
    const sheet = book.addWorksheet('sheet1');
    sheet.addRows(rows);
    if (merges.length) {
      for (const merge of merges) {
        sheet.mergeCells(merge);
      }
    }
    const file = await new Promise((resolve, reject) => {
      tmp.file(
        {
          discardDescriptor: true,
          perfix: 'excelSheet',
          postfix: '.xlsx',
          mode: parseInt('0600', 8),
        },
        async (err, file) => {
          book.xlsx
            .writeFile(file)
            .then((_: any) => {
              resolve(file);
            })
            .catch((err: Error) => {
              this.logger.error(err);
              throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR);
            });
        },
      );
    });

    return file;
  }

  private getCellValue(row: Excel.Row, cellIndex: number) {
    const cell = row.getCell(cellIndex);
    return cell.value ? cell.value.toString() : '';
  }

  private getCellHeader(row: Excel.Row, headeKeys: string[]) {
    const headerMap = {};
    row.eachCell((cell: Cell, colNumber: number) => {
      if (cell?.value && headeKeys.includes(cell.value.toString())) {
        headerMap[cell.value.toString()] = colNumber;
      }
    });
    return headerMap;
  }

  public async readExcel(fileBuffer: Buffer, headeKeys: string[], fileExt: SupportFileExt) {
    try {
      const workbook = new Excel.Workbook();
      const readable = Readable.from(fileBuffer);
      let worksheet;
      if (fileExt === SupportFileExt.XLSX) {
        const content = await workbook.xlsx.read(readable);
        worksheet = content.getWorksheet();
      } else {
        worksheet = await workbook.csv.read(readable);
      }
      const rowStartIndex = 2;
      const numberOfRows = worksheet.rowCount - 1;
      const rows = worksheet.getRows(rowStartIndex, numberOfRows) ?? [];
      const headerRow = worksheet.getRow(1);
      const headerMap = this.getCellHeader(headerRow, headeKeys) as { [key: string]: number };
      const excelData = rows.map((row: Excel.Row) => {
        const ddd = Object.entries(headerMap).reduce((_excelData, item) => {
          _excelData[item[0]] = this.getCellValue(row, item[1]);
          return _excelData;
        }, {});
        return ddd;
      });
      return excelData;
    } catch (err) {
      this.logger.error({ message: 'readExcel execl error ' }, err);
      throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }
}
