-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "SummaryKeyType" ADD VALUE 'IMAGE_STANDARD_1024_1024';
ALTER TYPE "SummaryKeyType" ADD VALUE 'IMAGE_STANDARD_1024_1792';
ALTER TYPE "SummaryKeyType" ADD VALUE 'IMAGE_STANDARD_1792_1024';
ALTER TYPE "SummaryKeyType" ADD VALUE 'IMAGE_HD_1024_1024';
ALTER TYPE "SummaryKeyType" ADD VALUE 'IMAGE_HD_1024_1792';
ALTER TYPE "SummaryKeyType" ADD VALUE 'IMAGE_HD_1792_1024';
