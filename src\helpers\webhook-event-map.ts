import { Method } from 'axios';
import { UserRequest } from 'src/modules/auth/auth.interface';
type WebhookEventMap = {
  [event: string]: {
    httpMethod: Method;
    generateBody: (userRequest: UserRequest) => any;
  };
};

const webhookEventMap: WebhookEventMap = {
  'resource-generation-callback': {
    httpMethod: 'POST',
    generateBody: (userRequest: UserRequest) => {
      const resourceId = parseInt(userRequest.params['id']);
      const groupId = parseInt(userRequest.params['groupId']);
      return {
        resourceId,
        groupId,
        downloadPaths: userRequest.body.filenames.map(
          (filename: string) => `/groups/${groupId}/ai-resources/${resourceId}/files/${filename}`,
        ),
      };
    },
  },
};

export default webhookEventMap;
