/*
  Warnings:

  - The values [PEDNDING] on the enum `DataPromotionRequestStatus` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "DataPromotionRequestStatus_new" AS ENUM ('PENDING', 'CANCELED', 'APPROVED', 'REJECTED');
ALTER TABLE "DataPromotionRequest" ALTER COLUMN "status" TYPE "DataPromotionRequestStatus_new" USING ("status"::text::"DataPromotionRequestStatus_new");
ALTER TYPE "DataPromotionRequestStatus" RENAME TO "DataPromotionRequestStatus_old";
ALTER TYPE "DataPromotionRequestStatus_new" RENAME TO "DataPromotionRequestStatus";
DROP TYPE "DataPromotionRequestStatus_old";
COMMIT;
