import { Module } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { S3Module } from '../../providers/s3/s3.module';
import { LLMBackendModule } from '../../providers/llm-backend/llm-backend.module';
import { DocumentTagService } from './document-tag.service';
import { LabelsModule } from '../labels/labels.module';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
    S3Module,
    LLMBackendModule,
    LabelsModule,
    FeatureFlagModule,
  ],
  providers: [DocumentTagService],
  exports: [DocumentTagService],
})
export class DocumentTagModule {}
