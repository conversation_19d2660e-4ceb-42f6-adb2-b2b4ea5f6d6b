DO $$

	DECLARE summaryRecord RECORD;
			botId BIGINT;
			botEnv TEXT;
            engineLimit BIGINT;
            engineSlug TEXT;
			enginePlanId INTEGER;
            engineRuleId INTEGER;
            engineQuotaValueId INTEGER;
			enginePermissionId INTEGER;
			engineResourceId INTEGER;
            featureFlagRecord RECORD;
	BEGIN
		RAISE NOTICE 'data patching 5M plan'; 
		FOR summaryRecord IN 
			SELECT * FROM (
				SELECT s."groupId", s."engineSlug",  sum(s."value") as "usageSum" FROM "Summary" s 
				WHERE s."startDate" >= '2024-07-01' AND s."endDate" < '2024-08-01' AND key = 'TOTAL_COMPLETION_TOKENS_TOTAL' GROUP BY s."groupId", s."engineSlug"
			) as sq  LEFT JOIN 
			(SELECT "targetValue", fo."metaData" as "metaData" FROM "FeatureFlagOverride" fo JOIN "FeatureFlag" f ON fo."featureFlagId" = f.id WHERE f."key" = 'BOT.CONFIG_BOT_TOKEN_MONTHLY_LIMIT' AND fo."targetType" = 'BOT' ) as fq	
			ON sq."groupId" = fq."targetValue"::INTEGER LEFT JOIN "Group" g ON sq."groupId" = g.id  WHERE "usageSum" >= 1000000 AND "usageSum" < 5000000 AND "engineSlug" IS NOT NULL AND "targetValue" IS NULL
		LOOP

	    botId:= summaryRecord."groupId";
		botEnv:= summaryRecord."env";
		engineSlug:= summaryRecord."engineSlug";
		SELECT id INTO enginePlanId FROM "Plan" 
		WHERE "planKey" = 'llm-engine-'|| engineSlug || '-plan-1-' || LOWER(botEnv);
		INSERT INTO "PlanSubscription" 
		("planId", "subscriberEntityType", "subscriberEntityId", "subscribeStartDate", "subscribeEndDate") VALUES (enginePlanId, 'BOT', botId, NOW(), null);
		
		END LOOP;
--------------------------------------------------
	    RAISE NOTICE 'data patching custom plan from summary'; 
		FOR summaryRecord IN 
			SELECT * FROM (SELECT s."groupId", s."engineSlug", sum(s."value") as "usageSum" FROM "Summary" s  WHERE s."startDate" >= '2024-07-01' AND s."endDate" < '2024-08-01' AND key = 'TOTAL_COMPLETION_TOKENS_TOTAL'  GROUP BY s."groupId", s."engineSlug") as sq LEFT JOIN 
			(SELECT "targetValue", fo."metaData" as "metaData" FROM "FeatureFlagOverride" fo JOIN "FeatureFlag" f ON fo."featureFlagId" = f.id WHERE f."key" = 'BOT.CONFIG_BOT_TOKEN_MONTHLY_LIMIT' AND fo."targetType" = 'BOT' ) as fq	
			ON sq."groupId" = fq."targetValue"::INTEGER LEFT JOIN "Group" g ON sq."groupId" = g.id  WHERE "usageSum" >= 5000000 AND "engineSlug" IS NOT NULL AND "targetValue" IS NULL
		LOOP
		botId:= summaryRecord."groupId";
		engineSlug:= summaryRecord."engineSlug";
		botEnv:= summaryRecord."env";
        engineLimit:= summaryRecord."usageSum" + 1000000;
        SELECT id INTO engineResourceId from "Resource" WHERE "resourceKey" = 'llm-engine-' || engineSlug;
		INSERT INTO "Plan" 
				("resourceId", "planKey", "planName", "description", "entityId", "entityType", "groupEnv")
				VALUES
				(engineResourceId, 'llm-engine-' || engineSlug || '-plan-' || botId, 
				engineLimit || ' Token Max Per Month', 'To allow maximum ' || engineSlug || ' ' || engineLimit || ' token usage per month', botId, 'BOT', botEnv::"Environment") 
				RETURNING id INTO enginePlanId;
        
        SELECT id INTO engineRuleId from "ResourceQuotaRule" WHERE "ruleKey" = 'group-{groupId}:llm-engine-'|| engineSlug || '-quota';

        INSERT INTO "ResourceQuotaValue" 
        ("quotaKey", "value", "ruleId") 
        VALUES
        ('group-{groupId}:llm-engine-'|| engineSlug || '-quota-' || botId, engineLimit, engineRuleId)
        RETURNING id INTO engineQuotaValueId;
				
        INSERT INTO "PlanQuota" ("planId", "quotaValueId") VALUES (enginePlanId, engineQuotaValueId);
	    SELECT id INTO enginePermissionId from "Permission"  WHERE "permissionKey" = 'group-{groupId}:llm-engine-'|| engineSlug;
		INSERT INTO "PlanPermission" ("planId", "permissionId") VALUES (enginePlanId, enginePermissionId);
        INSERT INTO "PlanSubscription" 
		("planId", "subscriberEntityType", "subscriberEntityId", "subscribeStartDate", "subscribeEndDate") VALUES (enginePlanId, 'BOT', botId, NOW(), null);    
        END LOOP;
--------------------------------------------------
	    RAISE NOTICE 'data patching custom plan from featureflag'; 
        FOR featureFlagRecord IN 
			SELECT "targetValue", fo."metaData" as "metaData", g."env" FROM "FeatureFlagOverride" fo JOIN "FeatureFlag" f ON fo."featureFlagId" = f.id LEFT JOIN         "Group" g ON fo."targetValue"::INTEGER = g.id WHERE f."key" = 'BOT.CONFIG_BOT_TOKEN_MONTHLY_LIMIT' AND fo."targetType" = 'BOT' 
		LOOP
		botId:= featureFlagRecord."targetValue"::BIGINT;

     	engineLimit:= (featureFlagRecord."metaData"->>'value')::BIGINT;
		IF engineLimit > 2147483647 THEN
			engineLimit:= 2147483647;
		END IF;
		botEnv:= featureFlagRecord."env";
            FOR engineSlug IN SELECT Distinct slug FROM "LlmEngine" LOOP
                SELECT id INTO engineResourceId from "Resource" WHERE "resourceKey" = 'llm-engine-' || engineSlug;

                INSERT INTO "Plan" 
				("resourceId", "planKey", "planName", "description", "entityId", "entityType", "groupEnv")
				VALUES
				(engineResourceId, 'llm-engine-' || engineSlug || '-plan-' || botId, 
				engineLimit || 'Token Max Per Month', 'To allow maximum ' || engineSlug || ' ' || engineLimit || ' token usage per month', botId, 'BOT', botEnv:: "Environment") 
				RETURNING id INTO enginePlanId;

                SELECT id INTO engineRuleId from "ResourceQuotaRule" WHERE "ruleKey" = 'group-{groupId}:llm-engine-'|| engineSlug || '-quota';
				
                INSERT INTO "ResourceQuotaValue" 
				("quotaKey", "value", "ruleId") 
				VALUES
				('group-{groupId}:llm-engine-'|| engineSlug || '-quota-' || botId, engineLimit, engineRuleId)
				RETURNING id INTO engineQuotaValueId;
				
				INSERT INTO "PlanQuota" ("planId", "quotaValueId") VALUES (enginePlanId, engineQuotaValueId);
				
				SELECT id INTO enginePermissionId from "Permission"  WHERE "permissionKey" = 'group-{groupId}:llm-engine-'|| engineSlug;
				INSERT INTO "PlanPermission" ("planId", "permissionId") VALUES (enginePlanId, enginePermissionId);
                
				INSERT INTO "PlanSubscription" 
                ("planId", "subscriberEntityType", "subscriberEntityId", "subscribeStartDate", "subscribeEndDate") VALUES (enginePlanId, 'BOT', botId, NOW(),null);
            END LOOP;
        END LOOP;
	END;
$$;