## COMMIT_PRACTICES

### Guidelines for VERSION_CONTROL

#### JIRA Ticket Number

- The commit message must prefix by the JIRA ticket number e.g. JIRA-1234
- Find the JIRA ticket number from the branch name by default, you can use `git branch --show-current`
- Ask User to provide the JIRA ticket number if you can't find it
- Missing JIRA ticket number is allowed only the user reject to provide it
- You are allow to get the JIRA ticket information if any tools or MCP are available.

#### CONVENTIONAL_COMMITS

- Follow the format: type(scope): description for all commit messages
- Use consistent types (feat, fix, docs, style, refactor, test, chore) across the project
- Include issue references in commit messages to link changes to requirements
- Use breaking change footer (!: or BREAKING CHANGE:) to clearly mark incompatible changes
- Configure commitlint to automatically enforce conventional commit format

#### Details of Commit

- You should read the stages diff before create the commit using `git diff --staged`
- Description the changes in point form
- Show the commit message and git commit command to user for preview
- Ask user for confirmation before execute the command