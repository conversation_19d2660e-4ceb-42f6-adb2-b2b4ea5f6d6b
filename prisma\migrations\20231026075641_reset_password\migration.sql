/*
  Warnings:

  - A unique constraint covering the columns `[userId,type]` on the table `KYCVerification` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterEnum
ALTER TYPE "VerificationType" ADD VALUE 'RESET_PASSWORD';

-- AlterTable
ALTER TABLE "KYCVerification" ALTER COLUMN "requestorId" DROP NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "KYCVerification.userId_type_unique" ON "KYCVerification"("userId", "type");
