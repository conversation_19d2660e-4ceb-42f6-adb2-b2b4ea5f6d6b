import { ApiHideProperty, ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { <PERSON>pi<PERSON>ey, ChatSessionType, User } from '@prisma/client';
import { Expose, Type } from 'class-transformer';
import {
  IsString,
  MinLength,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
  IsArray,
  IsEnum,
  IsInt,
  IsBoolean,
  ArrayMinSize,
  IsDefined,
} from 'class-validator';
import { ToolUsage, Usage } from 'src/providers/flow-backend/flow-backend.interface';
import {
  ChannelType,
  HistoryMessage,
  SecurityScanType,
} from '../llm-models/dto/chat-llm-model.dto';
import { InputScannersResultDTO } from '../bot-security/dto/input-scanners-result.DTO';
import { ScannnersOutputResultDTO } from 'src/providers/llm-backend/llm-backend.interface';
export interface GetSpecificFlowResponse {
  id: number;
  flowUuid: string;
  createdAt: Date;
  updatedAt: Date;
}

export class CreateGroupFlowRequest {
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  name: string;
}

export interface CreateGroupFlowResponse {
  id: number;
  groupId: number;
  flowUuid: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

export class UpdateGroupFlowRequest {
  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  flowData?: string;
}

class FLowOverrides {
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    type: 'boolean',
    required: false,
    description: 'Enable streaming response (SSE)',
  })
  stream: boolean;

  @IsOptional()
  @ApiPropertyOptional({
    enum: SecurityScanType,
    required: false,
    description: 'Enable prompt PII check',
    default: SecurityScanType.OFF,
  })
  security_scan: SecurityScanType;
}
export class FlowChatRequest {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => HistoryMessage)
  @ArrayMinSize(1)
  @IsDefined({ each: true })
  @ApiProperty({
    description: 'chat flow history',
    required: false,
    type: [HistoryMessage],
    example: [
      {
        role: 'user',
        content: 'please give me a list of air con that the HP is equal to or less than 1',
      },
      {
        role: 'assistant',
        content:
          'Here is a list of air conditioners with horsepower equal to or less than 1:\n\n- 開利 - 1.5匹全直流變頻式分體冷氣機 [冷暖型] 42QHG012DS: 1.5\n- 開利 - 1匹 R32 淨冷 變頻式 窗口機 CHK09EAVX: 1\n- 大金 - 3/4匹420藍光FTKA變頻淨冷掛牆分體式冷氣機(B系列) FTKA20B: 0.75',
      },
      {
        role: 'user',
        content: 'please give me the installation price',
      },
    ],
  })
  history: HistoryMessage[];

  @IsOptional()
  @ApiProperty({
    description:
      'if you use the Connect API. can past you need the variables into the custom Param Config ',
    required: false,
    type: [Map],
    example: { userId: '1' },
  })
  appendVariables?: Map<string, string>;

  // chat session is only available for playground, not supposed to be called from external chat API
  @ApiHideProperty()
  @IsEnum(ChatSessionType)
  @IsOptional()
  chatSessionType?: ChatSessionType;

  // chat session is only available for playground, not supposed to be called from external chat API
  @ApiHideProperty()
  @IsInt()
  @IsOptional()
  chatSessionId?: number;

  @ApiProperty({ type: [FLowOverrides] })
  overrides: FLowOverrides;

  // This param is only needed by Teams bot.
  // When this API is called without specifying channel, it will be determined as API_KEY with presence of api-key.
  // Otherwise, it will be determined as PLAYGROUND.
  @ApiHideProperty()
  @IsEnum(ChannelType)
  @IsOptional()
  channel?: ChannelType;

  @ApiHideProperty()
  @IsOptional()
  inputScannersResult?: InputScannersResultDTO;
}

export type CallChain = {
  id: string;
  name: string;
  input?: any;
  output?: string | undefined;
  nestedComponents?: CallChain;
  traceId?: string;
};

export class FlowChatResponse {
  @Expose()
  @ApiProperty({ description: 'chat flow answer' })
  answer: string;

  @Expose()
  @ApiProperty({ description: 'chat flow answer message' })
  message: HistoryMessage;

  @Expose()
  @ApiProperty({
    type: [Usage],
    description: 'chat flow token usage',
  })
  usage: Usage[];

  @ApiProperty({
    type: [ToolUsage],
    description: 'chat flow tool usage',
  })
  @Expose()
  toolUsage: ToolUsage[];

  @Expose()
  callChain?: CallChain;
}

export class InternalFlowChatResponse extends FlowChatResponse {
  @Expose()
  @ApiProperty({
    type: ScannnersOutputResultDTO,
    required: false,
    description: 'Security Scan Output Result',
  })
  output_scanners_result?: ScannnersOutputResultDTO;

  @Expose()
  @ApiProperty({
    type: InputScannersResultDTO,
    required: false,
    description: 'Security Scan Input Result',
  })
  input_scanners_result?: InputScannersResultDTO;

  @Expose()
  @ApiProperty({
    description: `The value is set to 'true' if the input passed all security checks, and 'false' otherwise..`,
    type: Boolean,
  })
  input_scanners_result_is_valid?: boolean;

  @Expose()
  @ApiProperty({
    description: `The value is set to 'true' if the output passed all security checks, and 'false' otherwise.`,
    type: Boolean,
  })
  output_scanners_result_is_valid?: boolean;
}

export enum ChatType {
  INTERNAL,
  EXTERNAL,
}
export interface UserIdentification {
  user?: User;
  apiKey?: ApiKey;
}
export interface LoadMethodOption {
  label: string;
  name: string;
  description?: string;
}
