import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { OrderByPipe } from 'src/pipes/order-by.pipe';
import { WherePipe } from 'src/pipes/where.pipe';
import { UserRequest } from '../auth/auth.interface';
import { Scopes } from '../auth/scope.decorator';
import { CreateTestCaseDto } from './dto/create-auto-test.dto';
import { ExcelHeaderKey } from './dto/test-case.dto';
import { UpdateTestCaseDto } from './dto/update-auto-test.dto';
import { TestCaseService } from './test-case.service';
import { ExcelService } from 'src/providers/excel/excel.service';

@Controller('groups/:groupId/test-case')
@ApiTags('testCase')
export class TestCaseController {
  private readonly allowedFileExt = ['csv', 'xlsx'];
  private readonly maxUploadFileSize = 25000000;
  constructor(
    private readonly testCaseService: TestCaseService,
    private readonly excelService: ExcelService,
  ) {}

  @Post('')
  @Scopes('group-{groupId}:write-test-case', 'group-*:write-test-case')
  async create(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() createTestCaseDto: CreateTestCaseDto,
    @Req() request: UserRequest,
  ) {
    return await this.testCaseService.create(groupId, createTestCaseDto, request);
  }

  @Post('/run')
  @Scopes('group-{groupId}:write-test-case', 'group-*:write-test-case')
  async createAndRun(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() createTestCaseDto: CreateTestCaseDto,
    @Req() request: UserRequest,
  ) {
    return await this.testCaseService.createAndRun(groupId, createTestCaseDto, request);
  }

  @Get()
  @Scopes('group-{groupId}:read-test-case', 'group-*:read-test-case')
  async findAll(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    const listData = await this.testCaseService.findAll(+groupId, take, skip, where, orderBy);
    return listData;
  }

  @Get(':id/execution')
  @Scopes('group-{groupId}:read-test-case', 'group-*:read-test-case')
  async findExecution(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) testCaseId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    const listData = await this.testCaseService.findExecutionById(
      +groupId,
      testCaseId,
      take,
      skip,
      where,
      orderBy,
    );
    return listData;
  }

  @Get('/execution/:executionId')
  @Scopes('group-{groupId}:read-test-case', 'group-*:read-test-case')
  async findExecutionOne(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('executionId', ParseIntPipe) executionId: number,
  ) {
    return await this.testCaseService.findExecutionOne(executionId, groupId);
  }

  @Get(':id/execution/result/:executionId')
  @Scopes('group-{groupId}:read-test-case', 'group-*:read-test-case')
  async findExecutionResult(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) testCaseId: number,
    @Param('executionId', ParseIntPipe) executionId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    const listData = await this.testCaseService.findExecutionResultById(
      +groupId,
      executionId,
      take,
      skip,
      where,
      orderBy,
    );
    return listData;
  }

  @Get(':id')
  @Scopes('group-{groupId}:read-test-case', 'group-*:read-test-case')
  async findOne(@Param('groupId', ParseIntPipe) groupId: number, @Param('id') id: string) {
    return await this.testCaseService.findOne(+id, groupId);
  }

  @Patch(':id')
  @Scopes('group-{groupId}:write-test-case', 'group-*:write-test-case')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() updateTestCaseDto: UpdateTestCaseDto,
    @Req() request: UserRequest,
  ) {
    return await this.testCaseService.update(id, groupId, updateTestCaseDto, request);
  }

  @Patch(':id/run')
  @Scopes('group-{groupId}:write-test-case', 'group-*:write-test-case')
  async updateAndRun(
    @Param('id') id: string,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() updateTestCaseDto: UpdateTestCaseDto,
    @Req() request: UserRequest,
  ) {
    return await this.testCaseService.updateAndRun(+id, groupId, updateTestCaseDto, request);
  }

  @Delete(':id')
  @Scopes('group-{groupId}:write-test-case', 'group-*:write-test-case')
  async remove(@Param('groupId', ParseIntPipe) groupId: number, @Param('id') id: string) {
    return await this.testCaseService.remove(+id, groupId);
  }

  @Get('file/:executionId/result')
  @Scopes('group-{groupId}:read-test-case', 'group-*:read-test-case')
  async downloadTestCaseExecutionId(
    @Res() res: Response,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('executionId') executionId: string,
  ) {
    const data = await this.testCaseService.downloadTestCaseExecutionResult(groupId, +executionId);
    res.download(`${data}`);
  }

  @Get('file/:testCaseId/prompt')
  @Scopes('group-{groupId}:read-test-case', 'group-*:read-test-case')
  async downloadTestCasePrompt(
    @Res() res: Response,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('testCaseId') testCaseId: string,
  ) {
    const data = await this.testCaseService.downloadTestCasePrompt(groupId, +testCaseId);
    res.download(`${data}`);
  }

  @Get('file/:testCaseId/executions')
  @Scopes('group-{groupId}:read-test-case', 'group-*:read-test-case')
  async downloadTestCaseExecution(
    @Res() res: Response,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('testCaseId') testCaseId: string,
    @Query('from') from?: string,
    @Query('to') to?: string,
  ) {
    const data = await this.testCaseService.downloadTestCaseExecutions(
      groupId,
      +testCaseId,
      from,
      to,
    );
    res.download(`${data}`);
  }

  @Get('file/testCases')
  @Scopes('group-{groupId}:read-test-case', 'group-*:read-test-case')
  async downloadTestCase(@Res() res: Response, @Param('groupId', ParseIntPipe) groupId: number) {
    const data = await this.testCaseService.downloadTestCaste(groupId);
    res.download(`${data}`);
  }

  @Post('/file/check')
  @Scopes('group-{groupId}:write-test-case', 'group-*:write-test-case')
  @UseInterceptors(FilesInterceptor('file'))
  async uploadFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Req() request: UserRequest,
    @UploadedFiles() files: Array<Express.Multer.File>
  ) {
    // file format checking
    if (!(files && files.length)) {
      throw new ApiException(ErrorCode.API_RESOURCE_UPLOAD_FIlE_NOT_FOUND);
    }
    if (files[0].size > this.maxUploadFileSize) {
      throw new ApiException(ErrorCode.FILE_TOO_LARGE);
    }
    const file = files[0];
    const fileExtension = file.originalname.split('.').pop();
    if (this.allowedFileExt.indexOf(fileExtension) === -1) {
      throw new ApiException(ErrorCode.INVALID_UPLOAD_FILE_FORMAT);
    }
    const fileContent = await this.testCaseService.checkExcelContentAndReturn(file, fileExtension);
    return fileContent;
  }

  @Get('/file/sample')
  @Scopes('group-{groupId}:read-test-case', 'group-*:read-test-case')
  async downloadFileSample(@Res() res: Response) {
    const data = [
      {
        question: 'your question',
        validationPrompt: '',
      },
      {
        question: 'your question',
        validationPrompt: '',
      },
      {
        question: 'your question',
        validationPrompt: '',
      },
    ];
    const result = await this.excelService.downloadExcel(Object.values(ExcelHeaderKey), data);
    res.download(`${result}`);
  }
}
