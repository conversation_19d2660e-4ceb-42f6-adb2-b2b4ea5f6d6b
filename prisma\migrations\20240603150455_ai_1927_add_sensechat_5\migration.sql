-- This is an empty migration.
update "LlmEngine" set "sequence" = 1 where "name"='ChatGPT 3.5 Turbo 16K';
update "LlmEngine" set "sequence" = 2 where "name"='ChatGPT 4 32K';
update "LlmEngine" set "sequence" = 3 where "name"='Chat Bison 32K@Latest';
update "LlmEngine" set "sequence" = 4 where "name"='Codechat Bison@Latest';
update "LlmEngine" set "sequence" = 5 where "name"='ChatGPT 4 128K@0125';
update "LlmEngine" set "sequence" = 6 where "name"='Gemini 1.5 Pro@001';
update "LlmEngine" set "sequence" = 7 where "name"='Gemini 1.5 Flash@001';
update "LlmEngine" set "sequence" = 8 where "name"='SenseChat 5 Cantonese';
update "LlmEngine" set "sequence" = 9 where "name"='SenseChat 5';
update "LlmEngine" set "sequence" = 10 where "name"='SenseChat 128K';
update "LlmEngine" set "sequence" = 11 where "name"='SenseNova XL';
update "LlmEngine" set "sequence" = 12 where "name"='SenseNova XS';
update "LlmEngine" set "sequence" = 13 where "name"='SenseNova YUE';
update "LlmEngine" set "sequence" = 14 where "name"='ChatGPT 4';
update "LlmEngine" set "sequence" = 15 where "name"='ChatGPT 4 Turbo';
update "LlmEngine" set "sequence" = 16 where "name"='Chat Bison@Latest';
update "LlmEngine" set "sequence" = 17 where "name"='Chat Bison@001';
update "LlmEngine" set "sequence" = 18 where "name"='Codechat Bison@001';
update "LlmEngine" set "sequence" = 19 where "name"='ChatGPT 3.5 Turbo@0613';
update "LlmEngine" set "sequence" = 20 where "name"='ChatGPT 3.5 Turbo@1106';
update "LlmEngine" set "sequence" = 21 where "name"='ChatGPT 3.5 Turbo';
update "LlmEngine" set "sequence" = 22 where "name"='Stable Diffusion@Text to Image';
update "LlmEngine" set "sequence" = 23 where "name"='MetaGPT';

update "LlmEngine" set config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0.01,\"max\":2,\"default\":0.8,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"What sampling temperature to use, between 0.01 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":50,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":0.01,\"max\":0.99,\"default\":0.7,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":131072,\"default\":2000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"repetition_penalty\":{\"label\":\"Repetition Penalty\",\"min\":0.05,\"max\":2,\"default\":1.05,\"type\":\"number\",\"input\":\"slider\",\"step\":0.01,\"description\":\"Number between 0.05 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"}}"'::jsonb where slug='nova-sensechat-5-cantonese';