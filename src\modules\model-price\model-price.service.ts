import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  FileEntityType,
  FileHistoryStatus,
  FileType,
  ModelPrice,
  ModelPriceProcess,
  ModelPriceProcessStatus,
  ModelPriceType,
  Prisma,
} from '@prisma/client';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { Configuration } from 'src/config/configuration.interface';
import moment from 'moment';
import { SummaryService } from '../summary/summary.service';
import { UserRequest } from '../auth/auth.interface';
import { TokensService } from 'src/providers/tokens/tokens.service';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { BulkCreatePriceItem } from './model-price.dto';
import { QueueService } from 'src/providers/queue/queue.service';

@Injectable()
export class ModelPriceService {
  private logger = new Logger(ModelPriceService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<Configuration>,
    private readonly sqsService: QueueService,
    private readonly summaryService: SummaryService,
    private readonly tokenService: TokensService,
  ) {}

  async getPriceByYearAndMonth(year: number, month: number, enabled: boolean) {
    this.logger.log(`Getting price for year: ${year} and month: ${month}`);
    const llmEngines = await this.prisma.llmEngine.findMany({
      select: { name: true, slug: true, platform: true },
      where: { isActive: enabled },
    });
    const currentDate = moment(`${year}-${month}`, 'YYYY-MM').subtract(1, 'month');
    const currentYear = currentDate.year();
    const currentMonth = currentDate.month() + 1;
    const currentPrices = await this.prisma.modelPriceUnit.findMany({
      select: {
        id: true,
        slug: true,
        llmEngineSlug: true,
        modelPriceSource: true,
        metadata: true,
        modelPrice: {
          select: {
            id: true,
            year: true,
            month: true,
            modelPriceUnitId: true,
            modelPriceType: true,
            value: true,
            updatedAt: true,
            updatedBy: true,
          },
          where: { year: currentYear, month: currentMonth },
        },
      },
      orderBy: { id: 'asc' },
    });
    const price = await this.prisma.modelPriceUnit.findMany({
      select: {
        id: true,
        slug: true,
        llmEngineSlug: true,
        order: true,
        modelPriceSource: true,
        metadata: true,
        modelPriceEvents: {
          select: {
            id: true,
            creator: true,
            createdAt: true,
          },
          where: { createdBy: { not: 0 } },
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
        modelPrice: {
          select: {
            id: true,
            year: true,
            month: true,
            modelPriceUnitId: true,
            modelPriceType: true,
            value: true,
            updatedAt: true,
            updatedBy: true,
            metadata: true,
          },
          where: { year, month },
        },
      },
      orderBy: { id: 'asc' },
    });
    const priceTable = price.map((p) => {
      const llmEngine = llmEngines.find((e) => e.slug === p.llmEngineSlug);
      const currentPrice = currentPrices
        .find((cp) => cp.slug === p.slug)
        ?.modelPrice.find((p) => p.modelPriceType === ModelPriceType.PRICE);
      let modelName: string;
      let platform: string;
      if (llmEngine) {
        modelName = llmEngine.name;
        platform = llmEngine.platform;
      } else if (p.llmEngineSlug === 'azure-speech') {
        modelName = 'Azure Speech';
        platform = 'AZURE';
      } else if (p.llmEngineSlug === 'azure-text-embedding-ada-002') {
        modelName = 'Text Embedding Ada 002';
        platform = 'AZURE';
      }
      const costSuggestion = p.modelPrice.find(
        (p) => p.modelPriceType === ModelPriceType.COST_SUGGESTION,
      );
      const cost = p.modelPrice.find((p) => p.modelPriceType === ModelPriceType.COST);
      const price = p.modelPrice.find((p) => p.modelPriceType === ModelPriceType.PRICE);

      const lastManualUpdateDate =
        p.modelPriceEvents.length > 0 &&
        [costSuggestion, cost, price].some((mp) => mp?.updatedBy !== 0)
          ? p.modelPriceEvents[0]?.createdAt
          : null;

      type PriceMetadata = { overrideAutoSync?: boolean };

      return {
        id: p.id,
        slug: p.slug,
        llmEngineSlug: p.llmEngineSlug,
        order: p.order,
        platform,
        modelName,
        unitName: (p.metadata as { unitName?: string })?.unitName,
        modelPriceSource: p.modelPriceSource,
        lastManualUpdateDate,
        [ModelPriceType.COST_SUGGESTION]: costSuggestion
          ? { ...costSuggestion, metadata: undefined }
          : null,
        [ModelPriceType.COST]: cost
          ? {
              ...cost,
              metadata: undefined,
              overrideAutoSync: (cost.metadata as PriceMetadata)?.overrideAutoSync ?? false,
              isManual: cost?.updatedBy !== 0,
            }
          : null,
        [ModelPriceType.PRICE]: price
          ? {
              ...price,
              metadata: undefined,
              overrideAutoSync: (price.metadata as PriceMetadata)?.overrideAutoSync ?? false,
              isManual: price?.updatedBy !== 0,
            }
          : null,
        CURRENT_PRICE: currentPrice ?? null,
      };
    });

    const getLastAutoUpdateDate = (type: ModelPriceType | 'CURRENT_PRICE') =>
      priceTable.reduce<Date | null>((date, cur) => {
        if (
          cur[type]?.updatedBy === 0 && // is Auto Update
          (date === null || moment(cur[type].updatedAt).isAfter(date)) // is latest date
        )
          return cur[type].updatedAt;
        return date;
      }, null);

    const lastCostSuggestionAutoUpdateDate = getLastAutoUpdateDate(ModelPriceType.COST_SUGGESTION);
    const lastCostAutoUpdateDate = getLastAutoUpdateDate(ModelPriceType.COST);
    const lastPriceAutoUpdateDate = getLastAutoUpdateDate(ModelPriceType.PRICE);
    const lastCurrentPriceAutoUpdateDate = getLastAutoUpdateDate('CURRENT_PRICE');
    const p = priceTable.filter((p) =>
      llmEngines.some(
        (e) =>
          e.slug === p.llmEngineSlug ||
          (enabled &&
            (p.llmEngineSlug === 'azure-speech' ||
              p.llmEngineSlug === 'azure-text-embedding-ada-002')),
      ),
    );

    // Sort by platform and model name and unit name
    p.sort((a, b) => {
      if (a.platform < b.platform) return -1;
      if (a.platform > b.platform) return 1;
      if (a.order < b.order) return -1;
      if (a.order > b.order) return 1;
      if (a.modelName < b.modelName) return -1;
      if (a.modelName > b.modelName) return 1;
      if (a.unitName < b.unitName) return -1;
      if (a.unitName > b.unitName) return 1;
      return 0;
    });

    return {
      count: p.length,
      lastCostSuggestionAutoUpdateDate,
      lastCostAutoUpdateDate,
      lastPriceAutoUpdateDate,
      lastCurrentPriceAutoUpdateDate,
      price: p,
    };
  }

  async getRateCard(year: number, month: number, enabled: boolean) {
    this.logger.log(`Getting ratecard: ${year} and month: ${month}`);
    const currentDate = moment(`${year}-${month}`, 'YYYY-MM').subtract(1, 'month');
    const currentYear = currentDate.year();
    const currentMonth = currentDate.month() + 1;
    const llmEngines = await this.prisma.llmEngine.findMany({
      select: { name: true, slug: true, platform: true },
      where: { isActive: enabled },
    });
    const price = await this.prisma.modelPriceUnit.findMany({
      select: {
        id: true,
        slug: true,
        llmEngineSlug: true,
        order: true,
        modelPriceSource: true,
        metadata: true,
        modelPrice: {
          select: {
            id: true,
            year: true,
            month: true,
            modelPriceType: true,
            value: true,
            updatedAt: true,
            updatedBy: true,
          },
          where: { year: currentYear, month: currentMonth },
        },
      },
      orderBy: { id: 'asc' },
    });
    const nextMonth = moment(`${year}-${month}`, 'YYYY-MM');
    const pricesOfNextMonth = await this.prisma.modelPriceUnit.findMany({
      select: {
        id: true,
        slug: true,
        llmEngineSlug: true,
        order: true,
        modelPriceSource: true,
        metadata: true,
        modelPrice: {
          select: {
            id: true,
            year: true,
            month: true,
            modelPriceType: true,
            value: true,
            updatedAt: true,
            updatedBy: true,
          },
          where: { year: nextMonth.year(), month: nextMonth.month() + 1 },
        },
      },
      orderBy: { id: 'asc' },
    });
    const priceTable = price.map((p) => {
      const llmEngine = llmEngines.find((e) => e.slug === p.llmEngineSlug);
      let modelName: string;
      let platform: string;
      if (llmEngine) {
        modelName = llmEngine.name;
        platform = llmEngine.platform;
      } else if (p.llmEngineSlug === 'azure-speech') {
        modelName = 'Azure Speech';
        platform = 'AZURE';
      } else if (p.llmEngineSlug === 'azure-text-embedding-ada-002') {
        modelName = 'Text Embedding Ada 002';
        platform = 'AZURE';
      }
      const currentPrice = p.modelPrice.find((p) => p.modelPriceType === ModelPriceType.PRICE);
      const backupPrice = pricesOfNextMonth
        .find((np) => np.slug === p.slug)
        ?.modelPrice.find((np) => np.modelPriceType === ModelPriceType.PRICE);
      const price = currentPrice ? currentPrice : backupPrice ? backupPrice : null;

      return {
        id: p.id,
        slug: p.slug,
        llmEngineSlug: p.llmEngineSlug,
        order: p.order,
        platform,
        modelName,
        unitName: (p.metadata as { unitName?: string })?.unitName,
        modelPriceSource: p.modelPriceSource,
        [ModelPriceType.PRICE]: price,
      };
    });

    const p = priceTable.filter((p) =>
      llmEngines.some(
        (e) =>
          e.slug === p.llmEngineSlug ||
          (enabled &&
            (p.llmEngineSlug === 'azure-speech' ||
              p.llmEngineSlug === 'azure-text-embedding-ada-002')),
      ),
    );

    // Sort by platform and model name and unit name
    p.sort((a, b) => {
      if (a.platform < b.platform) return -1;
      if (a.platform > b.platform) return 1;
      if (a.order < b.order) return -1;
      if (a.order > b.order) return 1;
      if (a.modelName < b.modelName) return -1;
      if (a.modelName > b.modelName) return 1;
      if (a.unitName < b.unitName) return -1;
      if (a.unitName > b.unitName) return 1;
      return 0;
    });

    return { count: p.length, price: p };
  }

  async getPriceByYearAndMonthInternal(
    year: number,
    month: number,
    opts: { modelPriceType?: ModelPriceType },
  ) {
    this.logger.log(`Getting price for year: ${year} and month: ${month}`);
    const price = await this.prisma.modelPriceUnit.findMany({
      select: {
        id: true,
        slug: true,
        llmEngineSlug: true,
        modelPriceSource: true,
        metadata: true,
        modelPrice: {
          select: { id: true, year: true, month: true, modelPriceType: true, value: true },
          where: { year, month, ...opts },
        },
      },
      orderBy: { id: 'asc' },
    });
    const priceTable = price.map((p) => ({
      id: p.id,
      slug: p.slug,
      llmEngineSlug: p.llmEngineSlug,
      modelPriceSource: p.modelPriceSource,
      metadata: p.metadata,
      ...p.modelPrice.reduce(
        (acc, curr) => ({ ...acc, [curr.modelPriceType]: curr }),
        Object.keys(ModelPriceType).reduce((acc, curr) => ({ ...acc, [curr]: null }), {}),
      ),
    }));

    return priceTable;
  }

  async createModelPrice(
    trx: Prisma.TransactionClient,
    req: UserRequest,
    data: Required<
      Pick<ModelPrice, 'year' | 'month' | 'modelPriceUnitId' | 'modelPriceType' | 'value'>
    > & { overrideAutoSync?: boolean },
  ) {
    this.logger.debug(`Creating price for year: ${data.year} and month: ${data.month}`);
    const now = new Date();
    try {
      const overrideAutoSync = data.overrideAutoSync ?? false;
      delete data.overrideAutoSync;
      await trx.modelPriceEvent.create({
        data: {
          modelPriceEventType: 'CREATE',
          modelPriceUnitId: data.modelPriceUnitId,
          modelPriceType: data.modelPriceType,
          value: data.value,
          metadata: { overrideAutoSync },
          createdAt: now,
          createdBy: req.user?.id ?? 0,
        },
      });

      return await trx.modelPrice.create({
        data: {
          ...data,
          metadata: { overrideAutoSync },
          createdAt: now,
          createdBy: req.user?.id ?? 0,
          updatedAt: now,
          updatedBy: req.user?.id ?? 0,
        },
      });
    } catch (err) {
      this.logger.error(err);
      throw new ApiException(ErrorCode.MODEL_PRICE_CREATE_FAILED, { error: err });
    }
  }

  async updateModelPrice(
    trx: Prisma.TransactionClient,
    req: UserRequest,
    modelPrice: ModelPrice,
    data: Pick<Prisma.ModelPriceUncheckedCreateInput, 'value'> & {
      overrideAutoSync?: boolean;
    },
  ) {
    this.logger.debug(`Updating price for year: ${modelPrice.year} and month: ${modelPrice.month}`);
    const now = new Date();
    try {
      await trx.modelPriceEvent.create({
        data: {
          modelPriceEventType: 'UPDATE',
          modelPriceUnitId: modelPrice.modelPriceUnitId,
          modelPriceType: modelPrice.modelPriceType,
          value: data.value,
          metadata: { overrideAutoSync: data.overrideAutoSync ?? false },
          createdAt: now,
          createdBy: req.user?.id ?? 0,
        },
      });

      return await trx.modelPrice.update({
        data: {
          value: data.value,
          metadata: { overrideAutoSync: data.overrideAutoSync ?? false },
          updatedAt: now,
          updatedBy: req.user?.id ?? 0,
        },
        where: { id: modelPrice.id },
      });
    } catch (err) {
      this.logger.error(err);
      throw new ApiException(ErrorCode.MODEL_PRICE_CREATE_FAILED, { error: err });
    }
  }

  async bulkCreatePrice(req: UserRequest, inputs: BulkCreatePriceItem[]): Promise<ModelPrice[]> {
    const now = new Date();
    this.logger.log(`Bulk creating price for ${inputs.length} records, now: ${now.getTime()}`);

    const modelPrices: ModelPrice[] = [];
    await this.prisma.$transaction(async (trx) => {
      for (const price of inputs) {
        const p = await trx.modelPrice.findUnique({
          where: {
            modelPriceIdentifier: {
              year: price.year,
              month: price.month,
              modelPriceUnitId: price.modelPriceUnitId,
              modelPriceType: price.modelPriceType,
            },
          },
        });
        if (p) modelPrices.push(await this.updateModelPrice(trx, req, p, price));
        else modelPrices.push(await this.createModelPrice(trx, req, price));
      }
    });

    return modelPrices;
  }

  async bulkCreateProcess(
    req: UserRequest,
    input: {
      mode: 'all' | 'partial';
      year: number;
      month: number;
      groupIds?: number[];
    },
  ) {
    const now = new Date();
    this.logger.log(
      `Creating process for mode: ${input.mode}, year: ${input.year}, month: ${
        input.month
      }, now: ${now.getTime()}`,
    );
    const sqsConfig = this.configService.get('queue', { infer: true });
    const date = moment(`${input.year}-${input.month}`, 'YYYY-MM').utcOffset(8);
    const dateFrom = date.clone().startOf('month').toISOString();
    const dateTo = date.clone().endOf('month').toISOString();

    let ids: number[] = [];
    if (input.mode === 'all') {
      const groups = await this.prisma.group.findMany({
        select: { id: true },
        where: {
          groupType: { not: 'FLOW' },
          createdAt: { lte: dateTo },
          isDeprecated: undefined, // Override the default value of isDeprecated in PrismaExtendClient
        },
      });
      ids = groups.map((g) => g.id);
    } else {
      ids = input.groupIds;
    }

    const createdProcesses = await this.prisma.$transaction(async (trx) => {
      const createdProcesses = await trx.modelPriceProcess.createManyAndReturn({
        select: { id: true, group: { select: { id: true, groupType: true } } },
        data: ids.map(
          (id): Prisma.ModelPriceProcessCreateManyInput => ({
            year: input.year,
            month: input.month,
            groupId: id,
            status: 'PROCESSING',
            metadata: {},
            createdBy: req.user?.id ?? 0,
            updatedBy: req.user?.id ?? 0,
          }),
        ),
      });

      return createdProcesses;
    });

    const size = 10;
    const chunkedProcesses = createdProcesses.reduce(
      (acc, _, i) => {
        if (i % size === 0) acc.push(createdProcesses.slice(i, i + size));
        return acc;
      },
      [] as (typeof createdProcesses)[],
    );
    for (const [index, chunk] of Object.entries(chunkedProcesses)) {
      const i = parseInt(index, 10);
      const second = Math.floor(i / 10);
      const delaySeconds = this.randomBetween(second, second + 3);
      await this.sqsService.sendMessageBatch({
        QueueUrl: sqsConfig.fileQueueUrl,
        Entries: chunk.map((p) => ({
          Id: `${now.getTime()}-${p.id.toString()}`,
          DelaySeconds: delaySeconds,
          MessageBody: JSON.stringify({
            fileId: p.id,
            fileType: 'MONTHLY_PRICE_RECORD_PER_GROUP',
            entityId: p.group.id,
            entityType: p.group.groupType,
            dateFrom,
            dateTo,
            createdAt: now.toISOString(),
          }),
        })),
      });
    }

    return createdProcesses.length;
  }

  async patchProcessStatus(
    req: UserRequest,
    id: number,
    data: Pick<ModelPriceProcess, 'year' | 'month' | 'status'> & {
      metadata?: ModelPriceProcess['metadata'];
    },
  ) {
    this.logger.log(`Patching process status for id: ${id}`);
    const process = await this.prisma.modelPriceProcess.findUnique({ where: { id } });
    if (!process) {
      return await this.prisma.modelPriceProcess.create({
        data: {
          year: data.year,
          month: data.month,
          groupId: id,
          status: data.status,
          metadata: data.metadata ?? {},
          createdBy: req.user?.id ?? 0,
          updatedBy: req.user?.id ?? 0,
        },
      });
    }
    return this.prisma.modelPriceProcess.update({
      data: { ...data, updatedBy: req.user?.id ?? 0 },
      where: { id },
    });
  }

  async generateAndDownloadReport(
    req: UserRequest,
    year: number,
    month: number,
    selectedFields?: string[],
    forceRegenerate?: boolean,
  ) {
    const now = moment();
    const sqsConfig = this.configService.get('queue', { infer: true });

    try {
      const date = moment(`${year}-${month}`, 'YYYY-MM').utcOffset(8);
      const dateFrom = date.clone().startOf('month');
      const dateTo = date.clone().endOf('month');

      const lastUpdatedAtRows = await this.prisma.$queryRaw<Pick<ModelPriceProcess, 'updatedAt'>[]>`
SELECT MAX("updatedAt") "updatedAt"
FROM (SELECT
  "id", "year", "month", "groupId", "status", "createdAt", "updatedAt",
  ROW_NUMBER() OVER(PARTITION BY "year", "month", "groupId" ORDER BY "createdAt" DESC) rn
  FROM "ModelPriceProcess" mpp) lastUpdatedAt
WHERE lastUpdatedAt."rn" = 1`;

      const lastUpdatedAt = lastUpdatedAtRows.length ? lastUpdatedAtRows[0].updatedAt : null;

      // Return FileHistory if report is already generated
      const generatedReport = await this.summaryService.checkIsGeneratedReport(
        req,
        dateFrom.toDate(),
        dateTo.toDate(),
        FileType.MONTHLY_PRICE_REPORT,
      );

      if (!forceRegenerate && generatedReport) {
        // if have both lastUpdatedAt and generatedReport.createdAt, compare them
        if (lastUpdatedAt && generatedReport.createdAt) {
          // if no billing is updated after the report is generated, return the generated report
          if (moment(lastUpdatedAt).isBefore(moment(generatedReport.createdAt))) {
            this.logger.log(
              `Last updated at: ${lastUpdatedAt}. No billing is updated after ${generatedReport.createdAt}. Return the generated report.`,
            );
            return generatedReport;
          } // Else keep going to generate the report

          // if no date to compare, return the generated report
        } else {
          this.logger.log(
            `Last updated at: ${lastUpdatedAt}. Generated report createdAt: ${generatedReport?.createdAt}. Return the generated report.`,
          );
          return generatedReport;
        }
      }

      // Otherwise, create FileHistory and generate report
      const newFileHistory: Prisma.FileHistoryUncheckedCreateInput = {
        fileType: FileType.MONTHLY_PRICE_REPORT,
        fileId: await this.tokenService.generateRandomString(10),
        createdAt: now.toISOString(),
        updatedAt: now.toISOString(),
        dateFrom: dateFrom.toISOString(),
        dateTo: dateTo.toISOString(),
        requesterId: req?.user?.id ?? 0,
        status: FileHistoryStatus.PROCESSING,
      };

      await this.sqsService.sendMessage({
        QueueUrl: sqsConfig.fileQueueUrl,
        MessageBody: JSON.stringify({ ...newFileHistory, selectedFields }),
      });
      return await this.prisma.fileHistory.create({ data: newFileHistory });
    } catch (err) {
      this.logger.error(err);
      if (err instanceof ApiException) throw err;
      throw new ApiException(ErrorCode.LIST_FILE_DOWNLOAD_FAILED, { error: err });
    }
  }

  async generateAndDownloadReportByGroupId(
    req: UserRequest,
    year: number,
    month: number,
    groupId: number,
    selectedFields?: string[],
    forceRegenerate?: boolean,
  ) {
    const now = moment();
    const sqsConfig = this.configService.get('queue', { infer: true });

    try {
      const date = moment(`${year}-${month}`, 'YYYY-MM').utcOffset(8);
      const dateFrom = date.clone().startOf('month');
      const dateTo = date.clone().endOf('month');

      const group = await this.prisma.group.findUnique({ where: { id: groupId } });
      if (group === null) {
        throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
      }

      const lastUpdatedAtRows = await this.prisma.$queryRaw<Pick<ModelPriceProcess, 'updatedAt'>[]>`
SELECT MAX("updatedAt") "updatedAt"
FROM (SELECT
  "id", "year", "month", "groupId", "status", "createdAt", "updatedAt",
  ROW_NUMBER() OVER(PARTITION BY "year", "month", "groupId" ORDER BY "createdAt" DESC) rn
  FROM "ModelPriceProcess" mpp) lastUpdatedAt
WHERE lastUpdatedAt."rn" = 1 AND "groupId" = ${group.id}`;

      const lastUpdatedAt = lastUpdatedAtRows.length ? lastUpdatedAtRows[0].updatedAt : null;

      const generatedReport = await this.summaryService.checkIsGeneratedReport(
        req,
        dateFrom.toDate(),
        dateTo.toDate(),
        FileType.MONTHLY_PRICE_REPORT_PER_GROUP,
        group.groupType,
        group.id,
      );

      if (!forceRegenerate && generatedReport) {
        // if have both lastUpdatedAt and generatedReport.createdAt, compare them
        if (lastUpdatedAt && generatedReport.createdAt) {
          // if no billing is updated after the report is generated, return the generated report
          if (moment(lastUpdatedAt).isBefore(moment(generatedReport.createdAt))) {
            this.logger.log(
              `Last updated at: ${lastUpdatedAt}. No billing is updated after ${generatedReport.createdAt}. Return the generated report.`,
            );
            return generatedReport;
          } // Else keep going to generate the report

          // if no date to compare, return the generated report
        } else {
          this.logger.log(
            `Last updated at: ${lastUpdatedAt}. Generated report createdAt: ${generatedReport?.createdAt}. Return the generated report.`,
          );
          return generatedReport;
        }
      }

      // Otherwise, create FileHistory and generate report
      const newFileHistory: Prisma.FileHistoryUncheckedCreateInput = {
        entityId: group.id,
        entityType: group.groupType as FileEntityType,
        fileType: FileType.MONTHLY_PRICE_REPORT_PER_GROUP,
        fileId: await this.tokenService.generateRandomString(10),
        createdAt: now.toISOString(),
        updatedAt: now.toISOString(),
        dateFrom: dateFrom.toISOString(),
        dateTo: dateTo.toISOString(),
        requesterId: req?.user?.id ?? 0,
        status: FileHistoryStatus.PROCESSING,
      };

      await this.sqsService.sendMessage({
        QueueUrl: sqsConfig.fileQueueUrl,
        MessageBody: JSON.stringify({ ...newFileHistory, selectedFields }),
      });
      return await this.prisma.fileHistory.create({ data: newFileHistory });
    } catch (err) {
      this.logger.error(err);
      if (err instanceof ApiException) throw err;
      throw new ApiException(ErrorCode.LIST_FILE_DOWNLOAD_FAILED, { error: err });
    }
  }

  async getGroupPriceReports(req: UserRequest, groupId: number) {
    this.logger.log(`Getting group price reports, user: ${req?.user?.id}, groupId: ${groupId}`);
    const date = moment();
    const yearMonthRange = this.getYearMonthRange(
      date.clone().subtract(24, 'month'),
      date.clone().subtract(1, 'month'),
    ).reverse();
    const processes = await this.prisma.$queryRaw<
      Pick<
        ModelPriceProcess,
        'id' | 'year' | 'month' | 'groupId' | 'status' | 'createdAt' | 'updatedAt'
      >[]
    >`SELECT
  "id", "year", "month", "groupId", "status", "createdAt", "updatedAt"
FROM
(SELECT
  "id", "year", "month", "groupId", "status", "createdAt", "updatedAt",
  ROW_NUMBER() OVER(PARTITION BY "year", "month",  "groupId" ORDER BY "createdAt" DESC) rn
FROM "ModelPriceProcess" mpp
WHERE "groupId" = ${groupId}) lastStatus
WHERE lastStatus."rn" = 1
ORDER BY "year" DESC, "month" DESC
LIMIT ${24}`;

    const reports: {
      year: string;
      month: string;
      price: number | null;
      status: ModelPriceProcessStatus | 'NOT_AVAILABLE';
    }[] = [];
    for (const [year, month] of yearMonthRange) {
      const found = processes.find((p) => year === p.year && month === p.month);
      if (found) {
        const date = moment(`${found.year}-${found.month}`, 'YYYY-MM').utcOffset(8);
        const dateFrom = date.clone().startOf('month');
        const dateTo = date.clone().endOf('month');
        const sum = await this.prisma.modelPriceSummary.aggregate({
          _sum: { value: true },
          where: {
            startDate: { gte: dateFrom.toISOString() },
            endDate: { lte: dateTo.toISOString() },
            key: 'MODEL_PRICE',
            groupId: groupId,
          },
        });

        let price = Number(sum._sum.value);
        let status: ModelPriceProcessStatus | 'NOT_AVAILABLE' = found.status;

        const nonCompletedCount = await this.prisma.$queryRaw<{ cnt: number }[]>`
SELECT
  count(*) cnt
FROM
(SELECT
  "id", "status",
  ROW_NUMBER() OVER(PARTITION BY "year", "month",  "groupId" ORDER BY "createdAt" DESC) rn
FROM "ModelPriceProcess" mpp
WHERE "year" = ${year} AND "month" = ${month}
) lastStatus
WHERE lastStatus."rn" = 1 AND "status" <> 'COMPLETED'`;
        // Only show completed status if all records in that year-month are completed
        if (status === 'COMPLETED' && nonCompletedCount.length && nonCompletedCount[0].cnt > 0) {
          price = null;
          status = 'PROCESSING';
        }

        // Don't show the record if $0
        if (!(status === 'COMPLETED' && price === 0)) {
          reports.push({ year, month, price, status });
        }
      } else {
        // Don't show the record if there is no record in that year-month
        // reports.push({ year, month, price: null, status: 'NOT_AVAILABLE' });
      }
    }

    return { reports, count: reports.length };
  }

  async getSystemPriceReports(req: UserRequest) {
    this.logger.log(`Getting system price reports, user: ${req?.user?.id}`);
    const date = moment();
    const yearMonthRange = this.getYearMonthRange(
      date.clone().subtract(24, 'month'),
      date.clone().subtract(1, 'month'),
    ).reverse();

    const reports: {
      year: string;
      month: string;
      price: number | null;
      status: ModelPriceProcessStatus | 'NOT_AVAILABLE';
    }[] = [];
    for (const [year, month] of yearMonthRange) {
      const rows = await this.prisma.$queryRaw<{ status: ModelPriceProcessStatus; cnt: number }[]>`
SELECT
  status, count(*) cnt
FROM
(SELECT
  "id", "status",
  ROW_NUMBER() OVER(PARTITION BY "year", "month",  "groupId" ORDER BY "createdAt" DESC) rn
FROM "ModelPriceProcess" mpp
WHERE "year" = ${year} AND "month" = ${month}
) lastStatus
WHERE lastStatus."rn" = 1
GROUP BY status`;
      if (rows.length === 0) {
        // Don't show the record if there is no record in that year-month
        // reports.push({ year, month, price: null, status: 'NOT_AVAILABLE' });
      } else if (rows.some((row) => row.status === 'PROCESSING')) {
        reports.push({ year, month, price: null, status: 'PROCESSING' });
      } else if (rows.some((row) => row.status === 'ERROR')) {
        reports.push({ year, month, price: null, status: 'ERROR' });
      } else if (rows.length === 1 && rows[0].status === 'COMPLETED') {
        const date = moment(`${year}-${month}`, 'YYYY-MM').utcOffset(8);
        const dateFrom = date.clone().startOf('month');
        const dateTo = date.clone().endOf('month');
        const sum = await this.prisma.modelPriceSummary.aggregate({
          _sum: { value: true },
          where: {
            startDate: { gte: dateFrom.toISOString() },
            endDate: { lte: dateTo.toISOString() },
            key: 'MODEL_PRICE',
          },
        });
        reports.push({
          year,
          month,
          price: Number(sum._sum.value),
          status: 'COMPLETED',
        });
      }
    }

    return { reports, count: reports.length };
  }

  async getModelPriceEvents(id: number, take: number, skip: number) {
    return this.prisma.modelPriceEvent.findMany({
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            ccc: true,
            department: true,
            businessUnit: true,
          },
        },
      },
      where: { modelPriceUnitId: id, createdAt: { lte: new Date() } },
      take,
      skip,
      orderBy: { createdAt: 'desc' },
    });
  }

  async countModelPriceEvents(id: number) {
    return this.prisma.modelPriceEvent.count({
      where: { modelPriceUnitId: id, createdAt: { lte: new Date() } },
    });
  }

  // Utility

  private getYearMonthRange(startDate: moment.Moment, endDate: moment.Moment) {
    const months = [];
    for (let m = startDate.clone(); m.isSameOrBefore(endDate); m.add(1, 'months')) {
      months.push([m.year(), m.month() + 1]);
    }

    return months;
  }

  private randomBetween(min: number, max: number) {
    return Math.floor(Math.random() * (max - min) + min);
  }
}
