import { ApiResourceType } from '@prisma/client';
import { IsBoolean, IsEnum, IsOptional, IsString, IsUrl } from 'class-validator';
import { ApiResourceService } from '../api-resource.service';
export class UpdateApiResourceDto {
  @IsString()
  @IsUrl(ApiResourceService.IS_URL_OPTIONS)
  hostUrl: string;

  @IsEnum(ApiResourceType)
  apiResourceType: ApiResourceType;

  @IsOptional()
  customParam?: string;

  @IsOptional()
  @IsBoolean()
  enable: boolean;
}
