import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ElasticSearchModule } from '../../providers/elasticsearch/elasticsearch.module';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { TokensModule } from '../../providers/tokens/tokens.module';
import { ApiKeyGroupController } from './api-keys-group.controller';
import { ApiKeysService } from './api-keys.service';
import { GraviteeModule } from '../../providers/gravitee/gravitee.module';
import { PermissionsService } from '../permissions/permissions.service';
import { GroupsModule } from '../groups/groups.module';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';
import { RedisModule } from 'src/providers/redis/redis.module';

@Module({
  imports: [
    PrismaModule,
    TokensModule,
    ConfigModule,
    ElasticSearchModule,
    GraviteeModule,
    FeatureFlagModule,
    forwardRef(() => GroupsModule),
    RedisModule,
  ],
  controllers: [ApiKeyGroupController],
  providers: [ApiKeysService, PermissionsService],
  exports: [ApiKeysService],
})
export class ApiKeysModule {}
