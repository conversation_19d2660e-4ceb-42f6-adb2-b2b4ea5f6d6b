import { ArgumentMetadata, BadRequestException, Injectable, PipeTransform } from '@nestjs/common';

@Injectable()
export class EnumPipe implements PipeTransform<any> {
  private readonly _enum: string[];

  constructor(_enum: any) {
    this._enum = Object.keys(_enum).filter((v) => isNaN(Number(v)));
  }

  transform(value: any, metadata: ArgumentMetadata) {
    if (this._enum.includes(value)) {
      return value;
    }
    throw new BadRequestException(`The ${metadata.data} invalid input`);
  }
}
