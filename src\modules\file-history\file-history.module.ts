import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { FileHistoryService } from './file-history.service';
import { S3Module } from 'src/providers/s3/s3.module';
import { ConfigModule } from '@nestjs/config';
import { FileHistoryController } from './file-history.controller';
import { MailService } from 'src/providers/mail/mail.service';

@Module({
  imports: [PrismaModule, S3Module, ConfigModule],
  controllers: [FileHistoryController],
  providers: [FileHistoryService, MailService],
  exports: [FileHistoryService],
})
export class FileHistoryModule {}
