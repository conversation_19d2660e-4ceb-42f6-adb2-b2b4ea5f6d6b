/*
  Warnings:

  - You are about to drop the `LogFileHistory` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "FileHistoryStatus" AS ENUM ('PROCESSING', 'COMPLETED', 'EXPIRED', 'ERROR');

-- C<PERSON><PERSON>num
CREATE TYPE "FileType" AS ENUM ('SUMMARY', 'BOT_SUMMARY', 'USER_SUMMARY', 'QUERY_LOG');

-- CreateEnum
CREATE TYPE "FileEntityType" AS ENUM ('BOT', 'FLOW', 'USER');

-- DropTable
DROP TABLE "LogFileHistory";

-- DropEnum
DROP TYPE "LogFileStatus";

-- CreateTable
CREATE TABLE "FileHistory" (
    "id" SERIAL NOT NULL,
    "fileId" TEXT NOT NULL,
    "requesterId" INTEGER NOT NULL,
    "fileType" "FileType" NOT NULL,
    "entityType" "FileEntityType",
    "entityId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "s3FilePath" VARCHAR(255),
    "status" "FileHistoryStatus" DEFAULT 'PROCESSING',
    "errorMsg" VARCHAR(255),
    "dateFrom" TIMESTAMP(3),
    "dateTo" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FileHistory_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "FileHistory_fileId_key" ON "FileHistory"("fileId");

-- CreateIndex
CREATE INDEX "fileHistory.search_index" ON "FileHistory"("fileId");
CREATE INDEX "fileHistory.list_search_index" ON "FileHistory"("fileType", "entityType", "entityId");
