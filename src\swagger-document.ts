import { NestExpressApplication } from '@nestjs/platform-express';
import {
  ApiTags,
  DocumentBuilder,
  OpenAPIObject,
  SwaggerDocumentOptions,
  SwaggerModule,
} from '@nestjs/swagger';
import basicAuth from 'express-basic-auth';
import { OperationObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { AppModule } from './app.module';
import { NestjsRedoxModule } from 'nestjs-redox';

export const externalTag = 'External';

export const ExternalApi = () => ApiTags(externalTag);

const getSchemaNames = (componentString: string) => {
  const schemaNames: string[] = [];
  const schemaMatchResult = componentString.match(/{"\$ref":"#\/components\/schemas\/(.*?)"}/gm);
  if (schemaMatchResult && schemaMatchResult.length > 0) {
    for (const schema of schemaMatchResult) {
      schemaNames.push(schema.match(/{"\$ref":"#\/components\/schemas\/(.*?)"}/)[1] as string);
    }
  }
  return schemaNames;
};

const getComponentDeepSchemaNames = (
  schemaNames: string[],
  originalSchemaNames: string[],
  doc: OpenAPIObject,
) => {
  const _schemaNames: string[] = [];
  for (const name of schemaNames) {
    const schemasStr = JSON.stringify(doc.components.schemas[name]);
    const _schemaName = getSchemaNames(schemasStr);
    _schemaNames.push(..._schemaName);
    originalSchemaNames.push(..._schemaName);
  }
  if (_schemaNames.length > 0) {
    getComponentDeepSchemaNames(_schemaNames, originalSchemaNames, doc);
  }
};

export const filterInternalRoutes = (doc: OpenAPIObject) => {
  const schemaNames = [];
  const publicDoc = structuredClone(doc);
  Object.entries(publicDoc.paths).map(([, path]) => {
    Object.entries(path).forEach(([k, operation]) => {
      const op = operation as OperationObject;
      if (!op.tags?.includes(externalTag)) {
        delete path[k];
      } else {
        if (path[k].tags.length >= 2) {
          path[k].tags = path[k].tags.filter((item) => item !== externalTag);
        }
        const pathString = JSON.stringify(path[k]);
        schemaNames.push(...getSchemaNames(pathString));
      }
    });
  });
  if (schemaNames.length > 0) {
    const schemas = publicDoc.components.schemas;
    getComponentDeepSchemaNames(schemaNames, schemaNames, publicDoc);
    publicDoc.components.schemas = Object.entries(schemas)
      .filter(([k]) => schemaNames.includes(k))
      .reduce((schemas, [k, schema]) => {
        schemas[k] = schema;
        return schemas;
      }, {});
  }
  return publicDoc;
};

export const initializeSwaggerDoc = (app: NestExpressApplication) => {
  const contextPath = process.env['CONTEXT_PATH'] || 'v1';
  app.setGlobalPrefix(contextPath);
  const internal = new DocumentBuilder()
    .setTitle('Bot Builder API')
    .setDescription('Draft version')
    .setVersion('1.0')
    .addServer(process.env['BASE_URL'], process.env['BACKEND_ENV'])
    .addApiKey(
      {
        type: 'apiKey',
        name: 'x-api-key',
        in: 'header',
      },
      'x-api-key',
    )
    .addBearerAuth(
      {
        type: 'http',
      },
      'bearer-auth',
    )
    .setBasePath(contextPath)
    .build();

  const external = new DocumentBuilder()
    .setTitle('Bot Builder API')
    .setDescription('Draft version')
    .setVersion('1.0')
    .addServer(process.env['BASE_URL'], process.env['BACKEND_ENV'])
    .addApiKey(
      {
        type: 'apiKey',
        name: 'x-api-key',
        in: 'header',
      },
      'x-api-key',
    )
    .setBasePath(contextPath)
    .build();

  const options: SwaggerDocumentOptions = {
    include: [AppModule],
    deepScanRoutes: true,
    operationIdFactory: (controllerKey: string, methodKey: string) => methodKey,
  };

  const document = SwaggerModule.createDocument(app, internal, options);
  const externalDocument = SwaggerModule.createDocument(app, external, options);
  const externalDoc = filterInternalRoutes(externalDocument);

  app.use(
    `/${contextPath}/open-api/internal`,
    basicAuth({
      challenge: true,
      users: {
        [process.env['SWAGGER_ADMIN']]: process.env['SWAGGER_ADMIN_PASSWORD'],
      },
    }),
  );

  app.use(
    `/${contextPath}/open-api`,
    basicAuth({
      challenge: true,
      users: {
        [process.env['SWAGGER_ADMIN']]: process.env['SWAGGER_ADMIN_PASSWORD'],
        [process.env['SWAGGER_USER']]: process.env['SWAGGER_USER_PASSWORD'],
      },
    }),
  );

  app.use(
    `/${contextPath}/redoc`,
    basicAuth({
      challenge: true,
      users: {
        [process.env['SWAGGER_ADMIN']]: process.env['SWAGGER_ADMIN_PASSWORD'],
        [process.env['SWAGGER_USER']]: process.env['SWAGGER_USER_PASSWORD'],
      },
    }),
  );
  SwaggerModule.setup(`${contextPath}/open-api/internal`, app, document);

  SwaggerModule.setup(`${contextPath}/open-api`, app, externalDoc, {
    swaggerOptions: {
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
    },
  });

  NestjsRedoxModule.setup(
    `${contextPath}/redoc`,
    app,
    externalDoc,
    { standalone: true },
    { requiredPropsFirst: true },
  );
};
