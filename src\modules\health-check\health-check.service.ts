import { Injectable, ServiceUnavailableException } from '@nestjs/common';
import { AxiosError, AxiosResponse } from 'axios';
import { GraviteeService } from 'src/providers/gravitee/gravitee.service';
import { LLMBackendService } from 'src/providers/llm-backend/llm-backend.service';
import { SsoService } from 'src/providers/sso/sso.service';
import { HealthCheckResponse, HealthCheckStatus } from './interface';

@Injectable()
export class HealthCheckService {
  constructor(
    private readonly ssoService: SsoService,
    private readonly graviteeService: GraviteeService,
    private readonly llmBackendService: LLMBackendService,
  ) {}

  private async SimpleServiceHealthCheck(
    axiosResponsePromise: Promise<AxiosResponse<any>>,
    showResponseData?: boolean,
  ): Promise<HealthCheckResponse> {
    return axiosResponsePromise
      .then((response) => {
        return this.healthyResponse(response, showResponseData);
      })
      .catch((error: AxiosError) => {
        throw this.UnhealthyException(error, showResponseData);
      });
  }

  private healthyResponse(
    response: AxiosResponse,
    showResponseData: boolean = false,
  ): HealthCheckResponse {
    return {
      status: HealthCheckStatus.OK,
      serverTime: new Date(),
      service: {
        healthCheckUrl: response.config.url,
        responseStatus: response.status,
        responseData: showResponseData ? response.data : undefined,
      },
    };
  }

  private UnhealthyException(error: AxiosError, showResponseData: boolean = false) {
    return new ServiceUnavailableException({
      status: HealthCheckStatus.UNAVAILABLE,
      serverTime: new Date(),
      service: {
        healthCheckUrl: error.config.url,
        responseStatus: error.response?.status,
        responseData: showResponseData ? error.response?.data : undefined,
      },
    });
  }

  async ssoHealthCheck(): Promise<HealthCheckResponse> {
    return this.SimpleServiceHealthCheck(this.ssoService.healthCheck(), true);
  }

  async graviteeManagementHealthCheck(): Promise<HealthCheckResponse> {
    return this.SimpleServiceHealthCheck(this.graviteeService.healthCheck());
  }

  async llmBackendHealthCheck(): Promise<HealthCheckResponse> {
    return this.SimpleServiceHealthCheck(this.llmBackendService.healthCheck());
  }
}
