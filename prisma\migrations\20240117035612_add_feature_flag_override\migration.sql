-- CreateEnum
CREATE TYPE "FeatureFlagTargetType" AS ENUM ('ENV', 'BOT', 'FLOW');

-- AlterTable
ALTER TABLE "FeatureFlag" ADD COLUMN     "updatedByUserId" INTEGER;

-- CreateTable
CREATE TABLE "FeatureFlagOverride" (
    "id" SERIAL NOT NULL,
    "value" TEXT NOT NULL,
    "targetType" "FeatureFlagTargetType" NOT NULL,
    "targetValue" TEXT NOT NULL,
    "level" INTEGER NOT NULL,
    "featureFlagId" INTEGER NOT NULL,
    "createdByUserId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "updatedByUserId" INTEGER NOT NULL,

    CONSTRAINT "FeatureFlagOverride_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "FeatureFlagOverride_targetType_targetValue_index" ON "FeatureFlagOverride"("targetType", "targetValue");

-- CreateIndex
CREATE UNIQUE INDEX "FeatureFlagOverride_targetType_targetValue_featureFlagId_key" ON "FeatureFlagOverride"("targetType", "targetValue", "featureFlagId");
