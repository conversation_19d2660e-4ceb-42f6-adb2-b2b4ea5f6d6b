-- Custom SQL: remove system level download permission
BEGIN;

DO $$
DECLARE
  permission_id_to_delete integer;

BEGIN

SELECT id INTO permission_id_to_delete from public."Permission" 
  WHERE "Permission"."permissionType" = 'SYSTEM' 
  AND"permissionKey"='group-*:download-llm-model-files-*';

DELETE FROM public."RolePermission" WHERE "permissionId"=permission_id_to_delete;
DELETE FROM public."PermissionGroupSetting" WHERE "permissionId"=permission_id_to_delete;
DELETE FROM public."Permission" WHERE id=permission_id_to_delete;

END; $$;

COMMIT;

-- remove unused user permissions
DELETE FROM public."Permission" WHERE "permissionKey" IN (
 'user-{userId}:write-api-key-*',
 'user-{userId}:read-api-key-*',
 'user-{userId}:delete-api-key-*',
 'user-{userId}:read-api-key-logs-*',
 'user-{userId}:read-approved-subnet-*',
 'user-{userId}:delete-approved-subnet-*',
 'user-{userId}:delete-mfa-*',
 'user-{userId}:write-mfa-regenerate',
 'user-{userId}:write-mfa-totp',
 'user-{userId}:write-mfa-sms',
 'user-{userId}:write-mfa-email'
);

