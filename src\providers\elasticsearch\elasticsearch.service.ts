import { Client } from '@elastic/elasticsearch';
import type { Index, Search } from '@elastic/elasticsearch/api/requestParams';
import type { TransportRequestOptions } from '@elastic/elasticsearch/lib/Transport';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import PQueue from 'p-queue';
import pRetry from 'p-retry';
import { Configuration } from '../../config/configuration.interface';
import { readFileSync } from 'fs';
import { ChatRecordMapping } from './elasticsearch.interface';
import { ChannelType } from 'src/modules/llm-models/dto/chat-llm-model.dto';

import HttpAmazonESConnector from 'http-aws-es';



@Injectable()
export class ElasticSearchService {
  private logger = new Logger(ElasticSearchService.name);
  private queue = new PQueue({ concurrency: 1 });
  client?: Client;

  constructor(private configService: ConfigService) {
    const config = this.configService.get<Configuration['elasticSearch']>('elasticSearch');
    if (config.aws?.accessKeyId) {
      const connector = new HttpAmazonESConnector({
      credentials: {
        accessKeyId: config.aws.accessKeyId,
        secretAccessKey: config.aws.secretAccessKey
      },
      region: config.aws.region
    });
    
    this.client = new Client({
      nodes: config.nodes,
      Connection: connector.Connection
    });

    } else if (config.nodes && config.ca) {
      this.client = new Client({
        auth: config.auth,
        nodes: config.nodes,
        ssl: {
          ca: readFileSync(config.ca),
          rejectUnauthorized: false,
        },
      });
    } else if (config.nodes) {
      this.client = new Client({
        auth: config.auth,
        nodes: config.nodes,
      });
    } else this.logger.warn('ElasticSearch tracking is not enabled');
  }

  logChatRecord(index: string, record: Record<string, any>) {
    if (this.client)
      this.queue
        .add(() =>
          pRetry(
            async () => {
              const {
                botId,
                flowId = 0,
                feature = null,
                usage = null,
                engine = '',
                channel = ChannelType.PLAYGROUND,
                requesterId = 0,
              } = record;
              const chatUsageRecord = {
                botId,
                flowId,
                feature,
                usage,
                engine,
                channel,
                requesterId,
              };
              this.logger.log(`[Chat Usage Record]: ${JSON.stringify(chatUsageRecord)}`);
              const exists = await this.client.indices.exists({ index });
              if (exists.statusCode === 404) {
                await this.client.indices.create({ index, body: ChatRecordMapping });
              }
              await this.client.index({ index, body: record });
            },
            {
              retries: this.configService.get<number>('elasticSearch.retries') ?? 3,
              onFailedAttempt: (error) => {
                this.logger.error(
                  `Indexing record failed, retrying (${error.retriesLeft} attempts left) the failed store record `,
                  error.name,
                );
              },
            },
          ),
        )
        .catch((error) => {
          this.logger.error(error);
          this.logger.error(`lost store record data: ${JSON.stringify(record)}`);
        });
  }

  index(index: string, record: Record<string, any>, params?: Index) {
    if (this.client)
      this.queue
        .add(() =>
          pRetry(() => this.indexRecord(index, record, params), {
            retries: this.configService.get<number>('elasticSearch.retries') ?? 3,
            onFailedAttempt: (error) => {
              this.logger.error(
                `Indexing record failed, retrying (${error.retriesLeft} attempts left)`,
                error.name,
              );
            },
          }),
        )
        .catch((error) => {
          this.logger.error(error);
        });
  }

  search(params?: Search<Record<string, any>>, options?: TransportRequestOptions) {
    if (this.client) return this.client.search(params, options);
  }

  /**
   * Delete old records from ElasticSearch
   * @param index - Index
   * @param days - Number of days ago (e.g., 30 will delete month-old data)
   */
  deleteOldRecords = async (index: string, days: number) => {
    const now = new Date();
    now.setDate(now.getDate() - days);
    if (this.client)
      return this.client.deleteByQuery({
        index,
        body: {
          query: {
            bool: {
              must: [
                {
                  range: {
                    date: {
                      lte: now,
                    },
                  },
                },
              ],
            },
          },
        },
      });
  };

  private async indexRecord(index: string, record: Record<string, any>, params?: Index) {
    return this.client.index({ index, body: record, ...params });
  }
}
