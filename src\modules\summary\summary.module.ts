import { Module, forwardRef } from '@nestjs/common';
import { PrismaModule } from 'src/providers/prisma/prisma.module';
import { SummaryController } from './summary.controller';
import { SummaryService } from './summary.service';
import { TokensService } from 'src/providers/tokens/tokens.service';
import { S3Service } from 'src/providers/s3/s3.service';
import { ConfigService } from '@nestjs/config';
import { ElasticSearchModule } from 'src/providers/elasticsearch/elasticsearch.module';
import { LLMModelsModule } from '../llm-models/llm-models.module';
import { MailService } from 'src/providers/mail/mail.service';
import { UsersModule } from '../users/users.module';
import { GroupsModule } from '../groups/groups.module';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';
import { FileHistoryModule } from '../file-history/file-history.module';
import { GroupNotificationModule } from '../group-notification/group-notification.module';
import { QueueModule } from 'src/providers/queue/queue.module';
import { LabelsModule } from '../labels/labels.module';

@Module({
  imports: [
    PrismaModule,
    ElasticSearchModule,
    LLMModelsModule,
    forwardRef(() => UsersModule),
    forwardRef(() => GroupsModule),
    FeatureFlagModule,
    FileHistoryModule,
    GroupNotificationModule,
    QueueModule,
    LabelsModule,
  ],
  controllers: [SummaryController],
  providers: [SummaryService, TokensService, S3Service, ConfigService, MailService],
  exports: [SummaryService],
})
export class SummaryModule {}
