#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx lint-staged && HAS_ERRORS="NO" || HAS_ERRORS="YES"
if [[ $HAS_ERRORS -eq "YES" ]]; then
  echo "*****************************************************************************"
  echo "The above lint errors are from your git staged files."
  echo "It may contain code that were not updated by you."
  echo ""
  echo "You should fix the lint errors caused by your changes."
  echo "You could run this git command to reset the commit and fix the lint errors:"
  echo "git reset --soft HEAD~1"
  echo "** Remember avoid touching code that you did not change."
  echo ""
  echo "You could run this command to lint your changed files:"
  echo "npm run lint:changed"
  echo "*****************************************************************************"
  echo ""
fi
exit 0
