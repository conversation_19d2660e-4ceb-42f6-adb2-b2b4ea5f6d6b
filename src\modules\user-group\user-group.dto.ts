import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
  ArrayUnique,
  MaxLength,
} from 'class-validator';
import { Type } from 'class-transformer';
import { GroupType } from '@prisma/client';

export class UserGroupFilterDto {
  @ArrayUnique()
  @IsOptional()
  groupIds?: number[];

  @ArrayUnique()
  @IsOptional()
  systemRoleIds?: number[];

  @ArrayUnique()
  @IsOptional()
  groupRoleIds?: number[];

  @ArrayUnique()
  @IsOptional()
  cccList?: string[];

  @ArrayUnique()
  @IsOptional()
  businessUnits?: string[];

  @IsEnum(GroupType)
  @IsOptional()
  groupType?: GroupType;
}

export class UserGroupDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  name: string;

  @IsEmail({}, { each: true })
  @ArrayUnique()
  @IsOptional()
  additionalEmails?: string[];

  @ValidateNested({ each: true })
  @Type(() => UserGroupFilterDto)
  @IsOptional()
  userGroupFilter?: UserGroupFilterDto;
}

export class UpdateUserGroupDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  name: string;
}
