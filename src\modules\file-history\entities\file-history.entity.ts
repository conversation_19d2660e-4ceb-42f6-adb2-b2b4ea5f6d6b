import { ApiProperty } from '@nestjs/swagger';
import { FileType, FileEntityType, FileHistoryStatus } from '@prisma/client';
import { Exclude } from 'class-transformer';

export class FileHistoryEntity {
  @ApiProperty({ description: 'id' })
  id: number;

  @ApiProperty({ description: 'requesterId' })
  requesterId: number;

  @ApiProperty({ description: 'FileType', enum: FileType })
  fileType: FileType;

  @ApiProperty({ description: 'createdAt' })
  createdAt: Date;

  @ApiProperty({ description: 'updatedAt' })
  updatedAt: Date;

  @ApiProperty({ description: 's3FilePath' })
  s3FilePath: String;

  @ApiProperty({ description: 'FileHistoryStatus', enum: FileHistoryStatus })
  status: FileHistoryStatus;

  @ApiProperty({ description: 'errorMsg' })
  errorMsg?: String;

  @ApiProperty({ description: 'dateFrom' })
  dateFrom: Date;

  @ApiProperty({ description: 'dateTo' })
  dateTo: Date;

  constructor(partial: Partial<FileHistoryEntity>) {
    Object.assign(this, partial);
  }

}
