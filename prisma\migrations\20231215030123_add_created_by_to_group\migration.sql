-- AlterTable
ALTER TABLE "Group" ADD COLUMN     "createdByUserId" INTEGER;
CREATE INDEX "createdByIndex" ON "Group"("createdByUserId");
-- CreateIndex
CREATE INDEX "nameIndex" ON "Group"("name");

-- CreateIndex
CREATE INDEX "envIndex" ON "Group"("env");

-- CreateIndex
CREATE INDEX "createdAtIndex" ON "Group"("createdAt");
UPDATE "Group" g SET "createdByUserId" = (Select "userId" FROM "Membership" m LEFT JOIN "Group" g1 ON m."groupId" = g1.id  WHERE m."roleId" = 1 AND g1.id = g.id  LIMIT 1)

