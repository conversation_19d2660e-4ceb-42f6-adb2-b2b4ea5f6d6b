/*
  Warnings:

  - You are about to drop the column `env` on the `ModelFilePermissionButton` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[isApproved,status,hasPII,isRequireSecondaryApproval,fileClassification,permissionKey,hasMalware]` on the table `ModelFilePermissionButton` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "HasMalware" AS ENUM ('YES', 'NO', 'ERROR');

-- AlterEnum
ALTER TYPE "ScanMalwareStatus" ADD VALUE 'PENDING';

-- DropIndex
DROP INDEX "ModelFilePermissionButton_isApproved_status_hasPII_isRequir_key";

delete from "ModelFilePermissionButton";
-- AlterTable
ALTER TABLE "ModelFilePermissionButton" DROP COLUMN "env",
ADD COLUMN     "hasMalware" "HasMalware";

-- CreateIndex
CREATE UNIQUE INDEX "ModelFilePermissionButton_isApproved_status_hasPII_isRequir_key" ON "ModelFilePermissionButton"("isApproved", "status", "hasPII", "isRequireSecondaryApproval", "fileClassification", "permissionKey", "hasMalware");
