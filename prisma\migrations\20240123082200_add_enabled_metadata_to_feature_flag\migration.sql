/*
  Warnings:

  - Added the required column `isEnabled` to the `FeatureFlagOverride` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "FeatureFlag" ADD COLUMN     "isEnabled" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "isForClientSide" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "FeatureFlagOverride" ADD COLUMN     "isEnabled" BOOLEAN NOT NULL,
ADD COLUMN     "metaData" JSONB;
