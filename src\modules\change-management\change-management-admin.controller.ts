import { Body, Controller, Get, Param, ParseIntPipe, <PERSON>, Query, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { DataPromotionRequest, DataPromotionRequestStatus } from '@prisma/client';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { OrderByPipe } from 'src/pipes/order-by.pipe';
import { WherePipe } from 'src/pipes/where.pipe';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { UserRequest } from '../auth/auth.interface';
import { Scopes } from '../auth/scope.decorator';
import { ChangeManagementService } from './change-management.service';
import { DataPromotionRequestDto } from './dto/data-promotion-request.dto';
import { UpdateDataPromotionRequestDto } from './dto/update-data-promotion-request.dto';

// Controller for managing changes at the system level, enabling admins to approve promotion requests among groups.
@ApiTags('Change Management - Admin')
@ApiBearerAuth('bearer-auth')
@Controller('data-promotion-requests')
export class ChangeManagementAdminController {
  constructor(private readonly changeManagementService: ChangeManagementService) {}

  @ApiOperation({ summary: 'Search data promotion requests' })
  @ApiResponse({
    status: 200,
    description: 'Success',
    schema: {
      properties: {
        list: {
          type: 'array',
          items: {
            $ref: '#/components/schemas/DataPromotionRequestDto',
          },
        },
        count: {
          type: 'number',
        },
      },
    },
  })
  @Get()
  @Scopes('group-*:read-data-promotion-request')
  async searchDataPromotionRequest(
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('groupId', OptionalIntPipe) groupId?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<{ list: object[]; count: number }> {
    return this.changeManagementService.searchDataPromotionRequests(
      skip,
      take,
      orderBy,
      where,
      groupId,
    );
  }

  @ApiOperation({ summary: 'Approve or reject data promotion request' })
  @ApiResponse({ type: DataPromotionRequestDto })
  @Patch(':id/status/:status')
  @AuditLog('approve-data-promotion-request')
  @Scopes('group-*:approve-data-promotion-request')
  async updateDataPromotionRequestStatus(
    @Req() request: UserRequest,
    @Param('id', ParseIntPipe) id: number,
    @Param('status') status: string,
    @Body() dto: UpdateDataPromotionRequestDto,
  ): Promise<DataPromotionRequest> {
    switch (status) {
      case DataPromotionRequestStatus.APPROVED:
        return this.changeManagementService.approveDataPromotionRequest(
          request.user.id,
          id,
          dto.comment,
        );
      case DataPromotionRequestStatus.REJECTED:
        return this.changeManagementService.rejectDataPromotionRequest(
          request.user.id,
          id,
          dto.comment,
        );
      default:
        throw new ApiException(ErrorCode.DATA_PROMOTION_REQUEST_STATUS_NOT_UNSUPPORTED);
    }
  }
}
