import { ApiHideProperty, ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  ChatResponse,
  ContentPoint,
  FilePoint,
  ROLE,
  Usage,
} from 'src/providers/llm-backend/llm-backend.interface';
import { ToolType } from '../dto/chat-llm-model.dto';
import { ScannersResultDTO as ScannersInputResultDTO } from 'src/modules/bot-security/dto/input-scanners-result.DTO';
import { ScannersResultDTO as ScannnersOutputResultDTO } from 'src/modules/bot-security/dto/output-scanners-result.DTO';
import { ChatFile } from '@prisma/client';

export class UsageEntity implements Usage {
  @ApiProperty({ description: 'Prompt token' })
  promptTokens: number;
  @ApiProperty({ description: 'Completion token' })
  completionTokens: number;
  @ApiProperty({ description: 'Total completion token = prompt token + completion token' })
  totalCompletionTokens: number;
  @ApiProperty({ description: 'Embedding token' })
  embeddingTokens: number;
}

export class FunctionCall {
  @ApiProperty({ description: 'The name of the function to call.' })
  name: string;
  @ApiProperty({
    description:
      'The arguments to call the function with, as generated by the model in JSON format. Note that the model does not always generate valid JSON, and may hallucinate parameters not defined by your function schema. Validate the arguments in your code before calling your function.',
  })
  arguments: string;
}

export class ToolCall {
  @ApiProperty({ description: 'tool type' })
  type: ToolType;

  @ApiProperty({
    description: 'function id. when you need send the tool call response to ai need pass the id',
  })
  id: string;

  @ApiProperty({ type: [FunctionCall], description: 'function call object' })
  function: FunctionCall;
}

class Message {
  @ApiProperty({ enum: ROLE })
  role: ROLE;

  @ApiProperty()
  content?: string;

  @ApiProperty({ required: false })
  tool_calls?: ToolCall;
}
class ConetntFilterStatus {
  @ApiProperty()
  filtered: boolean;

  @ApiProperty()
  severity: string;
}

class ContentFilterResults {
  @ApiProperty()
  hate: ConetntFilterStatus;

  @ApiProperty()
  self_harm: ConetntFilterStatus;

  @ApiProperty()
  sexual: ConetntFilterStatus;

  @ApiProperty()
  violence: ConetntFilterStatus;
}

export class FilePointEntity implements FilePoint {
  @ApiProperty({ description: 'Data source' })
  data_source: string;
  @ApiProperty({ description: 'File id' })
  file_id: string;
  @ApiProperty({ description: 'File name' })
  filename: string;
  @ApiProperty({ description: 'Page number' })
  page: number;
}

export class ContentPointEntity implements ContentPoint {
  @ApiProperty({ description: 'content id' })
  id: number;
  @ApiPropertyOptional({ description: 'file name' })
  filename?: string;
  @ApiPropertyOptional({ description: 'file id' })
  file_id?: string;
  @ApiPropertyOptional({ description: 'page number' })
  page?: string;
  @ApiPropertyOptional({ description: 'data source' })
  data_source?: string;
  @ApiPropertyOptional({ description: 'cited data source' })
  cited?: boolean;
  @ApiPropertyOptional({ description: 'source url path' })
  url?: string;
}

export class ChatResponseEntity implements ChatResponse {
  @ApiProperty({ required: false, description: 'Answer returns from chatGPT' })
  answer?: string;

  @ApiProperty({ required: false, description: 'Answer message object returns from chatGPT' })
  message: Message;

  @ApiProperty({
    required: false,
    description:
      'The name and arguments of a tools that should be called, as generated by the model.',
  })
  tool_calls?: ToolCall;

  @ApiProperty({ description: 'suggest replies question' })
  suggestReplies: string;

  @ApiProperty({ description: 'content filter', default: {} })
  content_filter_results: ContentFilterResults;

  @ApiProperty({
    description:
      'The number of retrieved relevant contexts, the size is equal to the value of top param',
    type: [String],
  })
  data_points: string[];

  @ApiProperty({
    required: false,
    description: 'Content point',
    type: [ContentPointEntity],
  })
  content_points?: ContentPointEntity[];

  @ApiProperty({ description: 'The final prompt sent to chatGPT' })
  thoughts: string;
  @ApiProperty({ description: 'Token usage' })
  usage: UsageEntity;

  @ApiProperty({
    required: false,
    description: 'File points, available in cwf approach only',
    type: [FilePointEntity],
    deprecated: true,
  })
  file_points?: FilePointEntity[];

  @ApiProperty({
    type: ScannnersOutputResultDTO,
    required: false,
    description: 'Security Scan Output Result',
  })
  output_scanners_result?: ScannnersOutputResultDTO;

  @ApiProperty({
    type: ScannersInputResultDTO,
    required: false,
    description: 'Security Scan Input Result',
  })
  input_scanners_result?: ScannersInputResultDTO;

  @ApiProperty({
    description: `The value is set to 'true' if the input passed all security checks, and 'false' otherwise..`,
    type: Boolean,
  })
  input_scanners_result_is_valid?: boolean;
  @ApiProperty({
    description: `The value is set to 'true' if the output passed all security checks, and 'false' otherwise.`,
    type: Boolean,
  })
  output_scanners_result_is_valid?: boolean;

  @ApiProperty({
    description: `The File expired time `,
    type: Date,
  })
  file_expired_at?: Date | null;

  @ApiProperty({
    description: `The file entity with chat. when the chat have any resource can be download it will show  the info as this `,
  })
  chatFile?: ChatFile;
}

class DeltaEntity {
  @ApiProperty({ description: 'The detla message content' })
  content?: string;

  @ApiProperty({ description: 'The role of the message' })
  role?: string;

  @ApiProperty({
    description:
      'The name and arguments of a tools that should be called, as generated by the model.',
  })
  tool_calls?: ToolCall;

  @ApiProperty({
    description: 'finish reason',
  })
  finish_reason?: string;

  @ApiProperty({
    description: 'index',
  })
  index: number;

  @ApiProperty({
    description: 'reserved',
  })
  logprobs?: string;

  @ApiProperty({ description: 'content filter' })
  content_filter_results?: ContentFilterResults;
}

export class MessageEventDeltaEntity {
  @ApiProperty({
    description: 'delta message',
  })
  data: DeltaEntity;
}
