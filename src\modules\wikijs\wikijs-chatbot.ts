export const WIKIJS_CHATBOT_CONTENT =
  '<div class="chat"> \n\t<div class="chatbox">\n\t\t<div class="chatheader">\n\t\t\tChat <span id ="chat-version"></span>\n    </div>\n    <div class="chatbody" id="chatbody">\n      <div class="msg assistant">How can I help you?</div>\n    </div>\n    <div class="chatfooter">\n      <button class="material-symbols-outlined" id="chatclear">mop</button>\n      <input type="text" id="chatinput" placeholder="Ask me anything...">\n      <button class="material-symbols-outlined" id="chatsend">send</button>\n    </div>\n\t</div>\n</div>';
export const WIKIJS_CHATBOT_JAVASCRIPT =
  '<script src="../../public/scripts/markdown-it-bundle.js"></script>\n<script src="../../public/scripts/chat-genkb.js"></script>\n\n<link rel="stylesheet" href="../../public/scripts/chat.css"/>\n<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400,0,0" />';
export const WIKIJS_CHATBOT_CSS ='';
