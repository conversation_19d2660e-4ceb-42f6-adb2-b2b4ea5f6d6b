import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
// import { SQS } from 'aws-sdk';
import { ServiceException } from '@smithy/smithy-client';

import {
  SendMessageBatchCommandInput,
  SendMessageCommandInput,
  SendMessageCommandOutput,
  SQS,
} from '@aws-sdk/client-sqs';

import { Configuration } from 'src/config/configuration.interface';
import { QueueServiceInterface } from './queue.service.interface';
import { BullMqService } from '../bullmq/bullmq.service';

@Injectable()
export class QueueService {
  private client: SQS | QueueServiceInterface;
  private logger = new Logger(QueueService.name);

  constructor(
    private configService: ConfigService,
    private bullMqService: BullMqService,
  ) {
    const config = this.configService.get<Configuration['queue']>('queue');
    // TODO::  if need support other cloud service need create Provider service for this
    this.client =
      config.provider == 'sqs' ? new SQS({
        region: config.region
      }) : this.bullMqService;
  }

  async sendMessageBatch(params: SendMessageBatchCommandInput) {
    return new Promise((resolve, reject) => {
      this.logger.log(`send SQS Msg Batch ${JSON.stringify(params)}`);
      this.client.sendMessageBatch(params, (err: ServiceException, data) => {
        if (err) {
          this.logger.error(err, 'send Message Error');
          reject('failed');
        } else {
          resolve(data);
        }
      });
    });
  }

  async sendMessage(params: SendMessageCommandInput) {
    try {
      this.logger.log(`send SQS Msg -> ${params}`);
      const data: SendMessageCommandOutput = await new Promise((resolve, reject) => {
        this.client.sendMessage(params, (err, data) => {
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        });
      });
      this.logger.debug(data.MessageId);
    } catch (err) {
      this.logger.error((err as any)?.message, `Failed to send sqs message - ${params.QueueUrl}`);
      throw new InternalServerErrorException();
    }
  }
}
