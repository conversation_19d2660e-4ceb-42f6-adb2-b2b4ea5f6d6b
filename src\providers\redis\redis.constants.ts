export const PROJECT_NAME = 'BOT_BUILDER';
export const MODEL_FEATURE_FLAG = 'FEATURE_FLAG';

export enum FEATURE_FLAG_TYPE {
  GROUP = 'GROUP',
  SYSTEM = 'SYSTEM',
  ENV = 'ENV',
}

export const FEATURE_FLAG_SYSTEM_ALL_KEYS = `${PROJECT_NAME}:${MODEL_FEATURE_FLAG}:${FEATURE_FLAG_TYPE.SYSTEM}:KEYS`;
export const FEATURE_FLAG_SYSTEM_KEY = `${PROJECT_NAME}:${MODEL_FEATURE_FLAG}:${FEATURE_FLAG_TYPE.SYSTEM}:KEY:{KEY}`;
export const FEATURE_FLAG_OVERRIDE_GROUP_ALL_KEYS = `${PROJECT_NAME}:${MODEL_FEATURE_FLAG}:${FEATURE_FLAG_TYPE.GROUP}:{GROUP_ID}:KEYS`;
export const FEATURE_FLAG_OVERRIDE_GROUP_KEY = `${PROJECT_NAME}:${MODEL_FEATURE_FLAG}:${FEATURE_FLAG_TYPE.GROUP}:{GROUP_ID}:KEY:{KEY}`;
export const FEATURE_FLAG_OVERRIDE_ENV_ALL_KEYS = `${PROJECT_NAME}:${MODEL_FEATURE_FLAG}:${FEATURE_FLAG_TYPE.ENV}:{ENV}:KEYS`;
export const FEATURE_FLAG_OVERRIDE_ENV_KEY = `${PROJECT_NAME}:${MODEL_FEATURE_FLAG}:${FEATURE_FLAG_TYPE.ENV}:{ENV}:KEY:{KEY}`;
export const USER_SCOPE_KEY = `${PROJECT_NAME}:USER_SCOPE:{USER_ID}`;
export const GROUP_MEMBERSHIP_SCOPE_KEY = `${PROJECT_NAME}:GROUP_MEMBERSHIP_SCOPE:{GROUP_ID}:USER:{USER_ID}`;
export const GROUP_RESOURCE_SCOPE_KEY = `${PROJECT_NAME}:GROUP_RESOURCE_SCOPE:{GROUP_ID}`;
export const GROUP_RESOURCE_QUOTA_KEY = `${PROJECT_NAME}:GROUP_RESOURCE_QUOTA:{GROUP_ID}`;
export const GROUP_LLM_ENGINE_PROMPT_TOKEN_USAGE_KEY = `${PROJECT_NAME}:GROUP_LLM_ENGINE_PROMPT_TOKEN_USAGE:{GROUP_ID}:{LLM_ENGINE}`;
export const GROUP_LLM_ENGINE_COMPLETION_TOKEN_USAGE_KEY = `${PROJECT_NAME}:GROUP_LLM_ENGINE_COMPLETION_TOKEN_USAGE:{GROUP_ID}:{LLM_ENGINE}`;
export const API_KEY_KEY = `${PROJECT_NAME}:API_KEY:{API_KEY}`;
export const GROUP_NOTIFICATION_SILENT_PERIOD_KEY = `${PROJECT_NAME}:GROUP_NOTIFICATION_SILENT_PERIOD:{NOTIFICATION_KEY}:{CHANNEL}:{GROUP_ID}`;
