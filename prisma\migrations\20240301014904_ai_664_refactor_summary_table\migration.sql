/*
  Warnings:

  - You are about to drop the column `date` on the `Summary` table. All the data in the column will be lost.
  - You are about to drop the column `entityId` on the `Summary` table. All the data in the column will be lost.
  - You are about to drop the column `entityType` on the `Summary` table. All the data in the column will be lost.
  - Added the required column `callingBy` to the `Summary` table without a default value. This is not possible if the table is not empty.
  - Added the required column `callingType` to the `Summary` table without a default value. This is not possible if the table is not empty.
  - Added the required column `endDate` to the `Summary` table without a default value. This is not possible if the table is not empty.
  - Added the required column `groupId` to the `Summary` table without a default value. This is not possible if the table is not empty.
  - Added the required column `startDate` to the `Summary` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `key` on the `Summary` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/

truncate "Summary";
-- CreateEnum
CREATE TYPE "SummaryCallingType" AS ENUM ('USER_PLAYGROUND', 'API_KEY');

-- CreateEnum
CREATE TYPE "SummaryKeyType" AS ENUM ('CALL_TOTAL', 'COMPLETION_TOKENS_TOTAL', 'TOTAL_COMPLETION_TOKENS_TOTAL', 'PROMPT_TOKENS_TOTAL', 'EMBEDDING_TOKENS_TOTAL', 'TOOLS_USAGE_TOTAL', 'DURATION_IN_MS_AVG');

-- DropIndex
DROP INDEX "entityId_type_index";

-- DropIndex
DROP INDEX "entityType_key_engineSlug_entityId_index";


-- AlterTable
ALTER TABLE "Summary" ALTER COLUMN "entityType" TYPE text;
ALTER TABLE "Summary" DROP COLUMN "date",
DROP COLUMN "entityId",
DROP COLUMN "entityType",
ADD COLUMN     "callingBy" INTEGER NOT NULL,
ADD COLUMN     "callingType" "SummaryCallingType" NOT NULL,
ADD COLUMN     "groupId" INTEGER NOT NULL,
ADD COLUMN     "flowId" INTEGER NOT NULL DEFAULT 0,
DROP COLUMN "key",
ADD COLUMN     "key" "SummaryKeyType" NOT NULL,
ADD COLUMN     "startDate" TIMESTAMP(6) NOT NULL,
ADD COLUMN     "endDate" TIMESTAMP(6) NOT NULL;


-- DropEnum
DROP TYPE "SummaryEntityType" CASCADE;

-- CreateIndex
CREATE INDEX "summary_groupId_index" ON "Summary"("groupId");

-- CreateIndex
CREATE INDEX "summary_key_date_index" ON "Summary"("key", "startDate");

-- CreateIndex
CREATE INDEX "summary_callingBy_callingType_groupId_date_index" ON "Summary"("callingBy", "callingType", "groupId", "startDate");

-- CreateIndex
CREATE INDEX "summary_flowId_groupId_date_index" ON "Summary"("flowId", "groupId", "startDate");
