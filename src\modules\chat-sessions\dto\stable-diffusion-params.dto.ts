import { IsBoolean, IsInt, IsNumber, IsOptional } from 'class-validator';

export class StableDiffusionParamsDto {
  @IsInt()
  @IsOptional()
  height?: number;

  @IsInt()
  @IsOptional()
  width?: number;

  @IsInt()
  @IsOptional()
  batch_size?: number;

  @IsNumber()
  @IsOptional()
  cfg_scale?: number;

  @IsBoolean()
  @IsOptional()
  enable_hr?: boolean;

  @IsNumber()
  @IsOptional()
  hr_scale?: number;
}
