-- AlterTable
ALTER TABLE "Resource" ADD COLUMN     "resourceCategoryId" INTEGER;

-- CreateTable
CREATE TABLE "ResourceCategory" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "subscriberType" "ResourceSubsciberType" NOT NULL,

    CONSTRAINT "ResourceCategory_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ResourceCategory_key_key" ON "ResourceCategory"("key");

-- CreateIndex
CREATE INDEX "Resource_resourceCategoryId_idx" ON "Resource"("resourceCategoryId");

-- CreateIndex
CREATE INDEX "ResourceCategory_subscriberType_idx" ON "ResourceCategory"("subscriberType");