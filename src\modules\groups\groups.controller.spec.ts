import { ModuleMocker, MockFunctionMetadata } from 'jest-mock';
import { mockDeep, DeepMockProxy } from 'jest-mock-extended';
import { Test, TestingModule } from '@nestjs/testing';
import { GroupController } from './controllers/groups.controller';
import { GroupsService } from './groups.service';
import { ChannelType } from '../llm-models/dto/chat-llm-model.dto';
const moduleMocker = new ModuleMocker(global);

describe('GroupsService', () => {
  let groupsService: DeepMockProxy<GroupsService>;

  let groupController: GroupController;
  beforeEach(async () => {
    groupsService = mockDeep<GroupsService>();
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GroupController],
      providers: [{ provide: GroupsService, useValue: groupsService }],
    })
      .useMocker((token) => {
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>;
          const Mock = moduleMocker.generateFromMetadata(mockMetadata);
          return new Mock();
        }
      })
      .compile();
    groupController = module.get(GroupController);
  });

  describe('get', () => {
    it('should be return group', async () => {
      const response = {
        points: 10,
        duration: 100,
      };
      groupsService.getGroupRateLimitQuota.mockResolvedValue(response);
      const res = await groupController.getRateLimitQuota(1, ChannelType.PLAYGROUND);
      expect(res).toEqual(response);
    });
  });
});
