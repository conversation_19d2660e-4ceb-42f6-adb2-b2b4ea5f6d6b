import { Injectable, Logger } from '@nestjs/common';
import { promises as fsPromises } from 'fs';

@Injectable()
export class VersionService {
  logger = new Logger(VersionService.name);
  private cachedVersion: Record<string, string> | null = null;

  async getVersionNumber(): Promise<Record<string, string>> {
    if (this.cachedVersion !== null) {
      // Use cached version if available
      return this.cachedVersion;
    }

    try {
      const fileContent = await fsPromises.readFile('./version.txt', 'utf-8');
      this.logger.log(`Succeeded to get version.txt (content = "${fileContent}")`);
      this.cachedVersion = { version: fileContent.trim() };
    } catch (error) {
      this.logger.error(error, 'Failed to get version.txt');
    }

    return this.cachedVersion || { version: '' };
  }
}
