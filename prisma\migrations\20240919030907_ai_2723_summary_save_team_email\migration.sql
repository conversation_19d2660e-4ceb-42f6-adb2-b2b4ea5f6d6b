-- AlterTable
ALTER TABLE "Summary" ADD COLUMN     "callingAttributes" TEXT;

-- AlterTable
ALTER TABLE "SummaryAll" ADD COLUMN     "callingAttributes" TEXT;

-- CreateIndex
CREATE INDEX "summary_callingBy_callingType_groupId_date_callingAtt_index" ON "Summary"("callingBy", "callingType", "groupId", "startDate", "callingAttributes");

-- CreateIndex
CREATE INDEX "summary_all_callingBy_callingType_groupId_date_callingAtt_index" ON "SummaryAll"("callingBy", "callingType", "callingAttributes", "groupId");



-- Summary triggers
CREATE OR REPLACE FUNCTION summary_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF EXISTS (SELECT 1 FROM "SummaryAll" WHERE "engineSlug" = NEW."engineSlug" AND "callingType" = NEW."callingType" AND "callingBy" = NEW."callingBy" AND "groupId" = NEW."groupId" AND "flowId" = NEW."flowId" AND "key" = NEW."key" AND "callingAttributes" = NEW."callingAttributes") 
    THEN
        UPDATE "SummaryAll" sa SET value = (sa."value" + NEW."value")  
        WHERE "engineSlug" = NEW."engineSlug" AND "callingType" = NEW."callingType" AND  "callingBy" = NEW."callingBy" AND "groupId" = NEW."groupId" AND "flowId" = NEW."flowId" AND "key" = NEW."key" AND "callingAttributes" = NEW."callingAttributes";
    ELSE INSERT INTO "SummaryAll" ("value", "engineSlug", "callingType", "callingBy", "groupId", "flowId", "key", "callingAttributes") 
    VALUES (NEW."value", NEW."engineSlug", NEW."callingType", NEW."callingBy", NEW."groupId", NEW."flowId", NEW."key", NEW."callingAttributes");
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER Summary_trigger
AFTER INSERT ON public."Summary"
FOR EACH ROW
EXECUTE FUNCTION summary_trigger_function();