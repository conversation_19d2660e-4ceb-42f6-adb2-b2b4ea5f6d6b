import { Module } from '@nestjs/common';
import { FlowsService } from './flows.service';
import { FlowsController } from './flows.controller';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { FlowBackendModule } from '../../providers/flow-backend/flow-backend.module';
import { ConfigModule } from '@nestjs/config';
import { UsersModule } from '../users/users.module';
import { ElasticSearchModule } from 'src/providers/elasticsearch/elasticsearch.module';
import { GroupsModule } from '../groups/groups.module';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';
import { ScopeModule } from '../scope/scope.module';
import { ChatSessionsModule } from '../chat-sessions/chat-sessions.module';
import { ApiKeysModule } from '../api-keys/api-keys.module';
import { BotSecurityModule } from '../bot-security/bot-security.module';
import { LLMModelsModule } from '../llm-models/llm-models.module';
import { LlmEnginesModule } from '../llm-engines/llm-engines.module';
import { ChatFilesModule } from '../chat-files/chat-files.module';

@Module({
  controllers: [FlowsController],
  imports: [
    PrismaModule,
    FlowBackendModule,
    ElasticSearchModule,
    UsersModule,
    ConfigModule,
    GroupsModule,
    FeatureFlagModule,
    ScopeModule,
    ChatSessionsModule,
    ApiKeysModule,
    BotSecurityModule,
    LLMModelsModule,
    LlmEnginesModule,
    ChatFilesModule,
  ],
  providers: [FlowsService],
  exports: [FlowsService],
})
export class FlowsModule {}
