import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { Configuration } from 'src/config/configuration.interface';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import {
  AutoTestCreateTestCaseDto,
  AutoTestUpdateTestCaseDto,
  TestCaseExecutionResultPage,
  TestCasePageDto,
} from './auto-test.interface';

@Injectable()
export class AutoTestService {
  private logger = new Logger(AutoTestService.name);

  private autoTestClient;

  constructor(private readonly configService: ConfigService) {
    const config = this.configService.get<Configuration['autoTest']>('autoTest');
    this.autoTestClient = axios.create({
      baseURL: config.clientUrl,
      timeout: config.timeout,
    });
    this.autoTestClient.interceptors.response.use(
      (res) => res.data,
      (error) => {
        this.logger.error({ error: error, data: error?.response?.data });
        const errorResponse = error?.response?.data;
        if (errorResponse && errorResponse?.error) {
          const errorMsg = `${errorResponse.error?.code}: ${errorResponse.error?.message}`;
          throw new ApiException(errorMsg);
        }
        throw new ApiException(ErrorCode.REQUEST_AUTO_TEST_SERVICE_FAILED);
      },
    );
    this.autoTestClient.interceptors.request.use((autoTestReq) => {
      this.logger.debug({ autoTestReq });
      return autoTestReq;
    });
  }

  async createTestCase(createTestCaseDto: AutoTestCreateTestCaseDto) {
    const createdTestCase = await this.autoTestClient.post('/v1/test-case', createTestCaseDto);
    return createdTestCase;
  }

  async createTestCaseAndRun(createTestCaseDto: AutoTestCreateTestCaseDto) {
    const createdTestCase = await this.autoTestClient.post('/v1/test-case/run', createTestCaseDto);
    return createdTestCase;
  }

  async findAllTestCase(
    groupId: number,
    take?: number,
    skip?: number,
    where?: Record<any, any>,
    orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<TestCasePageDto> {
    const listData = await this.autoTestClient.get(`/v1/test-case`, {
      params: {
        groupId,
        take,
        skip,
        where,
        orderBy,
      },
    });
    return listData;
  }

  async findAllTestCaseExecution(
    groupId: number,
    testCaseId: number,
    take?: number,
    skip?: number,
    where?: Record<any, any>,
    orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    const listData = await this.autoTestClient.get(`/v1/test-case-execution`, {
      params: {
        groupId,
        testCaseId,
        take,
        skip,
        where,
        orderBy,
      },
    });
    return listData;
  }

  async findAllTestCaseExecutionResult(
    groupId: number,
    testCaseExecutionId: number,
    take?: number,
    skip?: number,
    where?: Record<any, any>,
    orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<TestCaseExecutionResultPage> {
    const listData = await this.autoTestClient.get(`/v1/test-case-execution-result`, {
      params: {
        groupId,
        testCaseExecutionId,
        take,
        skip,
        where,
        orderBy,
      },
    });
    return listData;
  }

  async findOneTestCaseById(id: number) {
    const testCase = await this.autoTestClient.get(`/v1/test-case/${id}`);
    return testCase;
  }

  async findOneTestCaseExecution(testCaseExecutionId: number){
    const testCaseExecution = await this.autoTestClient.get(
      `/v1/test-case-execution/${testCaseExecutionId}`,
    );
    return testCaseExecution;
  }

  async updateTestCase(id: number, updateAutoTestDto: AutoTestUpdateTestCaseDto) {
    const createdTestCase = await this.autoTestClient.patch(
      `/v1/test-case/${id}`,
      updateAutoTestDto,
    );
    return createdTestCase;
  }

  async updateTestCaseAndRun(id: number, updateAutoTestDto: AutoTestUpdateTestCaseDto) {
    const createdTestCase = await this.autoTestClient.patch(
      `/v1/test-case/${id}/run`,
      updateAutoTestDto,
    );
    return createdTestCase;
  }

  async removeTestCase(id: number) {
    const remove = await this.autoTestClient.delete(`/v1/test-case/${id}`);
    return remove;
  }
}
