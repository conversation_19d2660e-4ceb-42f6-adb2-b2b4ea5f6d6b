import { Type } from 'class-transformer';
import {
  IsOptional,
  IsIn,
  ValidateNested,
  IsArray,
  ArrayMinSize,
  IsEnum,
  IsNotEmpty,
} from 'class-validator';
import {
  TestCaseType,
  TestCaseStatus,
  TestCaseConfig,
  TestCaseLLMParams,
  ByPassPiiType,
} from './test-case.dto';

export class UpdateTestCaseDto {
  @IsOptional()
  @IsNotEmpty()
  name?: string;

  @IsEnum(TestCaseType)
  @IsOptional()
  testCaseType?: TestCaseType;

  @IsOptional()
  @IsNotEmpty()
  iterations?: number;

  @IsIn(Object.values(TestCaseStatus))
  @IsOptional()
  testCaseStatus?: TestCaseStatus;

  @IsOptional()
  @IsNotEmpty()
  defaultValidationPrompt?: string;

  @IsOptional({ each: true })
  @ValidateNested({ each: true })
  @IsArray()
  @ArrayMinSize(1)
  @Type(() => TestCaseConfig)
  config?: TestCaseConfig[];

  @ValidateNested({ each: true })
  @IsOptional({ each: true })
  @Type(() => TestCaseLLMParams)
  llmParams?: TestCaseLLMParams;

  validationModel?: string;

  @IsOptional()
  @IsArray()
  recipients?: string[];

  @IsOptional()
  byPassPii?: ByPassPiiType;

  @IsOptional()
  jobConfig?: string;
}
