import { Controller, Get, Logger, Post } from '@nestjs/common';
import { TasksService } from '../../providers/tasks/tasks.service';
import { Public } from '../auth/public.decorator';
import { ApiTags } from '@nestjs/swagger';

@Controller('batch')
@ApiTags('Batch')
@Public()
export class BatchesController {
  constructor(private tasksService: TasksService) {}
  private logger = new Logger(BatchesController.name);

  @Get('deleteOldLogs')
  deleteOldLogs() {
    this.logger.log('Process Batch - deleteOldLogs');
    return this.tasksService.deleteOldLogs();
  }

  @Get('deleteInactiveUsers')
  deleteInactiveUsers() {
    this.logger.log('Process Batch - deleteInactiveUsers');
    return this.tasksService.deleteInactiveUsers();
  }

  @Get('deleteOldSessions')
  deleteOldSessions() {
    this.logger.log('Process Batch - deleteOldSessions');
    return this.tasksService.deleteOldSessions();
  }

  @Get('updateMetrics')
  updateMetrics() {
    return this.tasksService.updateMetrics();
  }

  @Get('deleteOldAuditLogs')
  deleteOldAuditLogs() {
    this.logger.log('Process Batch - deleteOldAuditLogs');
    return this.tasksService.deleteOldAuditLogs();
  }

  @Get('verifyDomain')
  verifyDomain() {
    this.logger.log('Process Batch - verifyDomain');
    return this.tasksService.verifyDomains();
  }
}
