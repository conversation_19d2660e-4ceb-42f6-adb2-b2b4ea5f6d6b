import { Environment, SystemName } from '@prisma/client';
import { PermissionGroupSettingSeed } from './groupPermission.seedData';

export interface SystemPermissionSeed {
  description: string;
  permissionKey: string;
  envs?: Environment[];
  groupSetting?: PermissionGroupSettingSeed;
}

interface Role {
  name: string;
  permissions: string[];
  systemName: SystemName;
}

export const systemPermissionList: SystemPermissionSeed[] = [
  {
    description: 'Create bot',
    permissionKey: 'system:create-bot',
  },
  {
    description: 'Create flow',
    permissionKey: 'system:create-flow',
  },
  {
    description: 'Create insight generator',
    permissionKey: 'system:create-insight',
  },
  {
    description: 'Read all audit log',
    permissionKey: 'system:read-audit-log',
  },
  {
    description: 'Read System Message Template',
    permissionKey: 'system:read-message-template',
  },
  {
    description: 'Create and Update System Message Template',
    permissionKey: 'system:write-message-template',
  },
  {
    description: 'Delete System Message Template',
    permissionKey: 'system:delete-message-template',
  },
  {
    description: 'Read System Role',
    permissionKey: 'system:read-role',
  },
  {
    description: 'Create and update feature flag',
    permissionKey: 'system:write-feat-flag',
  },
  {
    description: 'Create and update i18n',
    permissionKey: 'system:write-i18n',
  },
  {
    description: 'Delete i18n',
    permissionKey: 'system:delete-i18n',
  },
  {
    description: 'Read multiple level feature flag',
    permissionKey: 'system:read-mutilple-level-feat-flag',
  },
  {
    description: 'Create and update multiple level feature flag',
    permissionKey: 'system:write-mutilple-level-feat-flag',
  },
  {
    description: 'Delete multiple level feature flag',
    permissionKey: 'system:delete-mutilple-level-feat-flag',
  },
  {
    description: 'Delete redis cache',
    permissionKey: 'system:delete-redis-cache',
  },
  {
    description: 'Read GenKB sync status',
    permissionKey: 'system:read-wikijs-data',
  },
  {
    description: 'Sync GenKB users',
    permissionKey: 'system:sync-wikijs-data',
  },
  {
    description: 'Read system level feature flags',
    permissionKey: 'system:read-feat-flag',
  },
  {
    description: 'Read the data promotion requests of all groups',
    permissionKey: 'group-*:read-data-promotion-request',
  },
  {
    description: 'Approve or reject the data promotion requests of all groups',
    permissionKey: 'group-*:approve-data-promotion-request',
  },
  {
    description: 'Read all the information of groups',
    permissionKey: 'group-*:read-*',
  },
  {
    description: 'Read all the information of groups',
    permissionKey: 'group-*:read-info',
  },
  {
    description: 'Read the memberships of all groups',
    permissionKey: 'group-*:read-membership',
  },
  {
    description: 'Write the memberships of all groups',
    permissionKey: 'group-*:write-membership',
  },
  {
    description: 'Read the roles of all groups',
    permissionKey: 'group-*:read-role',
  },
  {
    description: 'Read the dashboard of all groups',
    permissionKey: 'group-*:read-dashboard',
  },
  {
    description: 'Read the llm files of all groups',
    permissionKey: 'group-*:read-llm-model-files',
  },
  {
    description: 'Read the llm files of all groups',
    permissionKey: 'group-*:read-llm-model-files',
  },
  {
    description: 'Process the llm files of all groups',
    permissionKey: 'group-*:process-llm-model-files',
  },
  {
    description: 'Delete the llm files of all groups',
    permissionKey: 'group-*:delete-llm-model-files',
  },
  {
    description: 'Read API keys for all groups',
    permissionKey: 'group-*:read-api-key',
  },
  {
    description: 'Create and update API keys for all groups',
    permissionKey: 'group-*:write-api-key',
  },
  {
    description: 'Read API key logs for all groups',
    permissionKey: 'group-*:read-api-key-logs',
  },
  {
    description: 'Read audit log for all groups',
    permissionKey: 'group-*:read-audit-log',
  },
  {
    description: 'Update group details for all groups',
    permissionKey: 'group-*:write-info',
  },
  {
    description: 'Create and update a LLM model config for all groups',
    permissionKey: 'group-*:write-llm-model',
  },
  {
    description: 'Read a LLM model config for all groups',
    permissionKey: 'group-*:read-llm-model',
  },
  {
    description: 'Use Playground in all groups',
    permissionKey: 'group-*:read-playground',
  },
  {
    description: 'Create and update model files for all groups',
    permissionKey: 'group-*:write-llm-model-files',
  },
  {
    description: 'Upload model files for all groups',
    permissionKey: 'group-*:upload-llm-model-files',
  },
  {
    description: 'Approve model file (first level) for all groups',
    permissionKey: 'group-*:approve-or-process-llm-model-files',
  },
  {
    description: 'Create and update Roles for all groups',
    permissionKey: 'group-*:write-role',
  },
  {
    description: 'Delete Roles for all groups',
    permissionKey: 'group-*:delete-role',
  },
  {
    description: 'Read AI generated resources for all groups',
    permissionKey: 'group-*:read-ai-resource',
  },
  {
    description: 'Write AI generated resources for all groups',
    permissionKey: 'group-*:write-ai-resource',
  },
  {
    description: 'Create and update webhooks for all groups',
    permissionKey: 'group-*:write-webhook',
  },
  {
    description: 'Read webhooks for all groups',
    permissionKey: 'group-*:read-webhook',
  },
  {
    description: 'Delete webhooks for all groups',
    permissionKey: 'group-*:delete-webhook',
  },
  {
    description: 'Write group message template for all groups',
    permissionKey: 'group-*:write-message-template',
  },
  {
    description: 'Read group message template for all groups',
    permissionKey: 'group-*:read-message-template',
  },
  {
    description: 'Delete group message template for all groups',
    permissionKey: 'group-*:delete-message-template',
  },
  {
    description: 'Manage Playground for all groups',
    permissionKey: 'group-*:write-playground',
  },
  {
    description: 'Read Gen KB member for all groups',
    permissionKey: 'group-*:read-gen-kb',
  },
  {
    description: 'Create and update Gen KB member for all groups',
    permissionKey: 'group-*:write-gen-kb',
  },
  {
    description: 'Read group entity snapshots for all groups',
    permissionKey: 'group-*:read-entity-snapshot',
  },
  {
    description: 'Create and update group entity snapshots for all groups',
    permissionKey: 'group-*:write-entity-snapshot',
  },
  {
    description: 'Delete group entity snapshots for all groups',
    permissionKey: 'group-*:delete-entity-snapshot',
  },
  {
    description: 'Create and update group data promotion requests for all groups',
    permissionKey: 'group-*:write-data-promotion-request',
  },
  {
    description: 'Assign Owner Role for all groups',
    permissionKey: 'group-*:write-owner-role',
  },
  {
    description: 'Create and update flows for all groups',
    permissionKey: 'group-*:write-flow',
  },
  {
    description: 'Read flows for all group',
    permissionKey: 'group-*:read-flow',
  },
  {
    description: 'Delete flows for all group',
    permissionKey: 'group-*:delete-flow',
  },
  {
    description: 'Create and update flow bot request for all groups',
    permissionKey: 'group-*:write-flow-bot-request',
  },
  {
    description: 'Read flow bot request for all groups',
    permissionKey: 'group-*:read-flow-bot-request',
  },
  {
    description: 'Use Flow Playground in all groups',
    permissionKey: 'group-*:read-flow-playground',
  },
  {
    description: 'Create and Update Resource for all groups',
    permissionKey: 'group-*:write-api-resource',
  },
  {
    description: 'Delete Api Resource for all groups',
    permissionKey: 'group-*:delete-api-resource',
  },
  {
    description: 'Read Api Resource for all groups',
    permissionKey: 'group-*:read-api-resource',
  },
  {
    description: 'Delete Api Resource Document for all groups',
    permissionKey: 'group-*:delete-api-resource-file',
  },
  {
    description: 'Upload Api Resource Document for all groups',
    permissionKey: 'group-*:write-api-resource-file',
  },
  {
    description: 'Read Flows Chat Debug for all groups',
    permissionKey: 'group-*:read-flow-debug',
  },
  {
    description: 'Read Group Feature Flag for all groups',
    permissionKey: 'group-*:read-feature-flag',
  },
  {
    description: 'Write Group Feature Flag for all groups',
    permissionKey: 'group-*:write-feature-flag',
  },
  {
    description: 'Delete Flow Bot Connection for all groups',
    permissionKey: 'group-*:delete-flow-bot',
  },
  {
    description: 'Read the information of users',
    permissionKey: 'user-*:read-info',
  },
  {
    description: 'Create, Update user accounts',
    permissionKey: 'user-*:write-info',
  },
  {
    description: 'Clear user cache',
    permissionKey: 'user-*:clear-cache',
  },
  {
    description: 'Deactivate the user',
    permissionKey: 'user-*:deactivate',
  },
  {
    description: 'Read All Chat Session Data',
    permissionKey: 'group-*:read-chat-session',
  },
  {
    description: 'Write All Chat Session Data',
    permissionKey: 'group-*:write-chat-session',
  },
  {
    description: 'Activate/Deactivate All Group',
    permissionKey: 'group-*:activate',
  },
  {
    description: 'Read Send Alert History',
    permissionKey: 'system:read-alert-history',
  },
  {
    description: 'Write or update llm engine',
    permissionKey: 'system:write-llm-engine',
  },
  {
    description: 'Upload temporary chat file to playground for all groups',
    permissionKey: 'group-*:upload-chat-files',
  },
  {
    description: 'Update group monthly token limit',
    permissionKey: 'group-*:update-token-limit',
  },
  {
    description: 'Read Security Report',
    permissionKey: 'system:read-security-report',
  },
  {
    description: 'Download Security Report',
    permissionKey: 'system:download-security-report',
  },
  {
    description: 'Read resource plan',
    permissionKey: 'system:read-resource-plan',
  },
  {
    description: 'Write resource plan',
    permissionKey: 'system:write-resource-plan',
  },
  {
    description: 'Operate resource plan',
    permissionKey: 'system:operate-resource-plan',
  },
  {
    description: 'Write bot review nomination',
    permissionKey: 'group-*:write-review-nomination',
  },
  {
    description: 'Read a LLM model basic info',
    permissionKey: 'group-*:read-llm-model-basic',
  },
  {
    description: 'Read all insight setting',
    permissionKey: 'group-*:read-insight',
  },
  {
    description: 'Create/Update all insight setting',
    permissionKey: 'group-*:write-insight',
  },
  {
    description: 'Write group test case',
    permissionKey: 'group-*:write-test-case',
  },
  {
    description: 'Read group test case',
    permissionKey: 'group-*:read-test-case',
  },
  {
    description: 'Deprecate group ',
    permissionKey: 'group-*:deprecate',
  },
  {
    description: 'Read user group',
    permissionKey: 'system:read-user-group',
  },
  {
    description: 'Write user group',
    permissionKey: 'system:write-user-group',
  },
  {
    description: 'Read broadcast',
    permissionKey: 'system:read-broadcast',
  },
  {
    description: 'Write broadcast',
    permissionKey: 'system:write-broadcast',
  },
  {
    description: 'Write LLM engine label',
    permissionKey: 'system:write-label',
  },
  {
    description: 'Write group llm model files tags',
    permissionKey: 'group-*:write-llm-model-files-tags',
  },
  {
    description: 'Read model prices',
    permissionKey: 'system:read-model-price',
  },
  {
    description: 'Read system price report',
    permissionKey: 'system:read-system-price-report',
  },
  {
    description: 'Read price unit events',
    permissionKey: 'system:read-price-unit-event',
  },
  {
    description: 'Read group price report',
    permissionKey: 'group-*:read-group-price-report',
  },
  {
    description: 'Download system price report',
    permissionKey: 'system:download-system-price-report',
  },
  {
    description: 'Download group price report',
    permissionKey: 'group-*:download-group-price-report',
  },
  {
    description: 'Create model price process',
    permissionKey: 'system:create-model-price-process',
  },
  {
    description: 'Create price',
    permissionKey: 'system:create-price',
  },
  {
    description: 'Update price',
    permissionKey: 'system:update-price',
  },
  {
    description: 'Assign membership to all roles',
    permissionKey: 'group-*:role-*',
  },
  {
    description: 'Write global data source',
    permissionKey: 'group-*:write-global-data-source',
  },
];
const sudoExcludedPermissionKeys = [
  'group-*:role-*',
  'group-*:read-membership',
  'group-*:write-membership',
  'user-*:write-info',
  'user-*:deactivate',
];

export const botCreatorPermissionKeys = ['system:create-bot', 'system:create-insight'];

export const botReviewerPermissionKeys = botCreatorPermissionKeys.concat([
  'group-*:read-info',
  'group-*:read-llm-model-files',
  'group-*:process-llm-model-files',
  'group-*:delete-llm-model-files',
  'group-*:read-membership',
  'group-*:write-review-nomination',
]);

export const systemDefaultRoles: Role[] = [
  {
    name: 'SUDO',
    systemName: SystemName.SUDO,
    permissions: systemPermissionList
      .map((permission) => permission.permissionKey)
      .filter((permission) => !sudoExcludedPermissionKeys.includes(permission)),
  },
  {
    name: 'OPERATION_TEAM',
    systemName: SystemName.OPERATION_TEAM,
    permissions: [
      'group-*:read-data-promotion-request',
      'group-*:approve-data-promotion-request',
      'group-*:update-token-limit',
      'group-*:read-info',
      'system:read-resource-plan',
      'system:operate-resource-plan',
    ],
  },
  {
    name: 'PRODUCT_TEAM',
    systemName: SystemName.PRODUCT_TEAM,
    permissions: [
      'group-*:read-dashboard',
      'group-*:read-info',
      'system:read-resource-plan',
      'user-*:read-info',
      'group-*:read-role',
      'group-*:read-llm-model',
      'system:read-role',
      'group-*:read-llm-model-files',
      'system:read-user-group',
      'system:write-user-group',
      'system:read-broadcast',
      'system:write-broadcast',
      'system:read-message-template',
      'system:write-message-template',
      'system:delete-message-template',
      'system:write-llm-engine',
      'system:read-feat-flag',
      'system:write-feat-flag',
      'system:read-mutilple-level-feat-flag',
      'system:write-mutilple-level-feat-flag',
      'system:write-i18n',
      'system:delete-i18n',
      'group-*:read-flow',
      'group-*:read-membership',
    ],
  },
  {
    name: 'ACCOUNT_MANAGEMENT',
    systemName: SystemName.ACCOUNT_MANAGEMENT,
    permissions: [
      'system:read-audit-log',
      'user-*:read-info',
      'user-*:write-info',
      'user-*:deactivate',
      'group-*:read-audit-log',
      'group-*:read-info',
      'group-*:read-membership',
      'group-*:write-membership',
      'group-*:role-*',
      'group-*:read-llm-model',
      'group-*:read-role',
      'group-*:read-dashboard',
      'system:read-role',
    ],
  },
  {
    name: 'BOT_CREATOR',
    systemName: SystemName.BOT_CREATOR,
    permissions: botCreatorPermissionKeys,
  },
  {
    name: 'SECURITY_TEAM',
    systemName: SystemName.SECURITY_TEAM,
    permissions: [
      'system:read-audit-log',
      'group-*:approve-data-promotion-request',
      'system:read-security-report',
      'system:download-security-report',
    ],
  },
  {
    name: 'BOT_REVIEWER',
    systemName: SystemName.BOT_REVIEWER,
    permissions: botReviewerPermissionKeys,
  },
  { name: 'USER', systemName: SystemName.USER, permissions: [] },
];
