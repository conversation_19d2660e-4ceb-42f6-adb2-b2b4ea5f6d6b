import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { UserRequest } from '../auth/auth.interface';
import { Scopes } from '../auth/scope.decorator';
import { ChatSessionsService } from './chat-sessions.service';
import {
  PublicChatSessionWhereDto,
  UpdatePublicChatSessionDto,
} from './dto/public-chat-session.dto';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { CreateChatHistoryRequestDto } from './dto/create-chat-history-request.dto';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';

@Controller('/chatSession/public')
@UsePipes(new ValidationPipe({ whitelist: true }))
@ApiTags('Public Chat Session')
@ApiBearerAuth('bearer-auth')
export class PublicChatSessionController {
  constructor(private readonly chatSessionService: ChatSessionsService) {}

  @Post('chat-histories')
  @Scopes('user-*:read-playground')
  async createChatHistory(@Req() request: UserRequest, @Body() dto: CreateChatHistoryRequestDto) {
    const chatSession = await this.chatSessionService.findChatSessionByIdAndUserId(
      dto.chatSessionId,
      request.user.id,
    );
    if (!chatSession) {
      throw new ApiException(ErrorCode.CHAT_SESSIOIN_NOT_FOUND);
    }
    return await this.chatSessionService.createChatHistory(
      chatSession.id,
      dto.contentType,
      dto.message,
    );
  }

  @Post('/:groupId')
  @Scopes('user-*:read-playground')
  public async createPublicChatSession(@Param('groupId') groupId: number, @Req() req: UserRequest) {
    const created = await this.chatSessionService.createPublicBotChatSession(groupId, req);
    return created;
  }

  @Post('/:groupId/preview')
  @Scopes('group-{groupId}:preview-public-bot', 'group-*:read-info')
  public async createPreviewPublicChatSession(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Req() req: UserRequest,
  ) {
    const created = await this.chatSessionService.createPublicBotChatSession(groupId, req, true);
    return created;
  }

  @Get('/:groupId/preview')
  @Scopes('group-{groupId}:preview-public-bot', 'group-*:read-info')
  public async getPublicPreviewChatSession(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
  ) {
    const publicChatSessions = await this.chatSessionService.getPublicChatSession(
      req,
      skip,
      take,
      groupId,
    );
    return publicChatSessions;
  }

  @Get()
  @Scopes('user-*:read-playground')
  public async getUserAllPublicChatSession(
    @Req() req: UserRequest,
    @Query() publicWhere: PublicChatSessionWhereDto,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
  ) {
    const { where } = publicWhere;
    const publicChatSessions = await this.chatSessionService.getPublicChatSession(req, skip, take, undefined, where);
    return publicChatSessions;
  }

  @Patch(':chatSessionId')
  @Scopes('user-*:read-playground')
  public async updateChatSession(
    @Req() req: UserRequest,
    @Param('chatSessionId', ParseIntPipe) chatSessionId: number,
    @Body() body: UpdatePublicChatSessionDto,
  ) {
    const update = await this.chatSessionService.updatePublicChatSession(
      req.user.id,
      chatSessionId,
      body,
    );
    return update;
  }

  @Delete(':chatSessionId')
  @Scopes('user-*:read-playground')
  public async removePublicBotChatSession(
    @Req() req: UserRequest,
    @Param('chatSessionId', ParseIntPipe) chatSessionId: number,
  ) {
    const remove = await this.chatSessionService.removePublicChatSession(
      req.user.id,
      chatSessionId,
    );
    return remove;
  }

  @Get('/:chatSessionId/chat-histories')
  @Scopes('user-*:read-playground')
  async searchChatHistory(
    @Req() request: UserRequest,
    @Param('chatSessionId', ParseIntPipe) chatSessionId: number,
    @Query('groupId', ParseIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
  ) {
    return this.chatSessionService.searchChatHistory(
      groupId,
      request.user.id,
      chatSessionId,
      'PUBLIC',
      skip,
      take,
    );
  }
}
