import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { QueueModule } from 'src/providers/queue/queue.module';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { LLMModelsModule } from '../llm-models/llm-models.module';
import { AiResourcesController } from './ai-resources.controller';
import { AiResourcesService } from './ai-resources.service';
import { S3Module } from 'src/providers/s3/s3.module';

@Module({
  imports: [PrismaModule, ConfigModule, S3Module, QueueModule, LLMModelsModule],
  controllers: [AiResourcesController],
  providers: [AiResourcesService],
  exports: [AiResourcesService],
})
export class AiResourcesModule {}
