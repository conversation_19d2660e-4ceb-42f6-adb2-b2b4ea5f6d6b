import { Injectable, Logger } from '@nestjs/common';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import {
  GetAvailableBotsForFlowResponse,
  RequestBotResponse,
  GetFlowBotRequestResponse,
  UpdateFlowBotRequestRequest,
  UpdateFlowBotRequestResponse,
  CreateFlowBotRequest,
} from './flow-bot-requests.dto';
import {
  Prisma,
  GroupType,
  Environment,
  FlowBotStatus,
  Group,
  FlowBotRequestStatus,
  FlowBotRequestType,
  FlowBotRequest,
  SystemName,
} from '@prisma/client';
import { GroupsService } from '../groups/groups.service';
import { AccessTokenParsed } from '../auth/auth.interface';

const SYSTEM_OPERATION_NOTES_AUTO_APPROVE =
  '[SYSTEM MESSAGE] Request by owner. Auto approve flow bot request.';

@Injectable()
export class FlowBotRequestsService {
  private logger = new Logger(FlowBotRequestsService.name);

  constructor(
    private prisma: PrismaService,
    private groupsService: GroupsService,
  ) {}

  async getAvailableBotsForFlow(
    userId: number,
    groupId: number,
  ): Promise<{ list: GetAvailableBotsForFlowResponse[]; count: number }> {
    const env = await this.groupsService.getGroupEnvByGroupId(groupId);

    // Get all the bots related to this user
    const memberships = await this.prisma.membership.findMany({
      include: {
        group: true,
      },
      where: {
        userId: userId,
        NOT: {
          groupId: groupId,
        },
        group: {
          AND: {
            groupType: GroupType.BOT,
            env: Environment[env],
          },
        },
      },
    });

    // Get all the existing flowBots
    const flowBots = await this.prisma.flowBot.findMany({
      select: {
        botGroupId: true,
      },
      where: {
        flowGroupId: groupId,
        botGroupId: {
          in: memberships.map((m) => m.groupId),
        },
        NOT: {
          status: FlowBotStatus.DELETED,
        },
      },
    });

    const flowBotRequests = await this.prisma.flowBotRequest.findMany({
      select: {
        botGroupId: true,
      },
      where: {
        flowGroupId: groupId,
        status: FlowBotRequestStatus.PENDING,
      },
    });

    // Filter the available bots which not added to flowBot & has no pending flowBotRequest
    const list = memberships
      .filter(
        (m) =>
          !flowBots.some((fb) => fb.botGroupId === m.groupId) &&
          !flowBotRequests.some((fbr) => fbr.botGroupId === m.groupId),
      )
      .map((m) => {
        return {
          id: m.id,
          groupId: m.group.id,
          name: m.group.name,
          createdAt: m.group.createdAt,
          updatedAt: m.group.updatedAt,
        };
      });

    return {
      list: list,
      count: list.length, // No pagination
    };
  }

  async createFlowBotRequest(
    userId: number,
    groupId: number,
    data: CreateFlowBotRequest,
  ): Promise<RequestBotResponse> {
    const { flowGroup, botGroup } = await this.validateCreateFlowBotRequest(
      groupId,
      data.targetGroupId,
      data.requestType,
    );

    let flowBotRequest: FlowBotRequest;

    // TODO: Remove switch-case statement, this function only handle create REQUEST_BOT request
    // As most operation on FlowBotRequest use flowBotId, not targetGroupId. This function need to set optional request param to handle.
    switch (data.requestType) {
      case FlowBotRequestType.REQUEST_BOT:
        // TODO: Use transactional
        flowBotRequest = await this.handleCreateRequestBot(
          userId,
          flowGroup,
          botGroup,
          data.requestNotes,
        );
        break;
      // Not supporting create the below request by createFlowBotRequest API call
      case FlowBotRequestType.DELETE_BOT:
      case FlowBotRequestType.LEAVE_FLOW:
      default:
        this.logger.error(`Cannot handle the flow bot request type: ${data.requestType}`);
        throw new ApiException(ErrorCode.INVALID_FLOW_BOT_REQUEST_REQUEST);
    }

    return {
      id: flowBotRequest.id,
      flowGroupId: flowBotRequest.flowGroupId,
      botGroupId: flowBotRequest.botGroupId,
      status: flowBotRequest.status,
      botName: botGroup.name,
      createdAt: flowBotRequest.createdAt,
      updatedAt: flowBotRequest.updatedAt,
    } as RequestBotResponse;
  }

  // For bot member request to add bot into flow.
  // If requester is the owner of the bot, auto approve flow bot request & create ACTIVE flow bot record
  private async handleCreateRequestBot(
    userId: number,
    flowGroup: Group,
    botGroup: Group,
    requestNotes: string,
  ): Promise<FlowBotRequest> {
    // TODO: Move flowBot related coding to flow-bots.service
    const flowBotCount = await this.prisma.flowBot.count({
      where: {
        flowGroupId: flowGroup.id,
        botGroupId: botGroup.id,
        NOT: {
          status: FlowBotStatus.DELETED,
        },
      },
    });

    // FlowBot must not exist in db, or its status must be DELETED
    if (flowBotCount > 0) {
      this.logger.error(`Flow bot already existed`);
      throw new ApiException(ErrorCode.INVALID_FLOW_BOT_REQUEST_REQUEST);
    }

    const botMembership = await this.prisma.membership.findFirst({
      include: { Role: true },
      where: {
        groupId: botGroup.id,
        userId: userId,
      },
    });

    if (!botMembership) {
      this.logger.error(`Must be a member of the bot`);
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }

    const isBotOwner =
      botMembership.Role.name === 'OWNER' &&
      botMembership.Role.groupId === 0 &&
      SystemName.GROUP_OWNER === botMembership.Role.systemName; // Must sync with seedData to filter owner permission
    const requestStatus = isBotOwner ? FlowBotRequestStatus.APPROVED : FlowBotRequestStatus.PENDING;
    const operationNotes = isBotOwner ? SYSTEM_OPERATION_NOTES_AUTO_APPROVE : null;

    // TODO: Use transactional
    if (isBotOwner) {
      await this.prisma.flowBot.upsert({
        where: {
          flowGroupId_botGroupId: {
            flowGroupId: flowGroup.id,
            botGroupId: botGroup.id,
          },
        },
        update: {
          status: FlowBotStatus.ACTIVE,
        },
        create: {
          flowGroupId: flowGroup.id,
          botGroupId: botGroup.id,
          status: FlowBotStatus.ACTIVE,
        },
      });
    }

    return await this.prisma.flowBotRequest.create({
      data: {
        flowGroupId: flowGroup.id,
        botGroupId: botGroup.id,
        requestType: FlowBotRequestType.REQUEST_BOT,
        dataSnapshot: {},
        status: requestStatus,
        requesterId: userId,
        requestNotes: requestNotes,
        operationNotes: operationNotes,
      },
    });
  }

  async handleCreateDeleteFlowBot(
    tx: Prisma.TransactionClient,
    userId: number,
    requestGroup: Group,
    targetGroup: Group,
  ): Promise<FlowBotRequest> {
    const requestType =
      GroupType.FLOW === requestGroup.groupType
        ? FlowBotRequestType.DELETE_BOT
        : FlowBotRequestType.LEAVE_FLOW;

    const flowGroup = GroupType.FLOW === requestGroup.groupType ? requestGroup : targetGroup;
    const botGroup = GroupType.BOT === requestGroup.groupType ? requestGroup : targetGroup;

    await this.checkDuplicatedFlowBotRequest(flowGroup, botGroup, requestType);

    return await tx.flowBotRequest.create({
      data: {
        flowGroupId: flowGroup.id,
        botGroupId: botGroup.id,
        requestType: requestType,
        dataSnapshot: {},
        status: FlowBotRequestStatus.APPROVED, // Supposed only trusted role (like OWNER) would have delete flow bot permission, so auto approve it
        requesterId: userId,
      },
    });
  }

  async updateFlowBotRequest(
    userId: number,
    groupId: number,
    flowBotRequestId: number,
    data: UpdateFlowBotRequestRequest,
  ): Promise<UpdateFlowBotRequestResponse> {
    const status = data.status;
    if (FlowBotRequestStatus.APPROVED !== status && FlowBotRequestStatus.REJECTED !== status)
      // TODO: Should be INVALID_FLOW_BOT_REQUEST_REQUEST
      throw new ApiException(ErrorCode.INVALID_FLOW_BOT_REQUEST);

    const flowBotRequest = await this.prisma.flowBotRequest.findFirst({
      where: { id: flowBotRequestId, status: FlowBotRequestStatus.PENDING },
    });

    if (!flowBotRequest) throw new ApiException(ErrorCode.FLOW_BOT_REQUEST_NOT_FOUND);
    if (flowBotRequest.botGroupId !== groupId)
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);

    switch (flowBotRequest.requestType) {
      case FlowBotRequestType.REQUEST_BOT:
        await this.handleUpdateRequestBot(
          flowBotRequest.flowGroupId,
          flowBotRequest.botGroupId,
          status,
        );
        break;
      // Not supporting update the below request by updateFlowBotRequest API call
      case FlowBotRequestType.DELETE_BOT:
      case FlowBotRequestType.LEAVE_FLOW:
      default:
        // TODO: Should be INVALID_FLOW_BOT_REQUEST_REQUEST
        throw new ApiException(ErrorCode.INVALID_FLOW_BOT_REQUEST);
    }

    const updatedFlowBotRequest = await this.prisma.flowBotRequest.update({
      data: {
        status: status,
        operatorId: userId,
        operationNotes: data.operationNotes,
      },
      where: {
        id: flowBotRequestId,
      },
    });

    return {
      id: updatedFlowBotRequest.id,
      flowGroupId: updatedFlowBotRequest.flowGroupId,
      botGroupId: updatedFlowBotRequest.botGroupId,
      status: updatedFlowBotRequest.status,
      createdAt: updatedFlowBotRequest.createdAt,
      updatedAt: updatedFlowBotRequest.updatedAt,
    } as UpdateFlowBotRequestResponse;
  }

  private async handleUpdateRequestBot(
    flowId: number,
    botId: number,
    status: FlowBotRequestStatus,
  ) {
    const updateFlowBotStatus =
      FlowBotRequestStatus.APPROVED === status ? FlowBotStatus.ACTIVE : FlowBotStatus.DELETED;

    const newFlowBot = await this.prisma.flowBot.upsert({
      where: {
        flowGroupId_botGroupId: {
          flowGroupId: flowId,
          botGroupId: botId,
        },
      },
      update: {
        status: updateFlowBotStatus,
      },
      create: {
        flowGroupId: flowId,
        botGroupId: botId,
        status: updateFlowBotStatus,
      },
    });

    return newFlowBot;
  }

  async getFlowBotRequest(
    user: AccessTokenParsed,
    groupId: number,
    skip?: number,
    take?: number,
    where?: Record<string, number | string>,
    orderBy?: Record<string, number | string>,
  ): Promise<{ list: GetFlowBotRequestResponse[]; count: number }> {
    const group = await this.prisma.group.findUnique({
      include: {
        memberships: {
          include: {
            Role: true,
          },
        },
      },
      where: { id: groupId },
    });

    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);

    const groupType = group.groupType;
    // Must sync with seedData to filter owner / admin permission
    const isGrantedRole =
      ([SystemName.SUDO, SystemName.SECURITY_TEAM] as SystemName[]).includes(user.role) ||
      ([SystemName.GROUP_OWNER] as SystemName[]).includes(group.memberships[0].Role.systemName);

    const include: Prisma.FlowBotRequestInclude = {
      requesterUser: true,
      operatorUser: true,
    };

    const whereInput: Prisma.FlowBotRequestWhereInput = {};

    // If user is not the owner, can only get the requests raised by this user
    if (!isGrantedRole) {
      whereInput.requesterId = user.id;
    }

    if (GroupType.BOT === groupType) {
      include.flowGroup = true;
      whereInput.botGroupId = groupId;
      whereInput.flowGroup = { ...where };
    } else if (GroupType.FLOW === groupType) {
      include.botGroup = true;
      whereInput.flowGroupId = groupId;
      whereInput.botGroup = { ...where };
    } else {
      throw new ApiException(ErrorCode.INVALID_FLOW_BOT_REQUEST_REQUEST);
    }

    const flowBotRequests = await this.prisma.flowBotRequest.findMany({
      include: include,
      skip,
      take,
      where: whereInput,
      orderBy,
    });

    const list = flowBotRequests.map((flowBotRequest) => {
      const group =
        GroupType.BOT === GroupType[groupType] ? flowBotRequest.flowGroup : flowBotRequest.botGroup;
      return {
        id: flowBotRequest.id,
        flowGroupId: flowBotRequest.flowGroupId,
        botGroupId: flowBotRequest.botGroupId,
        requestType: flowBotRequest.requestType,
        name: group.name,
        requesterId: flowBotRequest.requesterId,
        requesterName: flowBotRequest.requesterUser.name,
        requestNotes: flowBotRequest.requestNotes,
        operatorId: flowBotRequest.operatorId,
        operatorName: flowBotRequest.operatorUser?.name,
        operationNotes: flowBotRequest.operationNotes,
        status: flowBotRequest.status,
        createdAt: flowBotRequest.createdAt,
        updatedAt: flowBotRequest.updatedAt,
      } as GetFlowBotRequestResponse;
    });

    const count = await this.prisma.flowBotRequest.count({ where: whereInput });

    return {
      list: list,
      count: count,
    };
  }

  async validateFlowBotRequestedGroup(flowGroup: Group, botGroup: Group) {
    if (
      !flowGroup ||
      !botGroup ||
      GroupType.FLOW !== flowGroup.groupType ||
      GroupType.BOT !== botGroup.groupType
    ) {
      // Must be 1 of BOT group type and 1 of FLOW group type
      this.logger.error(
        `Request must include 1 of bot group and 1 of flow group: [${flowGroup.id} ${flowGroup.groupType}]; [${botGroup.id} ${botGroup.groupType}]`,
      );
      throw new ApiException(ErrorCode.INVALID_FLOW_BOT_REQUEST_REQUEST);
    }

    const { active: isBotActive } = await this.prisma.lLMModel.findFirst({
      select: { active: true },
      where: { groupId: botGroup.id },
    });

    // The status of requested flow and bot should be active
    if (!isBotActive) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);

    // The env of requested flow and bot must be equal
    // TODO: Should be INVALID_FLOW_BOT_REQUEST_REQUEST
    if (flowGroup.env !== botGroup.env) throw new ApiException(ErrorCode.INVALID_FLOW_BOT_REQUEST);
  }

  private async validateCreateFlowBotRequest(
    groupId: number,
    targetGroupId: number,
    requestType: FlowBotRequestType,
  ): Promise<{ flowGroup: Group; botGroup: Group }> {
    const groups = await this.prisma.group.findMany({
      where: {
        OR: [{ id: groupId }, { id: targetGroupId }],
      },
    });

    // The requested flow and bot must exist in db
    if (groups.length !== 2) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);

    const flowGroup = groups.find((g) => GroupType.FLOW === g.groupType);
    const botGroup = groups.find((g) => GroupType.BOT === g.groupType);

    await this.validateFlowBotRequestedGroup(flowGroup, botGroup);

    await this.checkDuplicatedFlowBotRequest(flowGroup, botGroup, requestType);

    return { flowGroup, botGroup };
  }

  async checkDuplicatedFlowBotRequest(
    flowGroup: Group,
    botGroup: Group,
    requestType: FlowBotRequestType,
  ) {
    const existsFlowBotRequestCount = await this.prisma.flowBotRequest.count({
      where: {
        flowGroupId: flowGroup.id,
        botGroupId: botGroup.id,
        requestType: requestType,
        status: FlowBotRequestStatus.PENDING,
      },
    });

    // Duplicated request
    if (existsFlowBotRequestCount > 0) {
      this.logger.error(`Duplicated flow bot request`);
      throw new ApiException(ErrorCode.INVALID_FLOW_BOT_REQUEST_REQUEST);
    }
  }
  // cancel flow is not considered, cancel flow bot request form FlowBotRequestType.REQUEST_BOT
  async cancelFlowBotRequest(userId: number, groupId: number, flowBotRequestId: number) {
    const flowBotRequest = await this.prisma.flowBotRequest.findUnique({
      include: {
        botGroup: true,
        requesterUser: true,
      },
      where: { id: flowBotRequestId },
    });
    if (!flowBotRequest) throw new ApiException(ErrorCode.FLOW_BOT_REQUEST_NOT_FOUND);
    if (FlowBotRequestStatus.PENDING !== flowBotRequest.status) {
      this.logger.error(`Flow bot requests status is not PENDING`);
      throw new ApiException(ErrorCode.INVALID_FLOW_BOT_REQUEST_REQUEST);
    }

    if (flowBotRequest.requesterId !== userId) {
      this.logger.error(
        `Invalid user: userId=${userId}, requesterId=${flowBotRequest.requesterId}`,
      );
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }

    if (flowBotRequest.flowGroupId !== groupId) {
      this.logger.error(
        `Invalid group call: groupId=${groupId}, flowGroupId=${flowBotRequest.flowGroupId}`,
      );
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }

    const updatedFlowBotRequest = await this.prisma.flowBotRequest.update({
      data: {
        status: FlowBotRequestStatus.CANCELLED,
        operatorId: userId,
      },
      where: {
        id: flowBotRequestId,
      },
    });

    return {
      id: updatedFlowBotRequest.id,
      flowGroupId: updatedFlowBotRequest.flowGroupId,
      botGroupId: updatedFlowBotRequest.botGroupId,
      requestType: updatedFlowBotRequest.requestType,
      name: flowBotRequest.botGroup.name,
      requesterId: updatedFlowBotRequest.requesterId,
      requesterName: flowBotRequest.requesterUser.name,
      requestNotes: updatedFlowBotRequest.requestNotes,
      operatorId: updatedFlowBotRequest.operatorId,
      operatorName: flowBotRequest.requesterUser.name,
      operationNotes: updatedFlowBotRequest.operationNotes,
      status: updatedFlowBotRequest.status,
      createdAt: updatedFlowBotRequest.createdAt,
      updatedAt: updatedFlowBotRequest.updatedAt,
    } as GetFlowBotRequestResponse;
  }
}
