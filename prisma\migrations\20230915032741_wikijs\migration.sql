/*
  Warnings:

  - A unique constraint covering the columns `[wikijsId]` on the table `Group` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[wikijsId]` on the table `User` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Group" ADD COLUMN     "wikijsId" INTEGER;

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "wikijsId" INTEGER;

-- CreateIndex
CREATE UNIQUE INDEX "Group_wikijsId_key" ON "Group"("wikijsId");

-- CreateIndex
CREATE UNIQUE INDEX "User_wikijsId_key" ON "User"("wikijsId");
