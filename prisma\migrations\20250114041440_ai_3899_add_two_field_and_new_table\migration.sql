-- AlterTable
ALTER TABLE "ModelFile" ADD COLUMN     "scanMalwareErrorMsg" TEXT,
ADD COLUMN     "scanMalwareVersion" TEXT;

-- CreateTable
CREATE TABLE "HistoricalFileMalwareScan" (
    "id" SERIAL NOT NULL,
    "errorCode" TEXT,
    "docId" TEXT NOT NULL,
    "malwareRating" TEXT,
    "scanMalwareVersion" TEXT,
    "isClean" BOOLEAN NOT NULL,

    CONSTRAINT "HistoricalFileMalwareScan_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "HistoricalFileMalwareScan_docId_idx" ON "HistoricalFileMalwareScan"("docId");
