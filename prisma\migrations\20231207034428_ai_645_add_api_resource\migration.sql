-- CreateEnum
CREATE TYPE "ApiResourceType" AS ENUM ('RESTAPI', 'GRAPHQL');

-- CreateEnum
CREATE TYPE "ApiResourceStatus" AS ENUM ('PROCESSING', 'GENERATED', 'FAILED', 'UNKNOWN');

-- CreateTable
CREATE TABLE "ApiResource" (
    "id" SERIAL NOT NULL,
    "groupId" INTEGER NOT NULL,
    "docPath" TEXT,
    "fileName" TEXT,
    "status" "ApiResourceStatus" NOT NULL DEFAULT 'UNKNOWN',
    "apiResourceType" "ApiResourceType" NOT NULL,
    "hostUrl" TEXT,
    "customParam" TEXT DEFAULT '"{}"',
    "enable" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),
    "generatedAt" TIMESTAMP(3),

    CONSTRAINT "ApiResource_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ApiResource_id_key" ON "ApiResource"("id");

-- CreateIndex
CREATE UNIQUE INDEX "ApiResource_groupId_key" ON "ApiResource"("groupId");
