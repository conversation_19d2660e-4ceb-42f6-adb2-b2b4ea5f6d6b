import { ApiProperty } from '@nestjs/swagger';
import { SnapshotEntityType } from '@prisma/client';
import { GroupDto } from './group.dto';
import { UserDto } from './user.dto';
import { DataPromotionRequestDto } from './data-promotion-request.dto';

// This DTO currently is for swagger only
// TODO: use inheritance to separate dto variations from difference APIs if necessary
export class EntitySnapshotDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  name: string;

  @ApiProperty({
    enum: SnapshotEntityType,
    required: false,
    description: 'Not in version API response.',
  })
  entityType: SnapshotEntityType;

  @ApiProperty({
    required: false,
    description: 'Not in version API response.',
  })
  entityId: string;

  @ApiProperty({
    required: false,
    description: 'Not in version API response.',
  })
  entityData: object;

  @ApiProperty({
    required: false,
    description: 'Not in version API response.',
  })
  groupId: number;

  @ApiProperty({
    required: false,
    description: 'Not in version API response.',
  })
  group: GroupDto;

  @ApiProperty()
  versionDate: Date;

  @ApiProperty({
    required: false,
    description: 'Not in version API response.',
  })
  createdByUserId: number;

  @ApiProperty({
    required: false,
    description: 'Not in create/delete API response.',
  })
  createdBy: UserDto;

  @ApiProperty({
    type: [DataPromotionRequestDto],
    required: false,
    description: "Pending request only. Not in data promotion request APIs' response.",
  })
  dataPromotionRequests: DataPromotionRequestDto[];

  @ApiProperty({
    required: false,
    description: 'Only in get single entity snapshot response.',
    example: { dataPromotionRequests: 2 },
  })
  _count: { dataPromotionRequests: number };
}
