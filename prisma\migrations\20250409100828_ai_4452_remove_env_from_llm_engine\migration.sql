/*
  Warnings:

  - You are about to drop the column `envToBeRemoved` on the `LlmEngine` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[slug]` on the table `LlmEngine` will be added. If there are existing duplicate values, this will fail.

*/
UPDATE "AiResource" ar
SET "llmEngineId" = (
    SELECT le_test.id
    FROM "LlmEngine" le_prod
             JOIN "LlmEngine" le_test
                  ON le_prod.slug = le_test.slug
    WHERE le_prod.id = ar."llmEngineId"
      AND le_prod."envToBeRemoved" = 'PROD'
      AND le_test."envToBeRemoved" = 'TEST'
)
WHERE ar."llmEngineId" in (
    SELECT le_prod.id
    FROM "LlmEngine" le_prod
    WHERE le_prod."envToBeRemoved" = 'PROD'
);

UPDATE "EntityLabels" el
SET "entityId" = (
    SELECT le_test.id
    FROM "LlmEngine" le_prod
             JOIN "LlmEngine" le_test
                  ON le_prod.slug = le_test.slug
    WHERE el."entityId" = le_prod.id
      AND le_prod."envToBeRemoved" = 'PROD'
      AND le_test."envToBeRemoved" = 'TEST'
)
WHERE el."LabelEntityType" = 'LLM_ENGINE' and el."entityId" in (
    SELECT le_prod.id
    FROM "LlmEngine" le_prod
    WHERE le_prod."envToBeRemoved" = 'PROD'
);


UPDATE "LLMModel" lm
SET "llmEngineId" = (
    SELECT le_test.id
    FROM "LlmEngine" le_prod
             JOIN "LlmEngine" le_test
                  ON le_prod.slug = le_test.slug
    WHERE lm."llmEngineId" = le_prod.id
      AND le_prod."envToBeRemoved" = 'PROD'
      AND le_test."envToBeRemoved" = 'TEST'
)
WHERE lm."llmEngineId" in (
    SELECT le_prod.id
    FROM "LlmEngine" le_prod
    WHERE le_prod."envToBeRemoved" = 'PROD'
);

DELETE FROM "LlmEngine"
WHERE "envToBeRemoved" = 'PROD';

-- DropIndex
DROP INDEX "LlmEngine_envToBeRemoved_slug_key";

-- AlterTable
ALTER TABLE "LlmEngine" DROP COLUMN "envToBeRemoved";

-- CreateIndex
CREATE UNIQUE INDEX "LlmEngine_slug_key" ON "LlmEngine"("slug");
