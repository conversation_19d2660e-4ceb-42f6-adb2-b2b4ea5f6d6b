import { CanActivate, ExecutionContext, Injectable, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import minimatch from 'minimatch';
import { AccessTokenParsed, UserRequest } from './auth.interface';

@Injectable()
export class ScopesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}
  // TODO: Make special handling to be general that support to replace by request param or request query
  // For example: update permission key as `xxx-xxx-{query-groupId}-*` / `xxx-xxx-{param-groupId}-*`
  // So the scope replacement can recognize it should get value from query or param

  private logger = new Logger(ScopesGuard.name);

  // To handle the special scope (SPECIAL_SCOPE) which will replace SPECIAL_SCOPE_KEY by the value of SPECIAL_QUERY_KEY.
  private readonly SPECIAL_QUERY_KEY = 'flowId';
  private readonly SPECIAL_SCOPE_KEY = 'groupId';
  private readonly SPECIAL_SCOPE = `group-{${this.SPECIAL_SCOPE_KEY}}:read-flow-playground`; // Should be updated if seed data is changed
  private readonly SPECIAL_SCOPE_OF_PUBLIC_BOT_CHAT_API = 'user-*:read-playground';
  private readonly chatApiUrl = '/llm-model/chat';
  canActivate(context: ExecutionContext): boolean {
    const scopes = this.reflector.get<string[]>('scopes', context.getHandler());
    const request = context.switchToHttp().getRequest<UserRequest>();
    if (!scopes) return true;
    const user: AccessTokenParsed = request.user;
    const authorized = false;
    if (!user) {
      this.logger.error('User not found');
      return false;
    }
    this.logger.log(`userScope - ${user.scopes}`);
    this.logger.log(`${request.url}`);
    for (let scope of scopes) {
      if (this.SPECIAL_SCOPE === scope && request.query[this.SPECIAL_QUERY_KEY]) {
        scope = scope.replace(
          `{${this.SPECIAL_SCOPE_KEY}}`,
          request.query[this.SPECIAL_QUERY_KEY] as string,
        );
      } else {
        for (const key in request.params) scope = scope.replace(`{${key}}`, request.params[key]);
      }
      this.logger.log(`required scope - ${scope}`);
      if (user.scopes.some((userScope) => this.checkScope(authorized, scope, userScope))) {
        this.logger.log(`checked and passed scope - ${scope}`);
        return true;
      }
    }
    const isPublicChatAPiCall =
      !authorized &&
      request.url.includes(this.chatApiUrl) &&
      request.params?.['groupId'] &&
      user.scopes.includes(this.SPECIAL_SCOPE_OF_PUBLIC_BOT_CHAT_API) &&
      (request?.body?.chatSessionId ?? 'notBody') != 'notBody';
    if (isPublicChatAPiCall) {
      return true;
    }
    return authorized;
  }

  checkScope(authorized: boolean, scope: string, userScope: string): boolean {
    authorized = authorized || minimatch(scope, userScope);
    if (authorized) return true;
  }
}
