/*
  Warnings:

  - You are about to drop the column `groupRoles` on the `UserGroupFilter` table. All the data in the column will be lost.
  - You are about to drop the column `systemRoles` on the `UserGroupFilter` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "UserGroupFilter" DROP COLUMN "groupRoles",
DROP COLUMN "systemRoles",
ADD COLUMN     "groupRoleIds" INTEGER[] DEFAULT ARRAY[]::INTEGER[],
ADD COLUMN     "systemRoleIds" INTEGER[] DEFAULT ARRAY[]::INTEGER[];

-- DropEnum
DROP TYPE "GroupDefaultRole";

-- DropEnum
DROP TYPE "SystemRole";
