-- This is an empty migration.
INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT','FLOW','INSIGHT']::"GroupType"[], 'Basic' , 'basic', true WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'basic');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT','FLOW','INSIGHT']::"GroupType"[], 'Group Setting' , 'group-setting', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'group-setting');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT', 'INSIGHT']::"GroupType"[], 'Upload Data' , 'upload-data', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'upload-data');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT']::"GroupType"[], 'GenKB' , 'gen-kb', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'gen-kb');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT']::"GroupType"[], 'Connect API' , 'connect-api', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'connect-api');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT']::"GroupType"[], 'AI Resource' , 'ai-resource', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'ai-resource');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT']::"GroupType"[], 'Test Automation' , 'test-automation', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'test-automation');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT', 'FLOW', 'INSIGHT']::"GroupType"[], 'Membership' , 'membership', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'membership');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT']::"GroupType"[], 'Message Template' , 'message-template', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'message-template');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['FLOW']::"GroupType"[], 'Flow Setting' , 'flow-setting', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'flow-setting');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT']::"GroupType"[], 'Dashboard' , 'dashboard', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'dashboard');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT']::"GroupType"[], 'Billing Report' , 'billing-report', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'billing-report');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT', 'FLOW', 'INSIGHT']::"GroupType"[], 'Feature Flag' , 'feature-flag', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'feature-flag');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT', 'FLOW', 'INSIGHT']::"GroupType"[], 'Role' , 'role', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'role');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT']::"GroupType"[], 'Change Management' , 'change-management', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'change-management');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT']::"GroupType"[], 'API Key' , 'api-key', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'api-key');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT', 'FLOW', 'INSIGHT']::"GroupType"[], 'Audit Log' , 'audit-log', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'audit-log');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT']::"GroupType"[], 'Chat Report' , 'chat-report', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'chat-report');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT']::"GroupType"[], 'Security Scan' , 'security-scan', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'security-scan');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT', 'FLOW', 'INSIGHT']::"GroupType"[], 'Plan & Sub' , 'plan-sub', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'plan-sub');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT']::"GroupType"[], 'Notification' , 'notification', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'notification');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT']::"GroupType"[], 'Webhook' , 'webhook', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'webhook');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['INSIGHT']::"GroupType"[], 'Insight' , 'insight', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'insight');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT']::"GroupType"[], 'Model Price' , 'model-price', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'model-price');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT']::"GroupType"[], 'Model Price Event' , 'model-price-event', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'model-price-event');

INSERT INTO "PermissionGroupFeature" ("groupTypes", "featureName", "featureKey", "isBasic") 
SELECT ARRAY['BOT']::"GroupType"[], 'Model Price Summary' , 'model-price-summary', false WHERE NOT EXISTS
(SELECT 1 FROM "PermissionGroupFeature" WHERE "featureKey" = 'model-price-summary');


UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'basic') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in (
'group-{groupId}:read-info',
'group-{groupId}:read-playground',
'group-{groupId}:read-llm-model-basic',
'group-{groupId}:read-flow-playground'
));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'group-setting') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in (
'group-{groupId}:write-playground',
'group-{groupId}:write-info',
'group-{groupId}:read-llm-model',
'group-{groupId}:write-llm-model',
'group-{groupId}:read-chat-session',
'group-{groupId}:write-chat-session',
'group-{groupId}:upload-chat-files',
'group-{groupId}:activate',
'group-{groupId}:delete'
));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'upload-data') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-llm-model-files', 'group-{groupId}:upload-llm-model-files', 'group-{groupId}:delete-llm-model-files', 'group-{groupId}:download-llm-model-files', 'group-{groupId}:approve-or-process-llm-model-files', 'group-{groupId}:write-llm-model-files-tags', 'group-{groupId}:delete-llm-model-files-tags', 'group-{groupId}:write-llm-model-files'));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'gen-kb') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-gen-kb', 'group-{groupId}:write-gen-kb'));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'connect-api') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-api-resource', 'group-{groupId}:write-api-resource', 'group-{groupId}:delete-api-resource', 'group-{groupId}:write-api-resource-file', 'group-{groupId}:delete-api-resource-file'));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'ai-resource') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-ai-resource', 'group-{groupId}:write-ai-resource'));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'test-automation') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-test-case', 'group-{groupId}:write-test-case'));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'membership') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-membership', 'group-{groupId}:write-membership', 'group-{groupId}:delete-membership', 'group-{groupId}:write-review-nomination'));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'message-template') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-message-template', 'group-{groupId}:write-message-template', 'group-{groupId}:delete-message-template'));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'flow-setting') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-flow-bot-request', 'group-{groupId}:read-flow', 'group-{groupId}:write-flow-bot-request', 'group-{groupId}:delete-flow-bot', 'group-{groupId}:read-flow-debug',
'group-{groupId}:write-flow', 'group-{groupId}:delete-flow'));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'dashboard') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-dashboard'));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'billing-report') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-group-price-report', 'group-{groupId}:download-group-price-report'));


UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'feature-flag') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-feature-flag', 'group-{groupId}:write-feature-flag'));


UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'role') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-role', 'group-{groupId}:write-role', 'group-{groupId}:delete-role', 'group-{groupId}:write-owner-role'));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'change-management') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-data-promotion-request', 'group-{groupId}:write-data-promotion-request', 'group-{groupId}:read-entity-snapshot', 'group-{groupId}:write-entity-snapshot',
'group-{groupId}:delete-entity-snapshot'
));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'api-key') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-api-key', 'group-{groupId}:read-api-key-logs', 'group-{groupId}:write-api-key',
'group-{groupId}:delete-api-key'
));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'security-scan') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:download-security-report', 'group-{groupId}:read-security-report'
));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'chat-report') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:export-logs'
));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'group-setting') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" LIKE 'group-{groupId}:llm-engine-%');

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'notification') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-notifications-config', 'group-{groupId}:write-notifications-config'
));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'webhook') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-webhook', 'group-{groupId}:write-webhook', 'group-{groupId}:delete-webhook'
));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'insight') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-insight', 'group-{groupId}:write-insight'
));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'audit-log') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-audit-log'
));

UPDATE "PermissionGroupSetting" pgs SET "featureId" = (SELECT id FROM "PermissionGroupFeature" WHERE "featureKey" = 'plan-sub') WHERE pgs."permissionId" in (SELECT id FROM "Permission" p WHERE p."permissionKey" in ('group-{groupId}:read-resource-plan' , 'group-{groupId}:write-resource-plan'
));

UPDATE "PermissionGroupSetting" SET "isCustomRoleAllowed" = false;

UPDATE "PermissionGroupSetting" pgs SET "isCustomRoleAllowed" = true WHERE 

(SELECT "permissionKey" FROM "Permission" p WHERE p.id = pgs."permissionId") in  (
	'group-{groupId}:read-info',
	'group-{groupId}:write-membership',
	'group-{groupId}:read-membership',
	'group-{groupId}:read-llm-model',
	'group-{groupId}:read-playground',
	'group-{groupId}:read-chat-session',
	'group-{groupId}:write-chat-session',
	'group-{groupId}:upload-llm-model-files',
	'group-{groupId}:read-llm-model-files',
	'group-{groupId}:download-llm-model-files',
	'group-{groupId}:read-gen-kb',
	'group-{groupId}:read-flow-playground',
	'group-{groupId}:read-flow-debug',
	'group-{groupId}:upload-chat-files',
	'group-{groupId}:read-llm-model-basic',
	'group-{groupId}:write-llm-model-files-tags',
	'group-{groupId}:role-group_contributor',
	'group-{groupId}:role-group_visitor',
	'group-{groupId}:role-group_member',
	'group-{groupId}:role-group_custom'
) ;

UPDATE "Role"  SET "isCustomRoleTemplateAllowed" = true WHERE "systemName" in (
	'GROUP_CONTRIBUTOR',
	'GROUP_MEMBER',
	'GROUP_ADMIN',
	'GROUP_VISITOR'
);



