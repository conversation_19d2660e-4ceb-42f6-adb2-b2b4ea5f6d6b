import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApprovedSubnet } from '@prisma/client';
import { hash } from 'bcryptjs';
import anonymize from 'ip-anonymize';
import { GeolocationService } from '../../providers/geolocation/geolocation.service';
import { PrismaService } from '../../providers/prisma/prisma.service';

@Injectable()
export class ApprovedSubnetsService {
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private geolocationService: GeolocationService,
  ) {}

  async approveNewSubnet(userId: number, ipAddress: string) {
    const subnet = await hash(
      anonymize(ipAddress),
      this.configService.get<number>('security.saltRounds') ?? 10,
    );
    const location = await this.geolocationService.getLocation(ipAddress);
    const approved = await this.prisma.approvedSubnet.create({
      data: {
        user: { connect: { id: userId } },
        subnet,
        city: location?.city?.names?.en,
        region: location?.subdivisions?.pop()?.names?.en,
        timezone: location?.location?.timeZone,
        countryCode: location?.country?.isoCode,
      },
    });
    return this.prisma.expose<ApprovedSubnet>(approved);
  }
}
