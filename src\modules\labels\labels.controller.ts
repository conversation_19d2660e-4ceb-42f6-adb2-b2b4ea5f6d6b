import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Labels } from '@prisma/client';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { OrderByPipe } from 'src/pipes/order-by.pipe';
import { WherePipe } from 'src/pipes/where.pipe';
import { UserRequest } from '../auth/auth.interface';
import { Scopes } from '../auth/scope.decorator';
import { CreateEntityLabelsDto, CreateLabelsDto } from './dto/create-labels.dto';
import { LabelsService } from './labels.service';
import { Public } from '../auth/public.decorator';
import { UpdateLabelsDto } from './dto/patch-labels.dto';

@Controller('labels')
@ApiBearerAuth('bearer-auth')
@ApiTags('Labels')
@UsePipes(new ValidationPipe({ whitelist: true }))
export class LabelsController {
  private readonly logger = new Logger(LabelsController.name);
  constructor(private readonly labelsService: LabelsService) {}

  @Post()
  @Scopes('system:write-label')
  async create(
    @Body() createLabelsDto: CreateLabelsDto,
    @Req() request: UserRequest,
  ): Promise<Labels> {
    const labels = await this.labelsService.create(createLabelsDto, request);
    return labels;
  }

  @Get()
  @Public()
  async findAll(
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    const list = await this.labelsService.findAll(take, skip, where, orderBy);
    return list;
  }

  @Delete('/entityLabels/:entityLabelsId')
  @Scopes('system:write-label')
  async removeEntityLabels(@Param('entityLabelsId', ParseIntPipe) entityLabelsId: number) {
    return await this.labelsService.removeEntityLabels(entityLabelsId);
  }

  @Post(':id/entityLabels')
  @Scopes('system:write-label')
  async addLabelInEntity(
    @Param('id', ParseIntPipe) id: number,
    @Body() createEntityLabelsDto: CreateEntityLabelsDto,
  ) {
    const addLabelInEntity = this.labelsService.addEntityLabels(id, createEntityLabelsDto);
    return addLabelInEntity;
  }

  @Patch(':id')
  @Scopes('system:write-label')
  async updateLabel(
    @Param('id', ParseIntPipe) id: number,
    @Body() createEntityLabelsDto: UpdateLabelsDto,
    @Req() request: UserRequest,
  ) {
    const update = await this.labelsService.updateLabels(id, createEntityLabelsDto, request);
    return update;
  }
}
