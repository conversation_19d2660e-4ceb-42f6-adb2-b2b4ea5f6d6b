import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { JsonValue } from '../feature-flags/feature-flags-model.dto';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class UpsertBotFeatFlagsDto {
  @IsString()
  @IsNotEmpty()
  key: string;
  isEnabled: boolean;
  @Transform((val) => {
    try {
      return JSON.parse(val);
    } catch (err) {
      throw new ApiException(ErrorCode.INVALID_META_DATA);
    }
  })
  @IsOptional()
  metaData?: JsonValue;
}
