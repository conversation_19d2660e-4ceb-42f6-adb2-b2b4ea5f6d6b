-- CreateEnum
CREATE TYPE "DataPromotionRequestStatus" AS ENUM ('PEDNDING', 'CANCELED', 'APPROVED', 'REJECTED');

-- CreateTable
CREATE TABLE "PrismaModelHistory" (
    "id" SERIAL NOT NULL,
    "model" TEXT NOT NULL,
    "modelId" INTEGER NOT NULL,
    "modelData" JSONB,
    "versionDate" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PrismaModelHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DataPromotionRequest" (
    "id" SERIAL NOT NULL,
    "targetGroupId" INTEGER NOT NULL,
    "promotedModelId" INTEGER,
    "historyId" INTEGER NOT NULL,
    "status" "DataPromotionRequestStatus" NOT NULL,
    "requesterId" INTEGER NOT NULL,
    "requestedDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "requesterComment" TEXT,
    "operatorId" INTEGER,
    "operatedDate" TIMESTAMP(3),
    "operatorComment" TEXT,

    CONSTRAINT "DataPromotionRequest_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "PrismaModelHistory_model_modelId_versionDate_key" ON "PrismaModelHistory"("model", "modelId", "versionDate");

-- AddForeignKey
ALTER TABLE "DataPromotionRequest" ADD CONSTRAINT "DataPromotionRequest_targetGroupId_fkey" FOREIGN KEY ("targetGroupId") REFERENCES "Group"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DataPromotionRequest" ADD CONSTRAINT "DataPromotionRequest_historyId_fkey" FOREIGN KEY ("historyId") REFERENCES "PrismaModelHistory"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DataPromotionRequest" ADD CONSTRAINT "DataPromotionRequest_requesterId_fkey" FOREIGN KEY ("requesterId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DataPromotionRequest" ADD CONSTRAINT "DataPromotionRequest_operatorId_fkey" FOREIGN KEY ("operatorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Create function for history triggers
CREATE OR REPLACE FUNCTION history_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF (TG_OP = 'DELETE') THEN
        INSERT INTO public."PrismaModelHistory" ("modelData", "modelId", "model", "versionDate")
        VALUES (null, OLD.id, TG_TABLE_NAME, CURRENT_TIMESTAMP)
        ON CONFLICT ("modelId", "model", "versionDate") DO UPDATE
        SET "modelData" = EXCLUDED."modelData";
    ELSIF (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') THEN
        INSERT INTO public."PrismaModelHistory" ("modelData", "modelId", "model", "versionDate")
        VALUES (to_jsonb(NEW), NEW.id, TG_TABLE_NAME, CURRENT_TIMESTAMP)
        ON CONFLICT ("modelId", "model", "versionDate") DO UPDATE
        SET "modelData" = EXCLUDED."modelData";
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- History triggers
CREATE OR REPLACE TRIGGER ApiKey_history_trigger
AFTER INSERT OR UPDATE OR DELETE ON public."ApiKey"
FOR EACH ROW
EXECUTE FUNCTION history_trigger_function();

CREATE OR REPLACE TRIGGER LLMModel_history_trigger
AFTER INSERT OR UPDATE OR DELETE ON public."LLMModel"
FOR EACH ROW
EXECUTE FUNCTION history_trigger_function();
