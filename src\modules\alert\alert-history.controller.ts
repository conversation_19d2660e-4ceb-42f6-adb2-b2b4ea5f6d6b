import { Controller, Get, Query } from '@nestjs/common';
import { AlertHistoryService } from './alert-history.service';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { WherePipe } from 'src/pipes/where.pipe';
import { Scopes } from '../auth/scope.decorator';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@Controller('/alert-history')
@ApiBearerAuth('bearer-auth')
@ApiTags('Alert History')
export class AlertHistoryController {
  constructor(private alertHistoryService: AlertHistoryService) {}

  @Get()
  @Scopes('system:read-alert-history')
  async findAll(
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
  ) {
    const list = await this.alertHistoryService.findAll({
      skip,
      take,
      where,
    });
    const count = await this.alertHistoryService.count(where);
    return {
      list,
      count,
    };
  }
}
