do
$$
declare needPatchData RECORD;
declare patchValue int;
begin  
    for needPatchData in 
	    select * from (
	select "groupId","flowId","callingType", "callingBy", "engineSlug",  "feature","key","callingAttributes", count(*) as "c"  FROM "SummaryAll"
	group by "groupId","flowId","callingType", "callingBy", "engineSlug",  "feature","key","callingAttributes"
	order by "c" desc
	) as summay
	where c > 1
loop 
	patchValue :=(select sum(value) from "Summary" sa where sa."groupId" = needPatchData."groupId" and 
	sa."flowId"  = needPatchData."flowId" and sa."callingType" = needPatchData."callingType" 
	and sa."callingBy" = needPatchData."callingBy" and sa."engineSlug" = needPatchData."engineSlug"
	and sa."feature" = needPatchData."feature" and sa."key"  = needPatchData."key" and  case when needPatchData."callingAttributes" is null then 
	sa."callingAttributes" is null when needPatchData."callingAttributes" is not null then sa."callingAttributes"  = needPatchData."callingAttributes" end);
	delete from "SummaryAll" sa where sa."groupId" = needPatchData."groupId" and 
	sa."flowId"  = needPatchData."flowId" and sa."callingType" = needPatchData."callingType" 
	and sa."callingBy" = needPatchData."callingBy" and sa."engineSlug" = needPatchData."engineSlug"
	and sa."feature" = needPatchData."feature" and sa."key"  = needPatchData."key" and  case when needPatchData."callingAttributes" is null then 
	sa."callingAttributes" is null when needPatchData."callingAttributes" is not null then sa."callingAttributes"  = needPatchData."callingAttributes" end;
	insert into "SummaryAll"("groupId","flowId","callingType", "callingBy", "engineSlug",  "feature","key","callingAttributes", value)
	values(needPatchData."groupId",needPatchData."flowId",needPatchData."callingType",needPatchData."callingBy",needPatchData."engineSlug",needPatchData."feature",
     needPatchData."key",needPatchData."callingAttributes" ,patchValue);
end loop;

end;
$$