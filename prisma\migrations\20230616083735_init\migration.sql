-- CreateEnum
CREATE TYPE "FileStatus" AS ENUM ('UPLOADED', 'PROCESSING', 'COMPLETED');

-- CreateEnum
CREATE TYPE "AccessLevel" AS ENUM ('ADMIN', 'USER');

-- CreateEnum
CREATE TYPE "Gender" AS ENUM ('MALE', 'FEMALE', 'NONBINARY', 'UNKNOWN');

-- CreateEnum
CREATE TYPE "NotificationEmail" AS ENUM ('ACCOUNT', 'UPDATES', 'PROMOTIONS');

-- CreateEnum
CREATE TYPE "PrefersColorScheme" AS ENUM ('NO_PREFERENCE', 'LIGHT', 'DARK');

-- C<PERSON><PERSON>num
CREATE TYPE "PrefersReducedMotion" AS ENUM ('NO_PREFERENCE', 'REDUCE');

-- CreateEnum
CREATE TYPE "UserRole" AS ENUM ('SUDO', 'USER');

-- CreateEnum
CREATE TYPE "MfaMethod" AS ENUM ('NONE', 'SMS', 'TOTP', 'EMAIL');

-- CreateEnum
CREATE TYPE "MembershipRole" AS ENUM ('OWNER', 'ADMIN', 'MEMBER');

-- CreateEnum
CREATE TYPE "IdentityType" AS ENUM ('GOOGLE', 'APPLE', 'SLACK');

-- CreateTable
CREATE TABLE "User" (
    "checkLocationOnLogin" BOOLEAN NOT NULL DEFAULT false,
    "countryCode" TEXT NOT NULL DEFAULT 'hk',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "gender" "Gender" NOT NULL DEFAULT 'UNKNOWN',
    "id" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "notificationEmail" "NotificationEmail" NOT NULL DEFAULT 'ACCOUNT',
    "password" TEXT,
    "prefersLanguage" TEXT NOT NULL DEFAULT 'en-us',
    "prefersColorScheme" "PrefersColorScheme" NOT NULL DEFAULT 'NO_PREFERENCE',
    "prefersReducedMotion" "PrefersReducedMotion" NOT NULL DEFAULT 'NO_PREFERENCE',
    "prefersEmailId" INTEGER,
    "profilePictureUrl" TEXT NOT NULL DEFAULT 'https://unavatar.now.sh/fallback.png',
    "role" "UserRole" NOT NULL DEFAULT 'USER',
    "timezone" TEXT NOT NULL DEFAULT 'America/Los_Angeles',
    "twoFactorMethod" "MfaMethod" NOT NULL DEFAULT 'NONE',
    "twoFactorPhone" TEXT,
    "twoFactorSecret" TEXT,
    "attributes" JSONB,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Group" (
    "autoJoinDomain" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "forceTwoFactor" BOOLEAN NOT NULL DEFAULT false,
    "id" INTEGER NOT NULL,
    "ipRestrictions" TEXT,
    "name" TEXT NOT NULL,
    "onlyAllowDomain" BOOLEAN NOT NULL DEFAULT false,
    "profilePictureUrl" TEXT NOT NULL DEFAULT 'https://unavatar.now.sh/fallback.png',
    "attributes" JSONB,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "parentId" INTEGER,

    CONSTRAINT "Group_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Email" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "email" TEXT NOT NULL,
    "emailSafe" TEXT NOT NULL,
    "id" SERIAL NOT NULL,
    "isVerified" BOOLEAN NOT NULL DEFAULT false,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" INTEGER NOT NULL,

    CONSTRAINT "Email_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ApiKey" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "description" TEXT,
    "id" SERIAL NOT NULL,
    "ipRestrictions" JSONB,
    "apiKey" TEXT NOT NULL,
    "name" TEXT,
    "groupId" INTEGER,
    "referrerRestrictions" JSONB,
    "scopes" JSONB,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" INTEGER,

    CONSTRAINT "ApiKey_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ApprovedSubnet" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" SERIAL NOT NULL,
    "subnet" TEXT NOT NULL,
    "city" TEXT,
    "region" TEXT,
    "timezone" TEXT,
    "countryCode" TEXT,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" INTEGER NOT NULL,

    CONSTRAINT "ApprovedSubnet_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BackupCode" (
    "id" SERIAL NOT NULL,
    "code" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "isUsed" BOOLEAN NOT NULL DEFAULT false,
    "userId" INTEGER NOT NULL,

    CONSTRAINT "BackupCode_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CouponCode" (
    "id" SERIAL NOT NULL,
    "code" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "expiresAt" TIMESTAMP(3),
    "maxUses" INTEGER NOT NULL DEFAULT 1000,
    "usedCount" INTEGER NOT NULL DEFAULT 0,
    "teamRestrictions" TEXT,
    "amount" DECIMAL(65,30) NOT NULL DEFAULT 0.00,
    "currency" TEXT NOT NULL,
    "description" TEXT,

    CONSTRAINT "CouponCode_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Domain" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "domain" TEXT NOT NULL,
    "id" SERIAL NOT NULL,
    "isVerified" BOOLEAN NOT NULL DEFAULT false,
    "groupId" INTEGER NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "verificationCode" TEXT NOT NULL,

    CONSTRAINT "Domain_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Identity" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" SERIAL NOT NULL,
    "loginName" TEXT NOT NULL,
    "type" "IdentityType" NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" INTEGER NOT NULL,

    CONSTRAINT "Identity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Membership" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" SERIAL NOT NULL,
    "groupId" INTEGER NOT NULL,
    "role" "MembershipRole" NOT NULL DEFAULT 'MEMBER',
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" INTEGER NOT NULL,

    CONSTRAINT "Membership_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Session" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" SERIAL NOT NULL,
    "ipAddress" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userAgent" TEXT,
    "city" TEXT,
    "region" TEXT,
    "timezone" TEXT,
    "countryCode" TEXT,
    "browser" TEXT,
    "operatingSystem" TEXT,
    "userId" INTEGER NOT NULL,

    CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Webhook" (
    "contentType" TEXT NOT NULL DEFAULT 'application/json',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "event" TEXT NOT NULL,
    "id" SERIAL NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT false,
    "lastFiredAt" TIMESTAMP(3),
    "groupId" INTEGER NOT NULL,
    "secret" TEXT,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "url" TEXT NOT NULL,

    CONSTRAINT "Webhook_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AuditLog" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "event" TEXT NOT NULL,
    "rawEvent" TEXT NOT NULL,
    "id" SERIAL NOT NULL,
    "groupId" INTEGER,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" INTEGER,
    "apiKeyId" INTEGER,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "city" TEXT,
    "region" TEXT,
    "timezone" TEXT,
    "countryCode" TEXT,
    "browser" TEXT,
    "operatingSystem" TEXT,

    CONSTRAINT "AuditLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LLMModel" (
    "id" SERIAL NOT NULL,
    "modelId" VARCHAR(100) NOT NULL,
    "name" VARCHAR(200) NOT NULL,
    "tone" TEXT,
    "startupMessage" TEXT,
    "groupId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "description" TEXT,
    "modelEngine" VARCHAR(200) DEFAULT 'gpt-35-turbo',

    CONSTRAINT "LLMModel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserAccessOnLLMModels" (
    "modelId" INTEGER NOT NULL,
    "userId" INTEGER NOT NULL,
    "accessLevel" "AccessLevel" NOT NULL DEFAULT 'USER',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserAccessOnLLMModels_pkey" PRIMARY KEY ("modelId","userId","accessLevel")
);

-- CreateTable
CREATE TABLE "ModelFile" (
    "id" SERIAL NOT NULL,
    "filename" TEXT NOT NULL,
    "filetype" TEXT,
    "summary" TEXT,
    "description" TEXT,
    "modelId" INTEGER,
    "groupId" INTEGER,
    "uploaderId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "docId" TEXT NOT NULL,
    "status" "FileStatus" DEFAULT 'UPLOADED',

    CONSTRAINT "ModelFile_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WhiteList" (
    "id" SERIAL NOT NULL,
    "email" VARCHAR(300) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "WhiteList_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "prefersEmailId" ON "User"("prefersEmailId");

-- CreateIndex
CREATE INDEX "parentId" ON "Group"("parentId");

-- CreateIndex
CREATE UNIQUE INDEX "Email.email_unique" ON "Email"("email");

-- CreateIndex
CREATE UNIQUE INDEX "Email.emailSafe_unique" ON "Email"("emailSafe");

-- CreateIndex
CREATE INDEX "Email.userId_index" ON "Email"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "ApiKey.apiKey_unique" ON "ApiKey"("apiKey");

-- CreateIndex
CREATE INDEX "ApiKey.groupId_index" ON "ApiKey"("groupId");

-- CreateIndex
CREATE INDEX "ApiKey.userId_index" ON "ApiKey"("userId");

-- CreateIndex
CREATE INDEX "ApprovedSubnet.userId_index" ON "ApprovedSubnet"("userId");

-- CreateIndex
CREATE INDEX "BackupCode.userId_index" ON "BackupCode"("userId");

-- CreateIndex
CREATE INDEX "Domain.groupId_index" ON "Domain"("groupId");

-- CreateIndex
CREATE INDEX "Identity.userId_index" ON "Identity"("userId");

-- CreateIndex
CREATE INDEX "Membership.groupId_index" ON "Membership"("groupId");

-- CreateIndex
CREATE INDEX "Membership.userId_index" ON "Membership"("userId");

-- CreateIndex
CREATE INDEX "Session.userId_index" ON "Session"("userId");

-- CreateIndex
CREATE INDEX "Webhook.groupId_index" ON "Webhook"("groupId");

-- CreateIndex
CREATE INDEX "apiKeyId" ON "AuditLog"("apiKeyId");

-- CreateIndex
CREATE INDEX "groupId" ON "AuditLog"("groupId");

-- CreateIndex
CREATE INDEX "userId" ON "AuditLog"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "LLMModel.modelId_unique" ON "LLMModel"("modelId");

-- CreateIndex
CREATE INDEX "LLMModel.modelId_index" ON "LLMModel"("modelId");

-- CreateIndex
CREATE INDEX "UserAccessOnLLMModels.modelId_userId_accessLevel_index" ON "UserAccessOnLLMModels"("modelId", "userId", "accessLevel");

-- CreateIndex
CREATE UNIQUE INDEX "ModelFile.docId_unique" ON "ModelFile"("docId");

-- CreateIndex
CREATE INDEX "files_filename" ON "ModelFile"("filename");

-- CreateIndex
CREATE INDEX "doc_id" ON "ModelFile"("docId");

-- CreateIndex
CREATE INDEX "files_groupId" ON "ModelFile"("groupId");

-- CreateIndex
CREATE UNIQUE INDEX "WhiteList_email_idx" ON "WhiteList"("email");

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_prefersEmailId_fkey" FOREIGN KEY ("prefersEmailId") REFERENCES "Email"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Group" ADD CONSTRAINT "Group_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "Group"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Email" ADD CONSTRAINT "Email_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApiKey" ADD CONSTRAINT "ApiKey_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApiKey" ADD CONSTRAINT "ApiKey_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApprovedSubnet" ADD CONSTRAINT "ApprovedSubnet_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BackupCode" ADD CONSTRAINT "BackupCode_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Domain" ADD CONSTRAINT "Domain_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Identity" ADD CONSTRAINT "Identity_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Membership" ADD CONSTRAINT "Membership_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Membership" ADD CONSTRAINT "Membership_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Webhook" ADD CONSTRAINT "Webhook_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_apiKeyId_fkey" FOREIGN KEY ("apiKeyId") REFERENCES "ApiKey"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LLMModel" ADD CONSTRAINT "LLMModel_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserAccessOnLLMModels" ADD CONSTRAINT "UserAccessOnLLMModels_modelId_fkey" FOREIGN KEY ("modelId") REFERENCES "LLMModel"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserAccessOnLLMModels" ADD CONSTRAINT "UserAccessOnLLMModels_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ModelFile" ADD CONSTRAINT "ModelFile_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ModelFile" ADD CONSTRAINT "ModelFile_modelId_fkey" FOREIGN KEY ("modelId") REFERENCES "LLMModel"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ModelFile" ADD CONSTRAINT "ModelFile_uploaderId_fkey" FOREIGN KEY ("uploaderId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
