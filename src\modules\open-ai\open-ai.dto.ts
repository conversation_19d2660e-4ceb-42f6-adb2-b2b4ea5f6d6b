import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsOptional, IsString } from 'class-validator';
import {
  ChatCompletionAudioParam,
  ChatCompletionCreateParams,
  ChatCompletionFunctionCallOption,
  ChatCompletionMessageParam,
  ChatCompletionModality,
  ChatCompletionPredictionContent,
  ChatCompletionReasoningEffort,
  ChatCompletionStreamOptions,
  ChatCompletionTool,
  ChatCompletionToolChoiceOption,
  Metadata,
  ResponseFormatJSONObject,
  ResponseFormatJSONSchema,
  ResponseFormatText,
} from 'openai/resources';
import { ChatCompletionCreateParamsBase } from 'openai/resources/chat/completions';

export class ChatCompletionCreateParamsDto implements ChatCompletionCreateParamsBase {
  @IsArray()
  @IsNotEmpty()
  messages: ChatCompletionMessageParam[];

  @IsString()
  @IsNotEmpty()
  model: string;

  @IsOptional()
  audio?: ChatCompletionAudioParam;

  @IsOptional()
  frequency_penalty?: number;

  @IsOptional()
  function_call?: 'none' | 'auto' | ChatCompletionFunctionCallOption;

  @IsOptional()
  functions?: ChatCompletionCreateParams.Function[];

  @IsOptional()
  logit_bias?: Record<string, number>;

  @IsOptional()
  logprobs?: boolean;

  @IsOptional()
  max_completion_tokens?: number;

  @IsOptional()
  max_tokens?: number;

  @IsOptional()
  metadata?: Metadata;

  @IsOptional()
  modalities?: ChatCompletionModality[];

  @IsOptional()
  n?: number;

  @IsOptional()
  parallel_tool_calls?: boolean;

  @IsOptional()
  prediction?: ChatCompletionPredictionContent;

  @IsOptional()
  presence_penalty?: number;

  @IsOptional()
  reasoning_effort?: ChatCompletionReasoningEffort;

  @IsOptional()
  response_format?: ResponseFormatText | ResponseFormatJSONObject | ResponseFormatJSONSchema;

  @IsOptional()
  seed?: number;

  @IsOptional()
  service_tier?: 'default' | 'auto';

  @IsOptional()
  stop?: string | string[];

  @IsOptional()
  store?: boolean;

  @IsOptional()
  stream_options?: ChatCompletionStreamOptions;

  @IsOptional()
  temperature?: number;

  @IsOptional()
  tool_choice?: ChatCompletionToolChoiceOption;

  @IsOptional()
  tools?: ChatCompletionTool[];

  @IsOptional()
  top_logprobs?: number;

  @IsOptional()
  top_p?: number;

  @IsOptional()
  user?: string;

  @IsOptional()
  stream?: boolean;
}
