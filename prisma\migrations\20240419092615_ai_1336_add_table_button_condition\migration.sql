-- CreateEnum
CREATE TYPE "HasPii" AS ENUM ('YES', 'NO', 'PENDING', 'ERROR');

-- CreateTable
CREATE TABLE "ButtonCondition" (
    "id" SERIAL NOT NULL,
    "isApproved" BOOLEAN,
    "status" "FileStatus",
    "hasPII" "HasPii",
    "isRequireSecondaryApproval" BOOLEAN,
    "fileClassification" "FileClassification",
    "roleId" INTEGER NOT NULL,
    "buttonList" TEXT[],
    "env" "Environment" NOT NULL DEFAULT 'TEST',

    CONSTRAINT "ButtonCondition_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ButtonCondition_isApproved_status_hasPII_isRequireSecondary_key" ON "ButtonCondition"("isApproved", "status", "hasPII", "isRequireSecondaryApproval", "fileClassification", "roleId", "env");
