import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsInt, IsOptional, IsString, Max, Min } from 'class-validator';

export enum BatchProcessStatus {
  PENDING = 'PENDING',
  PREPROCESSING = 'PREPROCESSING',
  PROCESSING = 'PROCESSING',
  POSTPROCESSING = 'POSTPROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

export class ListBatchProcessesDto {
  @ApiPropertyOptional({
    description: 'Number of records to skip for pagination',
    minimum: 0,
    type: Number,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Transform(({ value }) => parseInt(value, 10))
  skip?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of records to return',
    default: 10,
    maximum: 100,
    type: Number,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => parseInt(value, 10))
  take?: number = 10;

  @ApiPropertyOptional({
    description: 'Field to sort by',
    example: 'createdAt',
    type: String,
  })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['asc', 'desc'], // Lowercase as per example
    default: 'desc',
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc'; // Lowercase as per example

  @ApiPropertyOptional({
    description: 'Filter by batch process status',
    enum: BatchProcessStatus,
  })
  @IsOptional()
  @IsString() // Keep as string for validation flexibility
  status?: BatchProcessStatus;
}
