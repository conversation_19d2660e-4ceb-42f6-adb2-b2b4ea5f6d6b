-- This is an empty migration.
UPDATE "Permission" SET "permissionKey" = 'system:create-bot', "description" = 'Create bot' WHERE "permissionKey" = 'system:create-group';

INSERT INTO "Permission" ("createdAt", "description", "permissionKey", "permissionType", "envs") VALUES (NOW(), 'Create flow', 'system:create-flow', 'SYSTEM', '{TEST,PROD}');

INSERT INTO "RolePermission" ( "roleId", "permissionId") 
SELECT * FROM ( VALUES (
(SELECT id FROM "Role" WHERE "systemName" = 'SUDO' AND "roleType" = 'SYSTEM_DEFAULT'),
(SELECT id FROM "Permission" WHERE "permissionKey" = 'system:create-flow')))
AS v("roleId", "permissionId") WHERE v."roleId" IS NOT NULL AND v."permissionId" IS NOT NULL;


INSERT INTO "Permission" ("createdAt", "description", "permissionKey", "permissionType", "envs") VALUES (NOW(), 'Read resource plan', 'system:read-resource-plan', 'SYSTEM', '{TEST,PROD}');

INSERT INTO "Permission" ("createdAt", "description", "permissionKey", "permissionType", "envs") VALUES (NOW(), 'Write resource plan', 'system:write-resource-plan', 'SYSTEM', '{TEST,PROD}');

INSERT INTO "RolePermission" ("roleId", "permissionId") 
SELECT * FROM ( VALUES (
(SELECT id FROM "Role" WHERE "systemName" = 'SUDO' AND "roleType" = 'SYSTEM_DEFAULT'),
(SELECT id FROM "Permission" WHERE "permissionKey" = 'system:write-resource-plan')))
AS v("roleId", "permissionId") WHERE v."roleId" IS NOT NULL AND v."permissionId" IS NOT NULL;

INSERT INTO "RolePermission" ("roleId", "permissionId") 
SELECT * FROM ( VALUES (
(SELECT id FROM "Role" WHERE "systemName" = 'SUDO' AND "roleType" = 'SYSTEM_DEFAULT'),
(SELECT id FROM "Permission" WHERE "permissionKey" = 'system:read-resource-plan')))
AS v("roleId", "permissionId") WHERE v."roleId" IS NOT NULL AND v."permissionId" IS NOT NULL;

