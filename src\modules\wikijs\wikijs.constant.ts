export const wikiJSPermission = [
  {
    role: 'EDITOR',
    permissionList: [
      'read:pages',
      'write:pages',
      'manage:pages',
      'delete:pages',
      'read:history',
      'read:assets',
      'write:assets',
      'manage:assets',
      'write:styles',
      'read:comments',
      'write:comments',
      'manage:comments',
    ],
  },
  {
    role: 'VIEWER',
    permissionList: ['read:pages', 'read:history', 'read:assets', 'read:comments'],
  },
];
