import { LabelType } from '@prisma/client';
import {
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
  ValidateIf,
} from 'class-validator';

export class PatchLabelsDto {
  @IsNumber()
  @IsOptional()
  id?: number;

  @IsString()
  @IsNotEmpty()
  @ValidateIf((o) => o.actionType === 'CREATE' && o.id === undefined)
  name?: string;

  @MinLength(3)
  @ValidateIf(
    (o) => o.actionType === 'CREATE' && o.id === undefined && o.labelType === LabelType.LABELS,
  )
  color?: string | null;

  @IsOptional()
  desc?: string;

  @IsEnum(LabelType)
  @ValidateIf((o) => o.actionType === 'CREATE' && o.id === undefined)
  labelType?: LabelType;

  @ValidateIf((o) => o.actionType === 'REMOVE')
  @IsNumber()
  entityLabelsId?: number;

  @IsIn(['CREATE', 'REMOVE'])
  actionType: 'CREATE' | 'REMOVE';
}

export class UpdateLabelsDto {
  @IsString()
  @IsOptional()
  name?: string;

  @MinLength(3)
  @IsOptional()
  color?: string | null;

  @IsOptional()
  desc?: string;

  @IsOptional()
  seq?: number;
}
