import {
  Bad<PERSON>e<PERSON>Exception,
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Group, LLMModel } from '@prisma/client';
import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';
import { Observable } from 'rxjs';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { UserRequest } from 'src/modules/auth/auth.interface';
import { BotSecurityService } from 'src/modules/bot-security/bot-security.service';
import { FeatureFlagKey } from 'src/modules/feature-flags/feature-flags.constants';
import { FeatureFlagService } from 'src/modules/feature-flags/feature-flags.service';
import { FlowChatRequest } from 'src/modules/flows/flows.dto';
import { GroupsService } from 'src/modules/groups/groups.service';
import { LlmEnginesService } from 'src/modules/llm-engines/llm-engines.service';
import { ChatLlmModelDto } from 'src/modules/llm-models/dto/chat-llm-model.dto';
import { LLMModelsService } from 'src/modules/llm-models/llm-models.service';
import { ChatSessionsService } from '../chat-sessions.service';

@Injectable()
export class ChatRequestInterceptor implements NestInterceptor {
  private readonly llmParamsKey = 'llmParams';
  private readonly chatLevelBotSettingKey = 'botSetting';

  constructor(
    private readonly llmModelsService: LLMModelsService,
    private readonly chatSessionsService: ChatSessionsService,
    private readonly botSecurityService: BotSecurityService,
    private readonly groupsServices: GroupsService,
    private readonly llmEnginesService: LlmEnginesService,
    private readonly featureFlagService: FeatureFlagService,
  ) {}
  // the checking function call must be -> validateChatRequestBody -> piiScanUserQuestion -> applyLlmParams -> checkLLmEnginIsActive
  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<unknown>> {
    // before chat
    const request = context.switchToHttp().getRequest() as UserRequest;
    await this.validateChatRequestBody(request);
    const groupId = parseInt(request.params?.['groupId']);
    const group = await this.groupsServices.getGroup(groupId, {});
    const llmModel = await this.llmModelsService.findOneByGroupId(groupId);
    await this.piiScanUserQuestion(request, groupId);
    // the flow can't use LLM model by self setting
    if (group.groupType === 'FLOW') {
      return next.handle();
    }

    if (llmModel.modelEngine === 'sd-txt2img') {
      // stableDiffusionParams will not be consider here, since this interceptor is for /chat API
      return next.handle();
    }
    await this.applyLlmParams(request, llmModel, group);
    await this.checkLLmEnginIsActive(request);
    request.body.llmModel = llmModel;
    return next.handle();
  }

  private async validateChatRequestBody(req: UserRequest) {
    let chatRequest;
    if (req.path.includes('flows')) {
      chatRequest = plainToClass(FlowChatRequest, req.body);
    } else {
      chatRequest = plainToClass(ChatLlmModelDto, req.body);
    }
    const errors = await validate(chatRequest);
    if (chatRequest.overrides instanceof Array) {
      throw new ApiException(ErrorCode.INVALID_CHAT_OVERRIDES);
    }

    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }
    //if security_scan is ON,PROMPT_INJECTION, ANONYMIZE ,check prompt PII
    //if have no security_scan Field ,check botSetting prompt PII
    const groupId = req.params?.['groupId'];
    if (!groupId || Number.isNaN(parseInt(groupId))) {
      throw new ApiException(ErrorCode.SELECT_INCLUDE_PIPE_FORMAT);
    }
  }

  private async checkLLmEnginIsActive(request: UserRequest) {
    const llmEngine = await this.llmEnginesService.findLlmEngine(
      request.body.overrides.model,
    );
    if (!llmEngine.isActive) {
      throw new ApiException(
        ErrorCode.LLM_ENGINE_IS_NOT_ACTIVE.replaceAll('{engine}', llmEngine.slug),
      );
    }
  }

  private async piiScanUserQuestion(request: UserRequest, groupId: number) {
    const userAsk = request.body.history.filter((item) => item.role === 'user');
    if (!userAsk) {
      throw new ApiException(ErrorCode.INVALID_CHAT_MESSAGE_FORMAT);
    }
    const isChatEnablePIIScan = await this.botSecurityService.isChatEnablePIIScan(
      request.body.overrides,
      request.body.channel,
    );
    if (isChatEnablePIIScan) {
      const prompt = userAsk.at(-1);
      const piiReuslt = await this.botSecurityService.PromptDetect(
        prompt.content,
        groupId,
        request.user.id,
        request.body.overrides?.security_scan,
      );
      request.body['inputScannersResult'] = { ...piiReuslt };
    }
  }

  /**
   * @description this function will apply the llm default parameters in the chat request .if the user use the chat session will apply chat session parameters
   * @param req
   * @param llmModel
   */
  private async applyLlmParams(req: UserRequest, llmModel: LLMModel, group: Group) {
    const parameters = llmModel.parameters?.[this.llmParamsKey] ?? {};
    const overrides = req.body.overrides ?? {};
    let promptTemplate = await this.concatLLmToneAndTypeDefinition(
      overrides?.prompt_template ? overrides?.prompt_template : llmModel.tone,
      llmModel?.typeDefinition,
    );

    let defaultBotLevelOverrides = {
      ...parameters,
      show_reference: llmModel.showReference ?? true,
      model: llmModel.modelEngine,
    };

    if (req.body.chatSessionId) {
      // find chat session
      const chatSession = await this.chatSessionsService.findChatSessionByIdAndUserId(
        req.body.chatSessionId,
        req.user.id,
      );
      if (!chatSession) {
        throw new ApiException(ErrorCode.CHAT_SESSIOIN_NOT_FOUND);
      }
      if (chatSession.chatSessionType == 'PUBLIC') {
        const iPublicChatAndCheckIsValidate =
          group.groupType == 'BOT' &&
          ((llmModel.active && llmModel.makeLiveToPublic) || group.env == 'TEST');
        if (!iPublicChatAndCheckIsValidate) {
          throw new ApiException(ErrorCode.INVAILD_PUBLIC_CHAT);
        }
      }
      if (!chatSession.isDefault) {
        // if chat session not default, substitute missing config by chat setting
        const chatLevelBotSetting = chatSession.chatSetting?.[this.chatLevelBotSettingKey];
        const chatLevelLlmParams = chatSession.chatSetting?.[this.llmParamsKey] ?? {};
        promptTemplate = await this.concatLLmToneAndTypeDefinition(
          overrides?.prompt_template ? overrides?.prompt_template : chatLevelBotSetting?.tone,
          chatLevelBotSetting?.typeDefinition,
        );
        defaultBotLevelOverrides = {
          ...chatLevelLlmParams,
          model: chatLevelBotSetting?.modelEngine,
          show_reference: chatLevelBotSetting?.showReference ?? true,
        };
      }
    }
    const llmEngineDefaultParamSetting =
      await this.llmEnginesService.getLlmEngineDefaultParamSetting(
        overrides.model ?? defaultBotLevelOverrides.model,
      );
    req.body.overrides = {
      ...llmEngineDefaultParamSetting,
      ...defaultBotLevelOverrides,
      ...overrides,
      prompt_template: promptTemplate,
    };
  }

  /**
   * @description this function will concat the openai system message and typeDefinition. it will append TYPE_DEFINITION_PROMPT featureFlag as typeDefinition before.
   * @param tone
   * @param typeDefinition
   * @returns
   */
  private async concatLLmToneAndTypeDefinition(tone?: string, typeDefinition?: string) {
    const systemMessage = tone ?? '';
    if (!typeDefinition || typeDefinition.trim().length === 0) {
      return systemMessage;
    }
    const typeDefinitionPrompt = await this.featureFlagService.getOne(
      FeatureFlagKey.TYPE_DEFINITION_PROMPT,
    );
    if (!typeDefinitionPrompt?.isEnabled) {
      return systemMessage + typeDefinition;
    }
    return systemMessage + (typeDefinitionPrompt?.metaData?.['value'] ?? '') + typeDefinition;
  }
}
