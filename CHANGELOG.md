## v4.0.9 (2021-03-15)

[📝 Release notes](https://github.com/staart/api/releases/tag/v4.0.9) · [💻 Compare](https://github.com/staart/api/compare/v4.0.8...v4.0.9) · [🔖 Tag](https://github.com/staart/api/tree/v4.0.9) · 🗄️ Archive ([zip](https://github.com/staart/api/archive/v4.0.9.zip) · [tar.gz](https://github.com/staart/api/archive/v4.0.9.tar.gz))

### ⬆️ Dependency updates

- [`6982a103`](https://github.com/staart/api/commit/6982a103)  Bump @types/node from 14.14.30 to 14.14.32

## v4.0.8 (2021-03-01)

[📝 Release notes](https://github.com/staart/api/releases/tag/v4.0.8) · [💻 Compare](https://github.com/staart/api/compare/v4.0.7...v4.0.8) · [🔖 Tag](https://github.com/staart/api/tree/v4.0.8) · 🗄️ Archive ([zip](https://github.com/staart/api/archive/v4.0.8.zip) · [tar.gz](https://github.com/staart/api/archive/v4.0.8.tar.gz))

### ⬆️ Dependency updates

- [`e60fd38e`](https://github.com/staart/api/commit/e60fd38e)  Bump @nestjs/core from 7.6.11 to 7.6.13
- [`2ce3004a`](https://github.com/staart/api/commit/2ce3004a)  Bump aws-sdk from 2.846.0 to 2.849.0
- [`8c411507`](https://github.com/staart/api/commit/8c411507)  Bump actions/setup-node from v2.1.4 to v2.1.5
- [`dbb3b74c`](https://github.com/staart/api/commit/dbb3b74c)  Bump p-retry from 4.3.0 to 4.4.0
- [`f64a5110`](https://github.com/staart/api/commit/f64a5110)  Bump @nestjs/cli from 7.5.4 to 7.5.5
- [`bcae7edd`](https://github.com/staart/api/commit/bcae7edd)  Bump @typescript-eslint/parser from 4.15.0 to 4.15.2
- [`691fd32f`](https://github.com/staart/api/commit/691fd32f)  Bump aws-sdk from 2.849.0 to 2.850.0

## v4.0.7 (2021-02-22)

[📝 Release notes](https://github.com/staart/api/releases/tag/v4.0.7) · [💻 Compare](https://github.com/staart/api/compare/v4.0.6...v4.0.7) · [🔖 Tag](https://github.com/staart/api/tree/v4.0.7) · 🗄️ Archive ([zip](https://github.com/staart/api/archive/v4.0.7.zip) · [tar.gz](https://github.com/staart/api/archive/v4.0.7.tar.gz))

### ⬆️ Dependency updates

- [`ae120229`](https://github.com/staart/api/commit/ae120229)  Bump @typescript-eslint/eslint-plugin from 4.14.2 to 4.15.0
- [`866477fa`](https://github.com/staart/api/commit/866477fa)  Bump vsoch/pull-request-action from 1.0.13 to 1.0.14
- [`6611b210`](https://github.com/staart/api/commit/6611b210)  Bump pascalgn/automerge-action from v0.13.0 to v0.13.1
- [`5e9246cb`](https://github.com/staart/api/commit/5e9246cb)  Bump @types/puppeteer from 5.4.2 to 5.4.3
- [`11795072`](https://github.com/staart/api/commit/11795072)  Bump @types/node from 14.14.25 to 14.14.28
- [`fd3d507d`](https://github.com/staart/api/commit/fd3d507d)  Bump @octokit/rest from 18.1.0 to 18.1.1
- [`b04fc4ec`](https://github.com/staart/api/commit/b04fc4ec)  Bump typescript from 4.1.3 to 4.1.5
- [`f3455510`](https://github.com/staart/api/commit/f3455510)  Bump vsoch/pull-request-action from 1.0.14 to 1.0.15
- [`0330ddd0`](https://github.com/staart/api/commit/0330ddd0)  Bump @nestjs/swagger from 4.7.12 to 4.7.13
- [`370a2194`](https://github.com/staart/api/commit/370a2194)  Bump @nestjs/testing from 7.6.11 to 7.6.12
- [`d3346c2a`](https://github.com/staart/api/commit/d3346c2a)  Bump @elastic/elasticsearch from 7.10.0 to 7.11.0
- [`04d3337d`](https://github.com/staart/api/commit/04d3337d)  Bump ua-parser-js from 0.7.23 to 0.7.24
- [`c0b527de`](https://github.com/staart/api/commit/c0b527de)  Bump aws-sdk from 2.834.0 to 2.845.0
- [`a4418322`](https://github.com/staart/api/commit/a4418322)  Bump @nestjs/platform-express from 7.6.11 to 7.6.12
- [`48172884`](https://github.com/staart/api/commit/48172884)  Bump aws-sdk from 2.845.0 to 2.846.0
- [`087fc2af`](https://github.com/staart/api/commit/087fc2af)  Bump crypto-random-string from 3.3.0 to 3.3.1
- [`fa66722d`](https://github.com/staart/api/commit/fa66722d)  Bump @types/fs-extra from 9.0.6 to 9.0.7
- [`7567b09c`](https://github.com/staart/api/commit/7567b09c)  Bump @types/cheerio from 0.22.23 to 0.22.24
- [`7e95a6ab`](https://github.com/staart/api/commit/7e95a6ab)  Bump @types/node from 14.14.28 to 14.14.30
- [`2a6f4ea0`](https://github.com/staart/api/commit/2a6f4ea0)  Bump twilio from 3.55.1 to 3.56.0
- [`a014d424`](https://github.com/staart/api/commit/a014d424)  Bump nunjucks from 3.2.2 to 3.2.3
- [`7218f2ff`](https://github.com/staart/api/commit/7218f2ff)  Bump stripe from 8.135.0 to 8.137.0
- [`18d64b74`](https://github.com/staart/api/commit/18d64b74)  Bump nodemailer from 6.4.17 to 6.4.18
- [`f5009cb7`](https://github.com/staart/api/commit/f5009cb7)  Bump @octokit/rest from 18.1.1 to 18.2.0
- [`39d546b0`](https://github.com/staart/api/commit/39d546b0)  Bump eslint from 7.19.0 to 7.20.0

## v4.0.6 (2021-02-15)

[📝 Release notes](https://github.com/staart/api/releases/tag/v4.0.6) · [💻 Compare](https://github.com/staart/api/compare/v4.0.5...v4.0.6) · [🔖 Tag](https://github.com/staart/api/tree/v4.0.6) · 🗄️ Archive ([zip](https://github.com/staart/api/archive/v4.0.6.zip) · [tar.gz](https://github.com/staart/api/archive/v4.0.6.tar.gz))

### ⬆️ Dependency updates

- [`1a7cad3d`](https://github.com/staart/api/commit/1a7cad3d)  Bump stripe from 8.132.0 to 8.134.0
- [`be45b563`](https://github.com/staart/api/commit/be45b563)  Bump @types/node from 14.14.22 to 14.14.25
- [`74fe75ab`](https://github.com/staart/api/commit/74fe75ab)  Bump @nestjs/core from 7.6.8 to 7.6.11
- [`ae3a30c3`](https://github.com/staart/api/commit/ae3a30c3)  Bump @types/nunjucks from 3.1.3 to 3.1.4
- [`ab248292`](https://github.com/staart/api/commit/ab248292)  Bump @sentry/node from 6.0.4 to 6.1.0
- [`e84a503c`](https://github.com/staart/api/commit/e84a503c)  Bump stripe from 8.134.0 to 8.135.0
- [`5e6ac37e`](https://github.com/staart/api/commit/5e6ac37e)  Bump ts-loader from 8.0.14 to 8.0.16
- [`479707c4`](https://github.com/staart/api/commit/479707c4)  Bump @typescript-eslint/parser from 4.14.2 to 4.15.0

## v4.0.5 (2021-02-08)

[📝 Release notes](https://github.com/staart/api/releases/tag/v4.0.5) · [💻 Compare](https://github.com/staart/api/compare/v4.0.4...v4.0.5) · [🔖 Tag](https://github.com/staart/api/tree/v4.0.5) · 🗄️ Archive ([zip](https://github.com/staart/api/archive/v4.0.5.zip) · [tar.gz](https://github.com/staart/api/archive/v4.0.5.tar.gz))

### ⬆️ Dependency updates

- [`e7ab1d35`](https://github.com/staart/api/commit/e7ab1d35)  Bump @sentry/node from 6.0.2 to 6.0.3
- [`e59eea6a`](https://github.com/staart/api/commit/e59eea6a)  Bump cloudinary from 1.23.0 to 1.24.0
- [`6d3a3fd5`](https://github.com/staart/api/commit/6d3a3fd5)  Bump eslint from 7.18.0 to 7.19.0
- [`e35e5c18`](https://github.com/staart/api/commit/e35e5c18)  Bump ts-jest from 26.4.4 to 26.5.0
- [`0cb227e4`](https://github.com/staart/api/commit/0cb227e4)  Bump @octokit/rest from 18.0.14 to 18.0.15
- [`a55f5155`](https://github.com/staart/api/commit/a55f5155)  Bump @nestjs/testing from 7.6.7 to 7.6.8
- [`a4853571`](https://github.com/staart/api/commit/a4853571)  Bump @sentry/node from 6.0.3 to 6.0.4
- [`937d45a5`](https://github.com/staart/api/commit/937d45a5)  Bump @nestjs/core from 7.6.7 to 7.6.8
- [`c70193f3`](https://github.com/staart/api/commit/c70193f3)  Bump @typescript-eslint/parser from 4.14.1 to 4.14.2
- [`390c9eb7`](https://github.com/staart/api/commit/390c9eb7)  Bump @nestjs/platform-express from 7.6.7 to 7.6.8
- [`954548c3`](https://github.com/staart/api/commit/954548c3)  Bump aws-sdk from 2.833.0 to 2.834.0
- [`342eb12b`](https://github.com/staart/api/commit/342eb12b)  Bump @nestjs/common from 7.6.7 to 7.6.8
- [`3d34dc02`](https://github.com/staart/api/commit/3d34dc02)  Bump @nestjs/common from 7.6.8 to 7.6.11
- [`7382ade4`](https://github.com/staart/api/commit/7382ade4)  Bump @nestjs/testing from 7.6.8 to 7.6.11
- [`22a64eaa`](https://github.com/staart/api/commit/22a64eaa)  Bump @nestjs/platform-express from 7.6.8 to 7.6.11
- [`9facb61d`](https://github.com/staart/api/commit/9facb61d)  Bump @typescript-eslint/eslint-plugin from 4.14.1 to 4.14.2
- [`405b1895`](https://github.com/staart/api/commit/405b1895)  Bump @octokit/rest from 18.0.15 to 18.1.0
- [`e9641a7c`](https://github.com/staart/api/commit/e9641a7c)  Bump @nestjs/config from 0.6.2 to 0.6.3

## v4.0.4 (2021-02-01)

[📝 Release notes](https://github.com/staart/api/releases/tag/v4.0.4) · [💻 Compare](https://github.com/staart/api/compare/v4.0.3...v4.0.4) · [🔖 Tag](https://github.com/staart/api/tree/v4.0.4) · 🗄️ Archive ([zip](https://github.com/staart/api/archive/v4.0.4.zip) · [tar.gz](https://github.com/staart/api/archive/v4.0.4.tar.gz))

### ⬆️ Dependency updates

- [`c039311d`](https://github.com/staart/api/commit/c039311d)  Bump @nestjs/config from 0.6.1 to 0.6.2
- [`810f9e0b`](https://github.com/staart/api/commit/810f9e0b)  Bump aws-sdk from 2.830.0 to 2.831.0
- [`aeb037c9`](https://github.com/staart/api/commit/aeb037c9)  Bump aws-elasticsearch-connector from 9.0.1 to 9.0.3
- [`d281dc80`](https://github.com/staart/api/commit/d281dc80)  Bump @nestjs/schedule from 0.4.1 to 0.4.2
- [`470ad4d6`](https://github.com/staart/api/commit/470ad4d6)  Bump supertest from 6.1.1 to 6.1.2
- [`b4a0dc00`](https://github.com/staart/api/commit/b4a0dc00)  Bump p-retry from 4.2.0 to 4.3.0
- [`7d77fdcb`](https://github.com/staart/api/commit/7d77fdcb)  Bump @nestjs/schematics from 7.2.6 to 7.2.7
- [`7e265f6d`](https://github.com/staart/api/commit/7e265f6d)  Bump @octokit/rest from 18.0.13 to 18.0.14
- [`4bc1ea47`](https://github.com/staart/api/commit/4bc1ea47)  Bump @sentry/node from 6.0.1 to 6.0.2
- [`c2522e92`](https://github.com/staart/api/commit/c2522e92)  Bump supertest from 6.1.2 to 6.1.3
- [`aa8cdb72`](https://github.com/staart/api/commit/aa8cdb72)  Bump @typescript-eslint/parser from 4.14.0 to 4.14.1
- [`35553763`](https://github.com/staart/api/commit/35553763)  Bump @typescript-eslint/eslint-plugin from 4.14.0 to 4.14.1
- [`e9cfda57`](https://github.com/staart/api/commit/e9cfda57)  Bump @nestjs/testing from 7.6.5 to 7.6.6
- [`9c43988d`](https://github.com/staart/api/commit/9c43988d)  Bump @nestjs/common from 7.6.5 to 7.6.6
- [`132933f8`](https://github.com/staart/api/commit/132933f8)  Bump @nestjs/core from 7.6.5 to 7.6.6
- [`86d6d090`](https://github.com/staart/api/commit/86d6d090)  Bump @nestjs/core from 7.6.6 to 7.6.7
- [`87d17644`](https://github.com/staart/api/commit/87d17644)  Bump twilio from 3.55.0 to 3.55.1
- [`1a6bf548`](https://github.com/staart/api/commit/1a6bf548)  Bump @nestjs/platform-express from 7.6.5 to 7.6.7
- [`b158071e`](https://github.com/staart/api/commit/b158071e)  Bump @nestjs/common from 7.6.6 to 7.6.7
- [`838e17e9`](https://github.com/staart/api/commit/838e17e9)  Bump aws-sdk from 2.831.0 to 2.832.0
- [`70f116ac`](https://github.com/staart/api/commit/70f116ac)  Bump @nestjs/testing from 7.6.6 to 7.6.7
- [`3cdb9253`](https://github.com/staart/api/commit/3cdb9253)  Bump aws-sdk from 2.832.0 to 2.833.0

## v4.0.3 (2021-01-25)

[📝 Release notes](https://github.com/staart/api/releases/tag/v4.0.3) · [💻 Compare](https://github.com/staart/api/compare/v4.0.2...v4.0.3) · [🔖 Tag](https://github.com/staart/api/tree/v4.0.3) · 🗄️ Archive ([zip](https://github.com/staart/api/archive/v4.0.3.zip) · [tar.gz](https://github.com/staart/api/archive/v4.0.3.tar.gz))

### ⬆️ Dependency updates

- [`bd0baca6`](https://github.com/staart/api/commit/bd0baca6)  Bump eslint-config-prettier from 7.1.0 to 7.2.0
- [`92b705ad`](https://github.com/staart/api/commit/92b705ad)  Bump @typescript-eslint/parser from 4.13.0 to 4.14.0
- [`06043340`](https://github.com/staart/api/commit/06043340)  Bump helmet from 4.4.0 to 4.4.1
- [`5463fd71`](https://github.com/staart/api/commit/5463fd71)  Bump @typescript-eslint/eslint-plugin from 4.13.0 to 4.14.0
- [`8df7a8c2`](https://github.com/staart/api/commit/8df7a8c2)  Bump aws-sdk from 2.828.0 to 2.829.0
- [`c2022ccd`](https://github.com/staart/api/commit/c2022ccd)  Bump @sentry/node from 5.30.0 to 6.0.0
- [`f5a380f8`](https://github.com/staart/api/commit/f5a380f8)  Bump @types/node from 14.14.21 to 14.14.22
- [`fdf181df`](https://github.com/staart/api/commit/fdf181df)  Bump fs-extra from 9.0.1 to 9.1.0
- [`1e9cfec9`](https://github.com/staart/api/commit/1e9cfec9)  Bump @prisma/cli from 2.14.0 to 2.15.0
- [`b5ef46c5`](https://github.com/staart/api/commit/b5ef46c5)  Bump @googlemaps/google-maps-services-js
- [`616bfb48`](https://github.com/staart/api/commit/616bfb48)  Bump @octokit/rest from 18.0.12 to 18.0.13
- [`6669588a`](https://github.com/staart/api/commit/6669588a)  Bump @sentry/node from 6.0.0 to 6.0.1
- [`8987b207`](https://github.com/staart/api/commit/8987b207)  Bump aws-sdk from 2.829.0 to 2.830.0
- [`c8dd8a62`](https://github.com/staart/api/commit/c8dd8a62)  Bump stripe from 8.131.1 to 8.132.0
- [`e8bdc1e1`](https://github.com/staart/api/commit/e8bdc1e1)  Bump @nestjs/swagger from 4.7.11 to 4.7.12

## v4.0.2 (2021-01-18)

[📝 Release notes](https://github.com/staart/api/releases/tag/v4.0.2) · [💻 Compare](https://github.com/staart/api/compare/v4.0.1...v4.0.2) · [🔖 Tag](https://github.com/staart/api/tree/v4.0.2) · 🗄️ Archive ([zip](https://github.com/staart/api/archive/v4.0.2.zip) · [tar.gz](https://github.com/staart/api/archive/v4.0.2.tar.gz))

### ⬆️ Dependency updates

- [`12511426`](https://github.com/staart/api/commit/12511426)  Bump @types/express from 4.17.9 to 4.17.10
- [`dea87dc5`](https://github.com/staart/api/commit/dea87dc5)  Bump @typescript-eslint/eslint-plugin from 4.12.0 to 4.13.0
- [`3e244c63`](https://github.com/staart/api/commit/3e244c63)  Bump aws-sdk from 2.824.0 to 2.825.0
- [`b2c9f3c4`](https://github.com/staart/api/commit/b2c9f3c4)  Bump @typescript-eslint/parser from 4.12.0 to 4.13.0
- [`27c2a43d`](https://github.com/staart/api/commit/27c2a43d)  Bump aws-sdk from 2.825.0 to 2.826.0
- [`df090c76`](https://github.com/staart/api/commit/df090c76)  Bump @nestjs/swagger from 4.7.9 to 4.7.10
- [`b0227bd9`](https://github.com/staart/api/commit/b0227bd9)  Bump @types/express from 4.17.10 to 4.17.11
- [`d0ad37ca`](https://github.com/staart/api/commit/d0ad37ca)  Bump @slack/web-api from 5.15.0 to 6.0.0
- [`bf60e861`](https://github.com/staart/api/commit/bf60e861)  Bump aws-sdk from 2.826.0 to 2.827.0
- [`bd83ac32`](https://github.com/staart/api/commit/bd83ac32)  Bump twilio from 3.54.2 to 3.55.0
- [`e4688514`](https://github.com/staart/api/commit/e4688514)  Bump @sentry/node from 5.29.2 to 5.30.0
- [`5e37e70b`](https://github.com/staart/api/commit/5e37e70b)  Bump @types/node from 14.14.20 to 14.14.21
- [`3579cbb0`](https://github.com/staart/api/commit/3579cbb0)  Bump @googlemaps/google-maps-services-js
- [`f3ecfad1`](https://github.com/staart/api/commit/f3ecfad1)  Bump stripe from 8.130.0 to 8.131.0
- [`b0c7d6f7`](https://github.com/staart/api/commit/b0c7d6f7)  Bump aws-sdk from 2.827.0 to 2.828.0
- [`172f11b0`](https://github.com/staart/api/commit/172f11b0)  Bump class-transformer from 0.3.1 to 0.3.2
- [`96abb0e2`](https://github.com/staart/api/commit/96abb0e2)  Bump supertest from 6.0.1 to 6.1.1
- [`eb1cfb79`](https://github.com/staart/api/commit/eb1cfb79)  Bump stripe from 8.131.0 to 8.131.1
- [`c5012a6f`](https://github.com/staart/api/commit/c5012a6f)  Bump helmet from 4.3.1 to 4.4.0
- [`0e8c844d`](https://github.com/staart/api/commit/0e8c844d)  Bump @nestjs/swagger from 4.7.10 to 4.7.11
- [`ee45f005`](https://github.com/staart/api/commit/ee45f005)  Bump eslint from 7.17.0 to 7.18.0

## v4.0.1 (2021-01-11)

[📝 Release notes](https://github.com/staart/api/releases/tag/v4.0.1) · [💻 Compare](https://github.com/staart/api/compare/v4.0.0...v4.0.1) · [🔖 Tag](https://github.com/staart/api/tree/v4.0.1) · 🗄️ Archive ([zip](https://github.com/staart/api/archive/v4.0.1.zip) · [tar.gz](https://github.com/staart/api/archive/v4.0.1.tar.gz))

### ♻️ Updates

- [`9b3629c7`](https://github.com/staart/api/commit/9b3629c7)  Add create group to scopes
- [`5d660449`](https://github.com/staart/api/commit/5d660449)  Update scope for writing membership

### ⬆️ Dependency updates

- [`35aecbdc`](https://github.com/staart/api/commit/35aecbdc)  Bump rate-limiter-flexible from 2.1.16 to 2.2.1

## v4.0.0 (2021-01-09)

[📝 Release notes](https://github.com/staart/api/releases/tag/v4.0.0) · [💻 Compare](https://github.com/staart/api/compare/v3.6.17...v4.0.0) · [🔖 Tag](https://github.com/staart/api/tree/v4.0.0) · 🗄️ Archive ([zip](https://github.com/staart/api/archive/v4.0.0.zip) · [tar.gz](https://github.com/staart/api/archive/v4.0.0.tar.gz))

### ✨ New features

- [`ec40a981`](https://github.com/staart/api/commit/ec40a981)  Add audit logs to users/groups
- [`ede77f40`](https://github.com/staart/api/commit/ede77f40)  Add metrics module (#1142)
(Issues: [`#1142`](https://github.com/staart/api/issues/1142))- [`533f690a`](https://github.com/staart/api/commit/533f690a)  Update metrics, verify domains, delete audit logs in tasks
- [`a96f8245`](https://github.com/staart/api/commit/a96f8245)  Add profile picture, deleting to user
- [`93d9d78b`](https://github.com/staart/api/commit/93d9d78b)  Add meta module

### ♻️ Updates

- [`10f6cc8f`](https://github.com/staart/api/commit/10f6cc8f)  Update configuration
- [`0ebf7425`](https://github.com/staart/api/commit/0ebf7425)  Update API keys module, logger
- [`10e7ab82`](https://github.com/staart/api/commit/10e7ab82)  Add origin, session ID, role to auth
- [`385c93c9`](https://github.com/staart/api/commit/385c93c9)  Update domain module with methods
- [`670e7b31`](https://github.com/staart/api/commit/670e7b31)  Update email module with imports
- [`7398bc86`](https://github.com/staart/api/commit/7398bc86)  Update group module
- [`fd347eeb`](https://github.com/staart/api/commit/fd347eeb)  Update memberships module
- [`0cbe65c8`](https://github.com/staart/api/commit/0cbe65c8)  Update MFA module with returns
- [`62ac6266`](https://github.com/staart/api/commit/62ac6266)  Use config in Stripe module, add comments
- [`66e63e5e`](https://github.com/staart/api/commit/66e63e5e)  Remove playwrite, use puppeteer
- [`0d02e650`](https://github.com/staart/api/commit/0d02e650)  Update approved subnet, session, webhook modules
- [`54deaf57`](https://github.com/staart/api/commit/54deaf57)  Update services
- [`4d48402b`](https://github.com/staart/api/commit/4d48402b)  Add config for S3 profile picture
- [`0e63ccd7`](https://github.com/staart/api/commit/0e63ccd7)  Update app module with metrics, puppeteer, exception
- [`b3418d0f`](https://github.com/staart/api/commit/b3418d0f)  Make gravatar optional (fixed #1114)
(Issues: [`#1114`](https://github.com/staart/api/issues/1114))

### ⬆️ Dependency updates

- [`9c609b19`](https://github.com/staart/api/commit/9c609b19)  Bump @prisma/cli from 2.13.0 to 2.14.0
- [`6d2db524`](https://github.com/staart/api/commit/6d2db524)  Bump aws-sdk from 2.815.0 to 2.822.0
- [`78c68454`](https://github.com/staart/api/commit/78c68454)  Bump helmet from 4.2.0 to 4.3.1
- [`7f2c0c20`](https://github.com/staart/api/commit/7f2c0c20)  Bump @typescript-eslint/eslint-plugin from 4.11.1 to 4.12.0
- [`7c20f043`](https://github.com/staart/api/commit/7c20f043)  Bump @nestjs/swagger from 4.7.6 to 4.7.9
- [`12bcb6f3`](https://github.com/staart/api/commit/12bcb6f3)  Bump @nestjs/common from 7.6.4 to 7.6.5
- [`f05888c3`](https://github.com/staart/api/commit/f05888c3)  Bump eslint from 7.16.0 to 7.17.0
- [`212099c2`](https://github.com/staart/api/commit/212099c2)  Bump @typescript-eslint/parser from 4.10.0 to 4.12.0

### 💥 Breaking changes

- [`4f70e60c`](https://github.com/staart/api/commit/4f70e60c)  Update where pipe with filters

## v3.6.17 (2021-01-04)

[📝 Release notes](https://github.com/staart/api/releases/tag/v3.6.17) · [💻 Compare](https://github.com/staart/api/compare/v3.6.16...v3.6.17) · [🔖 Tag](https://github.com/staart/api/tree/v3.6.17) · 🗄️ Archive ([zip](https://github.com/staart/api/archive/v3.6.17.zip) · [tar.gz](https://github.com/staart/api/archive/v3.6.17.tar.gz))

### ⬆️ Dependency updates

- [`99ff80a0`](https://github.com/staart/api/commit/99ff80a0)  Bump akhileshns/heroku-deploy from v3.7.8 to v3.8.8
- [`78c5160c`](https://github.com/staart/api/commit/78c5160c)  Bump @nestjs/core from 7.6.3 to 7.6.5
- [`ac0e5f65`](https://github.com/staart/api/commit/ac0e5f65)  Bump @typescript-eslint/eslint-plugin from 4.11.0 to 4.11.1

## v3.6.16 (2020-12-28)

[📝 Release notes](https://github.com/staart/api/releases/tag/v3.6.16) · [💻 Compare](https://github.com/staart/api/compare/v3.6.15...v3.6.16) · [🔖 Tag](https://github.com/staart/api/tree/v3.6.16) · 🗄️ Archive ([zip](https://github.com/staart/api/archive/v3.6.16.zip) · [tar.gz](https://github.com/staart/api/archive/v3.6.16.tar.gz))

### ⬆️ Dependency updates

- [`2e06d1f6`](https://github.com/staart/api/commit/2e06d1f6)  Bump @nestjs/platform-express from 7.5.5 to 7.6.3
- [`94f92f7a`](https://github.com/staart/api/commit/94f92f7a)  Bump aws-sdk from 2.807.0 to 2.814.0
- [`d43ceecd`](https://github.com/staart/api/commit/d43ceecd)  Bump vsoch/pull-request-action from 1.0.12 to 1.0.13
- [`7f2beed6`](https://github.com/staart/api/commit/7f2beed6)  Bump aws-sdk from 2.814.0 to 2.815.0
- [`43329a70`](https://github.com/staart/api/commit/43329a70)  Bump typescript from 4.1.2 to 4.1.3
- [`5a1c3a74`](https://github.com/staart/api/commit/5a1c3a74)  Bump pascalgn/automerge-action from v0.12.0 to v0.13.0
- [`ea54504a`](https://github.com/staart/api/commit/ea54504a)  Bump node-notifier from 8.0.0 to 8.0.1
- [`a1c1e818`](https://github.com/staart/api/commit/a1c1e818)  Bump swagger-ui-express from 4.1.5 to 4.1.6
- [`030388b1`](https://github.com/staart/api/commit/030388b1)  Bump eslint from 7.15.0 to 7.16.0
- [`fe2fe64e`](https://github.com/staart/api/commit/fe2fe64e)  Bump @nestjs/common from 7.6.1 to 7.6.4
- [`22a80d4e`](https://github.com/staart/api/commit/22a80d4e)  Bump playwright from 1.7.0 to 1.7.1
- [`32f67c87`](https://github.com/staart/api/commit/32f67c87)  Bump @types/node from 14.14.10 to 14.14.16
- [`93b8203c`](https://github.com/staart/api/commit/93b8203c)  Bump @typescript-eslint/eslint-plugin from 4.10.0 to 4.11.0

## v3.6.15 (2020-12-21)

[📝 Release notes](https://github.com/staart/api/releases/tag/v3.6.15) · [💻 Compare](https://github.com/staart/api/compare/v3.6.14...v3.6.15) · [🔖 Tag](https://github.com/staart/api/tree/v3.6.15) · 🗄️ Archive ([zip](https://github.com/staart/api/archive/v3.6.15.zip) · [tar.gz](https://github.com/staart/api/archive/v3.6.15.tar.gz))

### ⬆️ Dependency updates

- [`1d54b8a1`](https://github.com/staart/api/commit/1d54b8a1)  Bump nodemailer from 6.4.16 to 6.4.17
- [`3df56fa8`](https://github.com/staart/api/commit/3df56fa8)  Bump firebase-admin from 9.4.1 to 9.4.2
- [`ff674348`](https://github.com/staart/api/commit/ff674348)  Bump @nestjs/swagger from 4.7.5 to 4.7.6
- [`b12c6e33`](https://github.com/staart/api/commit/b12c6e33)  Bump got from 11.8.0 to 11.8.1
- [`26c6347f`](https://github.com/staart/api/commit/26c6347f)  Bump @typescript-eslint/parser from 4.9.1 to 4.10.0
- [`596bf7ce`](https://github.com/staart/api/commit/596bf7ce)  Bump @nestjs/common from 7.5.5 to 7.6.1
- [`24bf0a77`](https://github.com/staart/api/commit/24bf0a77)  Bump @nestjs/core from 7.5.5 to 7.6.1
- [`ea1e0290`](https://github.com/staart/api/commit/ea1e0290)  Bump @prisma/client from 2.12.1 to 2.13.0
- [`bbf894d5`](https://github.com/staart/api/commit/bbf894d5)  Bump playwright from 1.6.2 to 1.7.0
- [`e0502541`](https://github.com/staart/api/commit/e0502541)  Bump @typescript-eslint/eslint-plugin from 4.9.1 to 4.10.0
- [`ec890edf`](https://github.com/staart/api/commit/ec890edf)  Bump @nestjs/cli from 7.5.3 to 7.5.4
- [`32a57284`](https://github.com/staart/api/commit/32a57284)  Bump ts-loader from 8.0.11 to 8.0.12
- [`17c4c5f2`](https://github.com/staart/api/commit/17c4c5f2)  Bump actions/setup-node from v2.1.3 to v2.1.4
- [`2296a22a`](https://github.com/staart/api/commit/2296a22a)  Bump @nestjs/testing from 7.5.5 to 7.6.3
- [`498512a5`](https://github.com/staart/api/commit/498512a5)  Bump rate-limiter-flexible from 2.1.13 to 2.1.14
- [`4f9f56d6`](https://github.com/staart/api/commit/4f9f56d6)  Bump @nestjs/core from 7.6.1 to 7.6.3
- [`7444e976`](https://github.com/staart/api/commit/7444e976)  Bump stripe from 8.127.0 to 8.129.0

## v3.6.14 (2020-12-14)

[📝 Release notes](https://github.com/staart/api/releases/tag/v3.6.14) · [💻 Compare](https://github.com/staart/api/compare/v3.6.13...v3.6.14) · [🔖 Tag](https://github.com/staart/api/tree/v3.6.14) · 🗄️ Archive ([zip](https://github.com/staart/api/archive/v3.6.14.zip) · [tar.gz](https://github.com/staart/api/archive/v3.6.14.tar.gz))

### ⬆️ Dependency updates

- [`8743ea89`](https://github.com/staart/api/commit/8743ea89)  Bump @nestjs/schematics from 7.2.2 to 7.2.5
- [`104de030`](https://github.com/staart/api/commit/104de030)  Bump @types/jest from 26.0.18 to 26.0.19
- [`8c28425c`](https://github.com/staart/api/commit/8c28425c)  Bump actions/setup-node from v2.1.2 to v2.1.3

## v3.6.13 (2020-12-10)

[📝 Release notes](https://github.com/staart/api/releases/tag/v3.6.13) · [💻 Compare](https://github.com/staart/api/compare/v3.6.12...v3.6.13) · [🔖 Tag](https://github.com/staart/api/tree/v3.6.13) · 🗄️ Archive ([zip](https://github.com/staart/api/archive/v3.6.13.zip) · [tar.gz](https://github.com/staart/api/archive/v3.6.13.tar.gz))

### 🐛 Bug fixes

- [`da8bd32c`](https://github.com/staart/api/commit/da8bd32c)  Add [*] scope to admins

### ⬆️ Dependency updates

- [`60478842`](https://github.com/staart/api/commit/60478842)  Bump eslint from 7.14.0 to 7.15.0
- [`c4c5d483`](https://github.com/staart/api/commit/c4c5d483)  Bump aws-sdk from 2.803.0 to 2.804.0
- [`5c04197c`](https://github.com/staart/api/commit/5c04197c)  Bump hibp from 9.0.0 to 9.0.3
- [`39b7601e`](https://github.com/staart/api/commit/39b7601e)  Bump ts-node from 9.1.0 to 9.1.1
- [`cc025c5b`](https://github.com/staart/api/commit/cc025c5b)  Bump @typescript-eslint/eslint-plugin from 4.9.0 to 4.9.1
- [`00d9497f`](https://github.com/staart/api/commit/00d9497f)  Bump aws-sdk from 2.804.0 to 2.805.0
- [`8b7398e6`](https://github.com/staart/api/commit/8b7398e6)  Bump @typescript-eslint/parser from 4.9.0 to 4.9.1
- [`68a7332e`](https://github.com/staart/api/commit/68a7332e)  Bump @types/jest from 26.0.16 to 26.0.18
- [`d7486ed7`](https://github.com/staart/api/commit/d7486ed7)  Bump @octokit/rest from 18.0.11 to 18.0.12
- [`87aebdbc`](https://github.com/staart/api/commit/87aebdbc)  Bump uuid from 8.3.1 to 8.3.2
- [`7e59c2aa`](https://github.com/staart/api/commit/7e59c2aa)  Bump @prisma/cli from 2.12.1 to 2.13.0
- [`d9e99280`](https://github.com/staart/api/commit/d9e99280)  Bump aws-sdk from 2.805.0 to 2.807.0
- [`c53701b9`](https://github.com/staart/api/commit/c53701b9)  Bump @types/ua-parser-js from 0.7.33 to 0.7.35
- [`7bfdc870`](https://github.com/staart/api/commit/7bfdc870)  Bump akhileshns/heroku-deploy from v3.6.8 to v3.7.8

## v3.6.12 (2020-12-07)

[📝 Release notes](https://github.com/staart/api/releases/tag/v3.6.12) · [💻 Compare](https://github.com/staart/api/compare/v3.6.11...v3.6.12) · [🔖 Tag](https://github.com/staart/api/tree/v3.6.12) · 🗄️ Archive ([zip](https://github.com/staart/api/archive/v3.6.12.zip) · [tar.gz](https://github.com/staart/api/archive/v3.6.12.tar.gz))

### ⬆️ Dependency updates

- [`c33e0ad6`](https://github.com/staart/api/commit/c33e0ad6)  Bump aws-sdk from 2.800.0 to 2.802.0
- [`b01e785d`](https://github.com/staart/api/commit/b01e785d)  Bump @octokit/rest from 18.0.9 to 18.0.10
- [`7f543317`](https://github.com/staart/api/commit/7f543317)  Bump @types/jest from 26.0.15 to 26.0.16
- [`4f8ea6ea`](https://github.com/staart/api/commit/4f8ea6ea)  Bump @koj/config from 1.2.9 to 1.2.11
- [`d4c35105`](https://github.com/staart/api/commit/d4c35105)  Bump @octokit/rest from 18.0.10 to 18.0.11
- [`dd082b6c`](https://github.com/staart/api/commit/dd082b6c)  Bump stripe from 8.126.0 to 8.127.0
- [`dd452b40`](https://github.com/staart/api/commit/dd452b40)  Bump aws-sdk from 2.802.0 to 2.803.0
- [`4a571b67`](https://github.com/staart/api/commit/4a571b67)  Bump ts-node from 9.0.0 to 9.1.0

## [v3.6.11](https://github.com/staart/api/compare/v3.6.10...v3.6.11) (2020-12-02)

### ♻️ Updates

- [`f8eb9cab`](https://github.com/staart/api/commit/f8eb9cab)  Use frontendUrl from meta config
- [`d2ea77b0`](https://github.com/staart/api/commit/d2ea77b0)  Use Configuration interface in service

### ⬆️ Dependency updates

- [`bb2181f1`](https://github.com/staart/api/commit/bb2181f1)  Bump aws-sdk from 2.799.0 to 2.800.0
- [`6b07b4c4`](https://github.com/staart/api/commit/6b07b4c4)  Bump @typescript-eslint/eslint-plugin from 4.8.2 to 4.9.0
- [`7db658c6`](https://github.com/staart/api/commit/7db658c6)  Bump @typescript-eslint/parser from 4.8.2 to 4.9.0

## [v3.6.10](https://github.com/staart/api/compare/v3.6.9...v3.6.10) (2020-11-30)

### ♻️ Updates

- [`373f40d1`](https://github.com/staart/api/commit/373f40d1)  Use acct:URL for access token
- [`eb98a1fb`](https://github.com/staart/api/commit/eb98a1fb)  Listen on env PORT

### 🐛 Bug fixes

- [`956bfb80`](https://github.com/staart/api/commit/956bfb80)  Remove extra # from UI colors

### ⬆️ Dependency updates

- [`5a37720b`](https://github.com/staart/api/commit/5a37720b)  Bump prettier from 2.2.0 to 2.2.1

## [v3.6.9](https://github.com/staart/api/compare/v3.6.8...v3.6.9) (2020-11-30)

### ⬆️ Dependency updates

- [`e06499e3`](https://github.com/staart/api/commit/e06499e3)  Bump @slack/web-api from 5.13.0 to 5.14.0
- [`01949b33`](https://github.com/staart/api/commit/01949b33)  Bump @prisma/client from 2.12.0 to 2.12.1
- [`364a477a`](https://github.com/staart/api/commit/364a477a)  Bump @prisma/cli from 2.12.0 to 2.12.1

## [v3.6.8](https://github.com/staart/api/compare/v3.6.7...v3.6.8) (2020-11-26)

### 🐛 Bug fixes

- [`4c5c427e`](https://github.com/staart/api/commit/4c5c427e)  Ignore auth if user is not found

## [v3.6.7](https://github.com/staart/api/compare/v3.6.6...v3.6.7) (2020-11-25)

### ♻️ Updates

- [`5afc6d23`](https://github.com/staart/api/commit/5afc6d23)  Add Slack method to message channel

## [v3.6.6](https://github.com/staart/api/compare/v3.6.5...v3.6.6) (2020-11-25)

### 🐛 Bug fixes

- [`553d08cd`](https://github.com/staart/api/commit/553d08cd)  Fix defaults for Twilio service

### ⬆️ Dependency updates

- [`5eb74986`](https://github.com/staart/api/commit/5eb74986)  Bump stripe from 8.125.0 to 8.126.0
- [`44136726`](https://github.com/staart/api/commit/44136726)  Bump @types/node from 14.14.9 to 14.14.10
- [`919bf7da`](https://github.com/staart/api/commit/919bf7da)  Bump @nestjs/passport from 7.1.3 to 7.1.5

## [v3.6.5](https://github.com/staart/api/compare/v3.6.4...v3.6.5) (2020-11-25)

### ♻️ Updates

- [`9ab66dfb`](https://github.com/staart/api/commit/9ab66dfb)  Use prisma code references

### 🐛 Bug fixes

- [`d63790e1`](https://github.com/staart/api/commit/d63790e1)  Fix Prisma update changes

### ⬆️ Dependency updates

- [`fd366850`](https://github.com/staart/api/commit/fd366850)  Update @prisma/client, @prisma/server to v2.12.0

## [v3.6.4](https://github.com/staart/api/compare/v3.6.3...v3.6.4) (2020-11-23)

## [v3.6.3](https://github.com/staart/api/compare/v3.6.2...v3.6.3) (2020-11-18)

### ♻️ Updates

- [`76181f1a`](https://github.com/staart/api/commit/76181f1a)  Add Stripe controllers to module
- [`018837c4`](https://github.com/staart/api/commit/018837c4)  Add billing portal session link
- [`dba69e18`](https://github.com/staart/api/commit/dba69e18)  Redirect to session home
- [`aa64deef`](https://github.com/staart/api/commit/aa64deef)  Use GET method for billing portal

### 🐛 Bug fixes

- [`a8011fc1`](https://github.com/staart/api/commit/a8011fc1)  Make req.user optional
- [`79ddd033`](https://github.com/staart/api/commit/79ddd033)  Use /billing/link URL for billing portal

### ⬆️ Dependency updates

- [`c2637fad`](https://github.com/staart/api/commit/c2637fad)  Update koj-co/template

## [v3.6.2](https://github.com/staart/api/compare/v3.6.1...v3.6.2) (2020-11-17)

### ♻️ Updates

- [`58971f05`](https://github.com/staart/api/commit/58971f05)  Use internal interceptor, decorator
- [`c6ba4afd`](https://github.com/staart/api/commit/c6ba4afd)  Use custom rate limiter

## [v3.6.1](https://github.com/staart/api/compare/v3.6.0...v3.6.1) (2020-11-17)

### 🐛 Bug fixes

- [`d1e7c654`](https://github.com/staart/api/commit/d1e7c654)  Remove unauthorized scopes from API keys

## [v3.6.0](https://github.com/staart/api/compare/v3.5.6...v3.6.0) (2020-11-16)

### ✨ New features

- [`666e37eb`](https://github.com/staart/api/commit/666e37eb)  Add endpoint for subgroups

### ♻️ Updates

- [`ef2dc724`](https://github.com/staart/api/commit/ef2dc724)  Get subgroup scopes on login
- [`c5c54c45`](https://github.com/staart/api/commit/c5c54c45)  Add select/include pipe
- [`2adfc21b`](https://github.com/staart/api/commit/2adfc21b)  Use create group helper in membership service
- [`4dd62efc`](https://github.com/staart/api/commit/4dd62efc)  Remove attributes from DTO
- [`9a9cca5d`](https://github.com/staart/api/commit/9a9cca5d)  Use /auth/link for token links
- [`0c2b15b3`](https://github.com/staart/api/commit/0c2b15b3)  Move merging accounts to auth.service
- [`70c5328b`](https://github.com/staart/api/commit/70c5328b)  Send token response on email verification

### 🐛 Bug fixes

- [`69a88ce7`](https://github.com/staart/api/commit/69a88ce7)  Return created membership with group
- [`3f9bbaef`](https://github.com/staart/api/commit/3f9bbaef)  Use email ID to verify, not user ID
- [`ed3a2103`](https://github.com/staart/api/commit/ed3a2103)  Combine specific keys of users, fix response

## [v3.5.6](https://github.com/staart/api/compare/v3.5.5...v3.5.6) (2020-11-15)

### ♻️ Updates

- [`d0a4aceb`](https://github.com/staart/api/commit/d0a4aceb)  Check for primary email on deleting emails
- [`1777332d`](https://github.com/staart/api/commit/1777332d)  Auto-join groups based on email address
- [`5abcc56d`](https://github.com/staart/api/commit/5abcc56d)  Use explicit ID type in params
- [`c499a62c`](https://github.com/staart/api/commit/c499a62c)  Store parsed details in session, Change Mac OS -&gt; macOS
- [`e54ddd9b`](https://github.com/staart/api/commit/e54ddd9b)  Allow cursors with implicit ID
- [`cf873b1a`](https://github.com/staart/api/commit/cf873b1a)  Use colon instead of space in order-by pipe

### 🐛 Bug fixes

- [`150f42f0`](https://github.com/staart/api/commit/150f42f0)  Ensure there is at least 1 owner in group
- [`3d73c616`](https://github.com/staart/api/commit/3d73c616)  Use groupId request param
- [`1804bf39`](https://github.com/staart/api/commit/1804bf39)  Render mustache before setting subject
- [`c3dcb4a5`](https://github.com/staart/api/commit/c3dcb4a5)  Fix scopes for user, groups routes
- [`3c645992`](https://github.com/staart/api/commit/3c645992)  Validate domain name
- [`3175c42d`](https://github.com/staart/api/commit/3175c42d)  Only add Stripe scopes if account exists

## [v3.5.5](https://github.com/staart/api/compare/v3.5.4...v3.5.5) (2020-11-15)

### 🐛 Bug fixes

- [`cfe9855a`](https://github.com/staart/api/commit/cfe9855a)  Return user info on update/delete

## [v3.5.4](https://github.com/staart/api/compare/v3.5.3...v3.5.4) (2020-11-15)

### 🐛 Bug fixes

- [`bacd1e0b`](https://github.com/staart/api/commit/bacd1e0b)  Include user in membership response

## [v3.5.3](https://github.com/staart/api/compare/v3.5.2...v3.5.3) (2020-11-15)

### ♻️ Updates

- [`ed3b942e`](https://github.com/staart/api/commit/ed3b942e)  Delete sessions on deactivate

## [v3.5.2](https://github.com/staart/api/compare/v3.5.1...v3.5.2) (2020-11-15)

### 🐛 Bug fixes

- [`a8c75f8b`](https://github.com/staart/api/commit/a8c75f8b)  Merge requests cannot go for the same user

## [v3.5.1](https://github.com/staart/api/compare/v3.5.0...v3.5.1) (2020-11-15)

### 🐛 Bug fixes

- [`********`](https://github.com/staart/api/commit/********)  Validate new email

## [v3.5.0](https://github.com/staart/api/compare/v3.4.0...v3.5.0) (2020-11-14)

### ✨ New features

- [`de4dd7a6`](https://github.com/staart/api/commit/de4dd7a6)  Add Google Maps module
- [`7d1c2918`](https://github.com/staart/api/commit/7d1c2918)  Add Playwrite module

### ♻️ Updates

- [`a7197cb5`](https://github.com/staart/api/commit/a7197cb5)  Rename service clients to &#x60;client&#x60;
- [`163f170e`](https://github.com/staart/api/commit/163f170e)  Use tokensService instead of jwtService
- [`16afe470`](https://github.com/staart/api/commit/16afe470)  Add default boolean configuration values

## [v3.4.0](https://github.com/staart/api/compare/v3.3.1...v3.4.0) (2020-11-13)

### ✨ New features

- [`99f17c8e`](https://github.com/staart/api/commit/99f17c8e)  Add Slack module
- [`bd930bec`](https://github.com/staart/api/commit/bd930bec)  Add Airtable module
- [`7bc5e7c6`](https://github.com/staart/api/commit/7bc5e7c6)  Add AWS S3 service
- [`37c1eb8b`](https://github.com/staart/api/commit/37c1eb8b)  Add Cloudinary module
- [`835eb270`](https://github.com/staart/api/commit/835eb270)  Add Firebase module
- [`a0eff5a8`](https://github.com/staart/api/commit/a0eff5a8)  Add GitHub module

## [v3.3.1](https://github.com/staart/api/compare/v3.3.0...v3.3.1) (2020-11-13)

### ♻️ Updates

- [`841ffef9`](https://github.com/staart/api/commit/841ffef9)  Add endpoints for API key logs
- [`f50a401c`](https://github.com/staart/api/commit/f50a401c)  Support logging authenticated requests

### 🐛 Bug fixes

- [`b9b30146`](https://github.com/staart/api/commit/b9b30146)  Ensure API key is a UUID

## [v3.3.0](https://github.com/staart/api/compare/v3.2.0...v3.3.0) (2020-11-13)

### ✨ New features

- [`bbb002f7`](https://github.com/staart/api/commit/bbb002f7)  Log API requests in ElasticSearch
- [`89b7bd2c`](https://github.com/staart/api/commit/89b7bd2c)  Delete old API logs

### ♻️ Updates

- [`a761d017`](https://github.com/staart/api/commit/a761d017)  Use Authorization header instead of X-Api-Key

### 🐛 Bug fixes

- [`b73cd6aa`](https://github.com/staart/api/commit/b73cd6aa)  Return API key data from LRU if available

## [v3.2.0](https://github.com/staart/api/compare/v3.1.2...v3.2.0) (2020-11-12)

### ✨ New features

- [`4631bbb8`](https://github.com/staart/api/commit/4631bbb8)  Add ElasticSearch service

## [v3.1.2](https://github.com/staart/api/compare/v3.1.1...v3.1.2) (2020-11-10)

### ♻️ Updates

- [`8e896920`](https://github.com/staart/api/commit/8e896920)  Add configuration for retries

### ⬆️ Dependency updates

- [`c03fb8d8`](https://github.com/staart/api/commit/c03fb8d8)  Update aws-sdk, stripe

## [v3.1.1](https://github.com/staart/api/compare/v3.1.0...v3.1.1) (2020-11-09)

### ♻️ Updates

- [`fb081a2c`](https://github.com/staart/api/commit/fb081a2c)  Allow email configuration of SES
- [`d576cf0e`](https://github.com/staart/api/commit/d576cf0e)  Change required config in SES

## [v3.1.0](https://github.com/staart/api/compare/v3.0.1...v3.1.0) (2020-11-09)

### ✨ New features

- [`48ba1763`](https://github.com/staart/api/commit/48ba1763)  Add account deactivate (fixed #1350)
(Issues: [`#1350`](https://github.com/staart/api/issues/1350))

### ♻️ Updates

- [`dc1a3704`](https://github.com/staart/api/commit/dc1a3704)  Auto-set account to active on login

## [v3.0.1](https://github.com/staart/api/compare/v3.0.0...v3.0.1) (2020-11-08)

### 🐛 Bug fixes

- [`ac17d989`](https://github.com/staart/api/commit/ac17d989)  Fix CWE-20 in URL parsing

## [v3.0.0](https://github.com/staart/api/compare/v2.21.0...v3.0.0) (2020-11-08)

### ✨ New features

- [`47ceb5bc`](https://github.com/staart/api/commit/47ceb5bc)  Add support for login links
- [`161b2643`](https://github.com/staart/api/commit/161b2643)  Add endpoint for password details
- [`ef5654db`](https://github.com/staart/api/commit/ef5654db)  Ship Casbin-powered permissions (fixed #337)
(Issues: [`#337`](https://github.com/staart/api/issues/337))- [`872559c6`](https://github.com/staart/api/commit/872559c6)  Add user access token scopes endpoint
- [`5225c309`](https://github.com/staart/api/commit/5225c309)  Add API scopes endpoint
- [`8de94323`](https://github.com/staart/api/commit/8de94323)  Add gender prediction API
- [`e9baebd9`](https://github.com/staart/api/commit/e9baebd9)  Auto-fill country, timezone
- [`cbb355ee`](https://github.com/staart/api/commit/cbb355ee)  Add Sentry
- [`ff44de69`](https://github.com/staart/api/commit/ff44de69)  Add support for disabling billing
- [`b248d84c`](https://github.com/staart/api/commit/b248d84c)  Add new user registrations check
- [`54774f25`](https://github.com/staart/api/commit/54774f25)  Add newUserRegistrationDomains check
- [`d6beaf83`](https://github.com/staart/api/commit/d6beaf83)  Add Prisma CRUD endpoints
- [`b1074976`](https://github.com/staart/api/commit/b1074976)  Add pipes for optional int, order by
- [`6b732e2a`](https://github.com/staart/api/commit/6b732e2a)  Add support for cursor
- [`6d43fcfe`](https://github.com/staart/api/commit/6d43fcfe)  Use DTO in PATCH method
- [`c9f03df3`](https://github.com/staart/api/commit/c9f03df3)  Add auth module with register
- [`c9291532`](https://github.com/staart/api/commit/c9291532)  Add registration with email conflict check
- [`b311e5af`](https://github.com/staart/api/commit/b311e5af)  Add common configuration
- [`7b6902d8`](https://github.com/staart/api/commit/7b6902d8)  Render and send emails
- [`9fe994d3`](https://github.com/staart/api/commit/9fe994d3)  Add HTML email layout
- [`a7979c22`](https://github.com/staart/api/commit/a7979c22)  Add resend email verification endpoint
- [`3b4a4680`](https://github.com/staart/api/commit/3b4a4680)  Add authentication
- [`5abd4987`](https://github.com/staart/api/commit/5abd4987)  Add refresh token endpoint
- [`d1e9e252`](https://github.com/staart/api/commit/d1e9e252)  Add scope authorization in Guard
- [`93d82e1c`](https://github.com/staart/api/commit/93d82e1c)  Expose data by removing secrets
- [`0e7c8b20`](https://github.com/staart/api/commit/0e7c8b20)  Add session endpoints
- [`8233d52a`](https://github.com/staart/api/commit/8233d52a)  Add endpoints for access tokens
- [`276c95a3`](https://github.com/staart/api/commit/276c95a3)  Add endpoints for user memberships
- [`f7082e0f`](https://github.com/staart/api/commit/f7082e0f)  Add emails module
- [`818ad11a`](https://github.com/staart/api/commit/818ad11a)  Add groups endpoints
- [`26c0c0ef`](https://github.com/staart/api/commit/26c0c0ef)  Add group membership controller
- [`a642b7ea`](https://github.com/staart/api/commit/a642b7ea)  Support creating groups, memberships
- [`183b6749`](https://github.com/staart/api/commit/183b6749)  Add Pwned module
- [`21a7cb4f`](https://github.com/staart/api/commit/21a7cb4f)  Add support for password change, refactor auth
- [`d99de49c`](https://github.com/staart/api/commit/d99de49c)  Add scheduler to delete sessions
- [`b3ec3fc4`](https://github.com/staart/api/commit/b3ec3fc4)  Add helmet for security
- [`754495e1`](https://github.com/staart/api/commit/754495e1)  Add OpenAPI docs
- [`e3628898`](https://github.com/staart/api/commit/e3628898)  Add tokens module, 2FA
- [`9aaee67a`](https://github.com/staart/api/commit/9aaee67a)  Add logout endpoint
- [`cee3a55c`](https://github.com/staart/api/commit/cee3a55c)  Add 2FA enable/disable endpoints
- [`f8f47f29`](https://github.com/staart/api/commit/f8f47f29)  Add password forgot/reset
- [`339a29da`](https://github.com/staart/api/commit/339a29da)  Add verify emails endpoint
- [`e4e78e1d`](https://github.com/staart/api/commit/e4e78e1d)  Add approved subnets endpoints
- [`b3f60938`](https://github.com/staart/api/commit/b3f60938)  Add geolocation service
- [`2c892e83`](https://github.com/staart/api/commit/2c892e83)  Add approve subnet endpoint
- [`0f219cdc`](https://github.com/staart/api/commit/0f219cdc)  Add support for MFA when logging in
- [`e314375b`](https://github.com/staart/api/commit/e314375b)  Login with email token endpoints
- [`8c7f926b`](https://github.com/staart/api/commit/8c7f926b)  Send membership welcome email
- [`9857fbc5`](https://github.com/staart/api/commit/9857fbc5)  Support logging in backup code
- [`f1337775`](https://github.com/staart/api/commit/f1337775)  Add group API keys module
- [`62ee163d`](https://github.com/staart/api/commit/62ee163d)  Add basic Stripe module
- [`0dc4b422`](https://github.com/staart/api/commit/0dc4b422)  Add Stripe invoices endpoints
- [`ec9ec37b`](https://github.com/staart/api/commit/ec9ec37b)  Add Twilio module
- [`ce710401`](https://github.com/staart/api/commit/ce710401)  Add SMS MFA OTP
- [`b147b685`](https://github.com/staart/api/commit/b147b685)  Add email MFA
- [`f39eddac`](https://github.com/staart/api/commit/f39eddac)  Add Stripe sources endpoints
- [`74df85c2`](https://github.com/staart/api/commit/74df85c2)  Add API key scopes
- [`2a8170f1`](https://github.com/staart/api/commit/2a8170f1)  Add domain module
- [`59a4a7cb`](https://github.com/staart/api/commit/59a4a7cb)  Add DNS module
- [`e160f8ef`](https://github.com/staart/api/commit/e160f8ef)  Add HTML domain verification
- [`9da99653`](https://github.com/staart/api/commit/9da99653)  Add subscription endpoints
- [`7506cb20`](https://github.com/staart/api/commit/7506cb20)  Handle Stripe webhook event
- [`049e3eb0`](https://github.com/staart/api/commit/049e3eb0)  Add audit logs module
- [`36bda486`](https://github.com/staart/api/commit/36bda486)  Create audit log
- [`47869cdd`](https://github.com/staart/api/commit/47869cdd)  Add webhooks module
- [`53ebb4a8`](https://github.com/staart/api/commit/53ebb4a8)  Trigger webhooks on audit log
- [`8a5c0152`](https://github.com/staart/api/commit/8a5c0152)  Implement LRU for API keys
- [`bf9976d8`](https://github.com/staart/api/commit/bf9976d8)  Add API key users controllers
- [`7c35bd21`](https://github.com/staart/api/commit/7c35bd21)  Serve static files
- [`cbc8034c`](https://github.com/staart/api/commit/cbc8034c)  Add support for merging users (fixed #950)
(Issues: [`#950`](https://github.com/staart/api/issues/950))- [`727b6112`](https://github.com/staart/api/commit/727b6112)  Add SMS-based MFA method

### ♻️ Updates

- [`3dadf22f`](https://github.com/staart/api/commit/3dadf22f)  Change snake to camel case
- [`e7ab0eb7`](https://github.com/staart/api/commit/e7ab0eb7)  Change organization to group
- [`5cd080ea`](https://github.com/staart/api/commit/5cd080ea)  Update organization group
- [`9691e797`](https://github.com/staart/api/commit/9691e797)  Update user service references
- [`dd86fb40`](https://github.com/staart/api/commit/dd86fb40)  Update org
- [`efbc78f9`](https://github.com/staart/api/commit/efbc78f9)  Organize imports, update user rest
- [`cabd9e13`](https://github.com/staart/api/commit/cabd9e13)  Make login password optional
- [`8b2e8e12`](https://github.com/staart/api/commit/8b2e8e12)  Use object param for mail
- [`6b09c3e3`](https://github.com/staart/api/commit/6b09c3e3)  Allow all attributes in mail
- [`4dc4bf69`](https://github.com/staart/api/commit/4dc4bf69)  Update helpers
- [`91ac3cc6`](https://github.com/staart/api/commit/91ac3cc6)  Use TWT instead of username
- [`3f3ccf14`](https://github.com/staart/api/commit/3f3ccf14)  Use number for ID, not string
- [`8cae2670`](https://github.com/staart/api/commit/8cae2670)  Remove fallback from TWT
- [`0efe1c8f`](https://github.com/staart/api/commit/0efe1c8f)  Use number instead of string in ID
- [`1a7a1181`](https://github.com/staart/api/commit/1a7a1181)  Use number IDs in controllers
- [`170ba999`](https://github.com/staart/api/commit/170ba999)  Add login link token to email
- [`5edf233a`](https://github.com/staart/api/commit/5edf233a)  Use number ID for user, validate number
- [`a3535905`](https://github.com/staart/api/commit/a3535905)  Use number for org ID
- [`2c201062`](https://github.com/staart/api/commit/2c201062)  Use TWT for IDs
- [`e70fa98c`](https://github.com/staart/api/commit/e70fa98c)  Use TWT of length 10
- [`8327891f`](https://github.com/staart/api/commit/8327891f)  Support all id-like keys with TWT
- [`bba4a39a`](https://github.com/staart/api/commit/bba4a39a)  Allow empty passwords
- [`ae0022b9`](https://github.com/staart/api/commit/ae0022b9)  Add decode TWT function
- [`5a6fa2e7`](https://github.com/staart/api/commit/5a6fa2e7)  Use string for ID validation
- [`08a2c53a`](https://github.com/staart/api/commit/08a2c53a)  Use Joi.number() for ID
- [`8c90ee64`](https://github.com/staart/api/commit/8c90ee64)  use take in rest
- [`d598a1f1`](https://github.com/staart/api/commit/d598a1f1)  Use any for res.locals
- [`9558c36d`](https://github.com/staart/api/commit/9558c36d)  Use take in Prisma
- [`905e0181`](https://github.com/staart/api/commit/905e0181)  Use new authorization helper in user.ts
- [`9301944a`](https://github.com/staart/api/commit/9301944a)  Add Casbin admin scopes
- [`02799be5`](https://github.com/staart/api/commit/02799be5)  Use new authorization can in group, auth
- [`28b0b142`](https://github.com/staart/api/commit/28b0b142)  Change params to subject, action, object
- [`cd1fb73e`](https://github.com/staart/api/commit/cd1fb73e)  Remove expiry from access tokens
- [`fefcdba2`](https://github.com/staart/api/commit/fefcdba2)  Move access token scopes to security
- [`2a75733f`](https://github.com/staart/api/commit/2a75733f)  Use constants in policy
- [`13490a14`](https://github.com/staart/api/commit/13490a14)  Update delete casbin policies
- [`3fac6c93`](https://github.com/staart/api/commit/3fac6c93)  Remove username validation
- [`63577fad`](https://github.com/staart/api/commit/63577fad)  Change API key, access token length to 32
- [`629b7aac`](https://github.com/staart/api/commit/629b7aac)  Update group ID as attribute
- [`1e040a76`](https://github.com/staart/api/commit/1e040a76)  Change stripeCustomerId to stripeCustomer
- [`9b760e1d`](https://github.com/staart/api/commit/9b760e1d)  Skip test for Stripe
- [`cd433d17`](https://github.com/staart/api/commit/cd433d17)  Make tracking optional
- [`d83146ce`](https://github.com/staart/api/commit/d83146ce)  Check tracking config before ES
- [`0f300e3b`](https://github.com/staart/api/commit/0f300e3b)  Move some config from file
- [`733f923d`](https://github.com/staart/api/commit/733f923d)  Use config helpr instead of imports
- [`b4c6176d`](https://github.com/staart/api/commit/b4c6176d)  Use email config with Nodemailer interface
- [`34eb080f`](https://github.com/staart/api/commit/34eb080f)  Use session UUID as refresh token
- [`527ab9a3`](https://github.com/staart/api/commit/527ab9a3)  Get user ID, scopes in JWT strategy
- [`3e259b43`](https://github.com/staart/api/commit/3e259b43)  Use access toke ngenerator abstraction
- [`6a31e886`](https://github.com/staart/api/commit/6a31e886)  Use local scope guards (https://stackoverflow.com/a/50801832/1656944)
- [`d30ce2fa`](https://github.com/staart/api/commit/d30ce2fa)  Use session ID as param
- [`959dc7b8`](https://github.com/staart/api/commit/959dc7b8)  Use global guards, @Public decorator
- [`be84f02f`](https://github.com/staart/api/commit/be84f02f)  Use new scope structure
- [`915c5ae6`](https://github.com/staart/api/commit/915c5ae6)  Send email not verified exception message
- [`b7cf9f39`](https://github.com/staart/api/commit/b7cf9f39)  Hash passwords, ensure uncompromised
- [`62d6ae04`](https://github.com/staart/api/commit/62d6ae04)  Use prisma directly not authService
- [`f7697ebd`](https://github.com/staart/api/commit/f7697ebd)  Use safe email helper
- [`75fc733f`](https://github.com/staart/api/commit/75fc733f)  Lowercase, remove plus from email
- [`e69cc370`](https://github.com/staart/api/commit/e69cc370)  Use import instead of import type
- [`750eb880`](https://github.com/staart/api/commit/750eb880)  Change 2fa to totp
- [`0857ae77`](https://github.com/staart/api/commit/0857ae77)  Use auth module-scoped constants
- [`3fb490fa`](https://github.com/staart/api/commit/3fb490fa)  Change approved location -&gt; approved subnet
- [`fe973961`](https://github.com/staart/api/commit/fe973961)  Hash approved subnets
- [`d0bb693c`](https://github.com/staart/api/commit/d0bb693c)  Add cache to geolocation
- [`d9899226`](https://github.com/staart/api/commit/d9899226)  Store geolocation in approved subnets
- [`24afea03`](https://github.com/staart/api/commit/24afea03)  Approve new subnets in auth
- [`63bac411`](https://github.com/staart/api/commit/63bac411)  Allow adding team members without name
- [`ffd8fdf8`](https://github.com/staart/api/commit/ffd8fdf8)  Generate/regenerate backup codes in 2FA
- [`e3349735`](https://github.com/staart/api/commit/e3349735)  Add email verification template
- [`d4c10d29`](https://github.com/staart/api/commit/d4c10d29)  Add create/delete/replace customer endpoint
- [`560c03ac`](https://github.com/staart/api/commit/560c03ac)  Allow uppercase sorting
- [`6c2897da`](https://github.com/staart/api/commit/6c2897da)  Use relative import paths
- [`170bc5de`](https://github.com/staart/api/commit/170bc5de)  Use .env data in configuration
- [`20d8cebb`](https://github.com/staart/api/commit/20d8cebb)  Change twoFactorEnabled -&gt; twoFactorMethod
- [`b5d94ce7`](https://github.com/staart/api/commit/b5d94ce7)  Use TokensService for UUID
- [`b490af16`](https://github.com/staart/api/commit/b490af16)  Update scopes in controllers
- [`3253de5b`](https://github.com/staart/api/commit/3253de5b)  Normalize domain URL
- [`c401a330`](https://github.com/staart/api/commit/c401a330)  Normalize +, . in emails
- [`c0b928a8`](https://github.com/staart/api/commit/c0b928a8)  Use raw/JSON middleware
- [`c797457b`](https://github.com/staart/api/commit/c797457b)  Change auth controller login routes
- [`aa80db1b`](https://github.com/staart/api/commit/aa80db1b)  Use constant for login token sub
- [`a39009b7`](https://github.com/staart/api/commit/a39009b7)  Add membership module to app
- [`27066a69`](https://github.com/staart/api/commit/27066a69)  Add pretty profile pictures for domain, group, user
- [`86a456d1`](https://github.com/staart/api/commit/86a456d1)  Add ID to auth token
- [`43e85967`](https://github.com/staart/api/commit/43e85967)  Use Gravatar as user profile picture
- [`5a1cb701`](https://github.com/staart/api/commit/5a1cb701)  Add audit log decorators on controller
- [`74705d67`](https://github.com/staart/api/commit/74705d67)  Add webhook scopes to API key
- [`6c546cfa`](https://github.com/staart/api/commit/6c546cfa)  Add webhook scopes endpoint
- [`89af8027`](https://github.com/staart/api/commit/89af8027)  Add group/user methods in API kes
- [`c84bccba`](https://github.com/staart/api/commit/c84bccba)  Add API key scopes for user
- [`24f0d246`](https://github.com/staart/api/commit/24f0d246)  Only allow clean, secure scopes in API keys
- [`b32c5af3`](https://github.com/staart/api/commit/b32c5af3)  Use custom JWT strategy
- [`49ed4a47`](https://github.com/staart/api/commit/49ed4a47)  Support referrer restrictions in API keys
- [`e24b26d0`](https://github.com/staart/api/commit/e24b26d0)  Check IP address restrictions in API keys
- [`269d1f17`](https://github.com/staart/api/commit/269d1f17)  Rename jwt -&gt; staart in auth
- [`500534bf`](https://github.com/staart/api/commit/500534bf)  Use user object in auth objects
- [`149651c2`](https://github.com/staart/api/commit/149651c2)  Use service name in loggers
- [`8ac6f7f3`](https://github.com/staart/api/commit/8ac6f7f3)  Send response time headers
- [`8e248ffa`](https://github.com/staart/api/commit/8e248ffa)  Don&#x27;t use native errors
- [`402ce140`](https://github.com/staart/api/commit/402ce140)  Don&#x27;t use HttpException
- [`a7ee700f`](https://github.com/staart/api/commit/a7ee700f)  Use error constants
- [`91aba2e3`](https://github.com/staart/api/commit/91aba2e3)  Add constants errors in pipes
- [`d0e58468`](https://github.com/staart/api/commit/d0e58468)  Add descriptions for errors
- [`52e5de85`](https://github.com/staart/api/commit/52e5de85)  Add global controller prefix

### 🐛 Bug fixes

- [`cf4a8fd7`](https://github.com/staart/api/commit/cf4a8fd7)  Wait for token to be generated
- [`128d995b`](https://github.com/staart/api/commit/128d995b)  Use Tokens.LOGIN_LINK to verify JWT
- [`661bf6d0`](https://github.com/staart/api/commit/661bf6d0)  Use string for userId in TWT
- [`75fc64fc`](https://github.com/staart/api/commit/75fc64fc)  Use take instead of first
- [`97b21181`](https://github.com/staart/api/commit/97b21181)  Use TWT in local to token
- [`780e7d6b`](https://github.com/staart/api/commit/780e7d6b)  Use where with ID key
- [`aea4d9f0`](https://github.com/staart/api/commit/aea4d9f0)  Use numbers not TWTs in controllers
- [`b3f2f9bc`](https://github.com/staart/api/commit/b3f2f9bc)  Use string adapter for casbin model
- [`294a5682`](https://github.com/staart/api/commit/294a5682)  Use integer IDs, not TWTs, in casbin policy
- [`14f3182e`](https://github.com/staart/api/commit/14f3182e)  Change scopes type in access token, API key
- [`17f07562`](https://github.com/staart/api/commit/17f07562)  Make sure user has a verified email
- [`dc67971e`](https://github.com/staart/api/commit/dc67971e)  Only allow admin, member roles in teams
- [`4d070cf1`](https://github.com/staart/api/commit/4d070cf1)  Create memberships manually
- [`27baa6ec`](https://github.com/staart/api/commit/27baa6ec)  Allow prefers email ID
- [`3a0f0c4d`](https://github.com/staart/api/commit/3a0f0c4d)  Change profilePicture to Url suffix
- [`87c9b13e`](https://github.com/staart/api/commit/87c9b13e)  Fix stripe customer ID key
- [`d3c8c255`](https://github.com/staart/api/commit/d3c8c255)  Change imports to config
- [`5e6d6918`](https://github.com/staart/api/commit/5e6d6918)  Make keys in DTO optional
- [`f27c6b9d`](https://github.com/staart/api/commit/f27c6b9d)  Ensure result exists before accessing user
- [`a166eaa1`](https://github.com/staart/api/commit/a166eaa1)  Fix use authentication in class
- [`9cdea097`](https://github.com/staart/api/commit/9cdea097)  Specify IP address when adding member, add module
- [`97096b3f`](https://github.com/staart/api/commit/97096b3f)  Use object payload for JWT
- [`99f9058d`](https://github.com/staart/api/commit/99f9058d)  Fix registration, location
- [`10a4b3c0`](https://github.com/staart/api/commit/10a4b3c0)  Fix password hash in Pwned
- [`087701ea`](https://github.com/staart/api/commit/087701ea)  Use module imports, not service
- [`fb46eeb4`](https://github.com/staart/api/commit/fb46eeb4)  Import StripeModule
- [`f883155b`](https://github.com/staart/api/commit/f883155b)  Move scopes endpoint to top
- [`e2541a16`](https://github.com/staart/api/commit/e2541a16)  Add ConfigModule to ApiKeysModule

### 🔒 Security issues

- [`e1d3e0cb`](https://github.com/staart/api/commit/e1d3e0cb)  Add AuthGuard in user endpoints
- [`653ccfc4`](https://github.com/staart/api/commit/653ccfc4)  Add scopes and guards on user routes

### ⬆️ Dependency updates

- [`5693784b`](https://github.com/staart/api/commit/5693784b)  Update @staart/redis
- [`c38e0464`](https://github.com/staart/api/commit/c38e0464)  Update @staart/redis to v2.3.0
- [`b9e775fd`](https://github.com/staart/api/commit/b9e775fd)  Update @staart/scripts to v1.17.0
- [`713dc5af`](https://github.com/staart/api/commit/713dc5af)  Update @staart/payments to v4.0.0
- [`59f2ec6b`](https://github.com/staart/api/commit/59f2ec6b)  Update @prisma to v2.4.1
- [`6e121dc5`](https://github.com/staart/api/commit/6e121dc5)  Update twt to v1.2.0
- [`71f81595`](https://github.com/staart/api/commit/71f81595)  Update cosmic to v1.0.1
- [`517fd0d1`](https://github.com/staart/api/commit/517fd0d1)  Update @staart/scripts to v1.18.0
- [`9bf7fcb5`](https://github.com/staart/api/commit/9bf7fcb5)  Update staart/scripts to v1.18.1
- [`51c8f8e8`](https://github.com/staart/api/commit/51c8f8e8)  Update @staart/elasticsearch to v2.2.4
- [`0edb1ad5`](https://github.com/staart/api/commit/0edb1ad5)  Update @staart/scripts to v1.18.2
- [`114e200d`](https://github.com/staart/api/commit/114e200d)  Update @sentry/node, @staart/scripts
- [`34242e21`](https://github.com/staart/api/commit/34242e21)  Update update-template to v1.1.2
- [`1262a0ff`](https://github.com/staart/api/commit/1262a0ff)  Update all dependencies
- [`5f48cf67`](https://github.com/staart/api/commit/5f48cf67)  Update all dependencies

### 💥 Breaking changes

- [`ec11d6f2`](https://github.com/staart/api/commit/ec11d6f2)  Add v3
