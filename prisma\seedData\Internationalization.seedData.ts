import { Language } from '@prisma/client';
import { WriteI18nDto } from 'src/modules/internationalization/internationalization.dto';

export const defaultI18nList: WriteI18nDto[] = [
  {
    key: "common.company.name",
    content: "HKT",
    language: Language.EN
  },
  {
    key: "common.company.name",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.company.name",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.platform.env",
    content: "",
    language: Language.EN
  },
  {
    key: "common.platform.env",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.platform.env",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.env.group.TEST",
    content: "TEST",
    language: Language.EN
  },
  {
    key: "common.env.group.TEST",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.env.group.TEST",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.env.group.PROD",
    content: "LIVE",
    language: Language.EN
  },
  {
    key: "common.env.group.PROD",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.env.group.PROD",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.env.group.LIVE",
    content: "LIVE",
    language: Language.EN
  },
  {
    key: "common.env.group.LIVE",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.env.group.LIVE",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.delete",
    content: "Delete",
    language: Language.EN
  },
  {
    key: "common.button.delete",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.delete",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.cancel",
    content: "Cancel",
    language: Language.EN
  },
  {
    key: "common.button.cancel",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.cancel",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.yes",
    content: "YES",
    language: Language.EN
  },
  {
    key: "common.button.yes",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.yes",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.download",
    content: "Download",
    language: Language.EN
  },
  {
    key: "common.button.download",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.download",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.import",
    content: "Import",
    language: Language.EN
  },
  {
    key: "common.button.import",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.import",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.template",
    content: "Template",
    language: Language.EN
  },
  {
    key: "common.button.template",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.template",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.save",
    content: "Save",
    language: Language.EN
  },
  {
    key: "common.button.save",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.save",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.update",
    content: "Update",
    language: Language.EN
  },
  {
    key: "common.button.update",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.update",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.export",
    content: "Export",
    language: Language.EN
  },
  {
    key: "common.button.export",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.export",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.exportReport",
    content: "Export Report",
    language: Language.EN
  },
  {
    key: "common.button.exportReport",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.exportReport",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.edit",
    content: "Edit",
    language: Language.EN
  },
  {
    key: "common.button.edit",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.edit",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.refresh",
    content: "Refresh",
    language: Language.EN
  },
  {
    key: "common.button.refresh",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.refresh",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.back",
    content: "Back",
    language: Language.EN
  },
  {
    key: "common.button.back",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.back",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.createSnapshot",
    content: "Create Snapshot",
    language: Language.EN
  },
  {
    key: "common.button.createSnapshot",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.createSnapshot",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.ok",
    content: "OK",
    language: Language.EN
  },
  {
    key: "common.button.ok",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.ok",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.remove",
    content: "Remove",
    language: Language.EN
  },
  {
    key: "common.button.remove",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.remove",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.reset",
    content: "Reset",
    language: Language.EN
  },
  {
    key: "common.button.reset",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.reset",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.proceed",
    content: "Proceed",
    language: Language.EN
  },
  {
    key: "common.button.proceed",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.proceed",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.confirm",
    content: "Confirm",
    language: Language.EN
  },
  {
    key: "common.button.confirm",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.confirm",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.button.submit",
    content: "Submit",
    language: Language.EN
  },
  {
    key: "common.button.submit",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.button.submit",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.table.action",
    content: "Action",
    language: Language.EN
  },
  {
    key: "common.table.action",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.table.action",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.table.updateAt",
    content: "Update At",
    language: Language.EN
  },
  {
    key: "common.table.updateAt",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.table.updateAt",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.table.updateBy",
    content: "Update By",
    language: Language.EN
  },
  {
    key: "common.table.updateBy",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.table.updateBy",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.table.startedAt",
    content: "Started At",
    language: Language.EN
  },
  {
    key: "common.table.startedAt",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.table.startedAt",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.table.startedBy",
    content: "Started By",
    language: Language.EN
  },
  {
    key: "common.table.startedBy",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.table.startedBy",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.table.completedAt",
    content: "Completed At",
    language: Language.EN
  },
  {
    key: "common.table.completedAt",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.table.completedAt",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.table.progress",
    content: "Progress",
    language: Language.EN
  },
  {
    key: "common.table.progress",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.table.progress",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.table.duration",
    content: "Duration",
    language: Language.EN
  },
  {
    key: "common.table.duration",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.table.duration",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.table.result",
    content: "Result",
    language: Language.EN
  },
  {
    key: "common.table.result",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.table.result",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.table.expireAt",
    content: "Expire at",
    language: Language.EN
  },
  {
    key: "common.table.expireAt",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.table.expireAt",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.table.filename",
    content: "File name",
    language: Language.EN
  },
  {
    key: "common.table.filename",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.table.filename",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.table.status",
    content: "Status",
    language: Language.EN
  },
  {
    key: "common.table.status",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.table.status",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.promptToken",
    content: "Prompt Token",
    language: Language.EN
  },
  {
    key: "common.dict.promptToken",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.promptToken",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.completionToken",
    content: "Completion Token",
    language: Language.EN
  },
  {
    key: "common.dict.completionToken",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.completionToken",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.embeddingToken",
    content: "Embedding Token",
    language: Language.EN
  },
  {
    key: "common.dict.embeddingToken",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.embeddingToken",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.success",
    content: "Success",
    language: Language.EN
  },
  {
    key: "common.dict.success",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.success",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.failed",
    content: "Failed",
    language: Language.EN
  },
  {
    key: "common.dict.failed",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.failed",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.pending",
    content: "Pending",
    language: Language.EN
  },
  {
    key: "common.dict.pending",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.pending",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.viewDetails",
    content: "View Details",
    language: Language.EN
  },
  {
    key: "common.dict.viewDetails",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.viewDetails",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.description",
    content: "Description",
    language: Language.EN
  },
  {
    key: "common.dict.description",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.description",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.filter",
    content: "Filter",
    language: Language.EN
  },
  {
    key: "common.dict.filter",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.filter",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.size",
    content: "Size",
    language: Language.EN
  },
  {
    key: "common.dict.size",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.size",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.platform",
    content: "Platform",
    language: Language.EN
  },
  {
    key: "common.dict.platform",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.platform",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.enable",
    content: "Enable",
    language: Language.EN
  },
  {
    key: "common.dict.enable",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.enable",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.disable",
    content: "Disable",
    language: Language.EN
  },
  {
    key: "common.dict.disable",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.disable",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.date",
    content: "Date",
    language: Language.EN
  },
  {
    key: "common.dict.date",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.date",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.ready",
    content: "Ready",
    language: Language.EN
  },
  {
    key: "common.dict.ready",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.ready",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.processing",
    content: "Processing",
    language: Language.EN
  },
  {
    key: "common.dict.processing",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.processing",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.dict.error",
    content: "Error",
    language: Language.EN
  },
  {
    key: "common.dict.error",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.dict.error",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.error.required",
    content: "Required",
    language: Language.EN
  },
  {
    key: "common.error.required",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.error.required",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "common.error.invalidEmails",
    content: "Invalid Email: {emails}, please check",
    language: Language.EN
  },
  {
    key: "common.error.invalidEmails",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "common.error.invalidEmails",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "llm.platform.VERTEX_AI",
    content: "Google Vertex AI",
    language: Language.EN
  },
  {
    key: "llm.platform.VERTEX_AI",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "llm.platform.VERTEX_AI",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "llm.platform.SENSENOVA",
    content: "SenseTime",
    language: Language.EN
  },
  {
    key: "llm.platform.SENSENOVA",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "llm.platform.SENSENOVA",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "llm.platform.AZURE",
    content: "Azure OpenAI",
    language: Language.EN
  },
  {
    key: "llm.platform.AZURE",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "llm.platform.AZURE",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "llm.platform.OPENAI",
    content: "OpenAI",
    language: Language.EN
  },
  {
    key: "llm.platform.OPENAI",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "llm.platform.OPENAI",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "llm.platform.AWS",
    content: "AWS Bedrock / SageMaker",
    language: Language.EN
  },
  {
    key: "llm.platform.AWS",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "llm.platform.AWS",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "llm.platform.HUGGING_FACE",
    content: "Hugging Face",
    language: Language.EN
  },
  {
    key: "llm.platform.HUGGING_FACE",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "llm.platform.HUGGING_FACE",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "llm.platform.ALIBABA",
    content: "Alibaba",
    language: Language.EN
  },
  {
    key: "llm.platform.ALIBABA",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "llm.platform.ALIBABA",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "menu.testAutomation",
    content: "Test Automation",
    language: Language.EN
  },
  {
    key: "menu.testAutomation",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "menu.testAutomation",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "selectLLM.label.locked",
    content: "Request to Unlock",
    language: Language.EN
  },
  {
    key: "selectLLM.label.locked",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "selectLLM.label.locked",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "selectLLM.tooltip.locked",
    content: "Go to “Upgrade Your Plan” to unlock this LLM.",
    language: Language.EN
  },
  {
    key: "selectLLM.tooltip.locked",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "selectLLM.tooltip.locked",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "selectLLM.label.deprecated",
    content: "Sunset",
    language: Language.EN
  },
  {
    key: "selectLLM.label.deprecated",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "selectLLM.label.deprecated",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "selectLLM.tooltip.deprecated",
    content: "This LLM is deprecated and will be sunset soon.",
    language: Language.EN
  },
  {
    key: "selectLLM.tooltip.deprecated",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "selectLLM.tooltip.deprecated",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "selectLLM.filter.isRecommended",
    content: " - System Recommended",
    language: Language.EN
  },
  {
    key: "selectLLM.filter.isRecommended",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "selectLLM.filter.isRecommended",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "selectLLM.filter.isMostlyUsed",
    content: " - Trending",
    language: Language.EN
  },
  {
    key: "selectLLM.filter.isMostlyUsed",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "selectLLM.filter.isMostlyUsed",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "selectLLM.filter.isNew",
    content: " - New",
    language: Language.EN
  },
  {
    key: "selectLLM.filter.isNew",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "selectLLM.filter.isNew",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "selectLLM.error.disabled",
    content: "Invalid LLM Model, please update",
    language: Language.EN
  },
  {
    key: "selectLLM.error.disabled",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "selectLLM.error.disabled",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "selectLLM.error.deprecated",
    content: "Invalid LLM Model, please update",
    language: Language.EN
  },
  {
    key: "selectLLM.error.deprecated",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "selectLLM.error.deprecated",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "selectLLM.error.required",
    content: "Please select the LLMs",
    language: Language.EN
  },
  {
    key: "selectLLM.error.required",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "selectLLM.error.required",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "selectMessage.filter.isRecommended",
    content: "Recommended",
    language: Language.EN
  },
  {
    key: "selectMessage.filter.isRecommended",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "selectMessage.filter.isRecommended",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "selectMessage.filter.isMostlyUsed",
    content: "Trending Models",
    language: Language.EN
  },
  {
    key: "selectMessage.filter.isMostlyUsed",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "selectMessage.filter.isMostlyUsed",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "selectMessage.filter.isNew",
    content: "New Models",
    language: Language.EN
  },
  {
    key: "selectMessage.filter.isNew",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "selectMessage.filter.isNew",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "selectMessage.filter.all",
    content: "All",
    language: Language.EN
  },
  {
    key: "selectMessage.filter.all",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "selectMessage.filter.all",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.common.aiSearch",
    content: "AI Search",
    language: Language.EN
  },
  {
    key: "dataSource.common.aiSearch",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.common.aiSearch",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.error.transfferLimitExceed",
    content: "Daily upload limit exceeds. You can only transffer {daily_remaining_upload} more file(s) to project space today",
    language: Language.EN
  },
  {
    key: "dataSource.error.transfferLimitExceed",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.error.transfferLimitExceed",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.error.filesCannotAiSearch",
    content: "Please select only files that can be processed by AI Search.",
    language: Language.EN
  },
  {
    key: "dataSource.error.filesCannotAiSearch",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.error.filesCannotAiSearch",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.fileHandling.fileSize",
    content: "Approximated Sizes:",
    language: Language.EN
  },
  {
    key: "dataSource.fileHandling.fileSize",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.fileHandling.fileSize",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.fileHandling.fileSize.tooltip.warning",
    content: "Warning: The selected data may be too large for the LLM context window, which may result in poor performance.",
    language: Language.EN
  },
  {
    key: "dataSource.fileHandling.fileSize.tooltip.warning",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.fileHandling.fileSize.tooltip.warning",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.fileHandling.originalDoc.title",
    content: "{name} - Small size content",
    language: Language.EN
  },
  {
    key: "dataSource.fileHandling.originalDoc.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.fileHandling.originalDoc.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.fileHandling.originalDoc.fileSizeRemarks",
    content: " ",
    language: Language.EN
  },
  {
    key: "dataSource.fileHandling.originalDoc.fileSizeRemarks",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.fileHandling.originalDoc.fileSizeRemarks",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.fileHandling.originalDoc.remarks",
    content: "Limitation depends on the LLM's own context window; for example, Gemini Series can process up to a 200-page PDF, approximately 7MB of data. This feature does not apply to the “Auto-Select All Files” and “Auto-Select Files By Filter” option in the project space.",
    language: Language.EN
  },
  {
    key: "dataSource.fileHandling.originalDoc.remarks",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.fileHandling.originalDoc.remarks",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.fileHandling.originalDoc.confirmPopUp.title",
    content: "Change File Handling",
    language: Language.EN
  },
  {
    key: "dataSource.fileHandling.originalDoc.confirmPopUp.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.fileHandling.originalDoc.confirmPopUp.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.fileHandling.originalDoc.confirmPopUp.content",
    content: "Due to the selection of 'Chat With Files,' the ‘Auto-Select All Files’ or ‘Auto-Select Files By Tags’ setting in your project space will be disabled. To proceed, please click 'Proceed'.",
    language: Language.EN
  },
  {
    key: "dataSource.fileHandling.originalDoc.confirmPopUp.content",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.fileHandling.originalDoc.confirmPopUp.content",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.fileHandling.aiSearch.title",
    content: "AI Search(Embedding) - Large size content/Searching",
    language: Language.EN
  },
  {
    key: "dataSource.fileHandling.aiSearch.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.fileHandling.aiSearch.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.fileHandling.aiSearch.remarks",
    content: "It must be located at “Project Space” to process embedding.\nIt may take a while for embedding to index if the file is embedding-not-ready.",
    language: Language.EN
  },
  {
    key: "dataSource.fileHandling.aiSearch.remarks",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.fileHandling.aiSearch.remarks",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.label.enableSource",
    content: "Enable Data Source:",
    language: Language.EN
  },
  {
    key: "dataSource.label.enableSource",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.label.enableSource",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.projectSpace.label.autoSelectAll",
    content: "Auto Select All Files:",
    language: Language.EN
  },
  {
    key: "dataSource.projectSpace.label.autoSelectAll",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.projectSpace.label.autoSelectAll",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.projectSpace.tooltip.autoSelectAll",
    content: "Select all files means apply all available data from “Upload Files” to data source. \nOnce enabled, no need to select files and “Chat with File” is not available, but still can enable max 3 file as “CwFs”",
    language: Language.EN
  },
  {
    key: "dataSource.projectSpace.tooltip.autoSelectAll",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.projectSpace.tooltip.autoSelectAll",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.projectSpace.label.fileSelectStrategy",
    content: "When New Files Approved:",
    language: Language.EN
  },
  {
    key: "dataSource.projectSpace.label.fileSelectStrategy",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.projectSpace.label.fileSelectStrategy",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.projectSpace.searchDoc",
    content: "Search Files:",
    language: Language.EN
  },
  {
    key: "dataSource.projectSpace.searchDoc",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.projectSpace.searchDoc",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.projectSpace.manageSpace",
    content: "Manage Project Space",
    language: Language.EN
  },
  {
    key: "dataSource.projectSpace.manageSpace",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.projectSpace.manageSpace",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.projectSpace.selectedDoc",
    content: "Selected Files:",
    language: Language.EN
  },
  {
    key: "dataSource.projectSpace.selectedDoc",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.projectSpace.selectedDoc",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.projectSpace.selectAll",
    content: "Select All",
    language: Language.EN
  },
  {
    key: "dataSource.projectSpace.selectAll",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.projectSpace.selectAll",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.projectSpace.option.autoSelectAll",
    content: "Auto-Select All Files",
    language: Language.EN
  },
  {
    key: "dataSource.projectSpace.option.autoSelectAll",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.projectSpace.option.autoSelectAll",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.projectSpace.option.remarks.autoSelectAll",
    content: "Available for AI Search Only",
    language: Language.EN
  },
  {
    key: "dataSource.projectSpace.option.remarks.autoSelectAll",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.projectSpace.option.remarks.autoSelectAll",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.projectSpace.option.filterSelect",
    content: "Auto-Select Files By Filter",
    language: Language.EN
  },
  {
    key: "dataSource.projectSpace.option.filterSelect",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.projectSpace.option.filterSelect",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.projectSpace.option.remarks.filterSelect",
    content: "Available for AI Search Only",
    language: Language.EN
  },
  {
    key: "dataSource.projectSpace.option.remarks.filterSelect",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.projectSpace.option.remarks.filterSelect",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.projectSpace.option.manualSelect",
    content: "Manual selection",
    language: Language.EN
  },
  {
    key: "dataSource.projectSpace.option.manualSelect",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.projectSpace.option.manualSelect",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.projectSpace.option.remarks.manualSelect",
    content: "Available for AI Search and Chat with Files",
    language: Language.EN
  },
  {
    key: "dataSource.projectSpace.option.remarks.manualSelect",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.projectSpace.option.remarks.manualSelect",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.yourSpace.uploadDocDeclaration",
    content: "Declaration: By uploading new files, you acknowledge that you(as a bot owner/admin/contributor) have self-approved the file and that the file only contains Public Domain Data.",
    language: Language.EN
  },
  {
    key: "dataSource.yourSpace.uploadDocDeclaration",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.yourSpace.uploadDocDeclaration",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.yourSpace.downloadReport",
    content: "Download Report",
    language: Language.EN
  },
  {
    key: "dataSource.yourSpace.downloadReport",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.yourSpace.downloadReport",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.yourSpace.pii.riskDetected",
    content: "Risk Detected",
    language: Language.EN
  },
  {
    key: "dataSource.yourSpace.pii.riskDetected",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.yourSpace.pii.riskDetected",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.yourSpace.pii.scanned",
    content: "Scanned",
    language: Language.EN
  },
  {
    key: "dataSource.yourSpace.pii.scanned",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.yourSpace.pii.scanned",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.yourSpace.error.fileSizeExceed",
    content: "File size should not exceed {size}",
    language: Language.EN
  },
  {
    key: "dataSource.yourSpace.error.fileSizeExceed",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.yourSpace.error.fileSizeExceed",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.yourSpace.button.uploadNew",
    content: "Upload New",
    language: Language.EN
  },
  {
    key: "dataSource.yourSpace.button.uploadNew",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.yourSpace.button.uploadNew",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.yourSpace.tooltip.uploadNew",
    content: "Public Domain Data Only",
    language: Language.EN
  },
  {
    key: "dataSource.yourSpace.tooltip.uploadNew",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.yourSpace.tooltip.uploadNew",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.genKb.label.numOfDoc",
    content: "Num. of pages to retrieve (Max: {max})",
    language: Language.EN
  },
  {
    key: "dataSource.genKb.label.numOfDoc",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.genKb.label.numOfDoc",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.genKb.label.numOfDoc.tooltip",
    content: "Controls number of pages retrieving from GenKB as most relevant searching result for AI Chat.",
    language: Language.EN
  },
  {
    key: "dataSource.genKb.label.numOfDoc.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.genKb.label.numOfDoc.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.table.selectPages",
    content: "Select pages",
    language: Language.EN
  },
  {
    key: "dataSource.table.selectPages",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.table.selectPages",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.table.classifications",
    content: "Classifications",
    language: Language.EN
  },
  {
    key: "dataSource.table.classifications",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.table.classifications",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.table.tagging",
    content: "Tagging",
    language: Language.EN
  },
  {
    key: "dataSource.table.tagging",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.table.tagging",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.table.piiScanning",
    content: "PII Scanning",
    language: Language.EN
  },
  {
    key: "dataSource.table.piiScanning",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.table.piiScanning",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.table.malwareScanning",
    content: "Virus Scanning",
    language: Language.EN
  },
  {
    key: "dataSource.table.malwareScanning",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.table.malwareScanning",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.table.aiSearch",
    content: "AI Search",
    language: Language.EN
  },
  {
    key: "dataSource.table.aiSearch",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.table.aiSearch",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.table.cell.indexing",
    content: "Indexing",
    language: Language.EN
  },
  {
    key: "dataSource.table.cell.indexing",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.table.cell.indexing",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.table.cell.fileToBeRemove",
    content: "File To Be Removed",
    language: Language.EN
  },
  {
    key: "dataSource.table.cell.fileToBeRemove",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.table.cell.fileToBeRemove",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.table.cell.scanPii",
    content: "Scan Pii",
    language: Language.EN
  },
  {
    key: "dataSource.table.cell.scanPii",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.table.cell.scanPii",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.table.cell.scanMalware",
    content: "Scan Malware",
    language: Language.EN
  },
  {
    key: "dataSource.table.cell.scanMalware",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.table.cell.scanMalware",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.table.cell.aiSearchTooltip",
    content: "AI Search is not available for this file [ {error_code} ]",
    language: Language.EN
  },
  {
    key: "dataSource.table.cell.aiSearchTooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.table.cell.aiSearchTooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.table.cell.notApplicable",
    content: "Not Applicable",
    language: Language.EN
  },
  {
    key: "dataSource.table.cell.notApplicable",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.table.cell.notApplicable",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.popUp.button.save",
    content: "Save Data Source",
    language: Language.EN
  },
  {
    key: "dataSource.popUp.button.save",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.popUp.button.save",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.popUp.button.saveSystem",
    content: "Save Global Settings",
    language: Language.EN
  },
  {
    key: "dataSource.popUp.button.saveSystem",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.popUp.button.saveSystem",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.popUp.button.toUploadData",
    content: "Go to Upload Data",
    language: Language.EN
  },
  {
    key: "dataSource.popUp.button.toUploadData",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.popUp.button.toUploadData",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.popUp.fileTransfer.title",
    content: "Personal Space Data Selected (AI Search)",
    language: Language.EN
  },
  {
    key: "dataSource.popUp.fileTransfer.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.popUp.fileTransfer.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.popUp.fileTransfer.message",
    content: "Data: Personal Space Data Selected\nData Handling: AI Search\nAction: Required to move data from Personal Space to Share Space and perform indexing.\n\nUpon clicking OK, the selected data will be moved from “Your Space” to “Project Space,” where it will be accessible to bot members who has permission to see “Upload Files”,  and start indexing the file for AI Search(embedding).\n\nTo index AI embedding, you acknowledge that you have self-approved the file and that the file only contains Public Domain Data.",
    language: Language.EN
  },
  {
    key: "dataSource.popUp.fileTransfer.message",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.popUp.fileTransfer.message",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.popUp.originalDocFileTransfer.title",
    content: "Personal Space Data Selected (Chat with Files)",
    language: Language.EN
  },
  {
    key: "dataSource.popUp.originalDocFileTransfer.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.popUp.originalDocFileTransfer.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.popUp.originalDocFileTransfer.message",
    content: "Data: Personal Space Data Selected\nData Handling: Chat with Files\nAction: Required to move data from Personal Space to Share Space.\n\nUpon clicking OK, the selected data will be moved from “Your Space” to “Project Space,” where it will be accessible to bot members who has permission to see “Upload Files”.\n\nTo upload file to “Share Space”, you acknowledge that you have self-approved the file and that the file only contains Public Domain Data.",
    language: Language.EN
  },
  {
    key: "dataSource.popUp.originalDocFileTransfer.message",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.popUp.originalDocFileTransfer.message",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.popUp.exceedMaxSelect.title",
    content: "Reaching max amount of file",
    language: Language.EN
  },
  {
    key: "dataSource.popUp.exceedMaxSelect.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.popUp.exceedMaxSelect.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.popUp.exceedMaxSelect.message",
    content: "Please remove some of your files at “Data Source” and upload again.",
    language: Language.EN
  },
  {
    key: "dataSource.popUp.exceedMaxSelect.message",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.popUp.exceedMaxSelect.message",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.popUp.noRightAiSearch.title",
    content: "Indexing AI Search",
    language: Language.EN
  },
  {
    key: "dataSource.popUp.noRightAiSearch.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.popUp.noRightAiSearch.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.popUp.noRightAiSearch.message",
    content: "Indexing AI Search is only available to files approved by owner or admin. Please remove your selection from “Your Space”, and select file from “Project Space” to enable AI embedding. \n\nIf you would like to use your selected file, please go to the 'Upload Files' page to complete the approval process.\n\n(You may check your role at Member Page)",
    language: Language.EN
  },
  {
    key: "dataSource.popUp.noRightAiSearch.message",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.popUp.noRightAiSearch.message",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.popUp.switchToData.title",
    content: "Switching to Data Mode",
    language: Language.EN
  },
  {
    key: "dataSource.popUp.switchToData.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.popUp.switchToData.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.popUp.switchToData.message",
    content: "\"Data Mode\" supports only .csv and .xls file types, with a maximum of {max_file_select} files in Original File mode. Once you click \"Proceed\", we will deselect all your currently selected files and change the mode to \"Original File\". Please reselect your files in the data source.\n\nNote: Connect API and GenKB are not supported.",
    language: Language.EN
  },
  {
    key: "dataSource.popUp.switchToData.message",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.popUp.switchToData.message",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.detectionDetails.title",
    content: "Detection Details",
    language: Language.EN
  },
  {
    key: "dataSource.detectionDetails.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.detectionDetails.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.detectionDetails.tabs.pii.label",
    content: "PII & Prompt Injection",
    language: Language.EN
  },
  {
    key: "dataSource.detectionDetails.tabs.pii.label",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.detectionDetails.tabs.pii.label",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.popUp.pii.label.tooltip",
    content: "Due to possible changes in malware scanning, the system will monthly re-scan files uploaded in past two months with latest version. You can retrieve time-based results for check.",
    language: Language.EN
  },
  {
    key: "dataSource.popUp.pii.label.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.popUp.pii.label.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.detectionDetails.tabs.malware.label",
    content: "Malware Scanning",
    language: Language.EN
  },
  {
    key: "dataSource.detectionDetails.tabs.malware.label",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.detectionDetails.tabs.malware.label",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dataSource.popUp.malware.label.tooltip",
    content: "Due to possible changes in security policies or audit requirements, the system may automatically re-scan with latest LLM Guard version. You can download time-based reports for check.",
    language: Language.EN
  },
  {
    key: "dataSource.popUp.malware.label.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dataSource.popUp.malware.label.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "scheduler.label.frequency",
    content: "Frequency",
    language: Language.EN
  },
  {
    key: "scheduler.label.frequency",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "scheduler.label.frequency",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "scheduler.label.repeat",
    content: "Repeat",
    language: Language.EN
  },
  {
    key: "scheduler.label.repeat",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "scheduler.label.repeat",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "scheduler.label.interval",
    content: "Repeat Every",
    language: Language.EN
  },
  {
    key: "scheduler.label.interval",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "scheduler.label.interval",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "scheduler.label.repeatOn",
    content: "Repeat On",
    language: Language.EN
  },
  {
    key: "scheduler.label.repeatOn",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "scheduler.label.repeatOn",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "scheduler.label.startDate",
    content: "Start Date",
    language: Language.EN
  },
  {
    key: "scheduler.label.startDate",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "scheduler.label.startDate",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "scheduler.label.presetDate",
    content: "Preset Date",
    language: Language.EN
  },
  {
    key: "scheduler.label.presetDate",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "scheduler.label.presetDate",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "scheduler.label.endDate",
    content: "End Date",
    language: Language.EN
  },
  {
    key: "scheduler.label.endDate",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "scheduler.label.endDate",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "scheduler.tooltip.expectedStartTime",
    content: "The expected start time is only used as a reference for system. When there are many job queues, there may be delays. Please refer to the actual situation.",
    language: Language.EN
  },
  {
    key: "scheduler.tooltip.expectedStartTime",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "scheduler.tooltip.expectedStartTime",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "scheduler.error.repeatOn",
    content: "Please select at least one day",
    language: Language.EN
  },
  {
    key: "scheduler.error.repeatOn",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "scheduler.error.repeatOn",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "scheduler.error.date",
    content: "Please select a valid date",
    language: Language.EN
  },
  {
    key: "scheduler.error.date",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "scheduler.error.date",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "scheduler.error.expectedStartTime",
    content: "Please select a valid time",
    language: Language.EN
  },
  {
    key: "scheduler.error.expectedStartTime",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "scheduler.error.expectedStartTime",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "botLists.button.createNewBot",
    content: "Create New Bot",
    language: Language.EN
  },
  {
    key: "botLists.button.createNewBot",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "botLists.button.createNewBot",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "botLists.table.tooltip.incomplete",
    content: "Bot info incomplete, complete now at",
    language: Language.EN
  },
  {
    key: "botLists.table.tooltip.incomplete",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "botLists.table.tooltip.incomplete",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "flowLists.table.tooltip.incomplete",
    content: "Flow info incomplete, complete now at",
    language: Language.EN
  },
  {
    key: "flowLists.table.tooltip.incomplete",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "flowLists.table.tooltip.incomplete",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "flowiseLists.button.createNewFlow",
    content: "Create New Flow",
    language: Language.EN
  },
  {
    key: "flowiseLists.button.createNewFlow",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "flowiseLists.button.createNewFlow",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "changeManagement.button.deleteSnapshot",
    content: "Delete Snapsnot",
    language: Language.EN
  },
  {
    key: "changeManagement.button.deleteSnapshot",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "changeManagement.button.deleteSnapshot",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "changeManagement.entityType.API_KEY",
    content: "Api Key",
    language: Language.EN
  },
  {
    key: "changeManagement.entityType.API_KEY",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "changeManagement.entityType.API_KEY",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "changeManagement.entityType.LLM_MODEL",
    content: "Bot Setting",
    language: Language.EN
  },
  {
    key: "changeManagement.entityType.LLM_MODEL",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "changeManagement.entityType.LLM_MODEL",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "changeManagement.entityType.FLOW",
    content: "Flow Setting",
    language: Language.EN
  },
  {
    key: "changeManagement.entityType.FLOW",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "changeManagement.entityType.FLOW",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "changeManagement.entityType.API_RESOURCE",
    content: "Connect API",
    language: Language.EN
  },
  {
    key: "changeManagement.entityType.API_RESOURCE",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "changeManagement.entityType.API_RESOURCE",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.createNewChat.select.selectProject",
    content: "Select Project",
    language: Language.EN
  },
  {
    key: "playground.createNewChat.select.selectProject",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.createNewChat.select.selectProject",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.common.invalidLLM",
    content: "Invalid LLM Model",
    language: Language.EN
  },
  {
    key: "playground.common.invalidLLM",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.common.invalidLLM",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.createNewChat.select.selectTemplate",
    content: "Select a Template",
    language: Language.EN
  },
  {
    key: "playground.createNewChat.select.selectTemplate",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.createNewChat.select.selectTemplate",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.createNewChat.label.botName",
    content: "Bot Name for LLM",
    language: Language.EN
  },
  {
    key: "playground.createNewChat.label.botName",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.createNewChat.label.botName",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.createNewChat.label.chatName",
    content: "Chat Name",
    language: Language.EN
  },
  {
    key: "playground.createNewChat.label.chatName",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.createNewChat.label.chatName",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.createNewChat.label.botInstruction",
    content: "Bot Instruction",
    language: Language.EN
  },
  {
    key: "playground.createNewChat.label.botInstruction",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.createNewChat.label.botInstruction",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.createNewChat.systemMessage.tooltip",
    content: "An instructions on how it should behave and what context it should consider when generating responses. This includes defining the bot's personality, specifying what types of questions it should and shouldn't answer, and outlining how responses should be formatted. This section will be included with every API call, so it's important to be concise and efficient with your wording.",
    language: Language.EN
  },
  {
    key: "playground.createNewChat.systemMessage.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.createNewChat.systemMessage.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.createNewChat.label.welcomeMessage",
    content: "Welcome Message",
    language: Language.EN
  },
  {
    key: "playground.createNewChat.label.welcomeMessage",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.createNewChat.label.welcomeMessage",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.createNewChat.welcomeMessage.tooltip",
    content: "The very first message the user sees when they first land on the chat session.",
    language: Language.EN
  },
  {
    key: "playground.createNewChat.welcomeMessage.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.createNewChat.welcomeMessage.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.createNewChat.label.typeDefinition",
    content: "Type Definition",
    language: Language.EN
  },
  {
    key: "playground.createNewChat.label.typeDefinition",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.createNewChat.label.typeDefinition",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.createNewChat.typeDefinition.tooltip",
    content: "List specific definitions of terms in your context that the LLM may not know, e.g., HKT means Hong Kong Telecom; EC means Enterprise Centrex.",
    language: Language.EN
  },
  {
    key: "playground.createNewChat.typeDefinition.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.createNewChat.typeDefinition.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chatSettings.button.cloneNewChat",
    content: "Clone This Chat",
    language: Language.EN
  },
  {
    key: "playground.chatSettings.button.cloneNewChat",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chatSettings.button.cloneNewChat",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chats.tooltip.system",
    content: "This is the default setting of this bot, it is pinned to top and no one can delete this chat. All users can enjoy the chat session with the system bot. Yet only Bot Owner and Admin can change its settings. ",
    language: Language.EN
  },
  {
    key: "playground.chats.tooltip.system",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chats.tooltip.system",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chatWithExtra.option.tooltip.uploadNewAvailability",
    content: "Available to: Owner/Admin",
    language: Language.EN
  },
  {
    key: "playground.chatWithExtra.option.tooltip.uploadNewAvailability",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chatWithExtra.option.tooltip.uploadNewAvailability",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chatWithExtra.systemMessage.noFileSelected.title",
    content: "No data being selected!",
    language: Language.EN
  },
  {
    key: "playground.chatWithExtra.systemMessage.noFileSelected.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chatWithExtra.systemMessage.noFileSelected.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chatWithExtra.systemMessage.noFileSelected.content.files",
    content: "The bucket is currently empty. Enabling this function without any files means that there is no data being analyzed. We strongly recommend you to upload chat data or select existing data to proceed.",
    language: Language.EN
  },
  {
    key: "playground.chatWithExtra.systemMessage.noFileSelected.content.files",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chatWithExtra.systemMessage.noFileSelected.content.files",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chatWithExtra.systemMessage.noFileSelected.content.data",
    content: "The bucket is currently empty. Enabling this function without any files means that there is no data being analyzed. We strongly recommend you to upload chat data to proceed.",
    language: Language.EN
  },
  {
    key: "playground.chatWithExtra.systemMessage.noFileSelected.content.data",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chatWithExtra.systemMessage.noFileSelected.content.data",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chatWithExtra.radio.all",
    content: "All",
    language: Language.EN
  },
  {
    key: "playground.chatWithExtra.radio.all",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chatWithExtra.radio.all",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chatWithExtra.radio.range",
    content: "Range",
    language: Language.EN
  },
  {
    key: "playground.chatWithExtra.radio.range",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chatWithExtra.radio.range",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chatWithFile.tooltip",
    content: "<b>File Format:</b><br/>• For common LLM, like GPT 4, Supports *.xlsx, *.csv, *.pdf files for analysis.<br/>• For Multimodal, like Gemini, Support *.pdf, *.csv, *.xlsx, *.jpg, *.mp3, *.mp4 files for analysis.<br/><br/><b>Additional Requirements:</b><br/>• Adding a maximum of {max_file_add} files<br/>• Selecting a maximum of {max_file_select} files<br/>• For common LLM, like GPT, File size < 20 MB. (Recommend ≤ 50 pages per pdf file. By default, process first 50 pages if exceeded).<br/>• For Multimodal, like Gemini, File size < 7 MB.<br/><br/><b>Requirements applied to Chat:</b><br/>• Recommend using multimodal (Gemini) or 32k LLMs.<br/><br/><b>Security Support:</b><br/>• Visible and usable exclusively by the uploader. Apply to all chat sessions.<br/>• The files in this bucket will be cleared after 24 hours.<br/><br/>Please be noted that while using Chat with file, the bot instruction will be ignored.",
    language: Language.EN
  },
  {
    key: "playground.chatWithFile.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chatWithFile.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chatWithData.tooltip",
    content: "<b>File Format:</b><br/>• Upload Excel (.xlsx)or comma-delimited( .csv) files for asking mathematical questions or plotting graphs.<br/>• The first row of the .csv or .xlsx must contain the column names in English<br/>• The columns names with date values should contain the word \"Date\".<br/><br/><b>Additional Requirements:</b><br/>• Adding a maximum of {max_file_add} files<br/>• Selecting a maximum of {max_file_select} files<br/>• File size under 7 MB.<br/><br/><b>Requirements applied to Chat:</b><br/>• Supported by LLM: GPT-4o. Recommend users to open a new chat session with GPT-4o as LLM.<br/>• It is recommended to ask questions in English for the best results.<br/>• Recommend setting 1 as past message before using if not default.<br/>• It is important to specify the column name in the question. And recommend adding “Do not use scientific notation for the_specific_column_name” as the prompt for question. Otherwise, the system might make assumption or handle data improperly, if value is too long.<br/>The question should be as specific as possible and avoid asking open-ended questions.<br/><br/><b>Security Support:</b><br/>• Visible and usable exclusively by the uploader. Apply to all chat sessions.<br/>• The files in this bucket will be cleared after 24 hours.<br/><br/>Please be noted that while using Chat with Data, the bot instruction will be ignored.",
    language: Language.EN
  },
  {
    key: "playground.chatWithData.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chatWithData.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chatWithData.switchButton.tableJoining",
    content: "Merge Files by Common Columns",
    language: Language.EN
  },
  {
    key: "playground.chatWithData.switchButton.tableJoining",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chatWithData.switchButton.tableJoining",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chatWithData.tooltip.tableJoining",
    content: "By enabling 'Merge Files by Common Columns', it means that it automatically matches and merges data from multiple files by same column names.  By default, it's not enabled. It means that the data will be automatically unioned without same column merging when multiple files are merged.",
    language: Language.EN
  },
  {
    key: "playground.chatWithData.tooltip.tableJoining",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chatWithData.tooltip.tableJoining",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.playgroundSettings.tooltip.reference",
    content: "Your prompt requires the LLM to provide source data, example:\nEach source information is formatted in [reference number], {page-x}: {given content}. eg. [1], page-1: {content}. If you are referencing the source information, please always cited the [reference number] eg. [1] at the end of answer.",
    language: Language.EN
  },
  {
    key: "playground.playgroundSettings.tooltip.reference",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.playgroundSettings.tooltip.reference",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chatInfo.tab.title",
    content: "Instructions",
    language: Language.EN
  },
  {
    key: "playground.chatInfo.tab.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chatInfo.tab.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.parameters.tab.title",
    content: "Parameters",
    language: Language.EN
  },
  {
    key: "playground.parameters.tab.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.parameters.tab.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chatList.searchChat",
    content: "Search Chat Name",
    language: Language.EN
  },
  {
    key: "playground.chatList.searchChat",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chatList.searchChat",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.tab.title",
    content: "Security",
    language: Language.EN
  },
  {
    key: "playground.botSettings.tab.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.tab.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.button.saveSettings",
    content: "Update Setting",
    language: Language.EN
  },
  {
    key: "playground.botSettings.button.saveSettings",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.button.saveSettings",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.popUp.title",
    content: "Security Settings",
    language: Language.EN
  },
  {
    key: "playground.botSettings.popUp.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.popUp.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.popUp.desccription",
    content: "This filter analyzes prompts for personal information, confidential data, or any content that could compromise user privacy. This includes names, addresses, phone numbers, and other identifiable information.",
    language: Language.EN
  },
  {
    key: "playground.botSettings.popUp.desccription",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.popUp.desccription",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.popUp.securityOptions.OFF",
    content: "Off",
    language: Language.EN
  },
  {
    key: "playground.botSettings.popUp.securityOptions.OFF",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.popUp.securityOptions.OFF",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.popUp.securityOptions.SOFT",
    content: "Soft (Show Warning)",
    language: Language.EN
  },
  {
    key: "playground.botSettings.popUp.securityOptions.SOFT",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.popUp.securityOptions.SOFT",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.popUp.securityOptions.HARD",
    content: "Hard (Not Allow to Proceed)",
    language: Language.EN
  },
  {
    key: "playground.botSettings.popUp.securityOptions.HARD",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.popUp.securityOptions.HARD",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.popUp.label.sensitivity",
    content: "Sensitivity (The lower the more sensitive)",
    language: Language.EN
  },
  {
    key: "playground.botSettings.popUp.label.sensitivity",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.popUp.label.sensitivity",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.popUp.label.whitelist",
    content: "Whitelist",
    language: Language.EN
  },
  {
    key: "playground.botSettings.popUp.label.whitelist",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.popUp.label.whitelist",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.popUp.whitelist.remarks",
    content: "The LLM Guard whitelisting is weak at detecting numbering and naming, especially for phrases with more than two words. Your feedback is valuable; we are improving this experience.",
    language: Language.EN
  },
  {
    key: "playground.botSettings.popUp.whitelist.remarks",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.popUp.whitelist.remarks",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.popUp.tooltip.whitelist",
    content: "Whitelist refers to a list of terms, phrases, or entities that are considered safe and are exempt from being anonymized. This means that if a term appears in a user's prompt, it will not be modified or removed during the anonymization process",
    language: Language.EN
  },
  {
    key: "playground.botSettings.popUp.tooltip.whitelist",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.popUp.tooltip.whitelist",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.select.Anonymize.title",
    content: "Input PII Detection",
    language: Language.EN
  },
  {
    key: "playground.botSettings.select.Anonymize.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.select.Anonymize.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.select.Anonymize.tooltip",
    content: "Input PII detection is a tool that helps protect the privacy of users interacting with large language models (LLMs). It can detect or block sensitive personal identification information from user inputs before sending them to the LLM.\n\nDetection level:\nOff: No Detection.\nSoft: Need confirm if detected.\nHard: Reject prompt if detected.",
    language: Language.EN
  },
  {
    key: "playground.botSettings.select.Anonymize.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.select.Anonymize.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.select.PromptInjection.title",
    content: "Prompt Injection Detection",
    language: Language.EN
  },
  {
    key: "playground.botSettings.select.PromptInjection.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.select.PromptInjection.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.select.PromptInjection.tooltip",
    content: "The Prompt Injection detection is a security tool that helps detect and block malicious attempts to inject harmful prompts into an LLM. It analyzes user inputs and can identify prompts that may try to manipulate the LLM's behavior in unintended ways, like generating abusive or biased content. The prompt injection scanner helps ensure the LLM remains safe and aligned with its intended purpose.\n\nDetection level:\nOff: No Detection.\nSoft: Need confirm if detected.\nHard: Reject prompt if detected.",
    language: Language.EN
  },
  {
    key: "playground.botSettings.select.PromptInjection.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.select.PromptInjection.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.select.Sensitive.title",
    content: "Output PII detection",
    language: Language.EN
  },
  {
    key: "playground.botSettings.select.Sensitive.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.select.Sensitive.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.botSettings.select.Sensitive.tooltip",
    content: "Output PII detection is a tool that helps detect and potentially sensitive in the outputs generated by large language model (LLM). This scanner analyzes the LLM's responses for detecting personal identification information (PII).\n\nDetection level:\nOff: No Detection.\nSoft: Outputs with prompt if detected.",
    language: Language.EN
  },
  {
    key: "playground.botSettings.select.Sensitive.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.botSettings.select.Sensitive.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.settings.tab.title",
    content: "Settings",
    language: Language.EN
  },
  {
    key: "playground.settings.tab.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.settings.tab.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.settings.popUp.title",
    content: "Permission Setting",
    language: Language.EN
  },
  {
    key: "playground.settings.popUp.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.settings.popUp.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.settings.label.activateBot",
    content: "Activate Bot",
    language: Language.EN
  },
  {
    key: "playground.settings.label.activateBot",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.settings.label.activateBot",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.settings.tooltip.activateBot",
    content: "Enable this to deploy your bot to the live environment after change request (CR) approval.",
    language: Language.EN
  },
  {
    key: "playground.settings.tooltip.activateBot",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.settings.tooltip.activateBot",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.settings.label.enableTeamsBot",
    content: "Enable Teams Bot",
    language: Language.EN
  },
  {
    key: "playground.settings.label.enableTeamsBot",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.settings.label.enableTeamsBot",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.settings.tooltip.enableTeamsBot",
    content: "Enable this to make your bot’s chat system accessible to your bot members in Microsoft Teams. They will have basic chat functionality without requiring the GDCN network.",
    language: Language.EN
  },
  {
    key: "playground.settings.tooltip.enableTeamsBot",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.settings.tooltip.enableTeamsBot",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.settings.label.refLinkInTeamsBot",
    content: "Show Reference Link In Teams Bot",
    language: Language.EN
  },
  {
    key: "playground.settings.label.refLinkInTeamsBot",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.settings.label.refLinkInTeamsBot",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.settings.tooltip.refLinkInTeamsBot",
    content: "Enable this so that reference links displayed in the playground are also shown in the Teams bot.",
    language: Language.EN
  },
  {
    key: "playground.settings.tooltip.refLinkInTeamsBot",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.settings.tooltip.refLinkInTeamsBot",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.settings.label.makeLiveToPublic",
    content: "Make Live Bot Public",
    language: Language.EN
  },
  {
    key: "playground.settings.label.makeLiveToPublic",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.settings.label.makeLiveToPublic",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.settings.tooltip.makeLiveToPublic",
    content: "If this setting is enabled, your live bot will be publicly accessible within this platform. Turning it off will make the bot private to your membership.",
    language: Language.EN
  },
  {
    key: "playground.settings.tooltip.makeLiveToPublic",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.settings.tooltip.makeLiveToPublic",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.settings.link.previewLiveBot",
    content: "Try Test Public Bot List Now",
    language: Language.EN
  },
  {
    key: "playground.settings.link.previewLiveBot",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.settings.link.previewLiveBot",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.settings.label.allowShareChat",
    content: "Allow Sharing Chat Answer",
    language: Language.EN
  },
  {
    key: "playground.settings.label.allowShareChat",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.settings.label.allowShareChat",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.settings.tooltip.allowShareChat",
    content: "Enable this to allow bot members to share AI-generated answers with individuals inside or outside the bot(login required).",
    language: Language.EN
  },
  {
    key: "playground.settings.tooltip.allowShareChat",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.settings.tooltip.allowShareChat",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.Anonymize.title",
    content: "PII Detected",
    language: Language.EN
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.Anonymize.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.Anonymize.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.PromptInjection.title",
    content: "Prompt Injection Detected",
    language: Language.EN
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.PromptInjection.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.PromptInjection.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.systemMessage.scannerLevel.hard",
    content: " (Level: Hard)",
    language: Language.EN
  },
  {
    key: "playground.chat.systemMessage.scannerLevel.hard",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.systemMessage.scannerLevel.hard",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.SOFT.SOFT",
    content: "We've identified your prompt is potentially containing sensitive information and is vulnerable to malicious manipulation. To protect the security and privacy of our platform and users, we advise masking detected information and rewriting your message. Alternatively, you can continue without masking or rewriting.",
    language: Language.EN
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.SOFT.SOFT",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.SOFT.SOFT",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.HARD.SOFT",
    content: "We've identified your prompt is potentially containing sensitive information and being vulnerable to malicious manipulation. Please rewrite your message and mask the PII Data to proceed.\n\nFor more Detection Level Tips, click <a class='text-key-blue underline' href='https://pccw0.sharepoint.com/:u:/r/sites/GenerativeAI/SitePages/Bot%20Setting%20%26%20Security%20Reports.aspx?csf=1&web=1&share=EakOgZx4yD9MvdWv36nS1DMBvuRJI2gAALSoUXUv7ihtig&e=xF5aRy' target='_blank'>here</a>",
    language: Language.EN
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.HARD.SOFT",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.HARD.SOFT",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.SOFT.HARD",
    content: "We've identified your prompt is potentially containing sensitive information and being vulnerable to malicious manipulation. To protect the security and privacy of our platform and users, we cannot proceed with your input at this time.\n\nFor more Detection Level Tips, click <a class='text-key-blue underline' href='https://pccw0.sharepoint.com/:u:/r/sites/GenerativeAI/SitePages/Bot%20Setting%20%26%20Security%20Reports.aspx?csf=1&web=1&share=EakOgZx4yD9MvdWv36nS1DMBvuRJI2gAALSoUXUv7ihtig&e=xF5aRy' target='_blank'>here</a>",
    language: Language.EN
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.SOFT.HARD",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.SOFT.HARD",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.HARD.HARD",
    content: "We've identified your prompt is potentially containing sensitive information and being vulnerable to malicious manipulation. To protect the security and privacy of our platform and users, we cannot proceed with your input at this time.\n\nFor more Detection Level Tips, click <a class='text-key-blue underline' href='https://pccw0.sharepoint.com/:u:/r/sites/GenerativeAI/SitePages/Bot%20Setting%20%26%20Security%20Reports.aspx?csf=1&web=1&share=EakOgZx4yD9MvdWv36nS1DMBvuRJI2gAALSoUXUv7ihtig&e=xF5aRy' target='_blank'>here</a>",
    language: Language.EN
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.HARD.HARD",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.HARD.HARD",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.Anonymize.SOFT",
    content: "Our system has detected sensitive information in your message. For your privacy and security, we recommend not sharing personal details. If you proceed, we advise masking detected information. Alternatively, you can continue without masking.",
    language: Language.EN
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.Anonymize.SOFT",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.Anonymize.SOFT",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.Anonymize.HARD",
    content: "Our system has detected sensitive information in your message. For your privacy and security, we recommend not sharing personal details.  Please rewrite your message or mask the PII Data to proceed.\n\nFor more Detection Level Tips, click <a class='text-key-blue underline' href='https://pccw0.sharepoint.com/:u:/r/sites/GenerativeAI/SitePages/Bot%20Setting%20%26%20Security%20Reports.aspx?csf=1&web=1&share=EakOgZx4yD9MvdWv36nS1DMBvuRJI2gAALSoUXUv7ihtig&e=xF5aRy' target='_blank'>here</a>",
    language: Language.EN
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.Anonymize.HARD",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.Anonymize.HARD",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.PromptInjection.SOFT",
    content: "We've identified your prompt is potentially vulnerable to malicious manipulation. To protect the security and privacy of our platform and users, we advise rewriting your prompt. Alternatively, you can continue without rewriting.",
    language: Language.EN
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.PromptInjection.SOFT",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.PromptInjection.SOFT",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.PromptInjection.HARD",
    content: "We've identified your prompt is potentially vulnerable to malicious manipulation. To protect the security and privacy of our platform and users, we cannot proceed with your input at this time.\n\nFor more Detection Level Tips, click <a class='text-key-blue underline' href='https://pccw0.sharepoint.com/:u:/r/sites/GenerativeAI/SitePages/Bot%20Setting%20%26%20Security%20Reports.aspx?csf=1&web=1&share=EakOgZx4yD9MvdWv36nS1DMBvuRJI2gAALSoUXUv7ihtig&e=xF5aRy' target='_blank'>here</a>",
    language: Language.EN
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.PromptInjection.HARD",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.systemMessage.inputScanFail.PromptInjection.HARD",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.systemMessage.outputScanFail.title",
    content: "PII Detected",
    language: Language.EN
  },
  {
    key: "playground.chat.systemMessage.outputScanFail.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.systemMessage.outputScanFail.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.systemMessage.outputScanFail",
    content: "The output you are about to view may contain sensitive or personal information.{pii_detected} Please review the content carefully and avoid sharing or acting on any private details without proper authorization.",
    language: Language.EN
  },
  {
    key: "playground.chat.systemMessage.outputScanFail",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.systemMessage.outputScanFail",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.fileExpiredAt",
    content: "*File(s) expiry time: {fileExpiredAt}.",
    language: Language.EN
  },
  {
    key: "playground.chat.fileExpiredAt",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.fileExpiredAt",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.copy.dropdown.allContent",
    content: "all content",
    language: Language.EN
  },
  {
    key: "playground.chat.copy.dropdown.allContent",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.copy.dropdown.allContent",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.copy.dropdown.markdown",
    content: "as markdown",
    language: Language.EN
  },
  {
    key: "playground.chat.copy.dropdown.markdown",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.copy.dropdown.markdown",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.fileHandling",
    content: "File Handling:",
    language: Language.EN
  },
  {
    key: "playground.chat.fileHandling",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.fileHandling",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.fileHandling.mode.originalDoc.files",
    content: "Chat With Files",
    language: Language.EN
  },
  {
    key: "playground.chat.fileHandling.mode.originalDoc.files",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.fileHandling.mode.originalDoc.files",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.fileHandling.mode.originalDoc.data",
    content: "Chat With Data",
    language: Language.EN
  },
  {
    key: "playground.chat.fileHandling.mode.originalDoc.data",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.fileHandling.mode.originalDoc.data",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.fileHandling.mode.aiSearch",
    content: "AI Search",
    language: Language.EN
  },
  {
    key: "playground.chat.fileHandling.mode.aiSearch",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.fileHandling.mode.aiSearch",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.mode.general",
    content: "General Chat",
    language: Language.EN
  },
  {
    key: "playground.chat.mode.general",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.mode.general",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.mode.data",
    content: "Data Mode",
    language: Language.EN
  },
  {
    key: "playground.chat.mode.data",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.mode.data",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.rating.tooltip",
    content: "Rate this response",
    language: Language.EN
  },
  {
    key: "playground.chat.rating.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.rating.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.rating.label.selectPositiveComment",
    content: "Please select your positive feedback",
    language: Language.EN
  },
  {
    key: "playground.chat.rating.label.selectPositiveComment",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.rating.label.selectPositiveComment",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.rating.label.selectImprovementComment",
    content: "Please select areas for improvement",
    language: Language.EN
  },
  {
    key: "playground.chat.rating.label.selectImprovementComment",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.rating.label.selectImprovementComment",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.rating.label.additionalComment",
    content: "Additional comments (max 100 characters)",
    language: Language.EN
  },
  {
    key: "playground.chat.rating.label.additionalComment",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.rating.label.additionalComment",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.chat.rating.placeholder.additionalComment",
    content: "Type your comment here...(NO PII DATA!)",
    language: Language.EN
  },
  {
    key: "playground.chat.rating.placeholder.additionalComment",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.chat.rating.placeholder.additionalComment",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.publicChat.button.goToAIStudio",
    content: "Go to AI Studio",
    language: Language.EN
  },
  {
    key: "playground.publicChat.button.goToAIStudio",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.publicChat.button.goToAIStudio",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.publicChat.text.yourRole",
    content: "You Role of this Workspace:",
    language: Language.EN
  },
  {
    key: "playground.publicChat.text.yourRole",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.publicChat.text.yourRole",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.inputPanel.button.send",
    content: "Send",
    language: Language.EN
  },
  {
    key: "playground.inputPanel.button.send",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.inputPanel.button.send",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.inputPanel.text.securityInDetection",
    content: "In Detection",
    language: Language.EN
  },
  {
    key: "playground.inputPanel.text.securityInDetection",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.inputPanel.text.securityInDetection",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.inputPanel.textarea.placeholder",
    content: "Talk to the bot...Do not enter any PII data!",
    language: Language.EN
  },
  {
    key: "playground.inputPanel.textarea.placeholder",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.inputPanel.textarea.placeholder",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.inputPanel.tooltip.changeDetectionLevel",
    content: "The current bot has set security detection. If need to change detection level, please let Bot Owner/Admin changes the bot settings in System Default Chat.",
    language: Language.EN
  },
  {
    key: "playground.inputPanel.tooltip.changeDetectionLevel",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.inputPanel.tooltip.changeDetectionLevel",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.inputPanel.dataSource",
    content: "Manage Your Data Source:",
    language: Language.EN
  },
  {
    key: "playground.inputPanel.dataSource",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.inputPanel.dataSource",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.inputPanel.dataSource.yourSpace.files",
    content: "Your Space(Chat with Files)",
    language: Language.EN
  },
  {
    key: "playground.inputPanel.dataSource.yourSpace.files",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.inputPanel.dataSource.yourSpace.files",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.inputPanel.dataSource.yourSpace.data",
    content: "Your Space(Chat with Data)",
    language: Language.EN
  },
  {
    key: "playground.inputPanel.dataSource.yourSpace.data",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.inputPanel.dataSource.yourSpace.data",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.inputPanel.dataSource.projectSpace.files",
    content: "Share Space(Chat with Files & AI Search)",
    language: Language.EN
  },
  {
    key: "playground.inputPanel.dataSource.projectSpace.files",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.inputPanel.dataSource.projectSpace.files",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.inputPanel.dataSource.projectSpace.data",
    content: "Share Space(Chat with Data)",
    language: Language.EN
  },
  {
    key: "playground.inputPanel.dataSource.projectSpace.data",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.inputPanel.dataSource.projectSpace.data",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.tokenIndicator.tooltip.title",
    content: "Token Breakdown:",
    language: Language.EN
  },
  {
    key: "playground.tokenIndicator.tooltip.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.tokenIndicator.tooltip.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.tokenIndicator.tooltip.embedding",
    content: "Embedding:",
    language: Language.EN
  },
  {
    key: "playground.tokenIndicator.tooltip.embedding",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.tokenIndicator.tooltip.embedding",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.tokenIndicator.tooltip.prompt",
    content: "Prompt:",
    language: Language.EN
  },
  {
    key: "playground.tokenIndicator.tooltip.prompt",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.tokenIndicator.tooltip.prompt",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.tokenIndicator.tooltip.completion",
    content: "Completion:",
    language: Language.EN
  },
  {
    key: "playground.tokenIndicator.tooltip.completion",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.tokenIndicator.tooltip.completion",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.dataSource.tab.yourSpace",
    content: "Your Space",
    language: Language.EN
  },
  {
    key: "playground.dataSource.tab.yourSpace",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.dataSource.tab.yourSpace",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.dataSource.tab.sharedSpace",
    content: "Project Space",
    language: Language.EN
  },
  {
    key: "playground.dataSource.tab.sharedSpace",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.dataSource.tab.sharedSpace",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.dataSource.tab.genKb",
    content: "Gen KB",
    language: Language.EN
  },
  {
    key: "playground.dataSource.tab.genKb",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.dataSource.tab.genKb",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.dataSource.tab.connectApi",
    content: "Connect API",
    language: Language.EN
  },
  {
    key: "playground.dataSource.tab.connectApi",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.dataSource.tab.connectApi",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.dataSource.tab.url",
    content: "URL",
    language: Language.EN
  },
  {
    key: "playground.dataSource.tab.url",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.dataSource.tab.url",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.dataSource.tab.title",
    content: "Data Source",
    language: Language.EN
  },
  {
    key: "playground.dataSource.tab.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.dataSource.tab.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.dataSource.tab.systemTitle",
    content: "Manage Data source (System Global Setting)",
    language: Language.EN
  },
  {
    key: "playground.dataSource.tab.systemTitle",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.dataSource.tab.systemTitle",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.confirmDeletePopUp.title",
    content: "Confirm Delete",
    language: Language.EN
  },
  {
    key: "playground.confirmDeletePopUp.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.confirmDeletePopUp.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.confirmDeletePopUp.content",
    content: "Confirm delete chat session",
    language: Language.EN
  },
  {
    key: "playground.confirmDeletePopUp.content",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.confirmDeletePopUp.content",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.share.button.copy",
    content: "Copy Share Link",
    language: Language.EN
  },
  {
    key: "playground.share.button.copy",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.share.button.copy",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.share.alert.copied",
    content: "Share link copied to clipboard",
    language: Language.EN
  },
  {
    key: "playground.share.alert.copied",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.share.alert.copied",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.share.selectedChats",
    content: " item(s) selected",
    language: Language.EN
  },
  {
    key: "playground.share.selectedChats",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.share.selectedChats",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.share.remarks",
    content: "Please note that data from Personal Space will not be shared. For non-bot members, the chat will be read-only, with no ‘continue chat’ feature.",
    language: Language.EN
  },
  {
    key: "playground.share.remarks",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.share.remarks",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.shareChat.header",
    content: " Shared Chat History:",
    language: Language.EN
  },
  {
    key: "playground.shareChat.header",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.shareChat.header",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.shareChat.button.continueChat",
    content: "Continue Chat",
    language: Language.EN
  },
  {
    key: "playground.shareChat.button.continueChat",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.shareChat.button.continueChat",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.shareChat.confirmContinuePrivateBotModal.title",
    content: "Alert",
    language: Language.EN
  },
  {
    key: "playground.shareChat.confirmContinuePrivateBotModal.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.shareChat.confirmContinuePrivateBotModal.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "playground.shareChat.confirmContinuePrivateBotModal.message",
    content: "Since this is part of a bot system chat thread, clicking this button will paste the conversation into your system chat session.",
    language: Language.EN
  },
  {
    key: "playground.shareChat.confirmContinuePrivateBotModal.message",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "playground.shareChat.confirmContinuePrivateBotModal.message",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "connectApi.link.viewOpenApiSpec",
    content: "View OpenAPI Specification Here",
    language: Language.EN
  },
  {
    key: "connectApi.link.viewOpenApiSpec",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "connectApi.link.viewOpenApiSpec",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "connectApi.button.upload",
    content: "Upload",
    language: Language.EN
  },
  {
    key: "connectApi.button.upload",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "connectApi.button.upload",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "connectApi.tooltip",
    content: "\"Connect API\" is used by OpenAI to communicate that it is able to access API information and make API calls. The API info will refer to the upload file content below, the upload file format need based on the https://swagger.io/specification/ standard.",
    language: Language.EN
  },
  {
    key: "connectApi.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "connectApi.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "connectApi.tooltip.customParam",
    content: "Custom Param is used for the API calling data injection.\n\nBelow are the listed Custom Param Sample data.\n\nThe @global setting will be applied to all API requests.\n\nThe key value should be the name of your API as specified in your YAML file.\n\nThe \"headers\" setting setting is used by the bot builder when making API calls to include your specified settings in the request header.\n\nThe \"appendBodyandappendQeuryParam\" setting is used by the bot builder when making API calls to include your specified settings in the request body. If there is already existing data with the same key value, this setting will replace it.\n\nThe \"pathParamReplace\" setting is used by the bot builder when making API calls to include your specified settings in the request URL. This setting replaces the key in your setting value in the URL.",
    language: Language.EN
  },
  {
    key: "connectApi.tooltip.customParam",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "connectApi.tooltip.customParam",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "connectApi.tooltip.snapshot",
    content: "To deploy changes to a certain environment, you first have to create a snapshot of that change. Once the snapshot is approved by the platform administrator, it will be deployed.",
    language: Language.EN
  },
  {
    key: "connectApi.tooltip.snapshot",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "connectApi.tooltip.snapshot",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.sectionTitle",
    content: "Upload Files",
    language: Language.EN
  },
  {
    key: "uploadFiles.sectionTitle",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.sectionTitle",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.text.uploadTimeReminder",
    content: "The speed of file scanning (Malware and PII scanning) depends on the file size and the queue. Est. time varies from 5 minutes to more than 2 hours.",
    language: Language.EN
  },
  {
    key: "uploadFiles.text.uploadTimeReminder",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.text.uploadTimeReminder",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.fileStatus.UPLOADED",
    content: "Uploading",
    language: Language.EN
  },
  {
    key: "uploadFiles.fileStatus.UPLOADED",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.fileStatus.UPLOADED",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.fileStatus.VERIFY_FAILED",
    content: "Fail to Upload",
    language: Language.EN
  },
  {
    key: "uploadFiles.fileStatus.VERIFY_FAILED",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.fileStatus.VERIFY_FAILED",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.fileStatus.VERIFY_SUCCESS",
    content: "Pending Approval",
    language: Language.EN
  },
  {
    key: "uploadFiles.fileStatus.VERIFY_SUCCESS",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.fileStatus.VERIFY_SUCCESS",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.fileStatus.PROCESSING",
    content: "Indexing",
    language: Language.EN
  },
  {
    key: "uploadFiles.fileStatus.PROCESSING",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.fileStatus.PROCESSING",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.fileStatus.COMPLETED",
    content: "File Index Successful and Approved",
    language: Language.EN
  },
  {
    key: "uploadFiles.fileStatus.COMPLETED",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.fileStatus.COMPLETED",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.fileStatus.PENDING_2ND_APPROVAL",
    content: "Pending 2nd Approval",
    language: Language.EN
  },
  {
    key: "uploadFiles.fileStatus.PENDING_2ND_APPROVAL",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.fileStatus.PENDING_2ND_APPROVAL",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.fileStatus.FAIL_TO_UPLOAD",
    content: "Fail to Upload",
    language: Language.EN
  },
  {
    key: "uploadFiles.fileStatus.FAIL_TO_UPLOAD",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.fileStatus.FAIL_TO_UPLOAD",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.fileStatus.APPROVED",
    content: "Approved",
    language: Language.EN
  },
  {
    key: "uploadFiles.fileStatus.APPROVED",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.fileStatus.APPROVED",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.fileError.err_DP001005",
    content: "Page or row count in file exceeded 1500 limit.",
    language: Language.EN
  },
  {
    key: "uploadFiles.fileError.err_DP001005",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.fileError.err_DP001005",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.piiStatus.YES",
    content: "PII Detected",
    language: Language.EN
  },
  {
    key: "uploadFiles.piiStatus.YES",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.piiStatus.YES",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.piiStatus.PENDING",
    content: "Pending to Scan PII",
    language: Language.EN
  },
  {
    key: "uploadFiles.piiStatus.PENDING",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.piiStatus.PENDING",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.piiStatus.NO",
    content: "No PII Detected",
    language: Language.EN
  },
  {
    key: "uploadFiles.piiStatus.NO",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.piiStatus.NO",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.piiStatus.ERROR",
    content: "Pending to Scan PII",
    language: Language.EN
  },
  {
    key: "uploadFiles.piiStatus.ERROR",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.piiStatus.ERROR",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.piiStatus.SCANNING",
    content: "Scanning",
    language: Language.EN
  },
  {
    key: "uploadFiles.piiStatus.SCANNING",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.piiStatus.SCANNING",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.piiQueue",
    content: "(Queue: {queue})",
    language: Language.EN
  },
  {
    key: "uploadFiles.piiQueue",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.piiQueue",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.promptInjection.YES",
    content: "Prompt Injection Detected",
    language: Language.EN
  },
  {
    key: "uploadFiles.promptInjection.YES",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.promptInjection.YES",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.promptInjection.NO",
    content: "No Prompt Injection Detected",
    language: Language.EN
  },
  {
    key: "uploadFiles.promptInjection.NO",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.promptInjection.NO",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.fileClassification.STRICTLY_CONFIDENTIAL",
    content: "Strictly Confidential",
    language: Language.EN
  },
  {
    key: "uploadFiles.fileClassification.STRICTLY_CONFIDENTIAL",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.fileClassification.STRICTLY_CONFIDENTIAL",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.fileClassification.CONFIDENTIAL",
    content: "Confidential",
    language: Language.EN
  },
  {
    key: "uploadFiles.fileClassification.CONFIDENTIAL",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.fileClassification.CONFIDENTIAL",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.fileClassification.INTERNAL_USE",
    content: "Internal Use",
    language: Language.EN
  },
  {
    key: "uploadFiles.fileClassification.INTERNAL_USE",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.fileClassification.INTERNAL_USE",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.fileClassification.PUBLIC_DOMAIN",
    content: "Public Domain",
    language: Language.EN
  },
  {
    key: "uploadFiles.fileClassification.PUBLIC_DOMAIN",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.fileClassification.PUBLIC_DOMAIN",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.table.tooltip.FAIL_TO_UPLOAD",
    content: "The file cannot be indexed. Please review if the file contains text, as OCR (Optical Character Recognition) is not supported. Please retry.",
    language: Language.EN
  },
  {
    key: "uploadFiles.table.tooltip.FAIL_TO_UPLOAD",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.table.tooltip.FAIL_TO_UPLOAD",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.table.tooltip.indexForLong",
    content: "Indexing timeout (>1 day).\nRecommended to delete and re-upload file.",
    language: Language.EN
  },
  {
    key: "uploadFiles.table.tooltip.indexForLong",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.table.tooltip.indexForLong",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.malwareScan.Clean",
    content: "Malware Scanned",
    language: Language.EN
  },
  {
    key: "uploadFiles.malwareScan.Clean",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.malwareScan.Clean",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.malwareScan.Failed",
    content: "Malware Detected",
    language: Language.EN
  },
  {
    key: "uploadFiles.malwareScan.Failed",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.malwareScan.Failed",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.malwareScan.status.SCANNING",
    content: "Scanning Malware",
    language: Language.EN
  },
  {
    key: "uploadFiles.malwareScan.status.SCANNING",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.malwareScan.status.SCANNING",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.malwareScan.status.SCANNING_FAILED",
    content: "Malware Scanning Failed",
    language: Language.EN
  },
  {
    key: "uploadFiles.malwareScan.status.SCANNING_FAILED",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.malwareScan.status.SCANNING_FAILED",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.malwareScan.status.PENDING",
    content: "Pending to Scan Malware",
    language: Language.EN
  },
  {
    key: "uploadFiles.malwareScan.status.PENDING",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.malwareScan.status.PENDING",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.status.pendingToScan",
    content: "Pending to Scan",
    language: Language.EN
  },
  {
    key: "uploadFiles.status.pendingToScan",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.status.pendingToScan",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.filter.fileType",
    content: "File Type: ",
    language: Language.EN
  },
  {
    key: "uploadFiles.filter.fileType",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.filter.fileType",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.filter.fileTagging",
    content: "File Tagging: ",
    language: Language.EN
  },
  {
    key: "uploadFiles.filter.fileTagging",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.filter.fileTagging",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.button.bulkActions",
    content: "Bulk Action",
    language: Language.EN
  },
  {
    key: "uploadFiles.button.bulkActions",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.button.bulkActions",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.dropdown.editTaggings",
    content: "Edit Tagging",
    language: Language.EN
  },
  {
    key: "uploadFiles.dropdown.editTaggings",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.dropdown.editTaggings",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.dropdown.removeTaggings",
    content: "Remove Tagging",
    language: Language.EN
  },
  {
    key: "uploadFiles.dropdown.removeTaggings",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.dropdown.removeTaggings",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.title.removeFile",
    content: "Remove File",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.title.removeFile",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.title.removeFile",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.removeFileConfirm",
    content: "Are you sure you want to remove this file?",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.removeFileConfirm",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.removeFileConfirm",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.title.removeAllTags",
    content: "Bulk Delete Tags",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.title.removeAllTags",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.title.removeAllTags",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.removeAllTagsConfirm",
    content: "Are you sure you want to delete all tags from selected files?",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.removeAllTagsConfirm",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.removeAllTagsConfirm",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.editTagging.title.step1",
    content: "Edit File Taggings",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.editTagging.title.step1",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.editTagging.title.step1",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.editTagging.title.step2",
    content: "Preview and Edit Taggings",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.editTagging.title.step2",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.editTagging.title.step2",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.editTagging.radio.manualTag",
    content: "Manual Tag",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.editTagging.radio.manualTag",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.editTagging.radio.manualTag",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.editTagging.radio.aiTag",
    content: "AI Tagging",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.editTagging.radio.aiTag",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.editTagging.radio.aiTag",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.editTagging.placeholder.aiTagPrompt",
    content: "Please input tagging instruction",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.editTagging.placeholder.aiTagPrompt",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.editTagging.placeholder.aiTagPrompt",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.editTagging.tooltip.aiTagging",
    content: "AI tagging is only available for files approved and with size smaller than {file_size}",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.editTagging.tooltip.aiTagging",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.editTagging.tooltip.aiTagging",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.editTagging.error.fileMaxTagsExceed",
    content: "Max number of tags per file is {max_tags_per_file}",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.editTagging.error.fileMaxTagsExceed",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.editTagging.error.fileMaxTagsExceed",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.editTagging.button.addNext",
    content: "Add and Next",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.editTagging.button.addNext",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.editTagging.button.addNext",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.editTagging.button.replaceNext",
    content: "Replace and Next",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.editTagging.button.replaceNext",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.editTagging.button.replaceNext",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.declaration.upload.point1",
    content: "You have reviewed “PCCW Group and HKT Group: GENERATIVE ARTIFICIAL INTELLIGENCE (AI) TOOLS USAGE GUIDELINE”",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.declaration.upload.point1",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.declaration.upload.point1",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.declaration.upload.point2",
    content: "You have reviewed the uploaded data that the data: Does not contain any Personally Identifiable Information (PII); or If it contains PII, the said information pertains to public figures (e.g., names of public singers) and is not subject to standard PII coverage.",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.declaration.upload.point2",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.declaration.upload.point2",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.declaration.approve.intro",
    content: "By clicking \"Approve\" or \"Process\", you confirm that:",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.declaration.approve.intro",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.declaration.approve.intro",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.declaration.approve.point1",
    content: "You have reviewed the uploaded data.",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.declaration.approve.point1",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.declaration.approve.point1",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.declaration.approve.point2",
    content: "The data:",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.declaration.approve.point2",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.declaration.approve.point2",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.declaration.approve.point2.1",
    content: "- Does not contain any Personally Identifiable Information (PII); or",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.declaration.approve.point2.1",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.declaration.approve.point2.1",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.declaration.approve.point2.2",
    content: "- If it contains PII, the said information pertains to public figures (e.g., names of public singers) and is not subject to standard PII coverage.",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.declaration.approve.point2.2",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.declaration.approve.point2.2",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.popup.fileClassification.label.autoIndex",
    content: "Auto Index AI Search for applicable files after approval:\n<a className=\"underline\" target=\"_blank\" href=\"https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/Bottleneck-for-all-file-processing-%26-Uploading.aspx\">Applicable files for AI Search</a>",
    language: Language.EN
  },
  {
    key: "uploadFiles.popup.fileClassification.label.autoIndex",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.popup.fileClassification.label.autoIndex",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.button.uploadFiles",
    content: "Upload File(s)",
    language: Language.EN
  },
  {
    key: "uploadFiles.button.uploadFiles",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.button.uploadFiles",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.upload.title",
    content: "Data Files",
    language: Language.EN
  },
  {
    key: "uploadFiles.upload.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.upload.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.upload.error.uploadLimitExceed",
    content: "Daily upload quota exceeds. You can only upload {daily_remaining_upload} more file(s) today",
    language: Language.EN
  },
  {
    key: "uploadFiles.upload.error.uploadLimitExceed",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.upload.error.uploadLimitExceed",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.upload.dropzone.hint1",
    content: "Click or drag file to this area to upload",
    language: Language.EN
  },
  {
    key: "uploadFiles.upload.dropzone.hint1",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.upload.dropzone.hint1",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.upload.dropzone.hint2",
    content: "Support for a single or bulk upload.",
    language: Language.EN
  },
  {
    key: "uploadFiles.upload.dropzone.hint2",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.upload.dropzone.hint2",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.upload.supportedFileTypes",
    content: "*.pdf, *.csv and *.xlsx files are supported",
    language: Language.EN
  },
  {
    key: "uploadFiles.upload.supportedFileTypes",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.upload.supportedFileTypes",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.upload.dailyUploadCount",
    content: "Daily Upload Quota",
    language: Language.EN
  },
  {
    key: "uploadFiles.upload.dailyUploadCount",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.upload.dailyUploadCount",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.upload.tooltip.uploadLimit",
    content: "Due to limitations in computing power, we recently had to limit the number of file uploads per bot per day to ensure the platform's stability. Please start with a small number of files to test it out.",
    language: Language.EN
  },
  {
    key: "uploadFiles.upload.tooltip.uploadLimit",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.upload.tooltip.uploadLimit",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.button.scanFullFile",
    content: "Scan Full File",
    language: Language.EN
  },
  {
    key: "uploadFiles.button.scanFullFile",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.button.scanFullFile",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.button.scanMalware",
    content: "Scan Malware",
    language: Language.EN
  },
  {
    key: "uploadFiles.button.scanMalware",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.button.scanMalware",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.button.scanPii",
    content: "Scan Pii",
    language: Language.EN
  },
  {
    key: "uploadFiles.button.scanPii",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.button.scanPii",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.aiSearch.status.ready",
    content: "Ready",
    language: Language.EN
  },
  {
    key: "uploadFiles.aiSearch.status.ready",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.aiSearch.status.ready",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.aiSearch.status.notReady",
    content: "Not Ready",
    language: Language.EN
  },
  {
    key: "uploadFiles.aiSearch.status.notReady",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.aiSearch.status.notReady",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.aiSearch.status.notApplicable",
    content: "Not Applicable",
    language: Language.EN
  },
  {
    key: "uploadFiles.aiSearch.status.notApplicable",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.aiSearch.status.notApplicable",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "uploadFiles.aiSearch.button.run",
    content: "Run AI Search",
    language: Language.EN
  },
  {
    key: "uploadFiles.aiSearch.button.run",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "uploadFiles.aiSearch.button.run",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notification.notificationPopup.buttonConfirmMessage",
    content: "OK",
    language: Language.EN
  },
  {
    key: "notification.notificationPopup.buttonConfirmMessage",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notification.notificationPopup.buttonConfirmMessage",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notification.notificationPopup.doNotShowFor24Hours",
    content: "Do not show again for 24 hours.",
    language: Language.EN
  },
  {
    key: "notification.notificationPopup.doNotShowFor24Hours",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notification.notificationPopup.doNotShowFor24Hours",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notification.notificationPopup.title",
    content: "Notification",
    language: Language.EN
  },
  {
    key: "notification.notificationPopup.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notification.notificationPopup.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "members.title.addMemberHint",
    content: "Hint: Bot Reviewer (CCC Head, VP, Legal Adviser or above) recommends adding as Admin or above.",
    language: Language.EN
  },
  {
    key: "members.title.addMemberHint",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "members.title.addMemberHint",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "login.err_401-2210-1",
    content: "User is Inactive. Please follow our  <a class='underline' href='https://pccw0.sharepoint.com/:u:/r/sites/GenerativeAI/SitePages/Gen%20AI%20Platform%20-%20Login%20Issue.aspx?csf=1&web=1&share=ESzCsp-qpiFClD7SfFraQB4B-pogz-T0_m-XEM5vePeJmw&e=0ygBDw' target='_blank' rel='noopener'>user guide</a> to re-activate your account. For more information, please contact Customer Success Team [<a class='underline' href='mailto:<EMAIL>'><EMAIL></a>].",
    language: Language.EN
  },
  {
    key: "login.err_401-2210-1",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "login.err_401-2210-1",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "login.error",
    content: "Login Failed. Please follow our <a class='underline' href='https://pccw0.sharepoint.com/:u:/r/sites/GenerativeAI/SitePages/Gen%20AI%20Platform%20-%20Login%20Issue.aspx?csf=1&web=1&share=ESzCsp-qpiFClD7SfFraQB4B-pogz-T0_m-XEM5vePeJmw&e=0ygBDw' target='_blank' rel='noopener'>user guide</a> for troubleshoot",
    language: Language.EN
  },
  {
    key: "login.error",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "login.error",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "copy.alert.success",
    content: "Copied to clipboard",
    language: Language.EN
  },
  {
    key: "copy.alert.success",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "copy.alert.success",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.title.upgradePlan",
    content: "Upgrade Your Plan",
    language: Language.EN
  },
  {
    key: "planAndSubscription.title.upgradePlan",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.title.upgradePlan",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.upgradePlanRequestFn.successAlertMessage",
    content: "Request to upgrade plan successfully",
    language: Language.EN
  },
  {
    key: "planAndSubscription.upgradePlanRequestFn.successAlertMessage",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.upgradePlanRequestFn.successAlertMessage",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.currentUsage.updatedAt",
    content: " Current Usage Updated At:",
    language: Language.EN
  },
  {
    key: "planAndSubscription.currentUsage.updatedAt",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.currentUsage.updatedAt",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.enableAndDisableTable.title",
    content: "Plan",
    language: Language.EN
  },
  {
    key: "planAndSubscription.enableAndDisableTable.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.enableAndDisableTable.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.sendRequestInformation",
    content: "*Please note that only the most recent submission will be considered for approval. Previous submissions will not be taken into account.",
    language: Language.EN
  },
  {
    key: "planAndSubscription.sendRequestInformation",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.sendRequestInformation",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.button.sendRequest",
    content: "Send Request",
    language: Language.EN
  },
  {
    key: "planAndSubscription.button.sendRequest",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.button.sendRequest",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.radioButton.label.other",
    content: "Custom",
    language: Language.EN
  },
  {
    key: "planAndSubscription.radioButton.label.other",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.radioButton.label.other",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.textInput.placeholder",
    content: "Number of quota",
    language: Language.EN
  },
  {
    key: "planAndSubscription.textInput.placeholder",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.textInput.placeholder",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.planCreationPopUp.title.update",
    content: "Update Plan for {env}",
    language: Language.EN
  },
  {
    key: "planAndSubscription.planCreationPopUp.title.update",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.planCreationPopUp.title.update",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.planCreationPopUp.title.create",
    content: "Create New Plan for {env}",
    language: Language.EN
  },
  {
    key: "planAndSubscription.planCreationPopUp.title.create",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.planCreationPopUp.title.create",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.groupRemovePopUp.button.activate",
    content: "Activate",
    language: Language.EN
  },
  {
    key: "planAndSubscription.groupRemovePopUp.button.activate",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.groupRemovePopUp.button.activate",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.groupRemovePopUp.button.deactivate",
    content: "Deactivate",
    language: Language.EN
  },
  {
    key: "planAndSubscription.groupRemovePopUp.button.deactivate",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.groupRemovePopUp.button.deactivate",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.groupRemovePopUp.message.deactivate",
    content: "There are {userNumber} bots using this plan, are you sure you want to deactivate this Plan?",
    language: Language.EN
  },
  {
    key: "planAndSubscription.groupRemovePopUp.message.deactivate",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.groupRemovePopUp.message.deactivate",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.groupRemovePopUp.message.activate",
    content: "There are {userNumber} bots using this plan, are you sure you want to activate this Plan?",
    language: Language.EN
  },
  {
    key: "planAndSubscription.groupRemovePopUp.message.activate",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.groupRemovePopUp.message.activate",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.warningPopUp.message",
    content: "There are {userNumber} bots using this plan, are you sure you want to keep updating this Plan?",
    language: Language.EN
  },
  {
    key: "planAndSubscription.warningPopUp.message",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.warningPopUp.message",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.warningPopUp.button",
    content: "Confirm",
    language: Language.EN
  },
  {
    key: "planAndSubscription.warningPopUp.button",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.warningPopUp.button",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.planHistory.errorMessage",
    content: "Failed to get List",
    language: Language.EN
  },
  {
    key: "planAndSubscription.planHistory.errorMessage",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.planHistory.errorMessage",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.planHistory.title",
    content: "Plan History",
    language: Language.EN
  },
  {
    key: "planAndSubscription.planHistory.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.planHistory.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.planHistory.button",
    content: "Export",
    language: Language.EN
  },
  {
    key: "planAndSubscription.planHistory.button",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.planHistory.button",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.planHistory.table.button",
    content: "View Details",
    language: Language.EN
  },
  {
    key: "planAndSubscription.planHistory.table.button",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.planHistory.table.button",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.planHistory.table.empty",
    content: "No Request found",
    language: Language.EN
  },
  {
    key: "planAndSubscription.planHistory.table.empty",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.planHistory.table.empty",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.botPlanRequest.title",
    content: "Plan Request",
    language: Language.EN
  },
  {
    key: "planAndSubscription.botPlanRequest.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.botPlanRequest.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.botPlanRequest.search.placeholder",
    content: "Search for the request by Request ID",
    language: Language.EN
  },
  {
    key: "planAndSubscription.botPlanRequest.search.placeholder",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.botPlanRequest.search.placeholder",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "sideMenu.button.upgradePlan",
    content: "Upgrade Plan Now",
    language: Language.EN
  },
  {
    key: "sideMenu.button.upgradePlan",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "sideMenu.button.upgradePlan",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.successAlertMessage.approve",
    content: "Approve the plan request successfully",
    language: Language.EN
  },
  {
    key: "planAndSubscription.successAlertMessage.approve",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.successAlertMessage.approve",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.successAlertMessage.reject",
    content: "Reject the plan request successfully",
    language: Language.EN
  },
  {
    key: "planAndSubscription.successAlertMessage.reject",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.successAlertMessage.reject",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.planDetail.requestId",
    content: "Request ID:",
    language: Language.EN
  },
  {
    key: "planAndSubscription.planDetail.requestId",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.planDetail.requestId",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.planDetail.botName",
    content: "Group Name:",
    language: Language.EN
  },
  {
    key: "planAndSubscription.planDetail.botName",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.planDetail.botName",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.planDetail.requestAt",
    content: "Request at:",
    language: Language.EN
  },
  {
    key: "planAndSubscription.planDetail.requestAt",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.planDetail.requestAt",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.planDetail.newChange",
    content: "New Change",
    language: Language.EN
  },
  {
    key: "planAndSubscription.planDetail.newChange",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.planDetail.newChange",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.planDetail.previous",
    content: "Previous",
    language: Language.EN
  },
  {
    key: "planAndSubscription.planDetail.previous",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.planDetail.previous",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.planDetail.button.approve",
    content: "Approve",
    language: Language.EN
  },
  {
    key: "planAndSubscription.planDetail.button.approve",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.planDetail.button.approve",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.planDetail.button.reject",
    content: "Reject",
    language: Language.EN
  },
  {
    key: "planAndSubscription.planDetail.button.reject",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.planDetail.button.reject",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.planDetail.information",
    content: "*Please note that only the most recent submission will be considered for approval. Previous submissions will not be taken into account.",
    language: Language.EN
  },
  {
    key: "planAndSubscription.planDetail.information",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.planDetail.information",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.groupRemovePopUp.title",
    content: "Reject Plan Request",
    language: Language.EN
  },
  {
    key: "planAndSubscription.groupRemovePopUp.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.groupRemovePopUp.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.groupRemovePopUp.message",
    content: "Are you sure you want to reject this plan request?",
    language: Language.EN
  },
  {
    key: "planAndSubscription.groupRemovePopUp.message",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.groupRemovePopUp.message",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.manageBotPlan.title",
    content: "Manage Plan",
    language: Language.EN
  },
  {
    key: "planAndSubscription.manageBotPlan.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.manageBotPlan.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.manageBotPlan.default",
    content: "Default Plan",
    language: Language.EN
  },
  {
    key: "planAndSubscription.manageBotPlan.default",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.manageBotPlan.default",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.manageBotPlan.tooltip.addNewPlan",
    content: "Add New Plan",
    language: Language.EN
  },
  {
    key: "planAndSubscription.manageBotPlan.tooltip.addNewPlan",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.manageBotPlan.tooltip.addNewPlan",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.manageBotPlan.tooltip.leaveEditMode",
    content: "Leave the edit mode of",
    language: Language.EN
  },
  {
    key: "planAndSubscription.manageBotPlan.tooltip.leaveEditMode",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.manageBotPlan.tooltip.leaveEditMode",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.manageBotPlan.tooltip.editPlanOf",
    content: "Edit plans of",
    language: Language.EN
  },
  {
    key: "planAndSubscription.manageBotPlan.tooltip.editPlanOf",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.manageBotPlan.tooltip.editPlanOf",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.common.insightCase",
    content: "Insight Case",
    language: Language.EN
  },
  {
    key: "insightGenerator.common.insightCase",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.common.insightCase",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightCase.search.placeholder",
    content: "Search by insight Case",
    language: Language.EN
  },
  {
    key: "insightCase.search.placeholder",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightCase.search.placeholder",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.common.insightGeneratorLists",
    content: "Insight Generator Lists",
    language: Language.EN
  },
  {
    key: "insightGenerator.common.insightGeneratorLists",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.common.insightGeneratorLists",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.common.insightGenerator",
    content: "Insight Generator",
    language: Language.EN
  },
  {
    key: "insightGenerator.common.insightGenerator",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.common.insightGenerator",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.common.insightReports",
    content: "Insight Reports",
    language: Language.EN
  },
  {
    key: "insightGenerator.common.insightReports",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.common.insightReports",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightReports.search.placeholder",
    content: "Search by insight reports",
    language: Language.EN
  },
  {
    key: "insightReports.search.placeholder",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightReports.search.placeholder",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.create.successful",
    content: "Create insight case successfully",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.create.successful",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.create.successful",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.update.successful",
    content: "Update insight case successfully",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.update.successful",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.update.successful",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.searchScope.warningMessage",
    content: "Please input the search scope!",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.searchScope.warningMessage",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.searchScope.warningMessage",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.searchScope.label",
    content: "Search Scope",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.searchScope.label",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.searchScope.label",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.poweredBy.label",
    content: "Powered by",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.poweredBy.label",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.poweredBy.label",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.poweredBy.warningMessage",
    content: "Please select a provider!",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.poweredBy.warningMessage",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.poweredBy.warningMessage",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.poweredBy.tooltip",
    content: "By default, Gemini Pro paired with Google search is recommended. You can change LLM model for more Insight.",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.poweredBy.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.poweredBy.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.timeFrame.label",
    content: "Time Frame (Past # Days)",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.timeFrame.label",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.timeFrame.label",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.timeFrame.warningMessage",
    content: "Please input the time frame!",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.timeFrame.warningMessage",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.timeFrame.warningMessage",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.timeFrame.placeholder",
    content: "Input Time Frame",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.timeFrame.placeholder",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.timeFrame.placeholder",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.maxArticleNumber.tooltip",
    content: "Max Article Number should be ≤ 20",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.maxArticleNumber.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.maxArticleNumber.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.maxArticleNumber.label",
    content: "Max Article Number",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.maxArticleNumber.label",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.maxArticleNumber.label",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.maxArticleNumber.placeholder",
    content: "Input max article number",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.maxArticleNumber.placeholder",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.maxArticleNumber.placeholder",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.maxArticleNumber.warningMessage",
    content: "Please input the max article number!",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.maxArticleNumber.warningMessage",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.maxArticleNumber.warningMessage",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.caseName.label",
    content: "Insight Case Name",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.caseName.label",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.caseName.label",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.caseName.warningMessage",
    content: "Please input the insight case name!",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.caseName.warningMessage",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.caseName.warningMessage",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.keywords.tooltip",
    content: "Keywords of focus, such as, HK Network or 盈科.",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.keywords.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.keywords.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.keywords.label",
    content: "Area(s) of Keywords",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.keywords.label",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.keywords.label",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.keywords.warningMessage",
    content: "Please input the keywords!",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.keywords.warningMessage",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.keywords.warningMessage",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.button.save",
    content: "Save",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.button.save",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.button.save",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.caseForm.button.generate",
    content: "Save and Generate",
    language: Language.EN
  },
  {
    key: "insightGenerator.caseForm.button.generate",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.caseForm.button.generate",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.common.newInsight.button",
    content: "Create New Insight",
    language: Language.EN
  },
  {
    key: "insightGenerator.common.newInsight.button",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.common.newInsight.button",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.common.noSummary",
    content: "No Summary",
    language: Language.EN
  },
  {
    key: "insightGenerator.common.noSummary",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.common.noSummary",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.common.createInsightCaseSuccess",
    content: "Created new insight case successfully",
    language: Language.EN
  },
  {
    key: "insightGenerator.common.createInsightCaseSuccess",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.common.createInsightCaseSuccess",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.common.totalCompletionToken",
    content: "Total Insight Token\n(Prompt + Completion)",
    language: Language.EN
  },
  {
    key: "insightGenerator.common.totalCompletionToken",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.common.totalCompletionToken",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.common.totalSummaryCompletionToken",
    content: "Total Summary Token\n(Prompt + Completion)",
    language: Language.EN
  },
  {
    key: "insightGenerator.common.totalSummaryCompletionToken",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.common.totalSummaryCompletionToken",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "insightGenerator.insightReport.systemMessage",
    content: "The Insights Details will be saved for 1 month. Please download report timely if needed.",
    language: Language.EN
  },
  {
    key: "insightGenerator.insightReport.systemMessage",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "insightGenerator.insightReport.systemMessage",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.upgradePlan.userGuide.message",
    content: "Once sent request, please follow our <a href='https://pccw0.sharepoint.com/:u:/r/sites/GenerativeAI/SitePages/Bot%20Setting%20%26%20Security%20Reports.aspx?csf=1&web=1&share=EakOgZx4yD9MvdWv36nS1DMBvuRJI2gAALSoUXUv7ihtig&e=xF5aRy' target='_blank'>user guide</a>and raise an Email for CCC endorsement.",
    language: Language.EN
  },
  {
    key: "planAndSubscription.upgradePlan.userGuide.message",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.upgradePlan.userGuide.message",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.common.testResult",
    content: "Test Result",
    language: Language.EN
  },
  {
    key: "testAutomation.common.testResult",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.common.testResult",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.common.executionId",
    content: "Execution ID",
    language: Language.EN
  },
  {
    key: "testAutomation.common.executionId",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.common.executionId",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.common.totalCompletionToken",
    content: "Total Completion Token\n(Prompt + Completion + Embedding)",
    language: Language.EN
  },
  {
    key: "testAutomation.common.totalCompletionToken",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.common.totalCompletionToken",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.button.createNewTest",
    content: "Create New Test",
    language: Language.EN
  },
  {
    key: "testAutomation.button.createNewTest",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.button.createNewTest",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.search.placeholder",
    content: "Search by Test Name",
    language: Language.EN
  },
  {
    key: "testAutomation.search.placeholder",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.search.placeholder",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.search.placeholder.executions",
    content: "Search by Execution ID",
    language: Language.EN
  },
  {
    key: "testAutomation.search.placeholder.executions",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.search.placeholder.executions",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.search.placeholder.executionResults",
    content: "Search by Keywords of Question",
    language: Language.EN
  },
  {
    key: "testAutomation.search.placeholder.executionResults",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.search.placeholder.executionResults",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.table.testCaseName",
    content: "Test Case Name",
    language: Language.EN
  },
  {
    key: "testAutomation.table.testCaseName",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.table.testCaseName",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.table.caseStatus",
    content: "Case Status",
    language: Language.EN
  },
  {
    key: "testAutomation.table.caseStatus",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.table.caseStatus",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.table.lastExecutionStatus",
    content: "Last Execution Status",
    language: Language.EN
  },
  {
    key: "testAutomation.table.lastExecutionStatus",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.table.lastExecutionStatus",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.table.latestResult",
    content: "Latest Result",
    language: Language.EN
  },
  {
    key: "testAutomation.table.latestResult",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.table.latestResult",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.table.executionStatus",
    content: "Execution Status",
    language: Language.EN
  },
  {
    key: "testAutomation.table.executionStatus",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.table.executionStatus",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.table.round",
    content: "Round",
    language: Language.EN
  },
  {
    key: "testAutomation.table.round",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.table.round",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.table.question",
    content: "Question",
    language: Language.EN
  },
  {
    key: "testAutomation.table.question",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.table.question",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.table.response",
    content: "Response",
    language: Language.EN
  },
  {
    key: "testAutomation.table.response",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.table.response",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.table.responseTime",
    content: "Response Time",
    language: Language.EN
  },
  {
    key: "testAutomation.table.responseTime",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.table.responseTime",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.table.reason",
    content: "Reason",
    language: Language.EN
  },
  {
    key: "testAutomation.table.reason",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.table.reason",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.tooltip.inSchedule",
    content: "In Schedule",
    language: Language.EN
  },
  {
    key: "testAutomation.tooltip.inSchedule",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.tooltip.inSchedule",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.tooltip.invalidLLM",
    content: "In the configuration, there is an invalid LLM model that cannot be executed. Please update.",
    language: Language.EN
  },
  {
    key: "testAutomation.tooltip.invalidLLM",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.tooltip.invalidLLM",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.executions.title",
    content: "Test Records",
    language: Language.EN
  },
  {
    key: "testAutomation.executions.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.executions.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.executions.subTitle",
    content: "Test Case",
    language: Language.EN
  },
  {
    key: "testAutomation.executions.subTitle",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.executions.subTitle",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.executions.reminder",
    content: "The details of Results will be saved for 1 month. Please download timely if needed.",
    language: Language.EN
  },
  {
    key: "testAutomation.executions.reminder",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.executions.reminder",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.label.testCaseName",
    content: "Test Case Name",
    language: Language.EN
  },
  {
    key: "testAutomation.form.label.testCaseName",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.label.testCaseName",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.placeholder.testCaseName",
    content: "Type Name",
    language: Language.EN
  },
  {
    key: "testAutomation.form.placeholder.testCaseName",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.placeholder.testCaseName",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.label.testModel",
    content: "Case Simulation Model",
    language: Language.EN
  },
  {
    key: "testAutomation.form.label.testModel",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.label.testModel",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.tooltip.testModel",
    content: "By default, selected the LLM Model of the System chat in the BOT. You can change options for Test.",
    language: Language.EN
  },
  {
    key: "testAutomation.form.tooltip.testModel",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.tooltip.testModel",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.label.testPromptFile",
    content: "Test Case File",
    language: Language.EN
  },
  {
    key: "testAutomation.form.label.testPromptFile",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.label.testPromptFile",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.tooltip.testPromptFile",
    content: "1.One file for one test case. \n2.The uploaded file is required with one question on each row and filled in according to the template header.\n3.File size is better less than 25MB and the number of questions better less than 100.",
    language: Language.EN
  },
  {
    key: "testAutomation.form.tooltip.testPromptFile",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.tooltip.testPromptFile",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.label.numberOfIterations",
    content: "Number of Iterations",
    language: Language.EN
  },
  {
    key: "testAutomation.form.label.numberOfIterations",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.label.numberOfIterations",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.tooltip.numberOfIterations",
    content: "Max 10 lterations",
    language: Language.EN
  },
  {
    key: "testAutomation.form.tooltip.numberOfIterations",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.tooltip.numberOfIterations",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.label.validationPrompt",
    content: "Validation Prompt",
    language: Language.EN
  },
  {
    key: "testAutomation.form.label.validationPrompt",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.label.validationPrompt",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.tooltip.validationPrompt",
    content: "In the case file, Validation Prompt is specialized that only applicable to specific test question.\nIn test case configuration page, Validation Prompt is general that works for test question without specialized Validation Prompt.\nPriority: Specialized Validation Prompt > General \nValidation Prompt",
    language: Language.EN
  },
  {
    key: "testAutomation.form.tooltip.validationPrompt",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.tooltip.validationPrompt",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.label.caseValidationModel",
    content: "Case Validation Model",
    language: Language.EN
  },
  {
    key: "testAutomation.form.label.caseValidationModel",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.label.caseValidationModel",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.tooltip.caseValidationModel",
    content: "1. Validation Model is used to validate the answers if match the Validation Prompt, which is in natural language.\n2. By default, validation model is same as Simulation Model. You can switch on for changing.",
    language: Language.EN
  },
  {
    key: "testAutomation.form.tooltip.caseValidationModel",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.tooltip.caseValidationModel",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.button.exportPromptFile",
    content: "Export Prompt File",
    language: Language.EN
  },
  {
    key: "testAutomation.form.button.exportPromptFile",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.button.exportPromptFile",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.button.template",
    content: "Template_Case File",
    language: Language.EN
  },
  {
    key: "testAutomation.form.button.template",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.button.template",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.button.saveAndRun",
    content: "Save and Run",
    language: Language.EN
  },
  {
    key: "testAutomation.form.button.saveAndRun",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.button.saveAndRun",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.button.updateAndRun",
    content: "Update and Run",
    language: Language.EN
  },
  {
    key: "testAutomation.form.button.updateAndRun",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.button.updateAndRun",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.button.reImport",
    content: "Re-import",
    language: Language.EN
  },
  {
    key: "testAutomation.form.button.reImport",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.button.reImport",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.remarks.fileFormat",
    content: "Please download template to fill and upload.",
    language: Language.EN
  },
  {
    key: "testAutomation.form.remarks.fileFormat",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.remarks.fileFormat",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.error.file",
    content: "Please import test prompt file.",
    language: Language.EN
  },
  {
    key: "testAutomation.form.error.file",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.error.file",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.label.resultRecipients",
    content: "Result Recipients",
    language: Language.EN
  },
  {
    key: "testAutomation.form.label.resultRecipients",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.label.resultRecipients",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.placeholder.resultRecipients",
    content: "Search Recipients",
    language: Language.EN
  },
  {
    key: "testAutomation.form.placeholder.resultRecipients",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.placeholder.resultRecipients",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.invalidRecipients.readonly",
    content: "The following recipients are invalid, which will not send email.",
    language: Language.EN
  },
  {
    key: "testAutomation.form.invalidRecipients.readonly",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.invalidRecipients.readonly",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.invalidRecipients.edit",
    content: "The following recipients are invalid, which will be automatically removed when clicking update.",
    language: Language.EN
  },
  {
    key: "testAutomation.form.invalidRecipients.edit",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.invalidRecipients.edit",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.tooltip.invalidRecipient",
    content: "Email is not for Bot Owner / Admin",
    language: Language.EN
  },
  {
    key: "testAutomation.form.tooltip.invalidRecipient",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.tooltip.invalidRecipient",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.tooltip.inactiveRecipient",
    content: "Recipient account is inactive",
    language: Language.EN
  },
  {
    key: "testAutomation.form.tooltip.inactiveRecipient",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.tooltip.inactiveRecipient",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.form.error.emptyRecipients",
    content: "Please select recipients",
    language: Language.EN
  },
  {
    key: "testAutomation.form.error.emptyRecipients",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.form.error.emptyRecipients",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.confirmActionPopup.title.delete",
    content: "Delete Case",
    language: Language.EN
  },
  {
    key: "testAutomation.confirmActionPopup.title.delete",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.confirmActionPopup.title.delete",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.confirmActionPopup.title.disable",
    content: "Disable",
    language: Language.EN
  },
  {
    key: "testAutomation.confirmActionPopup.title.disable",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.confirmActionPopup.title.disable",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.confirmActionPopup.title.enable",
    content: "Enable",
    language: Language.EN
  },
  {
    key: "testAutomation.confirmActionPopup.title.enable",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.confirmActionPopup.title.enable",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.confirmActionPopup.message.delete",
    content: "Are you sure you want to delete this pending case?",
    language: Language.EN
  },
  {
    key: "testAutomation.confirmActionPopup.message.delete",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.confirmActionPopup.message.delete",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.confirmActionPopup.message.disable",
    content: "Are you sure you want to disable this case?",
    language: Language.EN
  },
  {
    key: "testAutomation.confirmActionPopup.message.disable",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.confirmActionPopup.message.disable",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.confirmActionPopup.message.enable",
    content: "Are you sure you want to enable this case for test?",
    language: Language.EN
  },
  {
    key: "testAutomation.confirmActionPopup.message.enable",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.confirmActionPopup.message.enable",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.piiPopUp.title",
    content: "Risk Detected!",
    language: Language.EN
  },
  {
    key: "testAutomation.piiPopUp.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.piiPopUp.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.piiPopUp.button.adjust",
    content: "Adjust Settings",
    language: Language.EN
  },
  {
    key: "testAutomation.piiPopUp.button.adjust",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.piiPopUp.button.adjust",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.piiPopUp.button.bypass",
    content: "OK and Bypass",
    language: Language.EN
  },
  {
    key: "testAutomation.piiPopUp.button.bypass",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.piiPopUp.button.bypass",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.piiPopUp.message.soft",
    content: "We have identified Personally Identifiable Information (PII) or Prompt injection in your input. Based on your current setting <span class=\"text-tag-orange\">(Soft Level)</span>, we advise caution before proceeding.\n\nOptions:\n1. Review your content and try again.\n2. Adjust Settings: change the security settings to “Off” to proceed without restrictions.\n3. Bypass: (Soft only) Proceed with PII or Prompt Injection detected.\n\nPlease select your preferred option to continue with the test.",
    language: Language.EN
  },
  {
    key: "testAutomation.piiPopUp.message.soft",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.piiPopUp.message.soft",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.piiPopUp.message.hard",
    content: "We have identified Personally Identifiable Information (PII) or Prompt injection in your input. Based on your current setting <span class=\"text-tag-red\">(Hard Level)</span>, we advise caution before proceeding.\n\nOptions:\n1. Review your content and try again.\n2. Adjust Settings: change the security settings to “Off” to proceed without restrictions.\n\nPlease select your preferred option to continue with the test.",
    language: Language.EN
  },
  {
    key: "testAutomation.piiPopUp.message.hard",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.piiPopUp.message.hard",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.scheduler.tabTitle",
    content: "Schedule Setting",
    language: Language.EN
  },
  {
    key: "testAutomation.scheduler.tabTitle",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.scheduler.tabTitle",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.scheduler.tabTitle.tooltip",
    content: "Please save you case first",
    language: Language.EN
  },
  {
    key: "testAutomation.scheduler.tabTitle.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.scheduler.tabTitle.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "testAutomation.scheduler.scheduler.label.expectedStartTime",
    content: "Expected start time for test",
    language: Language.EN
  },
  {
    key: "testAutomation.scheduler.scheduler.label.expectedStartTime",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "testAutomation.scheduler.scheduler.label.expectedStartTime",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "users.userGroup.editIconMessage",
    content: "Edit Group",
    language: Language.EN
  },
  {
    key: "users.userGroup.editIconMessage",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "users.userGroup.editIconMessage",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "users.userGroup.cloneIconMessage",
    content: "Clone Group",
    language: Language.EN
  },
  {
    key: "users.userGroup.cloneIconMessage",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "users.userGroup.cloneIconMessage",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "users.userGroup.removeIconMessage",
    content: "Remove Group",
    language: Language.EN
  },
  {
    key: "users.userGroup.removeIconMessage",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "users.userGroup.removeIconMessage",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "users.userGroup.title",
    content: "Users Group",
    language: Language.EN
  },
  {
    key: "users.userGroup.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "users.userGroup.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "users.userGroup.button",
    content: "New Group",
    language: Language.EN
  },
  {
    key: "users.userGroup.button",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "users.userGroup.button",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "users.userGroup.removeGroup.title",
    content: "Remove Member Group",
    language: Language.EN
  },
  {
    key: "users.userGroup.removeGroup.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "users.userGroup.removeGroup.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "users.userGroup.removeGroup.message",
    content: "Are you sure you want to remove this member group?",
    language: Language.EN
  },
  {
    key: "users.userGroup.removeGroup.message",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "users.userGroup.removeGroup.message",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "users.userGroup.removeGroup.buttonMessage",
    content: "Remove",
    language: Language.EN
  },
  {
    key: "users.userGroup.removeGroup.buttonMessage",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "users.userGroup.removeGroup.buttonMessage",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "users.userGroup.search.message",
    content: "Search for the group Name",
    language: Language.EN
  },
  {
    key: "users.userGroup.search.message",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "users.userGroup.search.message",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "users.subCategory.userManagement",
    content: "User Management",
    language: Language.EN
  },
  {
    key: "users.subCategory.userManagement",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "users.subCategory.userManagement",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "menu.broadcastCenter",
    content: "Broadcast Center",
    language: Language.EN
  },
  {
    key: "menu.broadcastCenter",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "menu.broadcastCenter",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.table.id",
    content: "ID",
    language: Language.EN
  },
  {
    key: "broadcast.table.id",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.table.id",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.table.title",
    content: "Title",
    language: Language.EN
  },
  {
    key: "broadcast.table.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.table.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.table.content",
    content: "Content",
    language: Language.EN
  },
  {
    key: "broadcast.table.content",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.table.content",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.table.recipient",
    content: "Recipient",
    language: Language.EN
  },
  {
    key: "broadcast.table.recipient",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.table.recipient",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.table.numberSent",
    content: "Number Sent",
    language: Language.EN
  },
  {
    key: "broadcast.table.numberSent",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.table.numberSent",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.table.channel",
    content: "Channel",
    language: Language.EN
  },
  {
    key: "broadcast.table.channel",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.table.channel",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.table.status",
    content: "Status",
    language: Language.EN
  },
  {
    key: "broadcast.table.status",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.table.status",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.table.createdBy",
    content: "Created by",
    language: Language.EN
  },
  {
    key: "broadcast.table.createdBy",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.table.createdBy",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.table.sentAt",
    content: "Sent At",
    language: Language.EN
  },
  {
    key: "broadcast.table.sentAt",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.table.sentAt",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.table.action",
    content: "Action",
    language: Language.EN
  },
  {
    key: "broadcast.table.action",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.table.action",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.form.targetGroup",
    content: "Target Group",
    language: Language.EN
  },
  {
    key: "broadcast.form.targetGroup",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.form.targetGroup",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.form.user.group",
    content: "Select User Group",
    language: Language.EN
  },
  {
    key: "broadcast.form.user.group",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.form.user.group",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.form.message.title",
    content: "Message Title(80 words)",
    language: Language.EN
  },
  {
    key: "broadcast.form.message.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.form.message.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.form.message.content",
    content: "Message Content",
    language: Language.EN
  },
  {
    key: "broadcast.form.message.content",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.form.message.content",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.form.message.setting",
    content: "Message Setting",
    language: Language.EN
  },
  {
    key: "broadcast.form.message.setting",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.form.message.setting",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.button.createNewBroadcast",
    content: "New Message",
    language: Language.EN
  },
  {
    key: "broadcast.button.createNewBroadcast",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.button.createNewBroadcast",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.search.placeholder",
    content: "Search by title",
    language: Language.EN
  },
  {
    key: "broadcast.search.placeholder",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.search.placeholder",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.confirmActionPopup.title.delete",
    content: "Delete Broadcast",
    language: Language.EN
  },
  {
    key: "broadcast.confirmActionPopup.title.delete",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.confirmActionPopup.title.delete",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.confirmActionPopup.message.delete",
    content: "Are you sure you want to delete this pending broadcast?",
    language: Language.EN
  },
  {
    key: "broadcast.confirmActionPopup.message.delete",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.confirmActionPopup.message.delete",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.system.text.tip1",
    content: "The following is the system text that you can use for this message notification:",
    language: Language.EN
  },
  {
    key: "broadcast.system.text.tip1",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.system.text.tip1",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.system.text.tip2",
    content: "Tips for System txt:",
    language: Language.EN
  },
  {
    key: "broadcast.system.text.tip2",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.system.text.tip2",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.system.text.tip3",
    content: "{{Group List}} better used as a new paragraph, if there are multiple values when instantiating the target group.",
    language: Language.EN
  },
  {
    key: "broadcast.system.text.tip3",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.system.text.tip3",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.system.text.tip4",
    content: "Sample:",
    language: Language.EN
  },
  {
    key: "broadcast.system.text.tip4",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.system.text.tip4",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.system.text.tip5",
    content: "Dear {{User Name}}",
    language: Language.EN
  },
  {
    key: "broadcast.system.text.tip5",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.system.text.tip5",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.system.text.tip6",
    content: "We're so sorry to inform you that the following group needs to be updated at 24/10/2024 18:00.",
    language: Language.EN
  },
  {
    key: "broadcast.system.text.tip6",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.system.text.tip6",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "broadcast.system.text.tip7",
    content: "{{Group List}}",
    language: Language.EN
  },
  {
    key: "broadcast.system.text.tip7",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "broadcast.system.text.tip7",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "user.group.add.filter.tooltip",
    content: "Silent Period is used for instant alert and setting how long before the next alert interval to avoid repeated alerts.Type your expected silent period for alert intervals.If keep blank, default to system's default silent period.",
    language: Language.EN
  },
  {
    key: "user.group.add.filter.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "user.group.add.filter.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "user.notification.title",
    content: "Notifications",
    language: Language.EN
  },
  {
    key: "user.notification.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "user.notification.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "user.notification.table.title",
    content: "Title",
    language: Language.EN
  },
  {
    key: "user.notification.table.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "user.notification.table.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "user.notification.table.content",
    content: "Content",
    language: Language.EN
  },
  {
    key: "user.notification.table.content",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "user.notification.table.content",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "user.notification.table.type",
    content: "Type",
    language: Language.EN
  },
  {
    key: "user.notification.table.type",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "user.notification.table.type",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "user.notification.table.sentAt",
    content: "Sent At",
    language: Language.EN
  },
  {
    key: "user.notification.table.sentAt",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "user.notification.table.sentAt",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "user.notification.table.action",
    content: "Action",
    language: Language.EN
  },
  {
    key: "user.notification.table.action",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "user.notification.table.action",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.activeTeamsUser.toolTip.text",
    content: "The total number of active user in the team. ",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.activeTeamsUser.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.activeTeamsUser.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.totalTeamsUser.toolTip.text",
    content: "The total number of user in the team. ",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.totalTeamsUser.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.totalTeamsUser.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.flowConnected.toolTip.text",
    content: "The total Connected of Flow accumulated. ",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.flowConnected.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.flowConnected.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.EmbeddingToken.toolTip.text",
    content: "No. of token used to understand the content of the uploaded data during specific time range. ",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.EmbeddingToken.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.EmbeddingToken.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.CompletionToken.toolTip.text",
    content: "No. of token used to generate response to the user during specific time range. ",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.CompletionToken.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.CompletionToken.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.PromptToken.toolTip.text",
    content: "No. of token used to digest user's input during specific time range. ",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.PromptToken.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.PromptToken.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.totalCompletionToken.toolTip.text",
    content: "Prompt + Completion token. ",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.totalCompletionToken.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.totalCompletionToken.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.noOfChat.toolTip.text",
    content: "The total number of Chat(Playground + Teams + API) accumulated and during a specific time range.",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.noOfChat.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.noOfChat.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.noOfFlowChat.toolTip.text",
    content: "The total number of Chat(Playground + Teams + API) accumulated and during a specific time range.",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.noOfFlowChat.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.noOfFlowChat.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.noOfBotChat.toolTip.text",
    content: "The total number of Chat(Playground + Teams + API) accumulated and during a specific time range.",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.noOfBotChat.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.noOfBotChat.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.totalDataStorage.toolTip.text",
    content: "The total size of upload data accumulated. ",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.totalDataStorage.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.totalDataStorage.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.totalNoOfData.toolTip.text",
    content: "The total number of upload data accumulated.",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.totalNoOfData.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.totalNoOfData.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.activeUser.toolTip.text",
    content: "The total number of user with chat count greater than 1 during a specific time range. ",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.activeUser.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.activeUser.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.activeMembers.toolTip.text",
    content: "The total number of members with chat count greater than 1 during a specific time range. ",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.activeMembers.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.activeMembers.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.totalUsers.toolTip.text",
    content: "The total number of user created on the Gen AI Platform accumulated. ",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.totalUsers.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.totalUsers.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.totalMembers.toolTip.text",
    content: "The total number of members invate into the groups accumulated. ",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.totalMembers.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.totalMembers.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.activeFlows.toolTip.text",
    content: "The total number of flows with chat count greater than 1 during a specific time range. ",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.activeFlows.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.activeFlows.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.totalFlows.toolTip.text",
    content: "The total number of flows created on the Gen AI Platform accumulated. ",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.totalFlows.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.totalFlows.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.activeBots.toolTip.text",
    content: "The total number of bots with chat count greater than 1 during a specific time range.",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.activeBots.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.activeBots.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.summaryInfo.totalBots.toolTip.text",
    content: "The total number of bots created on the Gen AI Platform accumulated. ",
    language: Language.EN
  },
  {
    key: "dashboard.summaryInfo.totalBots.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.summaryInfo.totalBots.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.chartsTag.noOfBot.toolTip.title",
    content: "No. of Bot ",
    language: Language.EN
  },
  {
    key: "dashboard.chartsTag.noOfBot.toolTip.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.chartsTag.noOfBot.toolTip.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.chartsTag.noOfBot.toolTip.text",
    content: "No. of bot created 1 during a specific time range. ",
    language: Language.EN
  },
  {
    key: "dashboard.chartsTag.noOfBot.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.chartsTag.noOfBot.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.chartsTag.noOfChatBot.toolTip.title",
    content: "No. of Chat(Bot)",
    language: Language.EN
  },
  {
    key: "dashboard.chartsTag.noOfChatBot.toolTip.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.chartsTag.noOfChatBot.toolTip.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.chartsTag.noOfChatBot.toolTip.text",
    content: "Total no. of Chat until date. ",
    language: Language.EN
  },
  {
    key: "dashboard.chartsTag.noOfChatBot.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.chartsTag.noOfChatBot.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.chartsTag.totalCompletionToken.toolTip.title",
    content: "Total Completion Token Usages",
    language: Language.EN
  },
  {
    key: "dashboard.chartsTag.totalCompletionToken.toolTip.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.chartsTag.totalCompletionToken.toolTip.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.chartsTag.totalCompletionToken.toolTip.text",
    content: "Prompt token usage + completion token usage. ",
    language: Language.EN
  },
  {
    key: "dashboard.chartsTag.totalCompletionToken.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.chartsTag.totalCompletionToken.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.chartsTag.promptToken.toolTip.title",
    content: "Prompt Token Usage",
    language: Language.EN
  },
  {
    key: "dashboard.chartsTag.promptToken.toolTip.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.chartsTag.promptToken.toolTip.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.chartsTag.promptToken.toolTip.text",
    content: "No. of token used to digest user's input during specific time range. ",
    language: Language.EN
  },
  {
    key: "dashboard.chartsTag.promptToken.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.chartsTag.promptToken.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.chartsTag.completionToken.toolTip.title",
    content: "Completion Token Usage",
    language: Language.EN
  },
  {
    key: "dashboard.chartsTag.completionToken.toolTip.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.chartsTag.completionToken.toolTip.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.chartsTag.completionToken.toolTip.text",
    content: "No. of token used to generate response to the user during specific time range. ",
    language: Language.EN
  },
  {
    key: "dashboard.chartsTag.completionToken.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.chartsTag.completionToken.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.chartsTag.embeddingToken.toolTip.title",
    content: "Embedding Token Usage",
    language: Language.EN
  },
  {
    key: "dashboard.chartsTag.embeddingToken.toolTip.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.chartsTag.embeddingToken.toolTip.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.chartsTag.embeddingToken.toolTip.text",
    content: "No. of token used to understand the content of the uploaded data during specific time range. ",
    language: Language.EN
  },
  {
    key: "dashboard.chartsTag.embeddingToken.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.chartsTag.embeddingToken.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.chartsTag.members.toolTip.title",
    content: "Members",
    language: Language.EN
  },
  {
    key: "dashboard.chartsTag.members.toolTip.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.chartsTag.members.toolTip.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.chartsTag.members.toolTip.text",
    content: "No. of Members member create 1 during a specific time range. ",
    language: Language.EN
  },
  {
    key: "dashboard.chartsTag.members.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.chartsTag.members.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.chartsTag.users.toolTip.title",
    content: "Users",
    language: Language.EN
  },
  {
    key: "dashboard.chartsTag.users.toolTip.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.chartsTag.users.toolTip.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "dashboard.chartsTag.users.toolTip.text",
    content: "No. of Users user create on the Gen AI Platform during a specific time range. ",
    language: Language.EN
  },
  {
    key: "dashboard.chartsTag.users.toolTip.text",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "dashboard.chartsTag.users.toolTip.text",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "messageTemplate.createTemplatePopUp.tooltip.share",
    content: "Once enabled, all users can see the template in all bot/flow.",
    language: Language.EN
  },
  {
    key: "messageTemplate.createTemplatePopUp.tooltip.share",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "messageTemplate.createTemplatePopUp.tooltip.share",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "messageTemplate.createTemplatePopUp.tooltip.active",
    content: "Once enabled, you can see the template in the playground within this bot/flow.",
    language: Language.EN
  },
  {
    key: "messageTemplate.createTemplatePopUp.tooltip.active",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "messageTemplate.createTemplatePopUp.tooltip.active",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "error.429-0-0",
    content: "No Quota Left. Time remaining for quota released is {minutes}mins. You can also follow <a href='https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/Bot-Plan-and-Subscription.aspx' target='_blank' rel='noopener'>user guide</a> to upgrade your quota plan.",
    language: Language.EN
  },
  {
    key: "error.429-0-0",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "error.429-0-0",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "systemMessage.uploadFile.warningMessage.noBotReviewer",
    content: "No bot reviewer in this bot, please add reviewer in Member page.",
    language: Language.EN
  },
  {
    key: "systemMessage.uploadFile.warningMessage.noBotReviewer",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "systemMessage.uploadFile.warningMessage.noBotReviewer",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "systemMessage.uploadFile.warningMessage.infoMsg",
    content: "This bot requires a Bot Reviewer for the second-tier approval of uploading internal use/confidential/strictly confidential files.",
    language: Language.EN
  },
  {
    key: "systemMessage.uploadFile.warningMessage.infoMsg",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "systemMessage.uploadFile.warningMessage.infoMsg",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "home.title.messageTemplate",
    content: "Message Template",
    language: Language.EN
  },
  {
    key: "home.title.messageTemplate",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "home.title.messageTemplate",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "home.title.messageTemplate.all",
    content: "All Templates",
    language: Language.EN
  },
  {
    key: "home.title.messageTemplate.all",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "home.title.messageTemplate.all",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "home.explore.llm.noResult",
    content: "No LLMs result",
    language: Language.EN
  },
  {
    key: "home.explore.llm.noResult",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "home.explore.llm.noResult",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "home.explore.messageTemplate.noResult",
    content: "No message template result",
    language: Language.EN
  },
  {
    key: "home.explore.messageTemplate.noResult",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "home.explore.messageTemplate.noResult",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "home.explore.bot.noResult",
    content: "No bot",
    language: Language.EN
  },
  {
    key: "home.explore.bot.noResult",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "home.explore.bot.noResult",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "home.explore.bot.tooltip.guideline",
    content: "Public Bot Guide (e.g. tutorial, capability, user guide) will be shown for user's reference",
    language: Language.EN
  },
  {
    key: "home.explore.bot.tooltip.guideline",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "home.explore.bot.tooltip.guideline",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "home.explore.bot.text.learnMore",
    content: "Learn More",
    language: Language.EN
  },
  {
    key: "home.explore.bot.text.learnMore",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "home.explore.bot.text.learnMore",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "home.explore.bot.filter.all",
    content: "All",
    language: Language.EN
  },
  {
    key: "home.explore.bot.filter.all",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "home.explore.bot.filter.all",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "home.explore.bot.filter.official",
    content: "Official",
    language: Language.EN
  },
  {
    key: "home.explore.bot.filter.official",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "home.explore.bot.filter.official",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "home.explore.bot.filter.stars",
    content: "Your Stars",
    language: Language.EN
  },
  {
    key: "home.explore.bot.filter.stars",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "home.explore.bot.filter.stars",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "home.explore.bot.popUp.title",
    content: "Public Bot Guide Details",
    language: Language.EN
  },
  {
    key: "home.explore.bot.popUp.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "home.explore.bot.popUp.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "home.explore.bot.popUp.button.createChat",
    content: "Create Chat",
    language: Language.EN
  },
  {
    key: "home.explore.bot.popUp.button.createChat",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "home.explore.bot.popUp.button.createChat",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "home.explore.bot.popUp.button.back",
    content: "Go Back",
    language: Language.EN
  },
  {
    key: "home.explore.bot.popUp.button.back",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "home.explore.bot.popUp.button.back",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "home.oneClickChat.title",
    content: "Start chatting with your chatbot",
    language: Language.EN
  },
  {
    key: "home.oneClickChat.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "home.oneClickChat.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "admin.botLists.delete.tooltip",
    content: "Hidden in the frontend (UI and Report) for initial housekeeping; the database record still exists",
    language: Language.EN
  },
  {
    key: "admin.botLists.delete.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "admin.botLists.delete.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "admin.botLists.deactivate.tooltip",
    content: "Sets the group (Bot/Flow) status to inactive, disabling group functionality. The UI, frontend, and database record still exist.",
    language: Language.EN
  },
  {
    key: "admin.botLists.deactivate.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "admin.botLists.deactivate.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.botPlanRequest.rejectComment.placeholder",
    content: "Please type comment.",
    language: Language.EN
  },
  {
    key: "planAndSubscription.botPlanRequest.rejectComment.placeholder",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.botPlanRequest.rejectComment.placeholder",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.botPlanRequest.comment.title",
    content: "Comment.",
    language: Language.EN
  },
  {
    key: "planAndSubscription.botPlanRequest.comment.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.botPlanRequest.comment.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "planAndSubscription.botPlanRequest.quotaRule.example",
    content: "like 10000000 for token quota.",
    language: Language.EN
  },
  {
    key: "planAndSubscription.botPlanRequest.quotaRule.example",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "planAndSubscription.botPlanRequest.quotaRule.example",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "membersTable.getOptionFunction.visitor",
    content: "System chat only",
    language: Language.EN
  },
  {
    key: "membersTable.getOptionFunction.visitor",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "membersTable.getOptionFunction.visitor",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "membersTable.getOptionFunction.member",
    content: "Chat sessions and file uploads",
    language: Language.EN
  },
  {
    key: "membersTable.getOptionFunction.member",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "membersTable.getOptionFunction.member",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "membersTable.getOptionFunction.contributor",
    content: "Privilege on Chat with Files",
    language: Language.EN
  },
  {
    key: "membersTable.getOptionFunction.contributor",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "membersTable.getOptionFunction.contributor",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "membersTable.getOptionFunction.admin",
    content: "Admin with no access to log files",
    language: Language.EN
  },
  {
    key: "membersTable.getOptionFunction.admin",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "membersTable.getOptionFunction.admin",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "membersTable.getOptionFunction.owner",
    content: "Super Admin",
    language: Language.EN
  },
  {
    key: "membersTable.getOptionFunction.owner",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "membersTable.getOptionFunction.owner",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "membersTable.getOptionPermission.visitor",
    content: "Cannot add or remove anyone",
    language: Language.EN
  },
  {
    key: "membersTable.getOptionPermission.visitor",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "membersTable.getOptionPermission.visitor",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "membersTable.getOptionPermission.member",
    content: "Cannot add or remove anyone",
    language: Language.EN
  },
  {
    key: "membersTable.getOptionPermission.member",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "membersTable.getOptionPermission.member",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "membersTable.getOptionPermission.contributor",
    content: "Can add or remove visitors",
    language: Language.EN
  },
  {
    key: "membersTable.getOptionPermission.contributor",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "membersTable.getOptionPermission.contributor",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "membersTable.getOptionPermission.admin",
    content: "Can add or remove members and visitors",
    language: Language.EN
  },
  {
    key: "membersTable.getOptionPermission.admin",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "membersTable.getOptionPermission.admin",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "membersTable.getOptionPermission.owner",
    content: "Can add or remove anyone",
    language: Language.EN
  },
  {
    key: "membersTable.getOptionPermission.owner",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "membersTable.getOptionPermission.owner",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.title",
    content: "Notification Centre",
    language: Language.EN
  },
  {
    key: "notificationCentre.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.modal.override.title",
    content: "Override",
    language: Language.EN
  },
  {
    key: "notificationCentre.modal.override.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.modal.override.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.modal.disable.title",
    content: "Reminder",
    language: Language.EN
  },
  {
    key: "notificationCentre.modal.disable.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.modal.disable.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.table.overrideRecipients",
    content: "Override Recipients",
    language: Language.EN
  },
  {
    key: "notificationCentre.table.overrideRecipients",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.table.overrideRecipients",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.table.overrideStatus",
    content: "Override Status",
    language: Language.EN
  },
  {
    key: "notificationCentre.table.overrideStatus",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.table.overrideStatus",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.table.defaultRecipient",
    content: "Default Recipient",
    language: Language.EN
  },
  {
    key: "notificationCentre.table.defaultRecipient",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.table.defaultRecipient",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.table.interval",
    content: "Interval",
    language: Language.EN
  },
  {
    key: "notificationCentre.table.interval",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.table.interval",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.updateNotificationConfig.enable.success",
    content: "Notification config enabled successfully",
    language: Language.EN
  },
  {
    key: "notificationCentre.updateNotificationConfig.enable.success",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.updateNotificationConfig.enable.success",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.updateNotificationConfig.disable.success",
    content: "Notification config disabled successfully",
    language: Language.EN
  },
  {
    key: "notificationCentre.updateNotificationConfig.disable.success",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.updateNotificationConfig.disable.success",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.disable.overrideStatus.message",
    content: "Are you sure you want to disable the override status? The override recipient and/or silent period will be removed.",
    language: Language.EN
  },
  {
    key: "notificationCentre.disable.overrideStatus.message",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.disable.overrideStatus.message",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.editOverride.silentPeriod.title",
    content: "Silent period (hr) for alert",
    language: Language.EN
  },
  {
    key: "notificationCentre.editOverride.silentPeriod.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.editOverride.silentPeriod.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.editOverride.recipients.title",
    content: "Recipients:",
    language: Language.EN
  },
  {
    key: "notificationCentre.editOverride.recipients.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.editOverride.recipients.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.table.invalid.recipients",
    content: "Inactive or invalid Member",
    language: Language.EN
  },
  {
    key: "notificationCentre.table.invalid.recipients",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.table.invalid.recipients",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "menu.costCenter",
    content: "Cost Center",
    language: Language.EN
  },
  {
    key: "menu.costCenter",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "menu.costCenter",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.title.remarks",
    content: "Default cost-to-price margin: {margin}%. Set this at feature flag [Key:ADMIN.CONFIG_MODEL_PRICE]",
    language: Language.EN
  },
  {
    key: "costCenter.title.remarks",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.title.remarks",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.table.modelName",
    content: "Model Name",
    language: Language.EN
  },
  {
    key: "costCenter.table.modelName",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.table.modelName",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.table.unit",
    content: "Unit",
    language: Language.EN
  },
  {
    key: "costCenter.table.unit",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.table.unit",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.table.lastAutoUpdate",
    content: "Last Auto Update:",
    language: Language.EN
  },
  {
    key: "costCenter.table.lastAutoUpdate",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.table.lastAutoUpdate",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.table.margin",
    content: "Margin(%)",
    language: Language.EN
  },
  {
    key: "costCenter.table.margin",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.table.margin",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.table.new",
    content: "New:",
    language: Language.EN
  },
  {
    key: "costCenter.table.new",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.table.new",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.table.current",
    content: "Current:",
    language: Language.EN
  },
  {
    key: "costCenter.table.current",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.table.current",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.table.lastManualUpdate",
    content: "Last Manual Update",
    language: Language.EN
  },
  {
    key: "costCenter.table.lastManualUpdate",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.table.lastManualUpdate",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.table.button.viewLog",
    content: "View Log",
    language: Language.EN
  },
  {
    key: "costCenter.table.button.viewLog",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.table.button.viewLog",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.table.tooltip.COST_SUGGESTION",
    content: "Reference Cost per Unit From Internet (USD)\nCost reference per models will be crawl from internet at 15th of each month.",
    language: Language.EN
  },
  {
    key: "costCenter.table.tooltip.COST_SUGGESTION",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.table.tooltip.COST_SUGGESTION",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.table.tooltip.COST",
    content: "Cost per Unit by Admin(USD)\nThe system will automatically apply the “Cost From Internet” to “Cost by Admin” if the admin does not input the cost before the 25th of each month. \n\nOnce the admin makes a change to this record, the automatically synced record will always be overridden by the admin’s record.\n\nFormula:\nCost From Internet = Cost by Admin",
    language: Language.EN
  },
  {
    key: "costCenter.table.tooltip.COST",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.table.tooltip.COST",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.table.tooltip.PRICE",
    content: "New Price per Unit (USD)\nThe system will automatically apply the “Cost by Admin” to “New Price”x”1+Margin” if the admin does not input the cost before the 25th of each month. \n\nOnce the admin makes a change to this record, the automatically synced record will always be overridden by the admin’s record.\n\nFormula:\nCost by 3rd Party = Cost by Admin\nNew Price = Cost * (1 + Default Margin)",
    language: Language.EN
  },
  {
    key: "costCenter.table.tooltip.PRICE",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.table.tooltip.PRICE",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.table.tooltip.CURRENT_PRICE",
    content: "Current Price per Unit (USD)\nThe system will automatically apply the “New Price” to “Current Price” every 1st of each month. ",
    language: Language.EN
  },
  {
    key: "costCenter.table.tooltip.CURRENT_PRICE",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.table.tooltip.CURRENT_PRICE",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.table.tooltip.margin",
    content: "A calculated margin for reference.\n\nNew: \n(New Price - Admin Cost)/Admin Cost * 100%\nCurrent: \n(Current Price - Admin Cost)/Admin Cost * 100%",
    language: Language.EN
  },
  {
    key: "costCenter.table.tooltip.margin",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.table.tooltip.margin",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.table.tooltip.noSync",
    content: "Admin overrides record; no automatic update applies.",
    language: Language.EN
  },
  {
    key: "costCenter.table.tooltip.noSync",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.table.tooltip.noSync",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.popUp.label.newCost",
    content: "New Cost",
    language: Language.EN
  },
  {
    key: "costCenter.popUp.label.newCost",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.popUp.label.newCost",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.popUp.label.newPrice",
    content: "New Price",
    language: Language.EN
  },
  {
    key: "costCenter.popUp.label.newPrice",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.popUp.label.newPrice",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.popUp.label.overrideAutoSync",
    content: "Override Auto Sync",
    language: Language.EN
  },
  {
    key: "costCenter.popUp.label.overrideAutoSync",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.popUp.label.overrideAutoSync",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.popUp.tooltip.overrideAutoSync",
    content: "Switch on: The newly saved record will always override the automatically synced record.",
    language: Language.EN
  },
  {
    key: "costCenter.popUp.tooltip.overrideAutoSync",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.popUp.tooltip.overrideAutoSync",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.priceType.COST_SUGGESTION",
    content: "Cost From Internet",
    language: Language.EN
  },
  {
    key: "costCenter.priceType.COST_SUGGESTION",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.priceType.COST_SUGGESTION",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.priceType.COST",
    content: "Cost By Admin",
    language: Language.EN
  },
  {
    key: "costCenter.priceType.COST",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.priceType.COST",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.priceType.PRICE",
    content: "New Price",
    language: Language.EN
  },
  {
    key: "costCenter.priceType.PRICE",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.priceType.PRICE",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.priceType.CURRENT_PRICE",
    content: "Current Price",
    language: Language.EN
  },
  {
    key: "costCenter.priceType.CURRENT_PRICE",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.priceType.CURRENT_PRICE",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "costCenter.editPopUp.error.requiredAtLeastOne",
    content: "At least one of cost or price is required",
    language: Language.EN
  },
  {
    key: "costCenter.editPopUp.error.requiredAtLeastOne",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "costCenter.editPopUp.error.requiredAtLeastOne",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "usageReport.table.price",
    content: "Price(USD)",
    language: Language.EN
  },
  {
    key: "usageReport.table.price",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "usageReport.table.price",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "menu.billingReport",
    content: "Billing Report",
    language: Language.EN
  },
  {
    key: "menu.billingReport",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "menu.billingReport",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "rateCard.table.price",
    content: "Price Per Unit(USD)",
    language: Language.EN
  },
  {
    key: "rateCard.table.price",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "rateCard.table.price",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "rateCard.table.lastUpdate",
    content: "Last Update",
    language: Language.EN
  },
  {
    key: "rateCard.table.lastUpdate",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "rateCard.table.lastUpdate",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "flowRequests.title.remarks",
    content: "While accepting the flow request, you acknowledge that if the flow calls the bot to generate an answer and consumes any tokens, the usage will be counted against the bot.",
    language: Language.EN
  },
  {
    key: "flowRequests.title.remarks",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "flowRequests.title.remarks",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "user.admin.pageDescription",
    content: "These are all the users currently registered on the Bot Builder platform. These include both Admin team members and customers. The chosen time range will impact the usage token and the user status, and it will be applicable to the report and the table provided below.",
    language: Language.EN
  },
  {
    key: "user.admin.pageDescription",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "user.admin.pageDescription",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "menu.ratesCard",
    content: "Rates Card",
    language: Language.EN
  },
  {
    key: "menu.ratesCard",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "menu.ratesCard",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "login.platformName",
    content: "{company} Gen AI Platform",
    language: Language.EN
  },
  {
    key: "login.platformName",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "login.platformName",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "login.checkbox",
    content: "I certify that I am not uploading production data while using the UAT platform, and I have read and accept the <a target='_blank' class='text-xl underline' href='https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/Dos-and-Donts.aspx#do-s-and-don-ts'>HKT GenAI Platform Usage Guidelines</a>.",
    language: Language.EN
  },
  {
    key: "login.checkbox",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "login.checkbox",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "login.checkbox.error",
    content: "Please check the checkbox.",
    language: Language.EN
  },
  {
    key: "login.checkbox.error",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "login.checkbox.error",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.defaultRecipient.tooltip",
    content: "When any LLM monthly token usage in your bot reaches the high usage thresholds, system will daily trigger an usage alert to bot owners&admins.",
    language: Language.EN
  },
  {
    key: "notificationCentre.defaultRecipient.tooltip",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.defaultRecipient.tooltip",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.overrideStatus.tooltip.disable",
    content: "This notification does support overriding.",
    language: Language.EN
  },
  {
    key: "notificationCentre.overrideStatus.tooltip.disable",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.overrideStatus.tooltip.disable",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.overrideStatus.tooltip.enable",
    content: "Enable notification first before overriding.",
    language: Language.EN
  },
  {
    key: "notificationCentre.overrideStatus.tooltip.enable",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.overrideStatus.tooltip.enable",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.overrideRecipient.tooltip.inactive",
    content: "Recipient is inactivated in the platform",
    language: Language.EN
  },
  {
    key: "notificationCentre.overrideRecipient.tooltip.inactive",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.overrideRecipient.tooltip.inactive",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.overrideRecipient.tooltip.removedMember",
    content: "Recipient has been removed.",
    language: Language.EN
  },
  {
    key: "notificationCentre.overrideRecipient.tooltip.removedMember",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.overrideRecipient.tooltip.removedMember",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.modal.disable.success",
    content: "Disabled successfully",
    language: Language.EN
  },
  {
    key: "notificationCentre.modal.disable.success",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.modal.disable.success",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.modal.override.success",
    content: "Updated successfully",
    language: Language.EN
  },
  {
    key: "notificationCentre.modal.override.success",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.modal.override.success",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "notificationCentre.disable.message",
    content: "Are you sure you want to disable notification?",
    language: Language.EN
  },
  {
    key: "notificationCentre.disable.message",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "notificationCentre.disable.message",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "apiKey.title.remarks",
    content: "Click <a href='https://swagger:<EMAIL>/v1/redoc' target='_blank'>API doc</a> & <a href='https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/ChatBot-API-Developer-Guide.aspx' target='_blank'>Developer Guideline</a> for developing guidance.",
    language: Language.EN
  },
  {
    key: "apiKey.title.remarks",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "apiKey.title.remarks",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "member.sectionTitle.description",
    content: "Check <a target='_blank' class='text-key-blue underline' href='https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/Membership-and-permission.aspx'>role details</a>",
    language: Language.EN
  },
  {
    key: "member.sectionTitle.description",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "member.sectionTitle.description",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "404.msg",
    content: "Oops! The page you're looking for seems to have wandered off. Don't worry, just hit that refresh button and let's give it another try! If you think there is something broken, please report a problem to <a href='mailto:<EMAIL>'><EMAIL></a>.",
    language: Language.EN
  },
  {
    key: "404.msg",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "404.msg",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "footer.copyright",
    content: "©{year} HKT | Version number - FE: {versionFe} | BE: {versionBe}",
    language: Language.EN
  },
  {
    key: "footer.copyright",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "footer.copyright",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "breadcrumb.root",
    content: "{company} AI Bot Builder",
    language: Language.EN
  },
  {
    key: "breadcrumb.root",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "breadcrumb.root",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.editRole.successful",
    content: "Edit Role Successfully",
    language: Language.EN
  },
  {
    key: "roles.editRole.successful",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.editRole.successful",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.removeRole.successful",
    content: "Removed custom role Successfully",
    language: Language.EN
  },
  {
    key: "roles.removeRole.successful",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.removeRole.successful",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.createRole.successful",
    content: "Create Role Successfully",
    language: Language.EN
  },
  {
    key: "roles.createRole.successful",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.createRole.successful",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.breadcrumb",
    content: "Custom Role",
    language: Language.EN
  },
  {
    key: "roles.breadcrumb",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.breadcrumb",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.tableColumn.header.name",
    content: "Role Name",
    language: Language.EN
  },
  {
    key: "roles.tableColumn.header.name",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.tableColumn.header.name",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.tableColumn.type.default",
    content: "Default",
    language: Language.EN
  },
  {
    key: "roles.tableColumn.type.default",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.tableColumn.type.default",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.tableColumn.type.custom",
    content: "Custom",
    language: Language.EN
  },
  {
    key: "roles.tableColumn.type.custom",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.tableColumn.type.custom",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.tableColumn.action.seePermission",
    content: "See Permission",
    language: Language.EN
  },
  {
    key: "roles.tableColumn.action.seePermission",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.tableColumn.action.seePermission",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.tableColumn.action.editRole",
    content: "Edit Role",
    language: Language.EN
  },
  {
    key: "roles.tableColumn.action.editRole",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.tableColumn.action.editRole",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.button.createRole",
    content: "Create Custom Role",
    language: Language.EN
  },
  {
    key: "roles.button.createRole",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.button.createRole",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.popup.header.rolePermission",
    content: "Role Permissions",
    language: Language.EN
  },
  {
    key: "roles.popup.header.rolePermission",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.popup.header.rolePermission",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.popup.onOk.create",
    content: "Create",
    language: Language.EN
  },
  {
    key: "roles.popup.onOk.create",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.popup.onOk.create",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.popup.onOk.save",
    content: "Save",
    language: Language.EN
  },
  {
    key: "roles.popup.onOk.save",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.popup.onOk.save",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.popup.onOk.confirm",
    content: "Confirm",
    language: Language.EN
  },
  {
    key: "roles.popup.onOk.confirm",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.popup.onOk.confirm",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.popup.title.remove",
    content: "Remove Custom Role",
    language: Language.EN
  },
  {
    key: "roles.popup.title.remove",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.popup.title.remove",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.popup.message.remove",
    content: "Are you sure to remove this custom role?",
    language: Language.EN
  },
  {
    key: "roles.popup.message.remove",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.popup.message.remove",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.popup.create.template",
    content: "Use Existing Role as Template",
    language: Language.EN
  },
  {
    key: "roles.popup.create.template",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.popup.create.template",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.popup.create.name",
    content: "Role Name",
    language: Language.EN
  },
  {
    key: "roles.popup.create.name",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.popup.create.name",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "roles.popup.edit.custom",
    content: "Custom Permissions",
    language: Language.EN
  },
  {
    key: "roles.popup.edit.custom",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "roles.popup.edit.custom",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "completeProfilePopUp.title.account",
    content: "Complete Your Profile Now!",
    language: Language.EN
  },
  {
    key: "completeProfilePopUp.title.account",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "completeProfilePopUp.title.account",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "completeProfilePopUp.content.account",
    content: "Provide us with more information about yourself by completing your account settings now!",
    language: Language.EN
  },
  {
    key: "completeProfilePopUp.content.account",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "completeProfilePopUp.content.account",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "completeProfilePopUp.title.publicBot",
    content: "Please Complete Your Profile!",
    language: Language.EN
  },
  {
    key: "completeProfilePopUp.title.publicBot",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "completeProfilePopUp.title.publicBot",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "completeProfilePopUp.content.publicBot",
    content: "Go to Account Settings to complete profile setup to gain access to the Public Bot.",
    language: Language.EN
  },
  {
    key: "completeProfilePopUp.content.publicBot",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "completeProfilePopUp.content.publicBot",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "completeProfilePopUp.button",
    content: "Account Setting",
    language: Language.EN
  },
  {
    key: "completeProfilePopUp.button",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "completeProfilePopUp.button",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "genKB.title",
    content: "Gen KB Setting",
    language: Language.EN
  },
  {
    key: "genKB.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "genKB.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "genKB.text.instruction",
    content: "How to add member on GenKB:\n<ol><li>Enable GenKB by turning on below &apos;Enable&apos; button</li><li>Go to <a href='https://genkb.prd.bot-builder.pccw.com'>https://genkb.prd.bot-builder.pccw.com</a></li><li>Add member on GenKB (<a target='_blank' href='https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/Access-of-GenKB.aspx?csf=1&web=1&share=EbaClrIYA59EqLSQz7SE0dUBxMyt2k9zCGOuB9YK80aVVQ&e=O6hcYi&xsdata=MDV8MDJ8fDE5M2M4MjMzYjYwODRlMGY5NGVjMDhkZDFkYTMxZjM4fGM1OTI0ZGE2ZGViMzQyMWJhYTk4NTdiY2JhMGJhMDUwfDB8MHw2Mzg2OTkzMDg1OTczMTEzMDd8VW5rbm93bnxWR1ZoYlhOVFpXTjFjbWwwZVZObGNuWnBZMlY4ZXlKV0lqb2lNQzR3TGpBd01EQWlMQ0pRSWpvaVYybHVNeklpTENKQlRpSTZJazkwYUdWeUlpd2lWMVFpT2pFeGZRPT18MXxMMk5vWVhSekx6RTVPbTFsWlhScGJtZGZUbGRaTlU1WFZteE9iVlYwV2tSRk5VOURNREJPZWsxM1RGZEZkMWxVVlhSYVIwNXFUVEpWZDAxcVRUUmFSMUY2UUhSb2NtVmhaQzUyTWk5dFpYTnpZV2RsY3k4eE56TTBNek16T1RJNE56VTF8MGMwMzMyNzYyNjVmNDlhNjk0ZWMwOGRkMWRhMzFmMzh8NGFjMDQ1MzFiZDAwNGQ0MjllZDFmYTRmNGM3YzBmMTk%3D&sdata=NXhjNEw0WjE5Vk8vM0pnVHZ4RkJqbnRlczlITDFrM0lmNXFZVVk5SzUvTT0%3D&ovuser=c5924da6-deb3-421b-aa98-57bcba0ba050%2C02009900%40corphq.hk.pccw.com#3.-how-to-assign-or-remove-user-role-for-kb-owner-admin'>Guide</a>)<ol><li>All members must be already whitelisted on GenAI platform</li><li>KB Owner (Bot Creator) clicks &apos;Roles&apos;/&apos;Membership&apos; under GenKB to add KB Owner / Admin</li><li>KB Admin clicks &apos;Roles&apos;/&apos;Membership&apos; Tab under GenKB to add general member</li></ol></li></ol>\nRemarks:\nIf Bot Owner or Bot Admin would like to grant access to a member for the AI Bot inside GenKB, they must go to the &apos;Membership&apos; Tab under the GenAI platform to add the member (<a target='_blank' href='https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/01.-Playground.aspx?xsdata=MDV8MDJ8fGM0Y2JjN2FkMjkxNTRhY2ZmZjJhMDhkZDFlNzJlOTkxfGM1OTI0ZGE2ZGViMzQyMWJhYTk4NTdiY2JhMGJhMDUwfDB8MHw2Mzg3MDAyMDEwNTAyNDI4NTh8VW5rbm93bnxWR1ZoYlhOVFpXTjFjbWwwZVZObGNuWnBZMlY4ZXlKV0lqb2lNQzR3TGpBd01EQWlMQ0pRSWpvaVYybHVNeklpTENKQlRpSTZJazkwYUdWeUlpd2lWMVFpT2pFeGZRPT18MXxMMk5vWVhSekx6RTVPakEyTUdFeE5EVTJMV1pqWWpjdE5EWmhZUzA1TW1KaExUTTJPVEkwWXprNU9UbGxZVjltWW1KbU5XRXhaQzFtTVdabExUUXhaR0l0WVRBME55MDNNV0prWlRWak5USXdaVEpBZFc1eExtZGliQzV6Y0dGalpYTXZiV1Z6YzJGblpYTXZNVGN6TkRReU16TXdOREF6TVE9PXw4NzkxOTZlNjBjNzc0ZGUxZmYyYTA4ZGQxZTcyZTk5MXw4Mjc3YzcyYjY0MDQ0NmVjYjRmY2Y0Njk5NGI1NTY1MQ%3D%3D&sdata=TmlzOG00M1VuQUYzWVpEUHRXZUE2TGg1UzdpSUVvcFZSeWVTUENtVlFvST0%3D&ovuser=c5924da6-deb3-421b-aa98-57bcba0ba050%2C02006000%40corphq.hk.pccw.com&OR=Teams-HL&CT=1734423883980&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiI1MC8yNDExMDExNTcyNCIsIkhhc0ZlZGVyYXRlZFVzZXIiOmZhbHNlfQ%3D%3D#02.-invite-members'>Guide</a>).",
    language: Language.EN
  },
  {
    key: "genKB.text.instruction",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "genKB.text.instruction",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "header.link.info",
    content: "https://pccw0.sharepoint.com/sites/GenerativeAI",
    language: Language.EN
  },
  {
    key: "header.link.info",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "header.link.info",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "login.link.requestAccess",
    content: "https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/Gen-AI-Platform-Whitelisting.aspx",
    language: Language.EN
  },
  {
    key: "login.link.requestAccess",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "login.link.requestAccess",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "emptyBot.title",
    content: "Begin Your Generative AI Journey Today!",
    language: Language.EN
  },
  {
    key: "emptyBot.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "emptyBot.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "emptyBot.text.startingGuideline",
    content: "To get started, select an existing bot from the top or create a new one. Please note that only the bot owner can create new bots. If you need access to your teammate&apos;s bot, please contact your team administrator.",
    language: Language.EN
  },
  {
    key: "emptyBot.text.startingGuideline",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "emptyBot.text.startingGuideline",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "emptyBot.text.remarks",
    content: "Need help from getting started? Check out our <a href='https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/Create-Your-Bot-with-A-Few-Click.aspx?web=1' target='_blank'>Online User Guide</a> for guidance!",
    language: Language.EN
  },
  {
    key: "emptyBot.text.remarks",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "emptyBot.text.remarks",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "header.tooltip.faqBot",
    content: "FAQ Bot",
    language: Language.EN
  },
  {
    key: "header.tooltip.faqBot",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "header.tooltip.faqBot",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "messageTemplate.createTemplatePopUp.tooltip.guide",
    content: "Template Guide (e.g., tutorial, capability, user guide) will be shown for user's reference.",
    language: Language.EN
  },
  {
    key: "messageTemplate.createTemplatePopUp.tooltip.guide",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "messageTemplate.createTemplatePopUp.tooltip.guide",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "messageTemplate.createTemplatePopUp.guide.title",
    content: "Template Guide Details (not support batch upload)",
    language: Language.EN
  },
  {
    key: "messageTemplate.createTemplatePopUp.guide.title",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "messageTemplate.createTemplatePopUp.guide.title",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "publicBotManagement.edit.tooltip.guideline",
    content: "Public Bot Guide (e.g., tutorial, capability, user guide) will be shown for user's reference.",
    language: Language.EN
  },
  {
    key: "publicBotManagement.edit.tooltip.guideline",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "publicBotManagement.edit.tooltip.guideline",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "publicBotManagement.edit.label.guideline",
    content: "Public Bot Guide Details",
    language: Language.EN
  },
  {
    key: "publicBotManagement.edit.label.guideline",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "publicBotManagement.edit.label.guideline",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "publicBotManagement.edit.label.businessUnit",
    content: "BU/FU",
    language: Language.EN
  },
  {
    key: "publicBotManagement.edit.label.businessUnit",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "publicBotManagement.edit.label.businessUnit",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "publicBotManagement.edit.label.official",
    content: "Official",
    language: Language.EN
  },
  {
    key: "publicBotManagement.edit.label.official",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "publicBotManagement.edit.label.official",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "publicBotManagement.edit.label.order",
    content: "Ordering",
    language: Language.EN
  },
  {
    key: "publicBotManagement.edit.label.order",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "publicBotManagement.edit.label.order",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "publicBotManagement.edit.label.botName",
    content: "Bot Name",
    language: Language.EN
  },
  {
    key: "publicBotManagement.edit.label.botName",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "publicBotManagement.edit.label.botName",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "publicBotManagement.edit.label.botId",
    content: "Bot ID",
    language: Language.EN
  },
  {
    key: "publicBotManagement.edit.label.botId",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "publicBotManagement.edit.label.botId",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "publicBotManagement.edit.label.category",
    content: "Category",
    language: Language.EN
  },
  {
    key: "publicBotManagement.edit.label.category",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "publicBotManagement.edit.label.category",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "publicBotManagement.edit.label.labels",
    content: "Labels",
    language: Language.EN
  },
  {
    key: "publicBotManagement.edit.label.labels",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "publicBotManagement.edit.label.labels",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "publicBotManagement.table.column.creator",
    content: "Creator",
    language: Language.EN
  },
  {
    key: "publicBotManagement.table.column.creator",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "publicBotManagement.table.column.creator",
    content: "",
    language: Language.ZH_HANT
  },
  {
    key: "publicBotManagement.table.column.isOfficial",
    content: "Is Official",
    language: Language.EN
  },
  {
    key: "publicBotManagement.table.column.isOfficial",
    content: "",
    language: Language.ZH_HANS
  },
  {
    key: "publicBotManagement.table.column.isOfficial",
    content: "",
    language: Language.ZH_HANT
  }
];
