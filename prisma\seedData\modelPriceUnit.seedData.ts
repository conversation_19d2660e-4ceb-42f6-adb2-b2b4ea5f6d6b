import { ModelPriceSource } from '@prisma/client';
import { JsonValue } from 'src/modules/feature-flags/feature-flags-model.dto';

export interface ModelPriceUnitSeed {
  slug: string;
  llmEngineSlug: string;
  modelPriceSource: ModelPriceSource;
  metadata: JsonValue;
}

export const modelPriceUnits: ModelPriceUnitSeed[] = [
  {
    llmEngineSlug: 'dalle-3',
    metadata: {
      litellm: {
        path: ['azure/hd/1024-x-1024/dall-e-3', 'input_cost_per_pixel'],
        transform: 'pixel2Image',
      },
      quality: 'hd',
      formulas: [
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? 1 : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Image (HD 1024x1024)',
      resolution: '1024x1024',
      defaultFeature: 'TEXT_TO_IMAGE',
    },
    modelPriceSource: 'LITELLM',
    slug: 'dalle3-hd-1024x1024-cost-per-image',
  },
  {
    llmEngineSlug: 'dalle-3',
    metadata: {
      litellm: {
        path: ['azure/hd/1024-x-1792/dall-e-3', 'input_cost_per_pixel'],
        transform: 'pixel2Image',
      },
      quality: 'hd',
      formulas: [
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? 1 : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Image (HD 1024x1792)',
      resolution: '1024x1792',
      defaultFeature: 'TEXT_TO_IMAGE',
    },
    modelPriceSource: 'LITELLM',
    slug: 'dalle3-hd-1024x1792-cost-per-image',
  },
  {
    llmEngineSlug: 'dalle-3',
    metadata: {
      litellm: {
        path: ['azure/hd/1792-x-1024/dall-e-3', 'input_cost_per_pixel'],
        transform: 'pixel2Image',
      },
      quality: 'hd',
      formulas: [
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? 1 : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Image (HD 1792x1024)',
      resolution: '1792x1024',
      defaultFeature: 'TEXT_TO_IMAGE',
    },
    modelPriceSource: 'LITELLM',
    slug: 'dalle3-hd-1792x1024-cost-per-image',
  },
  {
    llmEngineSlug: 'dalle-3',
    metadata: {
      litellm: {
        path: ['azure/standard/1024-x-1024/dall-e-3', 'input_cost_per_pixel'],
        transform: 'pixel2Image',
      },
      quality: 'standard',
      formulas: [
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? 1 : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Image (Standard 1024x1024)',
      resolution: '1024x1024',
      defaultFeature: 'TEXT_TO_IMAGE',
    },
    modelPriceSource: 'LITELLM',
    slug: 'dalle3-std-1024x1024-cost-per-image',
  },
  {
    llmEngineSlug: 'dalle-3',
    metadata: {
      litellm: {
        path: ['azure/standard/1024-x-1792/dall-e-3', 'input_cost_per_pixel'],
        transform: 'pixel2Image',
      },
      quality: 'standard',
      formulas: [
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? 1 : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Image (Standard 1024x1792)',
      resolution: '1024x1792',
      defaultFeature: 'TEXT_TO_IMAGE',
    },
    modelPriceSource: 'LITELLM',
    slug: 'dalle3-std-1024x1792-cost-per-image',
  },
  {
    llmEngineSlug: 'dalle-3',
    metadata: {
      litellm: {
        path: ['azure/standard/1792-x-1024/dall-e-3', 'input_cost_per_pixel'],
        transform: 'pixel2Image',
      },
      quality: 'standard',
      formulas: [
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula:
            'compareText(usage.imageUsage.quality, priceUnit.metadata.quality) == 0 and compareText(usage.imageUsage.resolution, priceUnit.metadata.resolution) == 0 ? 1 : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Image (Standard 1792x1024)',
      resolution: '1792x1024',
      defaultFeature: 'TEXT_TO_IMAGE',
    },
    modelPriceSource: 'LITELLM',
    slug: 'dalle3-std-1792x1024-cost-per-image',
  },
  {
    llmEngineSlug: 'gpt-35-turbo',
    metadata: {
      litellm: {
        path: ['azure/gpt-35-turbo', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-35-turbo-input-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-35-turbo',
    metadata: {
      litellm: {
        path: ['azure/gpt-35-turbo', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-35-turbo-output-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-35-turbo-0613',
    metadata: {
      litellm: {
        path: ['azure/gpt-35-turbo-0613', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-35-turbo-0613-input-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-35-turbo-0613',
    metadata: {
      litellm: {
        path: ['azure/gpt-35-turbo-0613', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-35-turbo-0613-output-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-35-turbo-1106',
    metadata: {
      litellm: {
        path: ['azure/gpt-35-turbo-1106', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-35-turbo-1106-input-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-35-turbo-1106',
    metadata: {
      litellm: {
        path: ['azure/gpt-35-turbo-1106', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-35-turbo-1106-output-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-35-turbo-16k',
    metadata: {
      litellm: {
        path: ['azure/gpt-35-turbo-16k', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-35-turbo-16k-input-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-35-turbo-16k',
    metadata: {
      litellm: {
        path: ['azure/gpt-35-turbo-16k', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-35-turbo-16k-output-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4',
    metadata: {
      litellm: {
        path: ['azure/gpt-4', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4-input-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4',
    metadata: {
      litellm: {
        path: ['azure/gpt-4', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4-output-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4-0125',
    metadata: {
      litellm: {
        path: ['azure/gpt-4-0125-preview', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4-0125-input-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4-0125',
    metadata: {
      litellm: {
        path: ['azure/gpt-4-0125-preview', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4-0125-output-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4-32k',
    metadata: {
      litellm: {
        path: ['azure/gpt-4-32k', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4-32k-input-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4-32k',
    metadata: {
      litellm: {
        path: ['azure/gpt-4-32k', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4-32k-output-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4-turbo',
    metadata: {
      litellm: {
        path: ['azure/gpt-4-turbo', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4-turbo-input-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4-turbo',
    metadata: {
      litellm: {
        path: ['azure/gpt-4-turbo', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4-turbo-output-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4o',
    metadata: {
      litellm: {
        path: ['azure/gpt-4o', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4o-input-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4o',
    metadata: {
      litellm: {
        path: ['azure/gpt-4o', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4o-output-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4o-mini',
    metadata: {
      litellm: {
        path: ['azure/gpt-4o-mini', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4o-mini-input-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4o-mini',
    metadata: {
      litellm: {
        path: ['azure/gpt-4o-mini', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4o-mini-output-cost-per-token',
  },
  // gpt-4.1
  {
    llmEngineSlug: 'gpt-4.1',
    metadata: {
      litellm: {
        path: ['azure/gpt-4.1', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4.1-input-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4.1',
    metadata: {
      litellm: {
        path: ['azure/gpt-4.1', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4.1-output-cost-per-token',
  },
  // gpt 4.1 mini
  {
    llmEngineSlug: 'gpt-4.1-mini',
    metadata: {
      litellm: {
        path: ['azure/gpt-4.1-mini', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4.1-mini-input-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4.1-mini',
    metadata: {
      litellm: {
        path: ['azure/gpt-4.1-mini', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4.1-mini-output-cost-per-token',
  },
  // gpt 4.1 nano
  {
    llmEngineSlug: 'gpt-4.1-nano',
    metadata: {
      litellm: {
        path: ['azure/gpt-4.1-nano', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4.1-nano-input-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4.1-nano',
    metadata: {
      litellm: {
        path: ['azure/gpt-4.1-nano', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'gpt-4.1-nano-output-cost-per-token',
  },
  {
    llmEngineSlug: 'nova-ptc-xl-v1',
    metadata: {
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'SENSENOVA',
    slug: 'nova-ptc-xl-v1-input-cost-per-token',
  },
  {
    llmEngineSlug: 'nova-ptc-xl-v1',
    metadata: {
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'SENSENOVA',
    slug: 'nova-ptc-xl-v1-output-cost-per-token',
  },
  {
    llmEngineSlug: 'nova-ptc-xs-v1',
    metadata: {
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'SENSENOVA',
    slug: 'nova-ptc-xs-v1-input-cost-per-token',
  },
  {
    llmEngineSlug: 'nova-ptc-xs-v1',
    metadata: {
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'SENSENOVA',
    slug: 'nova-ptc-xs-v1-output-cost-per-token',
  },
  {
    llmEngineSlug: 'nova-sensechat-128K',
    metadata: {
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'SENSENOVA',
    slug: 'nova-sensechat-128K-input-cost-per-token',
  },
  {
    llmEngineSlug: 'nova-sensechat-128K',
    metadata: {
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'SENSENOVA',
    slug: 'nova-sensechat-128K-output-cost-per-token',
  },
  {
    llmEngineSlug: 'nova-sensechat-5',
    metadata: {
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'SENSENOVA',
    slug: 'nova-sensechat-5-input-cost-per-token',
  },
  {
    llmEngineSlug: 'nova-sensechat-5',
    metadata: {
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'SENSENOVA',
    slug: 'nova-sensechat-5-output-cost-per-token',
  },
  {
    llmEngineSlug: 'nova-sensechat-5-cantonese',
    metadata: {
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'SENSENOVA',
    slug: 'nova-sensechat-5-cantonese-input-cost-per-token',
  },
  {
    llmEngineSlug: 'nova-sensechat-5-cantonese',
    metadata: {
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'SENSENOVA',
    slug: 'nova-sensechat-5-cantonese-output-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-chat-bison',
    metadata: {
      litellm: {
        path: ['chat-bison', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-chat-bison-input-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-chat-bison',
    metadata: {
      litellm: {
        path: ['chat-bison', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-chat-bison-output-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-chat-bison-002',
    metadata: {
      litellm: {
        path: ['chat-bison@002', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-chat-bison-002-input-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-chat-bison-002',
    metadata: {
      litellm: {
        path: ['chat-bison@002', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-chat-bison-002-output-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-chat-bison-32k',
    metadata: {
      litellm: {
        path: ['chat-bison-32k', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-chat-bison-32k-input-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-chat-bison-32k',
    metadata: {
      litellm: {
        path: ['chat-bison-32k', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-chat-bison-32k-output-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-codechat-bison-002',
    metadata: {
      litellm: {
        path: ['codechat-bison@002', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-codechat-bison-002-input-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-codechat-bison-002',
    metadata: {
      litellm: {
        path: ['codechat-bison@002', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-codechat-bison-002-output-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-codechat-bison-latest',
    metadata: {
      litellm: {
        path: ['codechat-bison@latest', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-codechat-bison-latest-input-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-codechat-bison-latest',
    metadata: {
      litellm: {
        path: ['codechat-bison@latest', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-codechat-bison-latest-output-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-gemini-1.5-flash-001',
    metadata: {
      litellm: {
        path: ['gemini-1.5-flash-001', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens <= 128000 ? usage.promptTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens <= 128000 ? usage.promptTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens <= 128000 ? usage.promptTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-1.5-flash-001-input-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-gemini-1.5-flash-001',
    metadata: {
      litellm: {
        path: ['gemini-1.5-flash-001', 'input_cost_per_token_above_128k_tokens'],
      },
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens > 128000 ? usage.promptTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens > 128000 ? usage.promptTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens > 128000 ? usage.promptTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token (>128k)',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-1.5-flash-001-input-cost-per-token-above-128k-tokens',
  },
  {
    llmEngineSlug: 'vertexai-gemini-1.5-flash-001',
    metadata: {
      litellm: {
        path: ['gemini-1.5-flash-001', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens <= 128000 ? usage.completionTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens <= 128000 ? usage.completionTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens <= 128000 ? usage.completionTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-1.5-flash-001-output-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-gemini-1.5-flash-001',
    metadata: {
      litellm: {
        path: ['gemini-1.5-flash-001', 'output_cost_per_token_above_128k_tokens'],
      },
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens > 128000 ? usage.completionTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens > 128000 ? usage.completionTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens > 128000 ? usage.completionTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token (>128k)',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-1.5-flash-001-output-cost-per-token-above-128k-tokens',
  },
  {
    llmEngineSlug: 'vertexai-gemini-1.5-flash-002',
    metadata: {
      litellm: {
        path: ['gemini-1.5-flash-002', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens <= 128000 ? usage.promptTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens <= 128000 ? usage.promptTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens <= 128000 ? usage.promptTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-1.5-flash-002-input-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-gemini-1.5-flash-002',
    metadata: {
      litellm: {
        path: ['gemini-1.5-flash-002', 'input_cost_per_token_above_128k_tokens'],
      },
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens > 128000 ? usage.promptTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens > 128000 ? usage.promptTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens > 128000 ? usage.promptTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token (>128k)',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-1.5-flash-002-input-cost-per-token-above-128k-tokens',
  },
  {
    llmEngineSlug: 'vertexai-gemini-1.5-flash-002',
    metadata: {
      litellm: {
        path: ['gemini-1.5-flash-002', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens <= 128000 ? usage.completionTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens <= 128000 ? usage.completionTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens <= 128000 ? usage.completionTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-1.5-flash-002-output-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-gemini-1.5-flash-002',
    metadata: {
      litellm: {
        path: ['gemini-1.5-flash-002', 'output_cost_per_token_above_128k_tokens'],
      },
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens > 128000 ? usage.completionTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens > 128000 ? usage.completionTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens > 128000 ? usage.completionTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token (>128k)',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-1.5-flash-002-output-cost-per-token-above-128k-tokens',
  },
  {
    llmEngineSlug: 'vertexai-gemini-1.5-pro-001',
    metadata: {
      litellm: {
        path: ['gemini-1.5-pro-001', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens <= 128000 ? usage.promptTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens <= 128000 ? usage.promptTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens <= 128000 ? usage.promptTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-1.5-pro-001-input-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-gemini-1.5-pro-001',
    metadata: {
      litellm: {
        path: ['gemini-1.5-pro-001', 'input_cost_per_token_above_128k_tokens'],
      },
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens > 128000 ? usage.promptTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens > 128000 ? usage.promptTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens > 128000 ? usage.promptTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Toke (>128k)',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-1.5-pro-001-input-cost-per-token-above-128k-tokens',
  },
  {
    llmEngineSlug: 'vertexai-gemini-1.5-pro-001',
    metadata: {
      litellm: {
        path: ['gemini-1.5-pro-001', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens <= 128000 ? usage.completionTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens <= 128000 ? usage.completionTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens <= 128000 ? usage.completionTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-1.5-pro-001-output-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-gemini-1.5-pro-001',
    metadata: {
      litellm: {
        path: ['gemini-1.5-pro-001', 'output_cost_per_token_above_128k_tokens'],
      },
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens > 128000 ? usage.completionTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens > 128000 ? usage.completionTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens > 128000 ? usage.completionTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token (>128k)',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-1.5-pro-001-output-cost-per-token-above-128k-tokens',
  },
  {
    llmEngineSlug: 'vertexai-gemini-1.5-pro-002',
    metadata: {
      litellm: {
        path: ['gemini-1.5-pro-002', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens <= 128000 ? usage.promptTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens <= 128000 ? usage.promptTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens <= 128000 ? usage.promptTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-1.5-pro-002-input-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-gemini-1.5-pro-002',
    metadata: {
      litellm: {
        path: ['gemini-1.5-pro-002', 'input_cost_per_token_above_128k_tokens'],
      },
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens > 128000 ? usage.promptTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens > 128000 ? usage.promptTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens > 128000 ? usage.promptTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token (>128k)',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-1.5-pro-002-input-cost-per-token-above-128k-tokens',
  },
  {
    llmEngineSlug: 'vertexai-gemini-1.5-pro-002',
    metadata: {
      litellm: {
        path: ['gemini-1.5-pro-002', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens <= 128000 ? usage.completionTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens <= 128000 ? usage.completionTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens <= 128000 ? usage.completionTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-1.5-pro-002-output-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-gemini-1.5-pro-002',
    metadata: {
      litellm: {
        path: ['gemini-1.5-pro-002', 'output_cost_per_token_above_128k_tokens'],
      },
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens > 128000 ? usage.completionTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens > 128000 ? usage.completionTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens > 128000 ? usage.completionTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token (>128k)',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-1.5-pro-002-output-cost-per-token-above-128k-tokens',
  },
  // gemini 2.0 flash thinking
  {
    llmEngineSlug: 'vertexai-gemini-2.0-flash-thinking-exp',
    metadata: {
      litellm: {
        path: ['gemini-2.0-flash-thinking-exp', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-2.0-flash-thinking-exp-input-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-gemini-2.0-flash-thinking-exp',
    metadata: {
      litellm: {
        path: ['gemini-2.0-flash-thinking-exp', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-2.0-flash-thinking-exp-output-cost-per-token',
  },
  // gemini 2.0 pro
  {
    llmEngineSlug: 'vertexai-gemini-2.0-pro',
    metadata: {
      litellm: {
        path: ['gemini-2.0-pro-exp-02-05', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-2.0-pro-input-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-gemini-2.0-pro',
    metadata: {
      litellm: {
        path: ['gemini-2.0-pro-exp-02-05', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-2.0-pro-output-cost-per-token',
  },
  // gemini 2.5 pro
  {
    llmEngineSlug: 'vertexai-gemini-2.5-pro',
    metadata: {
      litellm: {
        path: ['gemini-2.5-pro-exp-03-25', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'ADMIN',
    slug: 'vertexai-gemini-2.5-pro-input-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-gemini-2.5-pro',
    metadata: {
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens > 200000 ? usage.promptTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens > 200000 ? usage.promptTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens > 200000 ? usage.promptTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token (>200k)',
    },
    modelPriceSource: 'ADMIN',
    slug: 'vertexai-gemini-2.5-pro-input-cost-per-token-above-200k-tokens',
  },
  {
    llmEngineSlug: 'vertexai-gemini-2.5-pro',
    metadata: {
      litellm: {
        path: ['gemini-2.5-pro-exp-03-25', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'ADMIN',
    slug: 'vertexai-gemini-2.5-pro-output-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-gemini-2.5-pro',
    metadata: {
      formulas: [
        {
          formula:
            'usage.totalCompletionTokens > 200000 ? usage.completionTokens * priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.totalCompletionTokens > 200000 ? usage.completionTokens * priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.totalCompletionTokens > 200000 ? usage.completionTokens : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token (>200k)',
    },
    modelPriceSource: 'ADMIN',
    slug: 'vertexai-gemini-2.5-pro-output-cost-per-token-above-200k-tokens',
  },
  // gemini 2.0 flash
  {
    llmEngineSlug: 'vertexai-gemini-2.0-flash-001',
    metadata: {
      litellm: {
        path: ['gemini-2.0-flash-001', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-2.0-flash-001-input-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-gemini-2.0-flash-001',
    metadata: {
      litellm: {
        path: ['gemini-2.0-flash-001', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-2.0-flash-001-output-cost-per-token',
  },
  // gemini 2.0 flash lite
  {
    llmEngineSlug: 'vertexai-gemini-2.0-flash-lite-preview',
    metadata: {
      litellm: {
        path: ['gemini/gemini-2.0-flash-lite-preview-02-05', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-2.0-flash-lite-preview-input-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-gemini-2.0-flash-lite-preview',
    metadata: {
      litellm: {
        path: ['gemini/gemini-2.0-flash-lite-preview-02-05', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-2.0-flash-lite-preview-output-cost-per-token',
  },
  // gemini 2.0 flash lite
  {
    llmEngineSlug: 'vertexai-gemini-2.0-flash-lite',
    metadata: {
      litellm: {
        path: ['gemini/gemini-2.0-flash-lite', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-2.0-flash-lite-input-cost-per-token',
  },
  {
    llmEngineSlug: 'vertexai-gemini-2.0-flash-lite',
    metadata: {
      litellm: {
        path: ['gemini/gemini-2.0-flash-lite', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-gemini-2.0-flash-lite-output-cost-per-token',
  },
  // imagen 3.0 fast generate
  {
    llmEngineSlug: 'vertexai-imagen-3.0-fast-generate-001',
    metadata: {
      feature: 'image_generation',
      litellm: {
        path: ['vertex_ai/imagen-3.0-fast-generate-001', 'output_cost_per_image'],
      },
      formulas: [
        {
          formula:
            'compareText(usage.imageUsage.feature, priceUnit.metadata.feature) == 0 ? priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'compareText(usage.imageUsage.feature, priceUnit.metadata.feature) == 0 ? priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'compareText(usage.imageUsage.feature, priceUnit.metadata.feature) == 0 ? 1 : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Image',
      defaultFeature: 'TEXT_TO_IMAGE',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-imagen-3.0-fast-generate-001-image-generation-cost-per-image',
  },
  {
    llmEngineSlug: 'vertexai-imagen-3.0-generate-001',
    metadata: {
      feature: 'image_generation',
      litellm: {
        path: ['vertex_ai/imagen-3.0-generate-001', 'output_cost_per_image'],
      },
      formulas: [
        {
          formula:
            'compareText(usage.imageUsage.feature, priceUnit.metadata.feature) == 0 ? priceUnit.COST.value : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'compareText(usage.imageUsage.feature, priceUnit.metadata.feature) == 0 ? priceUnit.PRICE.value : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula:
            'compareText(usage.imageUsage.feature, priceUnit.metadata.feature) == 0 ? bignumber(1) : bignumber(0)',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Image',
      defaultFeature: 'TEXT_TO_IMAGE',
    },
    modelPriceSource: 'LITELLM',
    slug: 'vertexai-imagen-3.0-generate-001-image-generation-cost-per-image',
  },
  {
    llmEngineSlug: 'azure-text-embedding-ada-002',
    metadata: {
      litellm: {
        path: ['azure/text-embedding-ada-002', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.embeddingTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.embeddingTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.embeddingTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
      defaultFeature: 'EMBEDDING',
    },
    modelPriceSource: 'LITELLM',
    slug: 'azure-text-embedding-ada-002-input-cost-per-token',
  },
  {
    llmEngineSlug: 'azure-speech',
    metadata: {
      litellm: {
        path: ['azure/tts-1', 'input_cost_per_character'],
      },
      formulas: [
        {
          formula:
            'usage.audioUsage != null ? (usage.audioUsage.chars_count != null ? usage.audioUsage.chars_count * priceUnit.COST.value : 0) : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.audioUsage != null ? (usage.audioUsage.chars_count != null ? usage.audioUsage.chars_count * priceUnit.PRICE.value : 0) : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula:
            'usage.audioUsage != null ? (usage.audioUsage.chars_count != null ? usage.audioUsage.chars_count : 0) : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Character',
      defaultFeature: 'TTS',
    },
    modelPriceSource: 'LITELLM',
    slug: 'azure-speech-tts-input-cost-per-character',
  },
  {
    llmEngineSlug: 'azure-speech',
    metadata: {
      formulas: [
        {
          formula:
            'usage.audioUsage != null ? (usage.audioUsage.file_length != null ? usage.audioUsage.file_length * priceUnit.COST.value : 0) : 0',
          summaryKey: 'MODEL_COST',
        },
        {
          formula:
            'usage.audioUsage != null ? (usage.audioUsage.file_length != null ? usage.audioUsage.file_length * priceUnit.PRICE.value : 0) : 0',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula:
            'usage.audioUsage != null ? (usage.audioUsage.file_length != null ? usage.audioUsage.file_length : 0) : 0',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Audio (Second)',
      defaultFeature: 'STT',
    },
    modelPriceSource: 'AZURE',
    slug: 'azure-speech-stt-output-cost-per-second',
  },
  {
    llmEngineSlug: 'o1-preview',
    metadata: {
      litellm: {
        path: ['azure/o1-preview', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'o1-preview-input-cost-per-token',
  },
  {
    llmEngineSlug: 'o1-preview',
    metadata: {
      litellm: {
        path: ['azure/o1-preview', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'o1-preview-output-cost-per-token',
  },
  {
    llmEngineSlug: 'o1-mini',
    metadata: {
      litellm: {
        path: ['azure/o1-mini', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'o1-mini-input-cost-per-token',
  },
  {
    llmEngineSlug: 'o1-mini',
    metadata: {
      litellm: {
        path: ['azure/o1-mini', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'o1-mini-output-cost-per-token',
  },
  // o3
  {
    llmEngineSlug: 'o3',
    metadata: {
      litellm: {
        path: ['azure/o3', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'o3-input-cost-per-token',
  },
  {
    llmEngineSlug: 'o3',
    metadata: {
      litellm: {
        path: ['azure/o3', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'o3-output-cost-per-token',
  },
  // o3 mini
  {
    llmEngineSlug: 'o3-mini',
    metadata: {
      litellm: {
        path: ['azure/o3-mini', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'o3-mini-input-cost-per-token',
  },
  {
    llmEngineSlug: 'o3-mini',
    metadata: {
      litellm: {
        path: ['azure/o3-mini', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'o3-mini-output-cost-per-token',
  },
  // o4 mini
  {
    llmEngineSlug: 'o4-mini',
    metadata: {
      litellm: {
        path: ['azure/o4-mini', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'o4-mini-input-cost-per-token',
  },
  {
    llmEngineSlug: 'o4-mini',
    metadata: {
      litellm: {
        path: ['azure/o4-mini', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'o4-mini-output-cost-per-token',
  },
  {
    llmEngineSlug: 'phi-4',
    metadata: {
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'AZURE',
    slug: 'phi-4-input-cost-per-token',
  },
  {
    llmEngineSlug: 'phi-4',
    metadata: {
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'AZURE',
    slug: 'phi-4-output-cost-per-token',
  },
  {
    llmEngineSlug: 'deepseek-r1',
    metadata: {
      litellm: {
        path: ['azure_ai/deepseek-r1', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'deepseek-r1-input-cost-per-token',
  },
  {
    llmEngineSlug: 'deepseek-r1',
    metadata: {
      litellm: {
        path: ['azure_ai/deepseek-r1', 'output_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'LITELLM',
    slug: 'deepseek-r1-output-cost-per-token',
  },
  {
    llmEngineSlug: 'azure-text-embedding-3-small',
    metadata: {
      litellm: {
        path: ['azure/text-embedding-3-small', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.embeddingTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.embeddingTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.embeddingTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
      defaultFeature: 'EMBEDDING',
    },
    modelPriceSource: 'LITELLM',
    slug: 'azure-text-embedding-3-small-input-cost-per-token',
  },
  {
    llmEngineSlug: 'azure-text-embedding-3-large',
    metadata: {
      litellm: {
        path: ['azure/text-embedding-3-large', 'input_cost_per_token'],
      },
      formulas: [
        {
          formula: 'usage.embeddingTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.embeddingTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.embeddingTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
      defaultFeature: 'EMBEDDING',
    },
    modelPriceSource: 'LITELLM',
    slug: 'azure-text-embedding-3-large-input-cost-per-token',
  },
  {
    llmEngineSlug: 'deepseek-v3',
    metadata: {
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'ADMIN',
    slug: 'deepseek-v3-input-cost-per-token',
  },
  {
    llmEngineSlug: 'deepseek-v3',
    metadata: {
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'ADMIN',
    slug: 'deepseek-v3-output-cost-per-token',
  },
  {
    llmEngineSlug: 'qwq-32b',
    metadata: {
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'ADMIN',
    slug: 'qwq-32b-input-cost-per-token',
  },
  {
    llmEngineSlug: 'qwq-32b',
    metadata: {
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'ADMIN',
    slug: 'qwq-32b-output-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4o-batch',
    metadata: {
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'ADMIN',
    slug: 'gpt-4o-batch-input-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4o-batch',
    metadata: {
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'ADMIN',
    slug: 'gpt-4o-batch-output-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4o-mini-batch',
    metadata: {
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'ADMIN',
    slug: 'gpt-4o-mini-batch-input-cost-per-token',
  },
  {
    llmEngineSlug: 'gpt-4o-mini-batch',
    metadata: {
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'ADMIN',
    slug: 'gpt-4o-mini-batch-output-cost-per-token',
  },
  // qwen turbo
  {
    llmEngineSlug: 'qwen-turbo',
    metadata: {
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'ADMIN',
    slug: 'qwen-turbo-input-cost-per-token',
  },
  {
    llmEngineSlug: 'qwen-turbo',
    metadata: {
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'ADMIN',
    slug: 'qwen-turbo-output-cost-per-token',
  },
  // qwen plus
  {
    llmEngineSlug: 'qwen-plus',
    metadata: {
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'ADMIN',
    slug: 'qwen-plus-input-cost-per-token',
  },
  {
    llmEngineSlug: 'qwen-plus',
    metadata: {
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'ADMIN',
    slug: 'qwen-plus-output-cost-per-token',
  },
  // qwen max
  {
    llmEngineSlug: 'qwen-max',
    metadata: {
      formulas: [
        {
          formula: 'usage.promptTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.promptTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.promptTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Input Token',
    },
    modelPriceSource: 'ADMIN',
    slug: 'qwen-max-input-cost-per-token',
  },
  {
    llmEngineSlug: 'qwen-max',
    metadata: {
      formulas: [
        {
          formula: 'usage.completionTokens * priceUnit.COST.value',
          summaryKey: 'MODEL_COST',
        },
        {
          formula: 'usage.completionTokens * priceUnit.PRICE.value',
          summaryKey: 'MODEL_PRICE',
        },
        {
          formula: 'usage.completionTokens',
          summaryKey: 'MODEL_USAGE',
        },
      ],
      unitName: 'Output Token',
    },
    modelPriceSource: 'ADMIN',
    slug: 'qwen-max-output-cost-per-token',
  },
];
