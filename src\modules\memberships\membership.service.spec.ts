import { PrismaService } from 'src/providers/prisma/prisma.service';
import { DeepMockProxy, mockDeep } from 'jest-mock-extended';
import { Test, TestingModule } from '@nestjs/testing';
import { Email, Membership, PrismaClient, SystemName } from '@prisma/client';
import { MembershipsService } from './memberships.service';
import { MockFunctionMetadata, ModuleMocker } from 'jest-mock';
import { MailService } from '../../providers/mail/mail.service';
const moduleMocker = new ModuleMocker(global);
describe('GroupsService', () => {
  let membershipsService: MembershipsService;
  let email: DeepMockProxy<MailService>;
  let prismaService: DeepMockProxy<{ [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'> }>;
  beforeEach(async () => {
    prismaService = mockDeep<PrismaClient>() as unknown as DeepMockProxy<{
      [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'>;
    }>;
    email = mockDeep<MailService>();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MembershipsService,
        {
          provide: PrismaService,
          useValue: prismaService,
        },
        {
          provide: MailService,
          useValue: email,
        },
      ],
    })
      .useMocker((token) => {
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>;
          const Mock = moduleMocker.generateFromMetadata(mockMetadata);
          return new Mock();
        }
      })
      .compile();
    membershipsService = module.get(MembershipsService);
  });

  describe('getMembershipsCount', () => {
    it('should return memberships count ', async () => {
      const count = 1;
      prismaService.membership.count.mockResolvedValue(count);
      const res = await membershipsService.getMembershipsCount();
      expect(res).toEqual(count);
      expect(prismaService.membership.count).toBeCalled();
    });
  });

  describe('getUserMembership', () => {
    it('should return memberships ', async () => {
      const memberships = {
        id: 1,
        userId: 1,
      } as Membership;
      prismaService.membership.findUnique.mockResolvedValue(memberships);
      const res = await membershipsService.getUserMembership(1, 1);
      expect(res).toEqual(memberships);
      expect(prismaService.membership.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
        include: { group: true, user: true, Role: true },
      });
    });
  });

  describe('getGroupOwnerMembership', () => {
    it('should return Group Owner memberships ', async () => {
      const memberships = {
        id: 1,
        userId: 1,
      } as Membership;
      prismaService.membership.findFirst.mockResolvedValue(memberships);
      const res = await membershipsService.getGroupOwnerMembership(1);
      expect(res).toEqual(memberships);
      expect(prismaService.membership.findFirst).toHaveBeenCalledWith({
        where: { groupId: 1, Role: { systemName: SystemName.GROUP_OWNER } },
        include: { group: true, user: true, Role: true },
      });
    });
  });

  describe('getGroupMembership', () => {
    it('should return Group memberships ', async () => {
      const memberships = {
        id: 1,
        userId: 1,
        groupId: 1,
      } as Membership;
      const email = {
        email: '<EMAIL>',
      } as Email;
      const check = {
        ...memberships,
        ...email,
      };
      prismaService.membership.findUnique.mockResolvedValue(memberships);
      prismaService.email.findFirst.mockResolvedValue(email);
      const res = await membershipsService.getGroupMembership(1, 1);
      expect(res).toEqual(check);
      expect(prismaService.membership.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
        include: { group: true, user: true, Role: true },
      });
    });
  });
});
