import { <PERSON>, Get, Param, ParseIntPipe } from '@nestjs/common';

import { PermissionsService } from './permissions.service';
import { Permission, PermissionGroupFeature } from '@prisma/client';
import { Scopes } from '../auth/scope.decorator';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@Controller('groups/:groupId/permissions')
@ApiBearerAuth('bearer-auth')
@ApiTags('Permissions')
export class PermissionsGroupController {
  constructor(private permissionsService: PermissionsService) {}

  /** Get Permissions feature */
  @Get('/features')
  @Scopes('group-{groupId}:read-role')
  async getPermissionsFeatures(
    @Param('groupId', ParseIntPipe) groupId: number,
  ): Promise<PermissionGroupFeature[]> {
    return await this.permissionsService.getGroupPermissionOfFeature(groupId);
  }

  /** Get Full Group Permissions*/
  @Get('/')
  @Scopes('group-{groupId}:read-role')
  async getGroupPermissions(
    @Param('groupId', ParseIntPipe) groupId: number,
  ): Promise<Permission[]> {
    return await this.permissionsService.getGroupPermissions(groupId);
  }

  /** Get Custom Group Permissions */
  @Get('/custom')
  @Scopes('group-{groupId}:read-role')
  async getCustomGroupPermissions(
    @Param('groupId', ParseIntPipe) groupId: number,
  ): Promise<Permission[]> {
    return await this.permissionsService.getGroupPermissionsOfCustomRole(groupId);
  }

  /** Get Group Permissions by Role */
  @Get('/role/:roleId')
  @Scopes('group-{groupId}:read-role')
  async getGroupPermissionsByRole(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('roleId', ParseIntPipe) roleId: number,
  ): Promise<Permission[]> {
    return await this.permissionsService.getGroupPermissionsByRole(groupId, roleId);
  }
}
