name: CodeQL CI
on:
  schedule:
    - cron: '0 0 * * 1'
jobs:
  release:
    name: Build and analyze
    runs-on: ubuntu-18.04
    if: "!contains(github.event.head_commit.message, '[skip ci]')"
    steps:
      - name: Checkout
        uses: actions/checkout@v2.3.4
      - name: Setup Node.js
        uses: actions/setup-node@v2.1.5
        with:
          node-version: 14
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v1
        with:
          languages: javascript
      - name: Cache node modules
        uses: actions/cache@v2
        env:
          cache-name: cache-node-modules
        with:
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-
      - name: Install dependencies
        run: npm ci
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v1
