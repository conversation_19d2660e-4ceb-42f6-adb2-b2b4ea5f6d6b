-- DropFore<PERSON><PERSON>ey
ALTER TABLE "AiResource" DROP CONSTRAINT "AiResource_groupId_fkey";

-- DropForeignKey
ALTER TABLE "AiResource" DROP CONSTRAINT "AiResource_llmEngineId_fkey";

-- DropForeignKey
ALTER TABLE "ApiKey" DROP CONSTRAINT "ApiKey_groupId_fkey";

-- DropForeignKey
ALTER TABLE "ApiKey" DROP CONSTRAINT "ApiKey_rateLimitPlanId_fkey";

-- DropForeignKey
ALTER TABLE "ApiKey" DROP CONSTRAINT "ApiKey_userId_fkey";

-- DropForeignKey
ALTER TABLE "ApprovedSubnet" DROP CONSTRAINT "ApprovedSubnet_userId_fkey";

-- DropForeignKey
ALTER TABLE "AuditLog" DROP CONSTRAINT "AuditLog_apiKeyId_fkey";

-- DropFore<PERSON><PERSON><PERSON>
ALTER TABLE "AuditLog" DROP CONSTRAINT "AuditLog_groupId_fkey";

-- DropForeignKey
ALTER TABLE "AuditLog" DROP CONSTRAINT "AuditLog_userId_fkey";

-- DropForeignKey
ALTER TABLE "BackupCode" DROP CONSTRAINT "BackupCode_userId_fkey";

-- DropForeignKey
ALTER TABLE "ChatHistory" DROP CONSTRAINT "ChatHistory_chatSessionId_fkey";

-- DropForeignKey
ALTER TABLE "ChatSession" DROP CONSTRAINT "ChatSession_groupId_fkey";

-- DropForeignKey
ALTER TABLE "ChatSession" DROP CONSTRAINT "ChatSession_userId_fkey";

-- DropForeignKey
ALTER TABLE "DataPromotionRequest" DROP CONSTRAINT "DataPromotionRequest_entitySnapshotId_fkey";

-- DropForeignKey
ALTER TABLE "DataPromotionRequest" DROP CONSTRAINT "DataPromotionRequest_operatorId_fkey";

-- DropForeignKey
ALTER TABLE "DataPromotionRequest" DROP CONSTRAINT "DataPromotionRequest_requesterId_fkey";

-- DropForeignKey
ALTER TABLE "DataPromotionRequest" DROP CONSTRAINT "DataPromotionRequest_targetGroupId_fkey";

-- DropForeignKey
ALTER TABLE "Domain" DROP CONSTRAINT "Domain_groupId_fkey";

-- DropForeignKey
ALTER TABLE "Email" DROP CONSTRAINT "Email_userId_fkey";

-- DropForeignKey
ALTER TABLE "EntitySnapshot" DROP CONSTRAINT "EntitySnapshot_createdByUserId_fkey";

-- DropForeignKey
ALTER TABLE "EntitySnapshot" DROP CONSTRAINT "EntitySnapshot_groupId_fkey";

-- DropForeignKey
ALTER TABLE "Group" DROP CONSTRAINT "Group_parentId_fkey";

-- DropForeignKey
ALTER TABLE "Identity" DROP CONSTRAINT "Identity_userId_fkey";

-- DropForeignKey
ALTER TABLE "LLMModel" DROP CONSTRAINT "LLMModel_groupId_fkey";

-- DropForeignKey
ALTER TABLE "LLMModel" DROP CONSTRAINT "LLMModel_lastModUserId_fkey";

-- DropForeignKey
ALTER TABLE "LLMModel" DROP CONSTRAINT "LLMModel_llmEngineId_fkey";

-- DropForeignKey
ALTER TABLE "LogFileHistory" DROP CONSTRAINT "LogFileHistory_requesterId_fkey";

-- DropForeignKey
ALTER TABLE "Membership" DROP CONSTRAINT "Membership_groupId_fkey";

-- DropForeignKey
ALTER TABLE "Membership" DROP CONSTRAINT "Membership_userId_fkey";

-- DropForeignKey
ALTER TABLE "Membership" DROP CONSTRAINT "fk_member_role";

-- DropForeignKey
ALTER TABLE "MessageTemplate" DROP CONSTRAINT "MessageTemplate_groupId_fkey";

-- DropForeignKey
ALTER TABLE "ModelFile" DROP CONSTRAINT "ModelFile_groupId_fkey";

-- DropForeignKey
ALTER TABLE "ModelFile" DROP CONSTRAINT "ModelFile_modelId_fkey";

-- DropForeignKey
ALTER TABLE "ModelFile" DROP CONSTRAINT "ModelFile_uploaderId_fkey";

-- DropForeignKey
ALTER TABLE "Role" DROP CONSTRAINT "Role_groupId_fkey";

-- DropForeignKey
ALTER TABLE "RolePermission" DROP CONSTRAINT "RolePermission_permissionId_fkey";

-- DropForeignKey
ALTER TABLE "RolePermission" DROP CONSTRAINT "RolePermission_roleId_fkey";

-- DropForeignKey
ALTER TABLE "Session" DROP CONSTRAINT "Session_userId_fkey";

-- DropForeignKey
ALTER TABLE "User" DROP CONSTRAINT "User_prefersEmailId_fkey";

-- DropForeignKey
ALTER TABLE "UserAccessOnLLMModels" DROP CONSTRAINT "UserAccessOnLLMModels_modelId_fkey";

-- DropForeignKey
ALTER TABLE "UserAccessOnLLMModels" DROP CONSTRAINT "UserAccessOnLLMModels_userId_fkey";

-- DropForeignKey
ALTER TABLE "UserPermission" DROP CONSTRAINT "UserPermission_groupId_fkey";

-- DropForeignKey
ALTER TABLE "UserPermission" DROP CONSTRAINT "UserPermission_permissionId_fkey";

-- DropForeignKey
ALTER TABLE "UserPermission" DROP CONSTRAINT "UserPermission_userId_fkey";

-- DropForeignKey
ALTER TABLE "Webhook" DROP CONSTRAINT "Webhook_groupId_fkey";
