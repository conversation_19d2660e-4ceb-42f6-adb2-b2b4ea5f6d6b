/*
  Warnings:

  - A unique constraint covering the columns `[permissionKey]` on the table `Permission` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[name]` on the table `Role` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[roleId,permissionId]` on the table `RolePermission` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[userId,groupId,permissionId]` on the table `UserPermission` will be added. If there are existing duplicate values, this will fail.
  - Made the column `permissionKey` on table `Permission` required. This step will fail if there are existing NULL values in that column.
  - Made the column `name` on table `Role` required. This step will fail if there are existing NULL values in that column.
  - Made the column `isGlobal` on table `Role` required. This step will fail if there are existing NULL values in that column.
  - Made the column `roleId` on table `RolePermission` required. This step will fail if there are existing NULL values in that column.
  - Made the column `permissionId` on table `RolePermission` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `userId` to the `UserPermission` table without a default value. This is not possible if the table is not empty.
  - Made the column `groupId` on table `UserPermission` required. This step will fail if there are existing NULL values in that column.
  - Made the column `permissionId` on table `UserPermission` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "Permission" ALTER COLUMN "permissionKey" SET NOT NULL;

-- AlterTable
ALTER TABLE "Role" ALTER COLUMN "name" SET NOT NULL,
ALTER COLUMN "isGlobal" SET NOT NULL;

-- AlterTable
ALTER TABLE "RolePermission" ALTER COLUMN "roleId" SET NOT NULL,
ALTER COLUMN "permissionId" SET NOT NULL;

-- AlterTable
ALTER TABLE "UserPermission" ADD COLUMN     "userId" INTEGER NOT NULL,
ALTER COLUMN "groupId" SET NOT NULL,
ALTER COLUMN "permissionId" SET NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "Permission_permissionKey_key" ON "Permission"("permissionKey");

-- CreateIndex
CREATE UNIQUE INDEX "Role_name_key" ON "Role"("name");

-- CreateIndex
CREATE UNIQUE INDEX "RolePermission_roleId_permissionId_key" ON "RolePermission"("roleId", "permissionId");

-- CreateIndex
CREATE UNIQUE INDEX "UserPermission_userId_groupId_permissionId_key" ON "UserPermission"("userId", "groupId", "permissionId");

-- AddForeignKey
ALTER TABLE "UserPermission" ADD CONSTRAINT "UserPermission_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
