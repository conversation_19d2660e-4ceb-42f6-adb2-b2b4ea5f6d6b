import { IsBoolean, IsNotEmpty, <PERSON><PERSON><PERSON>ber, IsOptional, IsString } from 'class-validator';

export class CreateLlmModelDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  modelId: string;

  @IsOptional()
  @IsString()
  tone: string;

  @IsString()
  @IsOptional()
  startupMessage: string;

  @IsNumber()
  llmEngineId: number;

  @IsNumber()
  groupId: number;

  @IsString()
  modelEngine: string;

  @IsBoolean()
  active: boolean;
}
