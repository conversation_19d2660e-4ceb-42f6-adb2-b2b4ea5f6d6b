default:
  tags:
  - gbot

include:
- project: 'devops/internal/gitlab-ci-templates'
  ref: master
  file: '/standard-ci-docker-1.8.3.yml'


variables:
  MY_GLOBAL_KEY_0: baz

.template_rules_if_variables: &template_rules_if_variables
  rules:
  - if: '$CI_COMMIT_TAG =~ /v\d\d*\.\d\d*\.\d\d*/'
    variables:
      MY_RULES_0_KEY_0: foo
  - if: '$CI_COMMIT_TAG =~ /d\d\d*\.\d\d*\.\d\d*/'
    variables:
      MY_RULES_1_KEY_0: bar


job_dev_build:
  stage: dev_build
  <<: *template_rules_if_variables
  script:
  - echo "build stage defined by dev"

job_dev_test:
  stage: dev_test
  <<: *template_rules_if_variables
  script:
  - echo "test stage defined by dev"

