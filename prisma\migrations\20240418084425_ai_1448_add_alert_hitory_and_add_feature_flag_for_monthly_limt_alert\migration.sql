-- Create<PERSON>num
CREATE TYPE "AlertHistoryChannel" AS ENUM ('EMAIL');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "AlertHistoryEntityType" AS ENUM ('GROUP');

-- CreateEnum
CREATE TYPE "AlertHistoryStatus" AS ENUM ('PENDING', 'COMPLETED', 'FAILED');

-- CreateTable
CREATE TABLE "AlertHistory" (
    "id" SERIAL NOT NULL,
    "entityType" "AlertHistoryEntityType" NOT NULL,
    "entityId" INTEGER NOT NULL,
    "sender" TEXT,
    "tos" TEXT NOT NULL,
    "status" "AlertHistoryStatus" NOT NULL,
    "channel" "AlertHistoryChannel" NOT NULL,
    "alertType" TEXT NOT NULL,
    "sendTime" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "content" TEXT NOT NULL,
    "remark" TEXT,

    CONSTRAINT "AlertHistory_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "AlertHistory_alertType_sendTime_entity_status_index" ON "AlertHistory"("entityType", "entityId", "sendTime", "alertType", "status");

INSERT INTO "FeatureFlag"("key", "value", "metaData", "description", "isEnabled", "isForClientSide", "updatedAt")
SELECT 'ALERT.CONFIG_MONTHLY_TOKEN_LIMIT_SEND_ALERT_CONDITION', '', jsonb_build_object('percentage',jsonb_build_array('75','90','100'),'emailContent','Dear Bot Owner and Admin<br/><br/> 
Your {groupName} - {env} has reached {percentage}% of the maximum token limit per month.<br/><br/>
Usage: {usedToken}/{monthlyTokenLimit} ({percentage}%)<br/><br/>
Please login to <a href="{hostUrl}"> Gen AI Platform </a> and customize your maximum monthly token limit.<br/><br/>
Thank you very much.<br/><br/>
This email was sent to you from HKT Gen AI Platform.'),
'config the monthly token limit alert condition and alert content', true, false, now() 
WHERE NOT EXISTS (
SELECT id FROM "FeatureFlag" WHERE "key" = 'ALERT.CONFIG_MONTHLY_TOKEN_LIMIT_SEND_ALERT_CONDITION'
);