import { ApiProperty } from '@nestjs/swagger';
export class botSecurityScannerResultDTO {
  @ApiProperty({
    example: 'Anonymize',
    description: 'Scanners name for check PII',
  })
  name: string;
  @ApiProperty({
    example: '1',
    description: 'risk scores in scan results, 0.0 - 1.0 (highest risk)',
  })
  risk_score: number;
  @ApiProperty({
    example: 'SOFT',
    description: 'the level corresponding to the scanner(OFF/SOFT/HARD)',
  })
  security_level: string;
}

export class ScannersResultDTO {
  @ApiProperty({
    description: 'Scanners list',
    type: [botSecurityScannerResultDTO],
  })
  scanners: botSecurityScannerResultDTO[];
  @ApiProperty({
    example:
      'My name is [REDACTED_PERSON_7] and I live in [REDACTED_LOCATION_7][REDACTED_LOCATION_8]. Generate a resume for me',
    description: 'Text optimized for PII checking',
  })
  sanitized_prompt: string;
  @ApiProperty({
    example: 'My name is <PERSON> and I live in Shatin. Generate a resume for me',
    description: 'Original text returned by GPT',
  })
  original_prompt: string;
}

export class InputScannersResultDTO {
  @ApiProperty({
    example: false,
    description: `The value is set to 'true' if the input passed all security checks, and 'false' otherwise`,
  })
  input_scanners_result_is_valid: boolean;

  input_scanners_result: ScannersResultDTO;
}
