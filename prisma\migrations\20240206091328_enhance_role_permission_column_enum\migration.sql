/*
  Warnings:

  - The values [SE<PERSON><PERSON>_MANAGEMENT] on the enum `SystemName` will be removed. If these variants are still used in the database, this will fail.
  - Made the column `createdAt` on table `PermissionGroupSetting` required. This step will fail if there are existing NULL values in that column.
  - Made the column `updatedAt` on table `PermissionGroupSetting` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdAt` on table `Role` required. This step will fail if there are existing NULL values in that column.
  - Made the column `updatedAt` on table `Role` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "SystemName_new" AS ENUM ('SUDO', 'OPERATION_TEAM', 'SECURITY_TEAM', 'ACCOUNT_MANAGEMENT', 'SENIOR_MANAGEMENT' , 'BOT_REVIEWER', 'BOT_CREATOR', 'USER', 'GROUP_OWNER', 'G<PERSON>UP_ADMIN', '<PERSON><PERSON><PERSON>_MEMBER', 'G<PERSON><PERSON>_CUSTOM');
ALTER TABLE "Role" ALTER COLUMN "systemName" DROP DEFAULT;
ALTER TABLE "Role" ALTER COLUMN "systemName" TYPE "SystemName_new" USING ("systemName"::text::"SystemName_new");
ALTER TYPE "SystemName" RENAME TO "SystemName_old";
ALTER TYPE "SystemName_new" RENAME TO "SystemName";
DROP TYPE "SystemName_old";
ALTER TABLE "Role" ALTER COLUMN "name" SET DEFAULT 'USER';
COMMIT;

UPDATE "Role" SET "systemName" = 'BOT_REVIEWER', "name" = 'BOT_REVIEWER' WHERE "systemName" =  'SENIOR_MANAGEMENT';

UPDATE "User" SET "roleId" = (SELECT id FROM "Role" WHERE "systemName" = 'BOT_REVIEWER') WHERE "roleId" = (SELECT id FROM "Role" WHERE "systemName" = 'SENIOR_MANAGEMENT');

BEGIN;
CREATE TYPE "SystemName_new" AS ENUM ('SUDO', 'OPERATION_TEAM', 'SECURITY_TEAM', 'ACCOUNT_MANAGEMENT', 'BOT_REVIEWER', 'BOT_CREATOR', 'USER', 'GROUP_OWNER', 'GROUP_ADMIN', 'GROUP_MEMBER', 'GROUP_CUSTOM');
ALTER TABLE "Role" ALTER COLUMN "systemName" DROP DEFAULT;
ALTER TABLE "Role" ALTER COLUMN "systemName" TYPE "SystemName_new" USING ("systemName"::text::"SystemName_new");
ALTER TYPE "SystemName" RENAME TO "SystemName_old";
ALTER TYPE "SystemName_new" RENAME TO "SystemName";
DROP TYPE "SystemName_old";
ALTER TABLE "Role" ALTER COLUMN "systemName" SET DEFAULT 'USER';
COMMIT;

-- AlterTable
ALTER TABLE "Permission" ALTER COLUMN "updatedAt" SET DATA TYPE TIMESTAMP(3);


Update "PermissionGroupSetting" SET "updatedAt" = NOW() WHERE "updatedAt" is null;
Update "Role" SET "updatedAt" = NOW() WHERE "updatedAt" is null;


-- AlterTable
ALTER TABLE "PermissionGroupSetting" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "updatedAt" SET NOT NULL,
ALTER COLUMN "updatedAt" SET DATA TYPE TIMESTAMP(3);

-- AlterTable
ALTER TABLE "Role" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "updatedAt" SET NOT NULL,
ALTER COLUMN "updatedAt" SET DATA TYPE TIMESTAMP(3);

-- AlterTable
ALTER TABLE "User" ALTER COLUMN "roleId" DROP DEFAULT;

ALTER TABLE "Role" ALTER COLUMN "name" DROP DEFAULT;
