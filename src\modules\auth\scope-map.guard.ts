import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JSONPath } from 'jsonpath-plus';
import minimatch from 'minimatch';
import { UserRequest } from './auth.interface';
import { scopeRequirementMapMeatadataKey } from './scope-map.decorator';
import { ScopeRequirementMap } from './scope-requirement-map.interface';

@Injectable()
export class ScopeMapGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  private readonly scopePlaceholderRegex = /\{(.*?)\}/g;

  canActivate(context: ExecutionContext): boolean {
    const scopeRequirementMap = this.reflector.get<ScopeRequirementMap>(
      scopeRequirementMapMeatadataKey,
      context.getHandler(),
    );
    if (!scopeRequirementMap) return true;
    const request = context.switchToHttp().getRequest<UserRequest>();
    const userScopes = request.user.scopes;

    const jsonPathCache = new Map<string, string>();
    for (const key in scopeRequirementMap) {
      const value = this.resolveJsonPath(jsonPathCache, key, request);
      if (!value) continue;
      // value of the key is ready to compare
      for (const scopeRequirementSetting of scopeRequirementMap[key]) {
        if (!scopeRequirementSetting.whenValues.includes(value)) continue;
        // scopes of the current setting is ready to check
        const requiredScopes =
          request.body?.entityId || request.query?.['entityId']
            ? scopeRequirementSetting.entityIdScopes
            : scopeRequirementSetting.checkScopes;
        for (const scopeWithJsonPathPlaceholder of requiredScopes) {
          if (
            this.checkScopeWithJsonPathPlaceholder(
              userScopes,
              scopeWithJsonPathPlaceholder,
              jsonPathCache,
              request,
            )
          )
            return true;
        }
      }
    }

    return false;
  }

  resolveJsonPath(jsonPathCache: Map<string, string>, path: string, json: object) {
    let value = jsonPathCache.get(path);
    if (!value) {
      value = JSONPath({ path, json, wrap: false });
      if (value) {
        jsonPathCache.set(path, value.toString());
      }
    }
    return value;
  }

  checkScopeWithJsonPathPlaceholder(
    userScopes: string[],
    scopeWithJsonPathPlaceholder: string,
    jsonPathCache: Map<string, string>,
    json: object,
  ) {
    let scope = scopeWithJsonPathPlaceholder;

    // find set of jsonPaths in the scope (if any)
    const jsonPathMatchesSet = new Set(
      [...scopeWithJsonPathPlaceholder.matchAll(this.scopePlaceholderRegex)].map(
        (match) => match[1],
      ),
    );

    // replace all placeholder in the scope
    for (const jsonPathMatch of jsonPathMatchesSet) {
      const value = this.resolveJsonPath(jsonPathCache, jsonPathMatch, json);
      if (!value) continue;
      scope = scope.replaceAll(`{${jsonPathMatch}}`, value);
    }

    return userScopes.some((userScope) => minimatch(scope, userScope));
  }
}
