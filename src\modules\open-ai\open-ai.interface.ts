import { CompletionCreateParams } from 'openai/resources/completions';
import { Usage } from 'src/providers/llm-backend/llm-backend.interface';
import { ChannelType } from '../llm-models/dto/chat-llm-model.dto';
import { Feature } from '@prisma/client';

export interface PrefilledLog {
  date: moment.Moment;
  botId: number;
  botEnv: string;
  botName: string;
  requesterId: string;
  requesterName: string;
  requesterStaffId: string;
  requesterEmail: string;
  channel: ChannelType;
  engine: string;
  engineConfig: CompletionCreateParams;
  query: string | string[] | number[] | number[][];
  chatSessionName: string;
  isChatSessionDefault: boolean;
  feature: Feature;
  durationInMS?: number;
  usage?: Usage;
  answer?: string;
  traceId?: string;
}
