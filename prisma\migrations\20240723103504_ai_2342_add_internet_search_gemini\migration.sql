-- This is an empty migration.

update "LlmEngine" set config='"{\"temperature\": {\"label\": \"Temperature\",\"min\": 0,\"max\": 1,\"default\": 0.2,\"type\": \"number\",\"input\": \"slider\",\"step\": 0.1,\"description\": \"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\": {\"label\": \"Num. of relevant docs to retrieve\",\"min\": 0,\"max\": 50,\"default\": 3,\"type\": \"number\",\"input\": \"slider\",\"step\": 1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\": {\"label\": \"Top P\",\"min\": 0,\"max\": 1,\"default\": 0.94,\"type\": \"number\",\"input\": \"slider\",\"step\": 0.01,\"description\": \"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\": {\"label\": \"Max Tokens\",\"min\": 256,\"max\": 8192,\"default\": 4000,\"type\": \"number\",\"input\": \"slider\",\"step\": 1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length.\"},\"internet_search\": {\"label\": \"Internet Search\",\"min\": 0,\"max\": 1,\"default\": 0,\"type\": \"number\",\"input\": \"slider\",\"step\": 1,\"description\": \"Internet Search capability\"}}"'::jsonb where slug in ('vertexai-gemini-1.5-pro-001', 'vertexai-gemini-1.5-flash-001');

