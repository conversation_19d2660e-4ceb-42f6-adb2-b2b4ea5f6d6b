name: Merge PRs
on:
  pull_request:
    types:
      - labeled
      - synchronize
      - opened
      - edited
      - ready_for_review
      - reopened
      - unlocked
  pull_request_review:
    types:
      - submitted
jobs:
  automerge:
    runs-on: ubuntu-latest
    steps:
      - name: Automerge
        uses: pascalgn/automerge-action@v0.13.1
        env:
          GITHUB_TOKEN: "${{ secrets.GH_PAT }}"
          MERGE_LABELS: "merge,!work in progress,!wip"
          MERGE_REMOVE_LABELS: "merge"
          MERGE_METHOD: "merge"
          MERGE_COMMIT_MESSAGE: ":twisted_rightwards_arrows: Merge #{pullRequest.number} ({pullRequest.title})"
          MERGE_FORKS: false
          UPDATE_LABELS: "merge"
          UPDATE_METHOD: "merge"
      - name: Delete merged branch
        uses: koj-co/delete-merged-action@master
        with:
          branches: "!master, !production, *"
        env:
          GITHUB_TOKEN: "${{ secrets.GH_PAT }}"
