import { Controller, Get, Logger } from '@nestjs/common';
import { Public } from '../auth/public.decorator';
import { VersionService } from './version.service';
import { ApiTags } from '@nestjs/swagger';

@Controller('version')
@Public()
@ApiTags('Vsersion')
export class VersionController {
  logger = new Logger(VersionController.name);
  constructor(private versionService: VersionService) {}

  /** Get Version Nember */
  @Get('versionNumber')
  async process(): Promise<Record<string, string>> {
    return await this.versionService.getVersionNumber();
  }
}
