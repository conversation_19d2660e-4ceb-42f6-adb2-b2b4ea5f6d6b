import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { OptionOutput, OptionType } from './options.inteface';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { Prisma } from '@prisma/client';

@Injectable()
export class OptionsService {
  private readonly logger = new Logger(OptionsService.name);

  constructor(private prisma: PrismaService) {}

  async getUserOptions(
    userOption: OptionType,
    contains?: string,
    mode?: Prisma.QueryMode,
    skip?: number,
    take?: number,
  ): Promise<OptionOutput[]> {
    try {
      let options = [];
      switch (userOption) {
        case OptionType.BU: {
          options = await this.getOptions(
            'businessUnit',
            'name',
            'name',
            contains,
            mode,
            skip,
            take,
          );
          break;
        }
        case OptionType.DEPARTMENT: {
          options = await this.getOptions('department', 'name', 'name', contains, mode, skip, take);
          break;
        }
        case OptionType.CCC: {
          options = await this.getOptions(
            'costCentreCode',
            ['code', 'description'],
            'code',
            contains,
            mode,
            skip,
            take,
            { enable: true },
          );
          break;
        }
        default: {
          throw new ApiException(ErrorCode.INVALID_USER_OPTION);
        }
      }
      return options;
    } catch (err) {
      this.logger.error(err, `Failed to get user option failed, option type - ${userOption}`);
      if (err instanceof ApiException) {
        throw err;
      }
      throw new ApiException(ErrorCode.GET_USER_OPTION_FAILED);
    }
  }

  async getGroupOptions(
    groupOption: OptionType,
    contains?: string,
    mode?: Prisma.QueryMode,
    skip?: number,
    take?: number,
  ): Promise<OptionOutput[]> {
    try {
      let options = [];
      switch (groupOption) {
        case OptionType.BU: {
          options = await this.getOptions(
            'businessUnit',
            'name',
            'name',
            contains,
            mode,
            skip,
            take,
          );
          break;
        }
        case OptionType.DEPARTMENT: {
          options = await this.getOptions('department', 'name', 'name', contains, mode, skip, take);
          break;
        }
        case OptionType.CCC: {
          options = await this.getOptions(
            'costCentreCode',
            ['code', 'description'],
            'code',
            contains,
            mode,
            skip,
            take,
            { enable: true },
          );
          break;
        }
        default: {
          throw new ApiException(ErrorCode.INVALID_GROUP_OPTION);
        }
      }
      return options;
    } catch (err) {
      this.logger.error(err, `Failed to get group option failed, option type - ${groupOption}`);
      if (err instanceof ApiException) {
        throw err;
      }
      throw new ApiException(ErrorCode.GET_GROUP_OPTION_FAILED);
    }
  }

  async getOptions(
    optionType: string,
    optionLabelKey: string | string[],
    optionValueKey: string | string[],
    contains?: string,
    mode?: Prisma.QueryMode,
    skip?: number,
    take?: number,
    extraWhere?: Record<string, any>,
  ): Promise<OptionOutput[]> {
    const where =
      typeof optionLabelKey === 'string'
        ? { [optionLabelKey]: { contains, mode } }
        : { OR: optionLabelKey.map((key) => ({ [key]: { contains, mode } })) };
    const options = await this.prisma[optionType].findMany({
      where: { ...(contains ? where : {}), ...extraWhere },
      skip,
      take,
    });

    return options.map((option) => ({
      label:
        typeof optionLabelKey === 'string'
          ? option[optionLabelKey]
          : optionLabelKey.map((key) => option[key]).join(' - '),
      value:
        typeof optionValueKey === 'string'
          ? option[optionValueKey]
          : optionValueKey.map((key) => option[key]).join(' - '),
    }));
  }
}
