-- INSERT INTO "User" ("name", "updatedAt", "id", "active") 
-- SELECT "WhiteList".email, CURRENT_TIMESTAMP, 
-- FLOOR(RANDOM()* 90000000 + 10000000), true FROM "WhiteList" LEFT JOIN 
-- "Email" ON "Email".email = "WhiteList".email WHERE "emailSafe" IS NULL;


-- INSERT INTO "Email" ("email", "emailSafe", "updatedAt", "userId") 
-- SELECT "User".name, "User".name, CURRENT_TIMESTAMP, "User".id 
-- FROM "User" LEFT JOIN "Email" ON "Email"."userId" = "User".id
-- WHERE "userId" IS NULL;