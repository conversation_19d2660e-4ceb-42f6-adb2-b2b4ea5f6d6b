import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RateLimitService } from './rate-limit.service';
import { RedisModule } from '../redis/redis.module';
import { FeatureFlagModule } from '../../modules/feature-flags/feature-flags.module';
import { PrismaModule } from '../prisma/prisma.module';
import { GroupNotificationModule } from '../../modules/group-notification/group-notification.module';
import { GroupsModule } from '../../modules/groups/groups.module';
@Module({
  imports: [
    ConfigModule,
    RedisModule,
    FeatureFlagModule,
    PrismaModule,
    GroupNotificationModule,
    forwardRef(() => GroupsModule),
  ],
  providers: [RateLimitService],
  exports: [RateLimitService],
})
export class RateLimitModule {}
