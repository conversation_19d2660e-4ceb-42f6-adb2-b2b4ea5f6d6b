-- Manual remove permissions from ME<PERSON>ER role due to incorrect fixing conflicts during merging

DELETE FROM "RolePermission"
WHERE "roleId" = (
		SELECT
			"id"
		FROM
			"Role"
		WHERE
			"name" = 'MEMBER'
			AND "groupId" = 0
			AND "systemName" = 'GROUP_MEMBER')
		AND "permissionId" = (
			SELECT
				"id" FROM "Permission"
			WHERE
				"permissionKey" = 'group-{groupId}:read-flow');

DELETE FROM "RolePermission"
WHERE "roleId" = (
		SELECT
			"id"
		FROM
			"Role"
		WHERE
			"name" = 'MEMBER'
			AND "groupId" = 0
			AND "systemName" = 'GROUP_MEMBER')
		AND "permissionId" = (
			SELECT
				"id" FROM "Permission"
			WHERE
				"permissionKey" = 'group-{groupId}:read-flow-bot-request');
