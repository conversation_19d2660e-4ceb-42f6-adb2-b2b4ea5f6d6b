import { createStream, Options as RotateOption, Generator } from 'rotating-file-stream';
const LOG_FILE_ROTATE_SUFFIX = process.env['LOG_FILE_ROTATE_SUFFIX'] ?? '.txt';

const generator: Generator = (time: number | Date, index?: number): string => {
  const logFileName = 'bot-builder-backend';
  if (index) {
    return `${logFileName}-${index}${LOG_FILE_ROTATE_SUFFIX}`;
  }
  return `${logFileName}.log`;
};

module.exports = function (option: RotateOption) {
  return createStream(generator, option);
};
