-- CreateEnum
CREATE TYPE "GroupType" AS ENUM ('BOT', 'FLOW');

-- AlterTable
ALTER TABLE "Group" ADD COLUMN     "groupType" "GroupType" NOT NULL DEFAULT 'BOT';

-- AlterTable
ALTER TABLE "Permission" ADD COLUMN     "api<PERSON>eyAllowed" BO<PERSON>EAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "PermissionGroupType" (
    "permissionId" INTEGER NOT NULL,
    "groupType" "GroupType" NOT NULL DEFAULT 'BOT',
    "createdAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(6),

    CONSTRAINT "PermissionGroupType_pkey" PRIMARY KEY ("permissionId","groupType")
);
