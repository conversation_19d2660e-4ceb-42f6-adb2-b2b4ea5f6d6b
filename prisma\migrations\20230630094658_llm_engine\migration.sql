-- CreateEnum
CREATE TYPE "LlmServiceProvider" AS ENUM ('OPENAI', 'AZURE', 'AWS', 'HUGGING_FACE', 'VERTEX_AI');

-- AlterTable
ALTER TABLE "LLMModel" ADD COLUMN     "llmEngineId" INTEGER;

-- AlterTable
ALTER TABLE "UserAccessOnLLMModels" ALTER COLUMN "updatedAt" SET DEFAULT CURRENT_TIMESTAMP;

-- CreateTable
CREATE TABLE "LlmEngine" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "slug" VARCHAR(255) NOT NULL,
    "platform" "LlmServiceProvider" DEFAULT 'AZURE',
    "env" "Environment" DEFAULT 'TEST',
    "isActive" BOOLEAN NOT NULL DEFAULT false,
    "config" JSONB DEFAULT '"{}"',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LlmEngine_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "LlmEngine_env_platform_slug_key" ON "LlmEngine"("env", "platform", "slug");

-- AddForeignKey
ALTER TABLE "LLMModel" ADD CONSTRAINT "LLMModel_llmEngineId_fkey" FOREIGN KEY ("llmEngineId") REFERENCES "LlmEngine"("id") ON DELETE SET NULL ON UPDATE CASCADE;
