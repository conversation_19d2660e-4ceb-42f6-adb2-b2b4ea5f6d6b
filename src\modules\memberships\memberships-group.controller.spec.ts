import { ModuleMocker, MockFunctionMetadata } from 'jest-mock';
import { mockDeep, DeepMockProxy } from 'jest-mock-extended';
import { Test, TestingModule } from '@nestjs/testing';
import { Membership } from '@prisma/client';
import { ExtendedMembership, MembershipsService } from './memberships.service';
import { GroupMembershipController } from './memberships-group.controller';
import { UserRequest } from '../auth/auth.interface';
const moduleMocker = new ModuleMocker(global);

describe('MembershipsService', () => {
  let membershipsService: DeepMockProxy<MembershipsService>;

  let groupMembershipController: GroupMembershipController;
  beforeEach(async () => {
    membershipsService = mockDeep<MembershipsService>();
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GroupMembershipController],
      providers: [{ provide: MembershipsService, useValue: membershipsService }],
    })
      .useMocker((token) => {
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>;
          const Mock = moduleMocker.generateFromMetadata(mockMetadata);
          return new Mock();
        }
      })
      .compile();
    groupMembershipController = module.get(GroupMembershipController);
  });

  describe('getAll', () => {
    it('should be return membership list', async () => {
      const memberships = [
        {
          id: 1,
          groupId: 1,
          userId: 1,
          roleId: 1,
        },
        {
          id: 2,
          groupId: 2,
          userId: 2,
          roleId: 2,
        },
      ] as ExtendedMembership[];
      const count = {
        count: 2,
      };
      const req = {
        user: {
          id: 1,
          scopes: 'test',
          type: 'user',
        } as unknown,
      } as UserRequest;
      const check = {
        list: [
          {
            id: 1,
            groupId: 1,
            userId: 1,
            roleId: 1,
          },
          {
            id: 2,
            groupId: 2,
            userId: 2,
            roleId: 2,
          },
        ],
        ...count,
      };
      membershipsService.getMemberships.mockResolvedValue(memberships);
      membershipsService.getMembershipsCount.mockResolvedValue(count.count);
      const res = await groupMembershipController.getAll(req, 1);
      expect(res).toEqual(check);
    });
  });

  describe('remove', () => {
    it('should be return membership', async () => {
      const rep = {
        id: 1,
        groupId: 1,
        userId: 1,
        roleId: 1,
      } as Membership;
      const req = {
        user: {
          id: 1,
          scopes: 'test',
          type: 'user',
        } as unknown,
      } as UserRequest;
      membershipsService.deleteGroupMembership.mockResolvedValue(rep);
      const res = await groupMembershipController.remove(req, 1, 1);
      expect(res).toEqual(rep);
    });
  });
});
