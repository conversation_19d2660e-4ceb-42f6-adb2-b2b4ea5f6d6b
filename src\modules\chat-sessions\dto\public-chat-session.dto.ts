import { ChatSession, Environment } from '@prisma/client';
import { ChatSettingDto } from './chat-setting.dto';
import { IsOptional, IsString, ValidateNested } from 'class-validator';
import { PIIScanners } from 'src/modules/bot-security/entities/bot-security.entity';
import { PrismaStringFilterDto } from 'src/providers/prisma/prisma.interface';
import { Type } from 'class-transformer';

export type PublicChatSessionDto = Omit<ChatSession, 'chatSetting'> & {
  chatSetting: ChatSettingDto;
  botSecurity: PIIScanners;
  env: Environment;
  active: boolean;
};

export class UpdatePublicChatSessionDto {
  @IsString()
  name: string;
}

export class ChatSessionWhereDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => PrismaStringFilterDto)
  name?: PrismaStringFilterDto;
}

export class PublicChatSessionWhereDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => ChatSessionWhereDto)
  where: ChatSessionWhereDto;
}
