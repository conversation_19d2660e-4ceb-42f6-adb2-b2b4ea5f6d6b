import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import {
  Chat<PERSON><PERSON>ory,
  ChatMessageContentType,
  ChatSession,
  ChatSessionType,
  LLMModel,
  Prisma,
  ShareChat,
} from '@prisma/client';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { ContentPoint, Usage } from 'src/providers/llm-backend/llm-backend.interface';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { HistoryMessage } from '../llm-models/dto/chat-llm-model.dto';
import { FlowResponseUsage } from './dto/response-usage.dto';
import { ChatSettingDto } from './dto/chat-setting.dto';
import { GroupsService } from '../groups/groups.service';
import { LlmEnginesService } from '../llm-engines/llm-engines.service';
import {
  FileHandleStrategy,
  UpdateDataSourceRequestDto,
} from './dto/update-data-source-request.dto';
import { ChatFilesService } from '../chat-files/chat-files.service';
import { LLMModelsService } from '../llm-models/llm-models.service';
import { UserRequest } from '../auth/auth.interface';
import { BotSecurityService } from '../bot-security/bot-security.service';
import { BotSettingDto } from './dto/bot-setting.dto';
import { BotSecurityEntity } from '../bot-security/entities/bot-security.entity';
import {
  PublicChatSessionDto,
  PublicChatSessionWhereDto,
  UpdatePublicChatSessionDto,
} from './dto/public-chat-session.dto';

@Injectable()
export class ChatSessionsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly featureFlagService: FeatureFlagService,
    private readonly groupsService: GroupsService,
    private readonly llmEnginesService: LlmEnginesService,
    private readonly chatFilesService: ChatFilesService,
    @Inject(forwardRef(() => LLMModelsService))
    private readonly llmModelsService: LLMModelsService,
    private readonly botSecurityService: BotSecurityService,
  ) {}

  private logger = new Logger(ChatFilesService.name);

  //TODO: (low-priority) add cache
  async findChatSessionOrCreateDefault(
    chatSessionId: number,
    chatSessionType: ChatSessionType,
    groupId: number,
    userId: number,
  ): Promise<ChatSession> {
    if (chatSessionId) {
      // existing chat (can be default chat)
      return this.findChatSessionOrThrow(chatSessionId, groupId, userId);
    } else if (chatSessionType) {
      // default chat
      return this.findDefaultChatSessionOrCreate(chatSessionType, groupId, userId);
    } else {
      // throw exception
      throw new ApiException(ErrorCode.CHAT_SESSIOIN_NOT_FOUND);
    }
  }

  private async findChatSessionOrThrow(
    chatSessionId: number,
    groupId: number,
    userId: number,
  ): Promise<ChatSession> {
    const chatSession = await this.prisma.chatSession.findFirst({
      where: {
        id: chatSessionId,
        groupId, // for checking ownership
        userId, // for checking ownership
      },
    });
    if (!chatSession) {
      // throw exception
      throw new ApiException(ErrorCode.CHAT_SESSIOIN_NOT_FOUND);
    }
    return chatSession;
  }

  private async findDefaultChatSessionOrCreate(
    chatSessionType: ChatSessionType,
    groupId: number,
    userId: number,
  ): Promise<ChatSession> {
    return this.prisma.$transaction(async (tx) => {
      let chatSession: ChatSession;
      chatSession = await tx.chatSession.findFirst({
        where: {
          chatSessionType,
          isDefault: true,
          groupId,
          userId,
        },
      });
      if (!chatSession) {
        // if the default chat not created, create
        chatSession = await tx.chatSession.create({
          data: {
            name: chatSessionType,
            isDefault: true,
            groupId,
            userId,
            chatSessionType,
          },
        });
      }
      return chatSession;
    });
  }

  async findChatSessionByIdAndUserId(id: number, userId: number): Promise<ChatSession> {
    return this.prisma.chatSession.findFirst({ where: { id, userId } });
  }

  async searchChatSessions(
    groupId: number,
    userId: number,
    skip?: number,
    take?: number,
    name?: Prisma.StringFilter | string,
    chatSessionType?: Prisma.EnumChatSessionTypeFilter | ChatSessionType,
    orderBy?: Prisma.ChatSessionOrderByWithRelationInput,
  ): Promise<{ chatSessions: ChatSession[]; count: number }> {
    const where: Prisma.ChatSessionWhereInput = {
      groupId,
      userId,
      name,
      chatSessionType,
    };

    return this.prisma.$transaction(async (tx) => {
      const chatSessions = await tx.chatSession.findMany({
        skip,
        take,
        where,
        include: {
          chatHistories: {
            include: {
              chatFile: true,
            },
            take: 1,
            orderBy: {
              createdAt: 'desc',
            },
          },
        },
        orderBy: orderBy ?? [{ isDefault: 'desc' }, { lastActionDate: 'desc' }],
      });

      const count = await tx.chatSession.count({ where });

      return { chatSessions, count };
    });
  }

  async createChatSession(
    groupId: number,
    userId: number,
    name: string,
    chatSessionType: ChatSessionType,
    chatSetting: ChatSettingDto,
    messageTemplateId?: number,
  ): Promise<ChatSession> {
    const _chatSetting = await this.filterValidateLLMParam(groupId, chatSetting);
    const chatSession = this.prisma.$transaction(async (tx) => {
      // only allow users to create non default chat session
      const chatSession = await tx.chatSession.create({
        data: {
          name,
          groupId,
          userId,
          chatSessionType,
          chatSetting: _chatSetting as any,
        },
      });

      if (messageTemplateId) {
        await tx.messageTemplate.update({
          where: { id: messageTemplateId },
          data: { used: { increment: 1 } },
        });
      }
      return chatSession;
    });
    return chatSession;
  }

  async updateChatSession(
    groupId: number,
    userId: number,
    id: number,
    name: string,
    chatSetting: ChatSettingDto,
    messageTemplateId?: number,
  ): Promise<ChatSession> {
    // find chat session to validate the ownership
    const chatSession = await this.prisma.chatSession.findUnique({
      where: { id },
    });
    const _chatSetting: any = await this.filterValidateLLMParam(groupId, chatSetting);
    if (!chatSession || chatSession.groupId !== groupId || chatSession.userId !== userId) {
      throw new ApiException(ErrorCode.CHAT_SESSIOIN_NOT_FOUND);
    }
    if ((chatSession.chatSetting as any)?.dataSource) {
      _chatSetting.dataSource = (chatSession.chatSetting as any).dataSource;
    }
    const updatedChatSession = await this.prisma.$transaction(async (tx) => {
      const chatSession = await tx.chatSession.update({
        where: {
          id,
        },
        data: {
          name,
          chatSetting: _chatSetting,
        },
      });
      if (messageTemplateId) {
        await tx.messageTemplate.update({
          where: { id: messageTemplateId },
          data: { used: { increment: 1 } },
        });
      }
      return chatSession;
    });
    return updatedChatSession;
  }

  private async filterValidateLLMParam(groupId: number, chatSetting: ChatSettingDto) {
    const _chatSetting = { ...chatSetting };
    if (!chatSetting.llmParams || Object.keys(chatSetting.llmParams).length === 0) {
      return _chatSetting;
    }
    const group = await this.groupsService.getGroup(groupId, {});
    const llmEngine = await this.llmEnginesService.findLlmEngine(
      chatSetting.botSetting.modelEngine,
    );
    const allowedLLMParams = Object.keys(JSON.parse(llmEngine.config.toString()));
    const allowedLLMParamsObj = JSON.parse(llmEngine.config.toString());
    const llmParamsChildrenKey = allowedLLMParams
      .filter(
        (key) =>
          allowedLLMParamsObj[key]?.['values'] &&
          allowedLLMParamsObj[key]?.['values'] instanceof Array &&
          allowedLLMParamsObj[key]?.['values']?.[0] &&
          allowedLLMParamsObj[key]?.['values']?.[0]['children'] &&
          allowedLLMParamsObj[key]?.['values']?.[0]['children'] instanceof Array,
      )
      .map((item) => {
        return (allowedLLMParamsObj[item]?.['values'] as Array<any>).map((values) => {
          return (values['children'] as Array<any>).map(
            (childrenObj) => Object.keys(childrenObj)[0],
          );
        });
      });
    allowedLLMParams.push(...llmParamsChildrenKey.join(',').split(','));
    const _inputLLMParam = Object.keys(chatSetting.llmParams)
      .filter((key) => allowedLLMParams.includes(key))
      .reduce((llmparams, currentKey) => {
        llmparams[currentKey] = _chatSetting.llmParams[currentKey];
        return llmparams;
      }, {});
    _chatSetting.llmParams = _inputLLMParam;
    return _chatSetting;
  }

  async deleteChatSession(
    groupId: number,
    userId: number,
    chatSessionId: number,
  ): Promise<ChatSession> {
    return this.prisma.$transaction(async (tx) => {
      const chatSession = await tx.chatSession.findFirst({
        where: {
          id: chatSessionId,
          groupId, // for checking ownership
          userId, // for checking ownership
        },
      });

      if (!chatSession) {
        throw new ApiException(ErrorCode.CHAT_SESSIOIN_NOT_FOUND);
      }

      return await tx.chatSession.delete({
        where: { id: chatSessionId },
      });
    });
  }

  async searchChatHistory(
    groupId: number,
    userId: number,
    chatSessionId: number,
    chatSessionType: ChatSessionType,
    skip?: number,
    take?: number,
  ): Promise<{ chatHistories: ChatHistory[]; count: number }> {
    const chatSession = await this.findChatSessionOrCreateDefault(
      chatSessionId,
      chatSessionType,
      groupId,
      userId,
    );

    const where: Prisma.ChatHistoryWhereInput = { chatSessionId: chatSession.id };

    return this.prisma.$transaction(async (tx) => {
      const chatHistories = (
        await tx.chatHistory.findMany({
          include: { chatFile: true },
          skip,
          take,
          where,
          orderBy: {
            createdAt: 'desc',
          },
        })
      ).reverse(); // make the latest message the last element

      const count = await tx.chatHistory.count({ where });

      return { chatHistories, count };
    });
  }

  async createChatHistory(
    chatSessionId: number,
    contentType: ChatMessageContentType,
    message?: HistoryMessage,
    responseUsage?: Usage | FlowResponseUsage,
    contentPoints?: ContentPoint[],
    chatwithdata?: Record<any, any>,
    chatFileId?: number,
    file_expired_at?: Date,
    showReference?: boolean,
  ): Promise<ChatHistory> {
    return this.prisma.$transaction(async (tx) => {
      // checking before creating a CONTEXT_BREAK
      if (contentType === 'CONTEXT_BREAK') {
        const lastChatHistory = await tx.chatHistory.findFirst({
          where: {
            chatSessionId: chatSessionId,
          },
          orderBy: {
            createdAt: 'desc',
          },
        });
        if (!lastChatHistory || lastChatHistory.contentType === 'CONTEXT_BREAK') {
          // if no chat history or last history is a CONTEXT_BREAK, throw exception
          throw new ApiException(ErrorCode.CHAT_HISTORY_NO_CONTEXT_TO_CLEAR);
        }
      }

      // create history
      const createdChatHistory = await tx.chatHistory.create({
        data: {
          chatSessionId: chatSessionId,
          message: message ? JSON.parse(JSON.stringify(message)) : undefined, // parse to json object
          contentType,
          responseUsage: responseUsage ? JSON.parse(JSON.stringify(responseUsage)) : undefined,
          chatwithdata: chatwithdata ? JSON.parse(JSON.stringify(chatwithdata)) : undefined,
          contentPoints: contentPoints ? JSON.parse(JSON.stringify(contentPoints)) : undefined,
          chatFileId,
          file_expired_at,
          showReference,
        },
      });

      // update last chat date of the chat session
      await tx.chatSession.update({
        where: {
          id: chatSessionId,
        },
        data: {
          lastActionDate: createdChatHistory.createdAt,
        },
      });

      // housekeep: delete histories > threshold
      const maxHistroyThresholdFeatureFlag = await this.featureFlagService.getOne(
        FeatureFlagKey.BOT_MAX_CHAT_HISTORY_PER_CHAT_SESSION,
      );
      if (maxHistroyThresholdFeatureFlag) {
        const maxHistroyThreshold = parseInt(maxHistroyThresholdFeatureFlag.metaData['value']);
        const historyCount = await tx.chatHistory.count({
          where: { chatSessionId },
        });

        if (historyCount > maxHistroyThreshold) {
          // find createdAt of the first expired history for deleting older histories
          const firstExpiredChatHistory = await tx.chatHistory.findFirst({
            select: { createdAt: true },
            skip: maxHistroyThreshold,
            where: { chatSessionId },
            orderBy: {
              createdAt: 'desc',
            },
          });
          await tx.chatHistory.deleteMany({
            where: {
              chatSessionId,
              createdAt: {
                lte: firstExpiredChatHistory.createdAt,
              },
            },
          });
        }
      }

      return createdChatHistory;
    });
  }

  async updateChatHistory(
    req: UserRequest,
    chatHistoryId: number,
    rating: number,
    comment: string[],
  ): Promise<ChatHistory> {
    const chatHistories = await this.prisma.chatHistory.findFirst({
      where: {
        id: chatHistoryId,
        chatSession: {
          userId: req.user.id,
        },
      },
    });
    if (!chatHistories) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    return await this.prisma.chatHistory.update({
      where: {
        id: chatHistoryId,
        chatSession: {
          userId: req.user.id,
        },
      },
      data: {
        rating,
        comment,
      },
    });
  }

  async findChatSessionOrDefault(
    groupId: number,
    userId: number,
    chatSessionId?: number,
    chatSessionType?: ChatSessionType,
  ): Promise<ChatSession> {
    if (chatSessionId) {
      // existing chat (can be default chat)
      return await this.findChatSessionOrThrow(chatSessionId, groupId, userId);
    } else if (chatSessionType) {
      // default chat
      return await this.prisma.chatSession.findFirst({
        where: {
          chatSessionType,
          isDefault: true,
          groupId,
          userId,
        },
      });
    } else {
      // throw exception
      throw new ApiException(ErrorCode.CHAT_SESSIOIN_NOT_FOUND);
    }
  }

  async updateDataSource(
    groupId: number,
    chatSession: ChatSession,
    userId: number,
    dto: UpdateDataSourceRequestDto,
  ) {
    this.logger.log(`updateDataSource: ${JSON.stringify(dto)}`);
    const group = await this.groupsService.getGroup(groupId, {});
    let movedFile;
    let modelFilesForEmbedding: string[] = [];
    await this.prisma.$transaction(
      async (tx) => {
        if (
          dto.fileHandleStrategy === FileHandleStrategy.Embedding &&
          dto.chatFile?.files?.length > 0
        ) {
          // move chat files to model files
          movedFile = await this.chatFilesService.copyChatFilesToModelFiles(
            tx,
            group,
            userId,
            dto.chatFile.files,
          );
          if (!dto.modelFile) {
            const chatSetting: any = chatSession.chatSetting || {};
            dto.modelFile = chatSetting.dataSource?.modelFile || { enable: true };
          }
          dto.modelFile.files = [
            ...(dto.modelFile.files || []),
            ...movedFile.map((file) => ({ file_id: file.new_file_id, range: file.range })),
          ];
          dto.chatFile.files = [];
        }

        if (
          dto.fileHandleStrategy === FileHandleStrategy.Embedding &&
          dto.modelFile?.files?.length > 0
        ) {
          modelFilesForEmbedding = dto.modelFile.files.map((file) => file.file_id);
        }

        if (chatSession.isDefault) {
          await this.updateBotLevelDataSource(tx, groupId, dto);
        }
        const aiSearchMaxSupportChatWithFileNum = await this.featureFlagService.getOne(
          FeatureFlagKey.DATA_SOURCE_AI_SEARCH_SUPPORT_CHAT_WITH_FILE_MAX_NUM,
        );
        const modelFileChatWihtFiles = dto.modelFile.files.filter(
          (item) => item.chatWithFile ?? false,
        );
        if (
          (aiSearchMaxSupportChatWithFileNum?.isEnabled ?? true) &&
          (aiSearchMaxSupportChatWithFileNum?.metaData?.['value'] ?? 3) <
            modelFileChatWihtFiles.length
        ) {
          throw new ApiException(
            ErrorCode.EMBEDDING_CHAT_FILE_NUMBER_OVER_LIMIT.replace(
              '{limit}',
              aiSearchMaxSupportChatWithFileNum?.metaData?.['value'] ?? 3,
            ),
          );
        }
        await this.updateChatSessionDataSource(tx, chatSession, dto);

        // update the related chat session from chatFile to modelFile
        if (movedFile) {
          await this.updateRelatedChatSessionDataSource(
            tx,
            groupId,
            userId,
            chatSession.id,
            movedFile,
          );
        }
      },
      {
        timeout: 10000,
      },
    );
    // process the selected model files
    if (modelFilesForEmbedding?.length > 0) {
      for (const fileId of modelFilesForEmbedding) {
        this.logger.log(`process model file [${fileId}]`);
        this.llmModelsService.processModelFile(group.env, fileId).catch((e) => {
          this.logger.error(e);
        });
      }
    }
    return chatSession;
  }

  async updateChatSessionDataSource(
    tx: Prisma.TransactionClient,
    chatSession: ChatSession,
    dto: UpdateDataSourceRequestDto,
  ) {
    if (chatSession.isDefault && dto.modelFile) {
      delete dto.modelFile.files;
    }
    const chatSetting: any = chatSession.chatSetting || {};
    const dataSource = chatSetting.dataSource || {};
    dataSource.fileHandleStrategy = dto.fileHandleStrategy;
    if (dto.chatFile) dataSource.chatFile = dto.chatFile;
    if (!chatSession.isDefault) {
      if (dto.genKb) dataSource.genKb = dto.genKb;
      if (dto.modelFile) {
        dataSource.modelFile = dto.modelFile;
      }
      if (dto.top !== null && dto.top !== undefined) {
        const llmParams = chatSetting.llmParams || {};
        llmParams.top = dto.top;
        chatSetting.llmParams = llmParams;
      }
    }
    //update chat session level dataSource
    chatSetting.dataSource = dataSource;
    chatSession = await tx.chatSession.update({
      where: {
        id: chatSession.id,
      },
      data: {
        chatSetting: chatSetting,
      },
    });
  }

  private async updateBotLevelDataSource(
    tx: Prisma.TransactionClient,
    groupId: number,
    dto: UpdateDataSourceRequestDto,
  ) {
    // update bot level dataSource
    const model = await tx.lLMModel.findFirst({
      where: {
        groupId: groupId,
      },
    });
    if (!model) {
      throw new ApiException(ErrorCode.LLM_MODEL_CONFIG_NOT_FOUND);
    }
    const parameters: any = model.parameters || {};
    if (dto.modelFile) {
      const botDataSource = parameters.dataSource || {};
      botDataSource.modelFile = dto.modelFile;
      botDataSource.fileHandleStrategy = dto.fileHandleStrategy;
      parameters.dataSource = botDataSource;
    }
    if (dto.top !== null && dto.top !== undefined) {
      const llmParams = parameters.llmParams || {};
      llmParams.top = dto.top;
      parameters.llmParams = llmParams;
    }
    if (dto.genKb) {
      const botDataSource = parameters.dataSource || {};
      botDataSource.genKb = dto.genKb;
      parameters.dataSource = botDataSource;
    }
    if (dto.modelFile || (dto.top !== null && dto.top !== undefined) || dto.genKb) {
      await tx.lLMModel.update({
        where: {
          id: model.id,
        },
        data: {
          parameters: parameters,
        },
      });
    }
  }

  private async updateRelatedChatSessionDataSource(
    tx: Prisma.TransactionClient,
    groupId: number,
    userId: number,
    chatSessionId: number,
    movedFile: any[],
  ) {
    const relatedChatSessions = await tx.chatSession.findMany({
      where: {
        groupId: groupId,
        userId: userId,
        id: {
          not: chatSessionId,
        },
        chatSetting: {
          not: null,
        },
      },
    });
    for (const session of relatedChatSessions) {
      const sessionChatSetting: any = session.chatSetting || {};
      if (
        sessionChatSetting.dataSource?.chatFile?.files?.find((f) =>
          movedFile.find((mf) => mf.file_id === f.file_id),
        )
      ) {
        const sessionDataSource = sessionChatSetting.dataSource || {};
        const sessionChatFiles = sessionDataSource.chatFile?.files || [];
        const sessionModelFiles = sessionDataSource.modelFile?.files || [];

        const updatedChatFiles = [];
        for (const file of sessionChatFiles) {
          const movedFileEntry = movedFile.find((mf) => mf.file_id === file.file_id);
          if (movedFileEntry) {
            sessionModelFiles.push({
              file_id: movedFileEntry.new_file_id,
              range: file.range,
            });
          } else {
            updatedChatFiles.push(file);
          }
        }

        sessionDataSource.chatFile.files = updatedChatFiles;
        sessionDataSource.modelFile = sessionDataSource.modelFile || { enable: true };
        sessionDataSource.modelFile.files = sessionModelFiles;
        sessionChatSetting.dataSource = sessionDataSource;

        await tx.chatSession.update({
          where: { id: session.id },
          data: { chatSetting: sessionChatSetting },
        });
      }
    }
  }

  async isExChatSession(checkingData: { id: number; userId?: number; groupId?: number }) {
    const chatSession = await this.prisma.chatSession.findFirst({
      where: {
        ...checkingData,
      },
    });
    if (!chatSession) {
      throw new ApiException(ErrorCode.CHAT_SESSIOIN_NOT_FOUND);
    }
    return true;
  }

  async updatePublicChatSession(
    userId: number,
    chatSessionId: number,
    updateTdo: UpdatePublicChatSessionDto,
  ) {
    await this.isExChatSession({ id: chatSessionId, userId });
    const updated = await this.prisma.chatSession.update({
      where: {
        userId,
        id: chatSessionId,
      },
      data: {
        ...updateTdo,
      },
    });
    return updated;
  }

  async removePublicChatSession(userId: number, chatSessionId: number) {
    await this.isExChatSession({ id: chatSessionId, userId });
    const chatSession = await this.prisma.chatSession.delete({
      where: {
        id: chatSessionId,
      },
    });
    return chatSession;
  }

  async createChatSessionWithShare(shareChat: ShareChat, req: UserRequest) {
    const chatHistories = shareChat.chatHistories.map((item) => ({
      ...(item as any),
      id: undefined,
      showReference: false,
      chatSessionId: undefined,
      rating: null,
    }));
    // TODO :: this maybe need add transaction handle
    const isSystemChat =
      !shareChat.isPersonalChatShare && shareChat.shareChatSessionType !== ChatSessionType.PUBLIC;
    if (isSystemChat) {
      const chatSession = await this.findDefaultChatSessionOrCreate(
        shareChat.shareChatSessionType,
        shareChat.groupId,
        req.user.id,
      );
      await this.prisma.chatHistory.createMany({
        data: chatHistories.map((item) => ({ ...item, chatSessionId: chatSession.id })),
      });
      return isSystemChat;
    }
    const createdChatSession = await this.prisma.chatSession.create({
      data: {
        name: shareChat.name,
        chatSessionType: shareChat.shareChatSessionType,
        ...(shareChat.shareChatSessionType === ChatSessionType.PUBLIC
          ? { isDefault: true }
          : ({ chatSetting: shareChat.chatSetting } as any)),
        user: { connect: { id: req.user.id } },
        group: { connect: { id: shareChat.groupId } },
        createdAt: new Date(),
        chatHistories: {
          createMany: {
            data: chatHistories,
          },
        },
      },
    });
    return createdChatSession;
  }

  async createPublicBotChatSession(groupId: number, req: UserRequest, isPreview?: boolean) {
    const group = await this.groupsService.getGroup(groupId, { include: { llmModel: true } });
    const lLMModel: LLMModel = (group as any)?.llmModel;
    if (!isPreview) {
      const isPublicBot =
        group.groupType === 'BOT' && lLMModel?.makeLiveToPublic && lLMModel?.active;
      if (!isPublicBot) {
        throw new ApiException(ErrorCode.INVAILD_BOT_ID);
      }
    } else if (group.groupType !== 'BOT') {
      throw new ApiException(ErrorCode.INVAILD_PUBLIC_GROUP_TYPE);
    }
    const createChatSession = await this.prisma.chatSession.create({
      data: {
        name: group.name,
        isDefault: true,
        chatSessionType: 'PUBLIC',
        user: { connect: { id: req.user.id } },
        group: { connect: { id: groupId } },
        createdAt: new Date(),
      },
    });
    return createChatSession;
  }
  /**
   * Retrieves a list of public chat sessions based on the provided parameters.
   *
   * @param req - The user request object containing user information.
   * @param skip - The number of records to skip (optional).
   * @param take - The number of records to take (optional).
   * @param groupId - The group ID to filter the preview chat sessions ,
   * @returns A promise that resolves to an object containing the list of public chat sessions and the total count.
   *
   * @remarks
   * if the groupId have value is means is from the preview mode .
   */
  public async getPublicChatSession(
    req: UserRequest,
    skip?: number,
    take?: number,
    groupId?: number,
    _where?: PublicChatSessionWhereDto['where'],
  ): Promise<{ list: PublicChatSessionDto[]; count: number }> {
    const where: Prisma.ChatSessionWhereInput = {
      ..._where,
      group: {
        groupType: 'BOT',
        env: groupId ? 'TEST' : 'PROD',
        llmModel: {
          active: true,
        },
      },
      groupId,
      userId: req.user.id,
      chatSessionType: 'PUBLIC',
    };
    const count = await this.prisma.chatSession.count({ where });
    const chatSessions = await this.prisma.chatSession.findMany({
      include: {
        group: {
          include: {
            llmModel: true,
          },
        },
        chatHistories: {
          include: {
            chatFile: true,
          },
          take: 1,
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
      where,
      orderBy: { lastActionDate: 'desc' },
      skip,
      take,
    });
    const groupIds = chatSessions.map((item) => item.groupId);
    const botSecuritys = await this.botSecurityService.findByGroupIds(groupIds);
    const botSecurityMap = botSecuritys.reduce(
      (maps, item) => {
        maps[item.groupId] = item;
        return maps;
      },
      {} as { [key: string]: BotSecurityEntity },
    );
    const publicChatSession = chatSessions.map((item) => {
      const {
        group,
        group: {
          llmModel,
          llmModel: { parameters = {} },
        },
      } = item;
      const botSetting: BotSettingDto = {
        tone: llmModel.tone,
        startupMessage: llmModel.startupMessage,
        modelEngine: llmModel.modelEngine,
      };
      return {
        ...item,
        env: group.env,
        chatSetting: { botSetting, ...(parameters as any) },
        botSecurity: botSecurityMap[group.id].PIIScanners,
        group: undefined,
        active: groupId ? true : llmModel.active && llmModel.makeLiveToPublic,
      } as PublicChatSessionDto;
    });
    return { list: publicChatSession, count };
  }

  async findChatHistoriesByIds(historiesIds: number[], chatSessionId: number) {
    const chatSession = await this.prisma.chatSession.findFirst({
      include: {
        chatHistories: {
          where: {
            id: {
              in: historiesIds,
            },
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
      where: {
        id: chatSessionId,
      },
    });
    if (!chatSession || chatSession.chatHistories.length != historiesIds.length) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    return chatSession;
  }
}
