import {
  Environment,
  LlmServiceProvider,
  Permission,
  PermissionType,
  Prisma,
  PrismaClient,
  RoleType,
  SystemName,
} from '@prisma/client';
import { defaultFeatureFlag } from './seedData/featureFlag.seedData';
import {
  GroupPermissionSeed,
  additionalGroupPermissionList,
  groupDefaultRoles,
  groupPermissionList,
} from './seedData/groupPermission.seedData';
import {
  getAllEngines,
  getAllLlmEnginePlatforms,
  llmEnginesData,
} from './seedData/llmEngine.seedData';
import {
  SystemPermissionSeed,
  systemDefaultRoles,
  systemPermissionList,
} from './seedData/systemPermission.seedData';
import { UserPermissionSeed, userPermissionList } from './seedData/userPermission.seedData';
import { defaultButtonConditionList } from './seedData/buttonCondition.seedData';
import {
  UserResourceCategorySeed,
  UserResourcePlanSeed,
  UserResourceSeed,
  userResourceCategories,
  userResourcePlans,
  userResourcesList,
} from './seedData/userPlan.seedData';
import {
  BotResourceCategorySeed,
  BotResourcePlanSeed,
  BotResourceSeed,
  botResourceCategories,
  botResourcePlans,
  botResourcesList,
  chatChannelType,
} from './seedData/botPlan.seedData';
import { ResourceQuotaRuleSeed, resourceQuotaRules } from './seedData/resourceQuota.seedData';
import { defaultbotSecurityList } from './seedData/botSecurity.seedData';
import { defaultI18nList } from './seedData/Internationalization.seedData';
import { v4 } from 'uuid';
import { modelPriceUnits } from './seedData/modelPriceUnit.seedData';
import {
  GroupPermissionFeature,
  groupPermissionFeatureDetails,
} from './constants/permission-feature';
import { groupNotificationSeedData } from './seedData/groupNotification.seedData';

const prisma = new PrismaClient();
interface KeyIdPair {
  key: string;
  id: number;
}

const initPermission = async (
  savedPermissions: KeyIdPair[],
  permission: GroupPermissionSeed | UserPermissionSeed | SystemPermissionSeed,
  permissionType: PermissionType,
) => {
  // insert group level permission
  const savedPermission: Permission = await prisma.permission.upsert({
    where: { permissionKey: permission.permissionKey },
    update: {},
    create: {
      description: permission.description,
      permissionKey: permission.permissionKey,
      envs: permission?.envs ?? [Environment.PROD, Environment.TEST],
      permissionType: permissionType,
    },
  });
  if (permissionType === PermissionType.GROUP && permission.groupSetting) {
    // insert permissionGroupSetting for each group type (BOT/FLOW)
    for (const groupType of permission.groupSetting.groupTypes) {
      await prisma.permissionGroupSetting.upsert({
        where: {
          permissionId_groupType: {
            permissionId: savedPermission.id,
            groupType: groupType,
          },
        },
        update: {},
        create: {
          permission: {
            connect: {
              id: savedPermission.id,
            },
          },
          groupType: groupType,
          isActiveOnly: permission.groupSetting.isActiveOnly,
          isApiKeyAllowed: permission.groupSetting.isApiKeyAllowed,
          isCustomRoleAllowed: permission.groupSetting.isCustomRoleAllowed,
          feature: {
            connect: {
              featureKey: permission.groupSetting.featureKey,
            },
          },
        },
      });
    }
  }
  savedPermissions.push({ key: savedPermission.permissionKey, id: savedPermission.id });
};

const initPermissionFeatures = async () => {
  for (const featureKey of Object.values(GroupPermissionFeature)) {
    const feature = groupPermissionFeatureDetails[featureKey];
    await prisma.permissionGroupFeature.upsert({
      where: {
        featureKey: feature.key,
      },
      update: {},
      create: {
        featureKey: feature.key,
        featureName: feature.name,
        groupTypes: feature.groupTypes,
        isBasic: feature.isBasic ?? false,
      },
    });
  }
};

const initPermissions = async () => {
  const savedPermissions: KeyIdPair[] = [];
  // seed: group level permissions
  for (const permission of [...groupPermissionList, ...additionalGroupPermissionList]) {
    // special handling llm engine permission
    if (permission.link === 'LLM_ENGINE') {
      const llmEngines = getAllEngines();
      const upsertLlmEnginePermissions = llmEngines.map(async (engine) => {
        await initPermission(
          savedPermissions,
          {
            ...permission,
            description: permission.description.replace('{{engine}}', engine.name),
            permissionKey: permission.permissionKey.replace('{{engine}}', engine.slug),
          },
          PermissionType.GROUP,
        );
      });
      await Promise.all(upsertLlmEnginePermissions);
    }
    // special handling role permission
    else if (permission.link === 'ROLE') {
      const groupRoles = groupDefaultRoles;
      const upsertRolePermissions = groupRoles.map(async (role) => {
        await initPermission(
          savedPermissions,
          {
            ...permission,
            description: permission.description.replace('{{role}}', role.name.toLowerCase()),
            permissionKey: permission.permissionKey.replace(
              '{{role}}',
              role.systemName.toLowerCase(),
            ),
            groupSetting: {
              ...permission.groupSetting,
              isCustomRoleAllowed: role.isCustomRoleTemplateAllowed,
            },
          },
          PermissionType.GROUP,
        );
      });
      await Promise.all(upsertRolePermissions);
      await initPermission(
        savedPermissions,
        {
          ...permission,
          description: permission.description.replace('{{role}}', 'custom'),
          permissionKey: permission.permissionKey.replace(
            '{{role}}',
            SystemName.GROUP_CUSTOM.toLowerCase(),
          ),
        },
        PermissionType.GROUP,
      );
    } else {
      await initPermission(savedPermissions, permission, PermissionType.GROUP);
    }
  }
  // seed: system level permissions
  for (const permission of systemPermissionList) {
    await initPermission(savedPermissions, permission, PermissionType.SYSTEM);
  }
  // seed: user level permissions
  for (const permission of userPermissionList) {
    await initPermission(savedPermissions, permission, PermissionType.USER);
  }

  return savedPermissions;
};

const initRoles = async () => {
  const savedRoles: KeyIdPair[] = [];
  // seed: group level default roles
  for (const role of groupDefaultRoles) {
    const savedRole = await prisma.role.upsert({
      where: {
        name_groupId_systemName: {
          name: role.name,
          groupId: 0,
          systemName: role.systemName,
        },
      },
      update: {},
      create: {
        name: role.name,
        systemName: role.systemName,
        roleType: RoleType.GROUP_DEFAULT,
        order: role.order,
        isCustomRoleTemplateAllowed: role?.isCustomRoleTemplateAllowed ?? false,
      },
    });
    savedRoles.push({
      key: savedRole.systemName,
      id: savedRole.id,
    });
  }
  // seed: system level roles
  for (const role of systemDefaultRoles) {
    const savedRole = await prisma.role.upsert({
      where: {
        name_groupId_systemName: {
          name: role.name,
          groupId: 0,
          systemName: role.systemName,
        },
      },
      update: {},
      create: {
        name: role.name,
        systemName: role.systemName,
        roleType: RoleType.SYSTEM_DEFAULT,
      },
    });
    savedRoles.push({
      key: savedRole.systemName,
      id: savedRole.id,
    });
  }
  return savedRoles;
};

const initRolePermissions = async (savedPermissions: KeyIdPair[], savedRoles: KeyIdPair[]) => {
  // seed: role permission relation
  for (const role of [...groupDefaultRoles, ...systemDefaultRoles]) {
    const roleId = savedRoles.find((savedRole) => savedRole.key === role.systemName).id;
    for (const permission of role.permissions) {
      const permissionId = savedPermissions.find(
        (savedPermission) => savedPermission.key === permission,
      ).id;
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId,
            permissionId,
          },
        },
        update: {},
        create: {
          permissionId,
          roleId,
        },
      });
    }
  }
};

const initLlmEngines = async () => {
  // seed: LLM engines
  for (const llmEngine of llmEnginesData) {
    await prisma.llmEngine.upsert({
      where: {
        slug: llmEngine.slug,
      },
      update: {},
      create: {
        name: llmEngine.name,
        slug: llmEngine.slug,
        sequence: llmEngine.sequence,
        reportSlug: llmEngine.reportSlug,
        platform: llmEngine.platform as LlmServiceProvider,
        isActive: llmEngine.isActive,
        config: llmEngine.config,
      },
    });
  }
};

const initResourceQuotaRule = async (rule: ResourceQuotaRuleSeed) => {
  await prisma.resourceQuotaRule.upsert({
    where: {
      ruleKey: rule.ruleKey,
    },
    update: {},
    create: {
      ruleKey: rule.ruleKey,
      description: rule.description,
      quotaType: rule.quotaType,
    },
  });
};

const initResourceQuotaRules = async () => {
  // seed: resource quotas
  for (const resourceQuota of resourceQuotaRules) {
    // special handling llm engine quota
    if (resourceQuota.link === 'LLM_ENGINE') {
      const llmEngines = getAllEngines();
      const upsertAllLlmEnginQuotas = llmEngines.map(async (engine) => {
        await initResourceQuotaRule({
          ...resourceQuota,
          ruleKey: resourceQuota.ruleKey.replace('{{engine}}', engine.slug),
          description: resourceQuota.description.replace('{{engine}}', engine.name),
        });
      });
      await Promise.all(upsertAllLlmEnginQuotas);
    } else if (resourceQuota.link === 'CHAT_CHANNEL') {
      const upsertAllChatChannelQuotas = chatChannelType.map(async (channel) => {
        await initResourceQuotaRule({
          ...resourceQuota,
          ruleKey: resourceQuota.ruleKey.replace('{{channel}}', channel.key),
          description: resourceQuota.description.replace('{{channel}}', channel.name),
        });
      });
      await Promise.all(upsertAllChatChannelQuotas);
    } else {
      await initResourceQuotaRule(resourceQuota);
    }
  }
};

const initFeatureFlags = async () => {
  // seed: feature flags
  for (const featFlag of defaultFeatureFlag) {
    await prisma.featureFlag.upsert({
      where: {
        key: featFlag.key,
      },
      update: {},
      create: {
        ...featFlag,
        updatedAt: new Date(),
      },
    });
  }
};

const initButtonCondition = async () => {
  for (const buttonConditionObj of defaultButtonConditionList) {
    await prisma.modelFilePermissionButton.upsert({
      where: {
        isApproved_status_isRequireSecondaryApproval_fileClassification_permissionKey_hasMalware:
          {
            isApproved: buttonConditionObj.isApproved,
            status: buttonConditionObj.status,
            isRequireSecondaryApproval: buttonConditionObj.isRequireSecondaryApproval,
            fileClassification: buttonConditionObj.fileClassification,
            permissionKey: buttonConditionObj.permissionKey,
            hasMalware: buttonConditionObj.hasMalware,
          },
      },
      update: {},
      create: {
        isApproved: buttonConditionObj.isApproved,
        status: buttonConditionObj.status,
        isRequireSecondaryApproval: buttonConditionObj.isRequireSecondaryApproval,
        fileClassification: buttonConditionObj.fileClassification,
        permissionKey: buttonConditionObj.permissionKey,
        buttonList: buttonConditionObj.buttonList,
        hasMalware: buttonConditionObj.hasMalware,
      },
    });
  }
};

const initResourceCategory = async (
  tx: Prisma.TransactionClient,
  createValue: BotResourceCategorySeed | UserResourceCategorySeed,
) => {
  await tx.resourceCategory.upsert({
    where: {
      key: createValue.key,
    },
    update: {},
    create: createValue,
  });
};

const initResource = async (
  tx: Prisma.TransactionClient,
  resource: BotResourceSeed | UserResourceSeed,
) => {
  await tx.resource.upsert({
    where: {
      resourceKey: resource.resourceKey,
    },
    update: {},
    create: {
      resourceEntityKey: resource.resourceEntityKey,
      resourceEntityType: resource.resourceEntityType,
      resourceKey: resource.resourceKey,
      resourceName: resource.resourceName,
      subscriberTypes: resource.subscriberTypes,
      description: resource.description,
      ...(resource.permissionKey
        ? {
            permission: {
              connect: {
                permissionKey: resource.permissionKey,
              },
            },
          }
        : {}),
      ...(resource.quotaRuleKey
        ? {
            quotaRule: {
              connect: {
                ruleKey: resource?.quotaRuleKey ?? '',
              },
            },
          }
        : {}),
      resourceCategory: {
        connect: { key: resource.resourceCategory },
      },
    },
  });
};

const initResourcePlan = async (
  tx: Prisma.TransactionClient,
  plan: BotResourcePlanSeed | UserResourcePlanSeed,
) => {
  let roles = [];
  if (plan.planRolesRequired) {
    roles = await tx.role.findMany({
      select: { id: true },
      where: { systemName: { in: plan.planRolesRequired } },
    });
  }
  const existedPlan = await tx.plan.findUnique({
    where: { planKey: plan.planKey },
    select: { id: true },
  });
  if (!existedPlan) {
    const createdPlan = await tx.plan.upsert({
      where: {
        planKey: plan.planKey,
      },
      update: {},
      create: {
        planRoleIdsRequired: roles.map((role) => role.id),
        isDefault: plan.isDefault,
        isDisabledPlan: plan.isDisabledPlan,
        groupEnv: plan?.groupEnvs?.[0] ?? null,
        planKey: plan.planKey,
        planName: plan.planName,
        resource: {
          connect: {
            resourceKey: plan.resourceKey,
          },
        },
        description: plan.description,
      },
    });
    if (!plan.isDisabledPlan) {
      if (plan.permissionKey) {
        const permission = await tx.permission.findUnique({
          select: { id: true },
          where: { permissionKey: plan.permissionKey },
        });

        await tx.planPermission.upsert({
          where: {
            planId_permissionId: { permissionId: permission.id, planId: createdPlan.id },
          },
          update: {},
          create: {
            planId: createdPlan.id,
            permissionId: permission.id,
          },
        });
      }
      if (plan.quotaRuleKey && plan.quotaValue) {
        const randomString = v4();

        const quotaRule = await tx.resourceQuotaRule.findUnique({
          select: { id: true, ruleKey: true },
          where: { ruleKey: plan.quotaRuleKey },
        });
        const createdQuotaValue = await tx.resourceQuotaValue.upsert({
          where: {
            quotaKey: `${quotaRule.ruleKey}-${randomString}`,
          },
          update: {},
          create: {
            quotaKey: `${quotaRule.ruleKey}-${randomString}`,
            value: plan.quotaValue,
            ruleId: quotaRule.id,
          },
        });
        await tx.planQuota.upsert({
          where: {
            planId_quotaValueId: {
              planId: createdPlan.id,
              quotaValueId: createdQuotaValue.id,
            },
          },
          update: {},
          create: {
            planId: createdPlan.id,
            quotaValueId: createdQuotaValue.id,
          },
        });
      }
    }
  }
};

const initUserResourcePlans = async () => {
  await prisma.$transaction(
    async (tx) => {
      for (const category of userResourceCategories) {
        await initResourceCategory(tx, category);
      }
      for (const resource of userResourcesList) {
        await initResource(tx, resource);
      }
      for (const plan of userResourcePlans) {
        await initResourcePlan(tx, plan);
      }
    },
    { timeout: 50000 },
  );
};

const initBotResourcePlans = async () => {
  await prisma.$transaction(
    async (tx) => {
      for (const category of botResourceCategories) {
        // special handling llm engine resource category
        if (category.link === 'LLM_ENGINE') {
          const llmEnginePlatforms = getAllLlmEnginePlatforms();
          const upsertAllLlmEnginePlatformCategory = llmEnginePlatforms.map(async (platform) => {
            await initResourceCategory(tx, {
              name: category.name.replace('{{platform}}', platform),
              key: category.key.replace('{{platform}}', platform.toLowerCase()),
              subscriberTypes: category.subscriberTypes,
            });
          });
          await Promise.all(upsertAllLlmEnginePlatformCategory);
        } else {
          await initResourceCategory(tx, category);
        }
      }
      for (const resource of botResourcesList) {
        // special handling llm engine resource
        if (resource.link === 'LLM_ENGINE') {
          const llmEngines = getAllEngines();
          const upsertAllLlmEngineResource = llmEngines.map(async (engine) => {
            await initResource(tx, {
              ...resource,
              resourceKey: resource.resourceKey.replace('{{engine}}', engine.slug),
              resourceEntityKey: resource.resourceEntityKey.replace('{{engine}}', engine.slug),
              resourceEntityType: resource.resourceEntityType,
              resourceName: resource.resourceName.replace('{{engine}}', engine.name),
              description: resource.description.replace('{{engine}}', engine.name),
              permissionKey: resource.permissionKey.replace('{{engine}}', engine.slug),
              quotaRuleKey: resource.quotaRuleKey.replace('{{engine}}', engine.slug),
              resourceCategory: resource.resourceCategory.replace(
                '{{platform}}',
                engine.platform.toLowerCase(),
              ),
            });
          });
          await Promise.all(upsertAllLlmEngineResource);
        } else if (resource.link === 'CHAT_CHANNEL') {
          const upsertAllChatChannelResource = chatChannelType.map(async (channel) => {
            await initResource(tx, {
              ...resource,
              resourceKey: resource.resourceKey.replace('{{channel}}', channel.key),
              resourceName: resource.resourceName.replace('{{channel}}', channel.name),
              description: resource.description.replace('{{channel}}', channel.name),
              quotaRuleKey: resource.quotaRuleKey.replace('{{channel}}', channel.key),
            });
          });
          await Promise.all(upsertAllChatChannelResource);
        } else {
          await initResource(tx, resource);
        }
      }
      for (const plan of botResourcePlans) {
        // special handling llm engine resource plan
        if (plan.link === 'LLM_ENGINE') {
          const engines = getAllEngines();
          const upsertLlmEnginePlans = engines.map(async (engine) => {
            for (const env of plan.groupEnvs) {
              await initResourcePlan(tx, {
                ...plan,
                groupEnvs: [env],
                planKey: plan.planKey.replace('{{engine}}', engine.slug) + `-${env.toLowerCase()}`,
                planName: plan.planName.replace('{{engine}}', engine.name),
                resourceKey: plan.resourceKey.replace('{{engine}}', engine.slug),
                description: plan.description.replace('{{engine}}', engine.name),
                quotaRuleKey: plan.quotaRuleKey
                  ? plan.quotaRuleKey.replace('{{engine}}', engine.slug)
                  : null,
                permissionKey: plan.permissionKey
                  ? plan.permissionKey.replace('{{engine}}', engine.slug)
                  : null,
              });
            }
          });
          await Promise.all(upsertLlmEnginePlans);
        } else if (plan.link === 'CHAT_CHANNEL') {
          const upsertAllChatChannelResourcePlans = chatChannelType.map(async (channel) => {
            for (const env of plan.groupEnvs) {
              await initResourcePlan(tx, {
                ...plan,
                groupEnvs: [env],
                planKey: plan.planKey.replace('{{channel}}', channel.key) + `-${env.toLowerCase()}`,
                planName: plan.planName.replace('{{channel}}', channel.name),
                resourceKey: plan.resourceKey.replace('{{channel}}', channel.key),
                description: plan.description.replace('{{channel}}', channel.name),
                quotaRuleKey: plan.quotaRuleKey
                  ? plan.quotaRuleKey.replace('{{channel}}', channel.key)
                  : null,
              });
            }
          });
          await Promise.all(upsertAllChatChannelResourcePlans);
        } else {
          await initResourcePlan(tx, plan);
        }
      }
    },
    { timeout: 100000 },
  );
};

const initBotSecurity = async () => {
  for (const botSecurityObj of defaultbotSecurityList) {
    await prisma.botSecurity.upsert({
      where: {
        groupId: botSecurityObj.groupId,
      },
      update: {},
      create: {
        groupId: botSecurityObj.groupId,
        securityId: botSecurityObj.securityId,
        env: botSecurityObj.env,
        inputScanners: botSecurityObj.inputScanners,
        outputScanners: botSecurityObj.outputScanners,
      },
    });
  }
};

const initI18n = async () => {
  for (const i18nObj of defaultI18nList) {
    await prisma.internationalization.upsert({
      where: {
        key_language: {
          key: i18nObj.key,
          language: i18nObj.language,
        },
      },
      update: {},
      create: {
        key: i18nObj.key,
        content: i18nObj.content,
        language: i18nObj.language,
      },
    });
  }
};

const initModelPriceUnits = async () => {
  modelPriceUnits.map(async (unit) => {
    return await prisma.modelPriceUnit.upsert({
      where: { slug: unit.slug },
      update: {},
      create: {
        ...unit,
        createdBy: 0,
        createdAt: new Date(),
        updatedBy: 0,
        updatedAt: new Date(),
      },
    });
  });
};

const initGroupNotification = async () => {
  for (const groupNotification of groupNotificationSeedData) {
    await prisma.groupNotification.upsert({
      where: {
        groupNotificationName_channel: {
          groupNotificationName: groupNotification.groupNotificationName,
          channel: groupNotification.channel,
        },
      },
      update: {},
      create: {
        ...groupNotification,
      },
    });
  }
};

//  Seed data for permissions, roles, role-permission, llmEngines, featureFlags, buttonConditions, resourcePlans and plans
const main = async () => {
  await initPermissionFeatures();
  const savedPermissions = await initPermissions();
  const savedRoles = await initRoles();
  await initRolePermissions(savedPermissions, savedRoles);
  await initLlmEngines();
  await initResourceQuotaRules();
  await initFeatureFlags();
  await initButtonCondition();
  await initUserResourcePlans();
  await initBotResourcePlans();
  await initBotSecurity();
  await initI18n();
  await initModelPriceUnits();
  await initGroupNotification();
};

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
