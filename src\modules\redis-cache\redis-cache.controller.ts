import { Controller, Delete, Param, Post, Get } from '@nestjs/common';
import { RedisCacheService } from './redis-cache.service';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { Scopes } from '../auth/scope.decorator';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@Controller('caches')
@ApiBearerAuth('bearer-auth')
@ApiTags('Redis Cache')
export class RedisCacheController {
  constructor(private redisCacheService: RedisCacheService) {}

  /** Delete Caches */
  @Delete('/:key')
  @AuditLog('delete-redis-cache')
  @Scopes('system:delete-redis-cache')
  async deleteCache(@Param('key') key: string) {
    return await this.redisCacheService.deleteCache(key);
  }

  @Delete('/batch/:keyPattern')
  @AuditLog('batch-delete-redis-cache')
  @Scopes('system:delete-redis-cache')
  async batchDeleteCache(@Param('keyPattern') keyPattern: string) {
    return await this.redisCacheService.batchDeleteCache(keyPattern);
  }
}
