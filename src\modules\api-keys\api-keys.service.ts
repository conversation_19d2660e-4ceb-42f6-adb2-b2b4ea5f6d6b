import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
// import type { Group, InputJsonValue, JsonValue, Prisma } from '@prisma/client';
import type { ApiPlan, Group, Prisma, PrismaClient } from '@prisma/client';
import { Api<PERSON>ey, Environment } from '@prisma/client';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { ElasticSearchService } from '../../providers/elasticsearch/elasticsearch.service';
import { Expose } from '../../providers/prisma/prisma.interface';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { TokensService } from '../../providers/tokens/tokens.service';
import { GraviteeService } from '../../providers/gravitee/gravitee.service';
import { CreateApiKeyDto, UpdateApiKeyDto, UpdateApiKeyPlanDto } from './api-keys.dto';
import { PromotableService } from '../change-management/interfaces/promotable-service.interface';
import { GroupsService } from '../groups/groups.service';
import { PermissionsService } from '../permissions/permissions.service';
import { isIpAddressValid } from 'src/helpers/string';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { RedisService } from 'src/providers/redis/redis.service';
import { API_KEY_KEY } from 'src/providers/redis/redis.constants';

@Injectable()
export class ApiKeysService implements PromotableService {
  logger = new Logger(ApiKeysService.name);
  private apiKeyTtl = this.configService.get<number>('caching.apiKeyTtl');

  constructor(
    private prisma: PrismaService,
    private tokensService: TokensService,
    private configService: ConfigService,
    private elasticSearchService: ElasticSearchService,
    private graviteeService: GraviteeService,
    private groupService: GroupsService,
    private permissionsService: PermissionsService,
    private featureFlagService: FeatureFlagService,
    private redisService: RedisService,
  ) {}

  async createApiKeyForGroup(
    groupId: number,
    data: Omit<Omit<Prisma.ApiKeyCreateInput, 'apiKey'>, 'group'>,
  ): Promise<ApiKey> {
    const apiKey = await this.tokensService.generateRandomString();
    data.scopes = await this.cleanScopesForGroup(groupId, data.scopes);
    if (
      !Array.isArray(data.ipRestrictions) ||
      !(await this.isIpsFormatCorrect(groupId, data.ipRestrictions))
    ) {
      throw new ApiException(ErrorCode.INVALID_IP_FORMAT);
    }
    const group = await this.prisma.group.findUnique({
      where: { id: groupId },
      select: { env: true },
    });
    const apiPlan = await this.prisma.apiPlan.findFirst({
      where: { env: group.env },
    });
    if (!apiPlan || !apiPlan.planId) throw new ApiException(ErrorCode.API_PLAN_NOT_FOUND);
    const result = await this.graviteeService.createGraviteeApplicationWithSubscription(
      `${groupId} ${data.name} ${apiKey.slice(0, 5)}`,
      data.name,
      apiKey,
      apiPlan,
    );
    let gatewayApplicationId = null;
    let gatewaySubscriptionId = null;
    const rateLimitPlanId = apiPlan.planId;
    if (result) {
      gatewayApplicationId = result.applicationId;
      gatewaySubscriptionId = result.subscriptionId;
    } else {
      throw new ApiException(ErrorCode.API_GATEWAY_SERVICE_EXCEPTION);
    }
    return this.prisma.apiKey.create({
      data: {
        ...data,
        apiKey,
        gatewayApplicationId,
        gatewaySubscriptionId,
        group: { connect: { id: groupId } },
        apiPlan: { connect: { planId: rateLimitPlanId } },
      },
      include: {
        apiPlan: true,
      },
    });
  }

  async getApiKeysForGroup(
    groupId: number,
    params: {
      skip?: number;
      take?: number;
      cursor?: Prisma.ApiKeyWhereUniqueInput;
      where?: Prisma.ApiKeyWhereInput;
      orderBy?: Prisma.SessionOrderByWithRelationInput;
      //orderBy?: Prisma.ApiKeyOrderByInput;
    },
  ): Promise<Expose<ApiKey>[]> {
    const { skip, take, cursor, where, orderBy } = params;
    try {
      const apiKey = await this.prisma.apiKey.findMany({
        skip,
        take,
        cursor,
        where: { ...where, group: { id: groupId } },
        include: { apiPlan: true },
        orderBy,
      });
      return apiKey.map((group) => this.prisma.expose<ApiKey>(group));
    } catch (error) {
      return [];
    }
  }

  async getApiKeysCountForGroup(groupId: number, where?: Prisma.ApiKeyWhereInput) {
    return this.prisma.apiKey.count({
      where: { ...where, group: { id: groupId } },
    });
  }

  async getApiKeyForGroup(groupId: number, id: number): Promise<Expose<ApiKey>> {
    const apiKey = await this.prisma.apiKey.findUnique({
      where: { id },
      include: {
        apiPlan: true,
      },
    });
    if (!apiKey) throw new ApiException(ErrorCode.API_KEY_NOT_FOUND);
    if (apiKey.groupId !== groupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    return this.prisma.expose<ApiKey>(apiKey);
  }

  async getApiKeyFromKey(key: string): Promise<Expose<ApiKey>> {
    const getValueFunction = async (): Promise<ApiKey> => {
      const apiKey = await this.prisma.apiKey.findFirst({
        where: { apiKey: key },
      });
      if (!apiKey) throw new ApiException(ErrorCode.API_KEY_NOT_FOUND);
      return apiKey;
    };
    const apiKey = await this.redisService.getOrSet<ApiKey>(
      API_KEY_KEY.replace('{API_KEY}', key),
      getValueFunction,
      this.apiKeyTtl,
    );

    return this.prisma.expose<ApiKey>(apiKey);
  }

  async getApiKeyById(id: number): Promise<Expose<ApiKey>> {
    try {
      const apiKey = await this.prisma.apiKey.findUniqueOrThrow({
        where: { id: id },
      });
      return this.prisma.expose<ApiKey>(apiKey);
    } catch (error) {
      throw new ApiException(ErrorCode.API_KEY_NOT_FOUND);
    }
  }

  async updateApiKeyForGroup(
    groupId: number,
    id: number,
    data: Prisma.ApiKeyUpdateInput,
  ): Promise<Expose<ApiKey>> {
    this.logger.debug(data);
    const testApiKey = await this.prisma.apiKey.findUnique({
      where: { id },
    });
    if (!testApiKey) throw new ApiException(ErrorCode.API_KEY_NOT_FOUND);
    if (testApiKey.groupId !== groupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    data.scopes = await this.cleanScopesForGroup(groupId, data.scopes);
    //no plan change is allowed in update API
    if (
      !Array.isArray(data.ipRestrictions) ||
      !(await this.isIpsFormatCorrect(groupId, data.ipRestrictions))
    ) {
      throw new ApiException(ErrorCode.INVALID_IP_FORMAT);
    }

    const apiPlan = await this.prisma.apiPlan.findFirst({
      where: { planId: testApiKey.rateLimitPlanId },
    });
    if (!apiPlan || !apiPlan.planId) throw new ApiException(ErrorCode.API_PLAN_NOT_FOUND);
    //no plan change is allowed in update API
    data.apiPlan = {
      connect: {
        planId: testApiKey.rateLimitPlanId,
      },
    };
    const apiKey = await this.prisma.apiKey.update({
      where: { id },
      data,
      include: {
        apiPlan: true,
      },
    });
    await this.redisService.clearCache(API_KEY_KEY.replace('{API_KEY}', testApiKey.apiKey));
    return this.prisma.expose<ApiKey>(apiKey);
  }

  async replaceApiKeyForGroup(
    groupId: number,
    id: number,
    data: Prisma.ApiKeyCreateInput,
  ): Promise<Expose<ApiKey>> {
    const testApiKey = await this.prisma.apiKey.findUnique({
      where: { id },
    });
    if (!testApiKey) throw new ApiException(ErrorCode.API_KEY_NOT_FOUND);
    if (testApiKey.groupId !== groupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    data.scopes = await this.cleanScopesForGroup(groupId, data.scopes);
    const apiKey = await this.prisma.apiKey.update({
      where: { id },
      data,
    });
    await this.redisService.clearCache(API_KEY_KEY.replace('{API_KEY}', testApiKey.apiKey));
    return this.prisma.expose<ApiKey>(apiKey);
  }

  async deleteApiKeyForGroup(groupId: number, id: number): Promise<Expose<ApiKey>> {
    const testApiKey = await this.prisma.apiKey.findUnique({
      where: { id },
    });
    if (!testApiKey) throw new ApiException(ErrorCode.API_KEY_NOT_FOUND);
    if (testApiKey.groupId !== groupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    const apiPlan = await this.prisma.apiPlan.findFirst({
      where: { planId: testApiKey.rateLimitPlanId },
    });
    if (!apiPlan || !apiPlan.planId) throw new ApiException(ErrorCode.API_PLAN_NOT_FOUND);
    if (testApiKey.gatewayApplicationId) {
      await this.graviteeService.removeGraviteeApplication(testApiKey.gatewayApplicationId);
    }
    const apiKey = await this.prisma.apiKey.delete({
      where: { id },
    });
    await this.redisService.clearCache(API_KEY_KEY.replace('{API_KEY}', testApiKey.apiKey));
    return this.prisma.expose<ApiKey>(apiKey);
  }
  async deleteApiKeyForUser(userId: number, id: number): Promise<Expose<ApiKey>> {
    const testApiKey = await this.prisma.apiKey.findUnique({
      where: { id },
    });
    if (!testApiKey) throw new ApiException(ErrorCode.API_KEY_NOT_FOUND);
    if (testApiKey.userId !== userId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    const apiPlan = await this.prisma.apiPlan.findFirst({
      where: { planId: testApiKey.rateLimitPlanId },
    });
    if (!apiPlan || !apiPlan.planId) throw new ApiException(ErrorCode.API_PLAN_NOT_FOUND);
    if (testApiKey.gatewayApplicationId) {
      await this.graviteeService.removeGraviteeApplication(testApiKey.gatewayApplicationId);
    }
    const apiKey = await this.prisma.apiKey.delete({
      where: { id },
    });
    await this.redisService.clearCache(API_KEY_KEY.replace('{API_KEY}', testApiKey.apiKey));
    return this.prisma.expose<ApiKey>(apiKey);
  }

  async getApiKeyLogsForGroup(
    groupId: number,
    id: number,
    params: {
      take?: number;
      cursor?: { id?: number };
      where?: { after?: string };
    },
  ) {
    const testApiKey = await this.prisma.apiKey.findUnique({
      where: { id },
    });
    if (!testApiKey) throw new ApiException(ErrorCode.API_KEY_NOT_FOUND);
    if (testApiKey.groupId !== groupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    return this.getApiLogsFromKey(testApiKey.apiKey, params);
  }

  private async getApiLogsFromKey(
    apiKey: string,
    params: {
      take?: number;
      cursor?: { id?: number };
      where?: { after?: string };
    },
  ): Promise<Record<string, any>[]> {
    const now = new Date();
    now.setDate(now.getDate() - this.configService.get<number>('tracking.deleteOldLogsDays'));
    const result = await this.elasticSearchService.search({
      index: this.configService.get<string>('tracking.index'),
      from: params.cursor?.id,
      body: {
        query: {
          bool: {
            must: [
              {
                match: {
                  authorization: apiKey,
                },
              },
              {
                range: {
                  date: {
                    gte: params.where?.after
                      ? new Date(new Date().getTime() - new Date(params.where?.after).getTime())
                      : now,
                  },
                },
              },
            ],
          },
        },
        sort: [
          {
            date: { order: 'desc' },
          },
        ],
        size: params.take ?? 100,
      },
    });
    try {
      return result.body['hits'].hits.map(
        (item: {
          _index: string;
          _type: '_doc';
          _id: string;
          _score: any;
          _source: Record<string, any>;
        }) => ({ ...item._source, id: item._id }),
      );
    } catch (error) {}
    return [];
  }

  // Clean invalid scopes which doesn't belong to owner
  async cleanScopesForGroup(groupId: number, requiredScopes: any): Promise<any[]> {
    if (!Array.isArray(requiredScopes)) return [];
    const groupType = await this.groupService.getGroupTypeByGroupId(groupId);
    const permissions = await this.permissionsService.getOwnerPermissionsByGroupType(groupType);
    const mappedPermissions = permissions.map((p) =>
      p.permissionKey.replace('{groupId}', groupId.toString()),
    );
    return (requiredScopes as string[]).filter((requiredScope) =>
      mappedPermissions.includes(requiredScope),
    );
  }

  async getSubnetMaskForGroup(groupId: number) {
    const apiKeyWhiteListConfig = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      groupId,
      FeatureFlagKey.API_KEY_WHITELIST_CONFIG,
    );
    const minSubnetMask = (apiKeyWhiteListConfig?.metaData?.['subnetMask'] ?? 0) as number;
    return { subnetMask: minSubnetMask };
  }

  async getApiKeyScopesForGroup(groupId: number): Promise<Record<string, string>> {
    const groupType = await this.groupService.getGroupTypeByGroupId(groupId);
    const permissions = await this.permissionsService.getOwnerPermissionsByGroupType(
      groupType,
      true,
    );

    const scopes: Record<string, string> = {};

    permissions.forEach(
      (permission) =>
        (scopes[permission.permissionKey.replace('{groupId}', groupId.toString())] =
          permission.description),
    );
    return scopes;
  }

  async getGroupFromApiKey(key: string): Promise<Group> {
    const apiKey = await this.prisma.apiKey.findFirst({
      where: {
        apiKey: key,
      },
      include: {
        group: true,
      },
    });
    if (!apiKey) throw new ApiException(ErrorCode.API_KEY_NOT_FOUND);
    if (!apiKey.group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    return apiKey.group;
  }

  async getApiPlans(): Promise<Expose<ApiPlan>[]> {
    const apiPlan = await this.prisma.apiPlan.findMany();
    return apiPlan.map((item) => this.prisma.expose<ApiPlan>(item));
  }

  /**
   * @deprecated depreacted promotion function.
   * It only handle create case but not update & delete
   */
  async updatePlanForApiKeyForGroup(
    groupId: number,
    id: number,
    data: UpdateApiKeyPlanDto,
  ): Promise<Expose<ApiKey>> {
    this.logger.log('data: ' + JSON.stringify(data));
    const newApiKey = await this.tokensService.generateRandomString();
    const testApiKey = await this.prisma.apiKey.findUnique({
      where: { id },
    });
    if (!testApiKey) throw new ApiException(ErrorCode.API_KEY_NOT_FOUND);
    if (testApiKey.groupId !== groupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    if (testApiKey.promoted) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    let whitelistIps = [];
    if (
      Array.isArray(testApiKey.ipRestrictions) &&
      Array.isArray(testApiKey.referrerRestrictions)
    ) {
      whitelistIps = testApiKey.ipRestrictions.concat(testApiKey.referrerRestrictions);
    }

    const newApiPlan = await this.prisma.apiPlan.findFirst({
      where: { planId: data.rateLimitPlanId },
    });
    const oldApiPlan = await this.prisma.apiPlan.findFirst({
      where: { planId: testApiKey.rateLimitPlanId },
    });
    if (!newApiPlan || !newApiPlan.planId) throw new ApiException(ErrorCode.API_PLAN_NOT_FOUND);
    if (newApiPlan.planId === oldApiPlan?.planId) {
      // no change
      this.logger.log('No API plan change in the request');
      return this.prisma.expose<ApiKey>(testApiKey);
    }
    const currentGroup = await this.prisma.group.findFirst({
      where: { id: groupId },
    });
    const groupToPromote = await this.prisma.group.findFirst({
      where: { pairId: currentGroup.pairId, env: newApiPlan.env },
    });
    this.logger.debug('Promote: ' + JSON.stringify(groupToPromote));
    const result = await this.graviteeService.createGraviteeApplicationWithSubscription(
      `${groupToPromote.id} ${testApiKey.name} ${newApiKey.slice(0, 5)}`,
      testApiKey.name,
      newApiKey,
      newApiPlan,
    );
    let gatewayApplicationId = null;
    let gatewaySubscriptionId = null;
    const rateLimitPlanId = newApiPlan.planId;
    if (result) {
      gatewayApplicationId = result.applicationId;
      gatewaySubscriptionId = result.subscriptionId;
    } else {
      throw new ApiException(ErrorCode.API_GATEWAY_SERVICE_EXCEPTION);
    }
    const newKey = await this.prisma.apiKey.create({
      data: {
        ipRestrictions: testApiKey.ipRestrictions,
        name: testApiKey.name,
        referrerRestrictions: testApiKey.referrerRestrictions,
        scopes: testApiKey.scopes,
        apiKey: newApiKey,
        gatewayApplicationId,
        gatewaySubscriptionId,
        promoted: newApiPlan.precedence === 1,
        group: { connect: { id: groupToPromote.id } },
        apiPlan: { connect: { planId: rateLimitPlanId } },
      },
      include: {
        apiPlan: true,
      },
    });
    if (!newKey.apiKey) throw new ApiException(ErrorCode.FAIL_TO_CREATE_API_KEY);
    const currentApiKey = await this.prisma.apiKey.update({
      where: { id },
      data: {
        promoted: true,
      },
      include: {
        apiPlan: true,
      },
    });
    await this.redisService.clearCache(API_KEY_KEY.replace('{API_KEY}', testApiKey.apiKey));
    return this.prisma.expose<ApiKey>(currentApiKey);
    /*const result = await this.graviteeService.changeGraviteeSubscriptionPlan(
        testApiKey.gatewayApplicationId,
        testApiKey,
        oldApiPlan,
        newApiPlan,
        `${groupId}`,
        ipWhiteList,
        newApiKey)
    let gatewayApplicationId = null;
    let gatewaySubscriptionId = null;
    if(result){
      gatewayApplicationId = result.applicationId;
      gatewaySubscriptionId = result.subscriptionId;
    }else{
      throw new ApiException(ErrorCode.API_GATEWAY_SERVICE_EXCEPTION)
    }
    const apiKey = await this.prisma.apiKey.update({
      where: { id },
      data:{
        ...data,
        apiKey: newApiKey,
        gatewayApplicationId,
        gatewaySubscriptionId
      },
      include: {
        apiPlan: true
      }
    });
    this.lru.delete(testApiKey.apiKey);
    return this.prisma.expose<ApiKey>(apiKey);*/
  }

  async syncApiKeyToGraviteeForGroup(groupId: number, id: number): Promise<Expose<ApiKey>> {
    const testApiKey = await this.prisma.apiKey.findUnique({
      where: { id },
    });
    if (!testApiKey) throw new ApiException(ErrorCode.API_KEY_NOT_FOUND);
    if (testApiKey.groupId !== groupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    if (testApiKey.gatewayApplicationId !== null)
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    testApiKey.scopes = await this.cleanScopesForGroup(groupId, testApiKey.scopes);

    let whitelistIps = [];
    if (
      Array.isArray(testApiKey.ipRestrictions) &&
      Array.isArray(testApiKey.referrerRestrictions)
    ) {
      whitelistIps = testApiKey.ipRestrictions.concat(testApiKey.referrerRestrictions);
    }

    const apiPlan = await this.prisma.apiPlan.findFirst({
      where: { env: Environment.TEST },
    });
    if (!apiPlan || !apiPlan.planId) throw new ApiException(ErrorCode.API_PLAN_NOT_FOUND);
    const result = await this.graviteeService.createGraviteeApplicationWithSubscription(
      `${groupId} ${testApiKey.name} ${testApiKey.apiKey.slice(0, 5)}`,
      testApiKey.name,
      testApiKey.apiKey,
      apiPlan,
    );
    let gatewayApplicationId = null;
    let gatewaySubscriptionId = null;
    const rateLimitPlanId = apiPlan.planId;
    if (result) {
      gatewayApplicationId = result.applicationId;
      gatewaySubscriptionId = result.subscriptionId;
    } else {
      throw new ApiException(ErrorCode.API_GATEWAY_SERVICE_EXCEPTION);
    }

    const apiKey = await this.prisma.apiKey.update({
      where: { id },
      data: {
        gatewayApplicationId,
        gatewaySubscriptionId,
        apiPlan: { connect: { planId: rateLimitPlanId } },
      },
      include: {
        apiPlan: true,
      },
    });
    await this.redisService.clearCache(API_KEY_KEY.replace('{API_KEY}', testApiKey.apiKey));
    return this.prisma.expose<ApiKey>(apiKey);
  }

  private fillCommonPromotionData(
    data: CreateApiKeyDto | UpdateApiKeyDto,
    targetGroup: Group,
    entityData: ApiKey,
  ) {
    data.name = entityData.name;
    data.description = entityData.description;
    data.scopes = (entityData.scopes as string[]).map((scopeWithSrcGroupId) =>
      scopeWithSrcGroupId.replace(`group-${entityData.groupId}:`, `group-${targetGroup.id}:`),
    );
    data.ipRestrictions = entityData.ipRestrictions as string[];
    data.referrerRestrictions = [];
    return data;
  }

  async generateEntityDataForSnapshot(groupId: number, entityId: string): Promise<object> {
    const apiKey = await this.prisma.apiKey.findFirst({
      where: {
        groupId,
        id: parseInt(entityId),
      },
    });
    if (!apiKey) throw new ApiException(ErrorCode.API_KEY_NOT_FOUND);
    return apiKey;
  }

  async promoteCreate(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    entityData: ApiKey,
    operatorId: number,
  ): Promise<string> {
    let data = new CreateApiKeyDto();
    data = this.fillCommonPromotionData(data, targetGroup, entityData);
    // since api key is not created by default like llmModel, call create function here
    const createdApiKey = await this.createApiKeyForGroup(targetGroup.id, data);
    return createdApiKey.id.toString();
  }

  async promoteUpdate(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string,
    entityData: ApiKey,
    operatorId: number,
  ): Promise<void> {
    let data = new UpdateApiKeyDto();
    data = this.fillCommonPromotionData(data, targetGroup, entityData);
    // rateLimitPlanId is fixed after api key creation, no need to update
    await this.updateApiKeyForGroup(targetGroup.id, parseInt(targetEntityId), data);
  }

  async promoteDelete(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string,
  ): Promise<void> {
    await this.deleteApiKeyForGroup(targetGroup.id, parseInt(targetEntityId));
  }
  async deleteEntityDataForSnapshot(
    groupId: number,
    entityId: string,
    entityData: any,
  ): Promise<void> {
    return;
  }

  async checkPromotedEntityValid(targetEntityId: string) {
    return this.prisma.apiKey.findUnique({ where: { id: parseInt(targetEntityId) } });
  }

  private isIpsFormatCorrect = async (groupId: number, ips: string[]) => {
    const apiKeyWhiteListConfig = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      groupId,
      FeatureFlagKey.API_KEY_WHITELIST_CONFIG,
    );
    const minSubnetMask = (apiKeyWhiteListConfig?.metaData?.['subnetMask'] ?? 0) as number;
    return ips.some((ip) => {
      if (!isIpAddressValid(ip)) return false;
      const subnet = ip.split('/')?.[1];
      if (subnet) {
        if (parseInt(subnet) < minSubnetMask) return false;
      }
      return true;
    });
  };
}
