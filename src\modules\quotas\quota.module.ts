import { Module } from '@nestjs/common';
import { QuotaService } from './quota.service';
import { PrismaModule } from 'src/providers/prisma/prisma.module';
import { RedisModule } from 'src/providers/redis/redis.module';
import { ScopeModule } from '../scope/scope.module';

@Module({
  imports: [PrismaModule, RedisModule, ScopeModule],
  providers: [QuotaService],
  exports: [QuotaService],
})
export class QuotaModule {}
