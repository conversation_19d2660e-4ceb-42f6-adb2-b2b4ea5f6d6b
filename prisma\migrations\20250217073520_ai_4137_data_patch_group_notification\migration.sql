-- This is an empty migration.

update public."GroupNotification"
set "description" = 'On the 1st of each month, The system will send a notification of  Rating Report to bot owner & admin If notifications are enabled. Help bot owner & admin regularly know their bots'' usage rating and may take next action.',
    "interval" = 'Monthly',
    "defaultRecipientRoles" = array['OWNER','ADMIN']::"GroupNotificationRecipientRole"[]
where "groupNotificationName" = 'scoring-and-rating-monthly' AND "channel" = 'EMAIL';