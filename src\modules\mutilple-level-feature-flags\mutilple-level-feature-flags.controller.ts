import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
} from '@nestjs/common';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { OrderByPipe } from 'src/pipes/order-by.pipe';
import { WherePipe } from 'src/pipes/where.pipe';
import { UserRequest } from '../auth/auth.interface';
import { Scopes } from '../auth/scope.decorator';
import { MutilpleLevelFeatureFlagsModelDto } from './mutilple-level-feature-flags-model.dto';
import { FeatureFlagLevels } from './mutilple-level-feature-flags.constants';
import { MutilpleLevelFeatureFlagsService } from './mutilple-level-feature-flags.service';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@Controller('mutilple-level-feature-flags')
@ApiBearerAuth('bearer-auth')
@ApiTags('MutilpleLevel Feature Flags')
export class MutilpleLevelFeatureFlagsController {
  constructor(private mutilpleLevelFeatureFlagService: MutilpleLevelFeatureFlagsService) {}

  @Get()
  @Scopes('system:read-mutilple-level-feat-flag')
  async getFeatFlags(
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    const list = await this.mutilpleLevelFeatureFlagService.getAll(skip, take, where, orderBy);
    const count = await this.mutilpleLevelFeatureFlagService.getCount(where);
    return { list, count };
  }

  @Get('/keys')
  @Scopes('system:write-mutilple-level-feat-flag')
  async getFeatFlagsKeys() {
    const list = await this.mutilpleLevelFeatureFlagService.getAllFeatureFlagKeys();
    return { list };
  }

  @Post()
  @Scopes('system:write-mutilple-level-feat-flag')
  async createFeatFlag(
    @Req() request: UserRequest,
    @Body() body: MutilpleLevelFeatureFlagsModelDto,
  ) {
    body.createdByUserId = request.user.id;
    body.updatedByUserId = request.user.id;
    body.level = FeatureFlagLevels[body.targetType];
    return await this.mutilpleLevelFeatureFlagService.create(body);
  }

  @Put(':id')
  @Scopes('system:write-mutilple-level-feat-flag')
  async updateFeatFlag(
    @Req() request: UserRequest,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: MutilpleLevelFeatureFlagsModelDto,
  ) {
    body.updatedByUserId = request.user.id;
    return await this.mutilpleLevelFeatureFlagService.update(id, body);
  }

  @Delete(':id')
  @Scopes('system:delete-mutilple-level-feat-flag')
  async deleteFeatFlag(@Param('id', ParseIntPipe) id: number) {
    return await this.mutilpleLevelFeatureFlagService.delete(id);
  }
}
