## Environment. 
```bash
  nvm ls
  18.15.0 (Currently using 64-bit executable)
```
## Development

### Generate private key and public key for JWT Signature validation
```bash
$ openssl genpkey -algorithm RSA -out private_key.pem
$ openssl rsa -pubout -in private_key.pem -out public_key.pem
```
Put the generated keys into cert/ in top-level

```bash
$ npm ci

# generate prisma client, for connecting postgres
$ npx prisma generate

# sync prisma schema to DB
$ npx prisma migrate dev

# populate database with prefilled data
$ npx prisma db seed

# start locally on port 3001
$ npm run start:debug

```


### Real UAT Deployment
Trigger the CICD by creating a new tag v1.x.x in release branch.
#### Update Params / Secrets (UAT only currently)
If need to add new params / secrets,
Update the fields name in param_dict.yml / secret_dict.yml

If need to add / update params / secrets in real UAT environment,
please access club aws: https://theclub.awsapps.com/start#/ 
[account: aws-shared]
params:
  Systems Manager -> Parameter Store -> Search "bot-builder-backend/" -> update /k8s.parameter.env.yml
  Then trigger the restart of lambda / trigger the CICD by creating a new tag in release branch.

k8s config: 
  Systems Manager -> Parameter Store -> Search "bot-builder-backend/" -> update /k8s.general.config.yml
  Then trigger the restart of lambda / trigger the CICD by creating a new tag in release branch.

secrets:
  Secret Manager -> update the corresponding /serverless.secret
  Then trigger the restart of lambda / trigger the CICD by creating a new tag in release branch.


### API Key for Postman Monitor
Gravitee API gateway have setup for bot builder API management. 
We have created an API plan "Postman" which not applying ApiKeyFiltering for successful to pass the API gateway. Postman Monitor then can use the generated API key to call bot builder backend API.

1. Reference to the confluence:
  https://theclub.atlassian.net/wiki/spaces/AI/pages/1450410091/Bot+Builder+-+Postman#Pre-requisite
2. Then we can add the custom authorization logic like granting specific permissions in `modules/auth/staart.strategy` module (refer to `generatePostmanMonitorScope` method)
