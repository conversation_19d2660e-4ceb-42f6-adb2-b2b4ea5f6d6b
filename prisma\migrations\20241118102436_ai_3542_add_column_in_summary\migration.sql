-- CreateEnum
CREATE TYPE "Feature" AS ENUM ('CHAT', 'EMBEDDING', 'TTS', 'STT', 'AI_TAGGING', 'CHAT_WITH_FILE', 'CHAT_WITH_DATA', 'INSIGHT_GENERATOR', 'TEXT_TO_IMAGE');

-- AlterTable
ALTER TABLE "Summary" ADD COLUMN     "feature" "Feature" NOT NULL DEFAULT 'CHAT',
ADD COLUMN     "modelPriceUnitId" INTEGER,
ALTER COLUMN "callingBy" SET DEFAULT 0;

-- AlterTable
ALTER TABLE "SummaryAll" ADD COLUMN     "feature" "Feature" NOT NULL DEFAULT 'CHAT',
ADD COLUMN     "modelPriceUnitId" INTEGER,
ALTER COLUMN "callingBy" SET DEFAULT 0;

-- CreateIndex
CREATE INDEX "summary_with_feature_priceUnitId_index" ON "Summary"("callingBy", "callingType", "groupId", "startDate", "callingAttributes", "feature", "modelPriceUnitId");

-- CreateIndex
CREATE INDEX "summary_modelPrice_record_index" ON "Summary"("startDate", "endDate", "groupId");

-- CreateIndex
CREATE INDEX "summary_all_with_feature_priceUnitId_index" ON "SummaryAll"("callingBy", "callingType", "callingAttributes", "groupId", "feature", "modelPriceUnitId");

-- Summary triggers
CREATE OR REPLACE FUNCTION summary_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF EXISTS (SELECT 1 FROM "SummaryAll" WHERE "engineSlug" = NEW."engineSlug" AND "callingType" = NEW."callingType" AND "callingBy" = NEW."callingBy" AND "groupId" = NEW."groupId" AND "flowId" = NEW."flowId" AND "key" = NEW."key" AND "callingAttributes" = NEW."callingAttributes" AND "feature" = NEW."feature" AND "modelPriceUnitId" = NEW."modelPriceUnitId")
    THEN
        UPDATE "SummaryAll" sa SET value = (sa."value" + NEW."value")
        WHERE "engineSlug" = NEW."engineSlug" AND "callingType" = NEW."callingType" AND  "callingBy" = NEW."callingBy" AND "groupId" = NEW."groupId" AND "flowId" = NEW."flowId" AND "key" = NEW."key" AND "callingAttributes" = NEW."callingAttributes" AND "feature" = NEW."feature" AND "modelPriceUnitId" = NEW."modelPriceUnitId";
    ELSE INSERT INTO "SummaryAll" ("value", "engineSlug", "callingType", "callingBy", "groupId", "flowId", "key", "callingAttributes", "feature", "modelPriceUnitId")
    VALUES (NEW."value", NEW."engineSlug", NEW."callingType", NEW."callingBy", NEW."groupId", NEW."flowId", NEW."key", NEW."callingAttributes", NEW."feature", NEW."modelPriceUnitId");
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER Summary_trigger
AFTER INSERT ON public."Summary"
FOR EACH ROW
EXECUTE FUNCTION summary_trigger_function();
