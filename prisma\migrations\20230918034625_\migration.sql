/*
  Warnings:

  - A unique constraint covering the columns `[code]` on the table `KYCVerification` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "KYCVerification.search_index";

-- AlterTable
CREATE SEQUENCE user_id_seq;
ALTER TABLE "User" ALTER COLUMN "id" SET DEFAULT nextval('user_id_seq');
ALTER SEQUENCE user_id_seq OWNED BY "User"."id";

-- CreateIndex
CREATE INDEX "KYCVerification.search_index" ON "KYCVerification"("code", "userId", "type");

-- CreateIndex
CREATE UNIQUE INDEX "KYCVerification.code" ON "KYCVerification"("code");

UPDATE "Email" SET "isVerified" = true; 

UPDATE "Email" SET "email" = LOWER("email");