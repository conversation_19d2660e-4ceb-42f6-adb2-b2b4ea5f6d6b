import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from '../prisma/prisma.module';
import { FortiSanboxService } from './fortisandbox.service';
import { LLMBackendModule } from '../../providers/llm-backend/llm-backend.module';
import { ChatFilesModule } from '../../modules/chat-files/chat-files.module';

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
    forwardRef(() => LLMBackendModule),
    forwardRef(() => ChatFilesModule),
  ],
  providers: [FortiSanboxService],
  exports: [FortiSanboxService],
})
export class FortiSanboxModule {}
