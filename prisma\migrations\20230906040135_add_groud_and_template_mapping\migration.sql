/*
  Warnings:

  - Added the required column `accessLevel` to the `MessageTemplate` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "TemplateAccessLevel" AS ENUM ('GROUP', 'SYSTEM');

-- AlterTable
ALTER TABLE "MessageTemplate" ADD COLUMN "accessLevel" "TemplateAccessLevel" NOT NULL,
ADD COLUMN "groupId" INTEGER;

-- AddForeignKey
ALTER TABLE "MessageTemplate" ADD CONSTRAINT "MessageTemplate_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE SET NULL ON UPDATE CASCADE;
