import { Group, Prisma } from '@prisma/client';
export interface PromotableService {
  // generate entityData for snapshot taking
  // this function need to validate whether the entityId exsits in the group
  generateEntityDataForSnapshot: (groupId: number, entityId: string) => Promise<object>;

  deleteEntityDataForSnapshot: (
    groupId: number,
    entityId: string,
    entityData: any,
  ) => Promise<void>;

  // called when entity never promoted before
  promoteCreate: (
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    entityData: any,
    operatorId: number,
  ) => Promise<string>; // return created entity id in string format

  // called when entity promoted before
  promoteUpdate: (
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string, // target id to promote(update)
    entityData: any,
    operatorId: number,
  ) => Promise<void>;

  promoteDelete: (
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string, // target id to promote(delete)
    operatorId: number,
  ) => Promise<void>;

  checkPromotedEntityValid: (targetEntityId: string) => Promise<unknown>;
}
