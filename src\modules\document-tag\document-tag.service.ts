import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Group, LabelEntityType, LabelType, ModelFile, Prisma, Feature } from '@prisma/client';
import { LLMBackendService } from 'src/providers/llm-backend/llm-backend.service';
import {
  ChatResponse,
  CHAT_APPROACH,
  MODEL,
  ROLE,
  SOURCE,
  Usage,
} from 'src/providers/llm-backend/llm-backend.interface';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { S3Service } from 'src/providers/s3/s3.service';
import {
  DocumentTagBulkCreateInput,
  DocumentTagBulkCreateResponse,
  DocumentTagBulkDeleteAllInput,
  DocumentTagBulkSuggestInput,
  DocumentTagBulkSuggestResponse,
} from './document-tag.dto';
import { AccessTokenParsed, UserRequest } from '../auth/auth.interface';
import { LabelsService } from '../labels/labels.service';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { Configuration } from 'src/config/configuration.interface';
import {
  ChannelType,
  ChatLlmModelDto,
  ChatRequester,
  SecurityScanType,
} from '../llm-models/dto/chat-llm-model.dto';
import { Response } from 'express';
import moment from 'moment';

const utilFunctionLogger = new Logger('Utils');

async function retryIf<T>(
  shouldRetry: (e: unknown) => boolean,
  times: number,
  thunk: () => T,
): Promise<T> {
  for (let i: number = 0; i < times; i++) {
    try {
      return await thunk();
    } catch (e) {
      if (i === times - 1) throw e; // last attempt, throw error
      if (!shouldRetry(e)) throw e; // not a retryable error, throw error
      else {
        utilFunctionLogger.warn(`Invalid response format, retrying..., error:`);
        utilFunctionLogger.warn(e);
      }
    }
  }
}

function exceptionIn(exceptions: ErrorCode[]) {
  return (e: unknown) => {
    if (!(e instanceof ApiException)) return false;
    const errorCodes = exceptions.map((e) => ApiException.parseErrorString(e).code);
    const errRes = e.getResponse() as { error: { code: string } };
    const code = errRes?.error?.code;
    const isInExceptions = errorCodes.includes(code);
    utilFunctionLogger.debug(`${code} is in ${JSON.stringify(errorCodes)}: ${isInExceptions}`);
    return isInExceptions;
  };
}

@Injectable()
export class DocumentTagService {
  private readonly logger = new Logger(DocumentTagService.name);

  constructor(
    private readonly configService: ConfigService<Configuration>,
    private readonly prisma: PrismaService,
    private readonly s3Service: S3Service,
    private readonly llmBackendService: LLMBackendService,
    private readonly labelsService: LabelsService,
    private readonly featureFlagService: FeatureFlagService,
  ) {}

  async countPromptsByGroup(groupId: number) {
    this.logger.debug(`countPromptsByGroup called: ${JSON.stringify({ groupId })}`);
    try {
      const rawQueryResult = await this.prisma.$queryRaw<
        { count: number }[]
      >`SELECT COUNT(DISTINCT prompt) count FROM "ModelFileLabelHistory" WHERE "groupId" = ${groupId}`;
      this.logger.debug(
        `countPromptsByGroup raw query result ${JSON.stringify({ rawQueryResult })}`,
      );
      return rawQueryResult[0].count ?? 0;
    } catch (e) {
      this.logger.error(e);
      throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR, {
        error: String(e),
      });
    }
  }

  async getPromptsByGroup(groupId: number, opts: { take?: number; skip?: number } = {}) {
    this.logger.debug(`getPromptsByGroup called: ${JSON.stringify({ groupId, opts })}`);
    return await this.prisma.modelFileLabelHistory.findMany({
      where: { groupId },
      select: { prompt: true, createdAt: true },
      distinct: ['prompt'],
      orderBy: { createdAt: 'desc' },
      take: opts.take ?? 10,
      skip: opts.skip ?? 0,
    });
  }

  async bulkCreateTags(
    groupId: number,
    input: DocumentTagBulkCreateInput,
    user: AccessTokenParsed,
    method: 'add' | 'replace',
  ): Promise<DocumentTagBulkCreateResponse> {
    this.logger.debug(`bulkCreateTags called: ${JSON.stringify({ groupId, input, user, method })}`);
    // Validate max tags limit of group and each file based on feature flag
    await this.validateMaxTagsOnCreate(groupId, input);

    // Create EntityLabels and Label in a single transaction
    const files = await this.prisma.$transaction(async (trx) => {
      const results: { id: number; tags: { id: number; name: string }[] }[] = [];
      // Loop through each file to create Tags entities
      for (const file of input.files) {
        // Check if the file exists
        if (file.id && !(await this.isFileExist(trx, groupId, file.id)))
          throw new ApiException(ErrorCode.FIlE_NOT_FOUND, { groupId, fileId: file.id });

        // Delete all tags in the current file if method is 'replace'
        if (method === 'replace') await this.deleteAllTagsInFile(trx, file);

        // Put the id in the label/entityLabel if it is exist
        const resolvedTags = await this.resolveExistingTags(trx, groupId, file, file.tags);
        const tags: { id: number; Labels: { name: string } }[] = [];
        for (const tag of resolvedTags) {
          if ('id' in tag) tags.push(tag); // push existing tag without creation
          else tags.push(await this.createTag(trx, file, tag.Labels, user)); // create if not exist
        }
        // Return created EntityLabels with file id
        results.push({
          id: file.id,
          tags: tags.map((tag) => ({ id: tag.id, name: tag.Labels.name })),
        });
      }

      // Validate max tags per file before commit based on feature flag
      await this.validateMaxTagsBeforeCreateCommit(trx, groupId, input.files);
      return results;
    });

    this.logger.debug(`bulkCreateTags results: ${JSON.stringify(files)}`);

    return { files };
  }

  async bulkDeleteAllTags(groupId: number, input: DocumentTagBulkDeleteAllInput): Promise<void> {
    this.logger.debug(`bulkDeleteAllTags called: ${JSON.stringify({ groupId, input })}`);
    await this.prisma.$transaction(async (trx) => {
      for (const file of input.files) {
        // Check if the file exists
        if (file.id && !(await this.isFileExist(trx, groupId, file.id)))
          throw new ApiException(ErrorCode.FIlE_NOT_FOUND, { groupId, fileId: file.id });

        await this.deleteAllTagsInFile(trx, file);
      }
    });
    this.logger.debug(`bulkDeleteAllTags completed`);
  }

  async deleteTag(
    groupId: number,
    fileId: number,
    tagId: number,
    user: AccessTokenParsed,
  ): Promise<void> {
    this.logger.debug(`deleteTag called: ${JSON.stringify({ groupId, fileId, tagId, user })}`);
    if (!(await this.isTagExist(this.prisma, groupId, fileId, tagId)))
      throw new ApiException(ErrorCode.LABELS_NOT_FOUND, { groupId, fileId, tagId });

    await this.labelsService.removeEntityLabels(tagId);
    this.logger.debug(`deleteTag completed`);
  }

  async bulkSuggestTags(
    groupId: number,
    input: DocumentTagBulkSuggestInput,
    req: UserRequest,
    res?: Response,
  ): Promise<DocumentTagBulkSuggestResponse> {
    this.logger.debug(`bulkSuggestTags called: ${JSON.stringify({ groupId, input })}`);
    const taggingConfig = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      groupId,
      FeatureFlagKey.BOT_FILE_TAGGING_CONFIG,
    );
    const maxTagsPerFile = Number(taggingConfig?.metaData?.['maxTagsPerFile']) ?? 5;
    if (isNaN(maxTagsPerFile))
      throw new ApiException(ErrorCode.INVALID_FEATURE_FLAG_PARAMETER, { taggingConfig });

    // Check allowed LLM engine in feature flag
    const allowedModels = (taggingConfig?.metaData?.['allowedModels'] as string[]) ?? [];
    if (Array.isArray(allowedModels) && !allowedModels.includes(input.model))
      throw new ApiException(ErrorCode.MODEL_NOT_ALLOWED_FOR_TAGGING, {
        model: input.model,
        allowedModels,
      });

    // Get group, throw error if group not found.
    const group = await this.prisma.group.findFirst({
      select: { id: true, env: true, name: true },
      where: { id: groupId },
    });
    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND, { groupId });

    // Get bucket name, throw error if bucket not found.
    const buckets = this.configService.get(`s3.staticFilesBuckets`, { infer: true });
    if (!buckets)
      throw new ApiException(ErrorCode.INVALID_ENV_VARIABLE, { env: group.env, buckets });
    const bucket = buckets[group.env];
    if (!bucket)
      throw new ApiException(ErrorCode.INVALID_ENV_VARIABLE, { env: group.env, buckets });

    // Get files in db, throw error if any file is not valid
    const foundFiles = await this.prisma.modelFile.findMany({
      select: {
        id: true,
        docId: true,
        s3Path: true,
        filetype: true,
        fileSize: true,
        status: true,
        isApproved: true,
      },
      where: { id: { in: input.files.map((f) => f.id) }, groupId: group.id },
    });
    const files = input.files.map((f) => {
      const fileInDB = foundFiles.find((file) => file.id === f.id);
      // Throw error if file is not exist
      if (!fileInDB) throw new ApiException(ErrorCode.FIlE_NOT_FOUND, { groupId, fileId: f.id });

      // Throw error if file is not approved
      if (!fileInDB.isApproved)
        throw new ApiException(ErrorCode.MODEL_FILE_NOT_APPROVED, {
          fileId: fileInDB.id,
          isApproved: fileInDB.isApproved,
        });

      // Throw error if file's type is not allowed to auto-tag
      const allowedFileType = (taggingConfig?.metaData?.['allowedMimeTypes'] as string[]) ?? [];
      if (!allowedFileType.includes(fileInDB.filetype))
        throw new ApiException(ErrorCode.FILE_ENTITY_TYPE_INVALID, {
          fileId: fileInDB.id,
          fileType: fileInDB.filetype,
          allowedFileType,
        });
      return fileInDB;
    });

    // Process file one by one to prevent memory overflow
    const filesWithTags = [];
    const usages: Usage[] = [];
    for (const file of files) {
      const { tags, usage } = await retryIf(
        exceptionIn([ErrorCode.INVALID_FILE_TAG_RESPONSE]),
        3,
        () =>
          this.suggestTags(
            group,
            input.model,
            input.prompt,
            bucket,
            file,
            input.ignoreHistory,
            maxTagsPerFile,
            req,
          ),
      );
      const fileTags = { id: file.id, tags, usage };
      filesWithTags.push(fileTags);
      if (input.stream && res)
        res.write(this.formatSSE({ event: 'message', data: JSON.stringify(fileTags) }));
      usages.push(usage);
    }
    const usage = usages.reduce(
      (prev, curr) => {
        prev.promptTokens += curr.promptTokens;
        prev.completionTokens += curr.completionTokens;
        prev.totalCompletionTokens += curr.totalCompletionTokens;
        prev.embeddingTokens += curr.embeddingTokens;
        return prev;
      },
      { promptTokens: 0, completionTokens: 0, totalCompletionTokens: 0, embeddingTokens: 0 },
    );

    this.logger.debug(
      `bulkSuggestTags completed: ${JSON.stringify({ files: filesWithTags, usage })}`,
    );
    const response = { model: input.model, prompt: input.prompt, files: filesWithTags, usage };
    if (input.stream && res)
      res.write(this.formatSSE({ event: 'overall', data: JSON.stringify(response) }));
    return response;
  }

  async countTagsByGroup(groupId: number) {
    this.logger.debug(`countGroupTags called: ${JSON.stringify({ groupId })}`);
    const tags = await this.prisma.labels.count({
      where: {
        EntityLabel: {
          every: { modelFile: { groupId } },
        },
      },
    });

    this.logger.debug(`countGroupTags completed: ${JSON.stringify(tags)}`);
    return tags;
  }

  async getTagsByGroup(
    groupId: number,
    opts: { orderBy?: Prisma.LabelsFindManyArgs['orderBy']; take?: number; skip?: number } = {},
  ) {
    this.logger.debug(`getGroupTags called: ${JSON.stringify({ groupId, opts })}`);
    const tags = await this.prisma.labels.findMany({
      distinct: ['name'],
      where: {
        labelType: 'TAG',
        EntityLabel: {
          every: { modelFile: { groupId } },
        },
      },
      take: opts.take,
      skip: opts.skip,
      select: { name: true },
      orderBy: opts.orderBy,
    });

    this.logger.debug(`getGroupTags completed: ${JSON.stringify(tags)}`);
    return tags;
  }

  // Helper methods

  async isFileExist(trx: Prisma.TransactionClient, groupId: number, fileId: number) {
    const count = await trx.modelFile.count({ where: { groupId, id: fileId } });
    this.logger.debug(`isFileExist called: ${JSON.stringify({ groupId, fileId, count })}`);
    return count > 0;
  }

  async isTagExist(trx: Prisma.TransactionClient, groupId: number, fileId: number, tagId: number) {
    const count = await trx.entityLabels.count({
      where: { id: tagId, modelFile: { id: fileId, groupId } },
    });
    this.logger.debug(`isTagExist called: ${JSON.stringify({ groupId, fileId, tagId, count })}`);
    return count > 0;
  }

  async validateMaxTagsOnCreate(groupId: number, input: DocumentTagBulkCreateInput) {
    this.logger.debug(`validateMaxTagsOnCreate called: ${JSON.stringify({ groupId, input })}`);
    const taggingConfig = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      groupId,
      FeatureFlagKey.BOT_FILE_TAGGING_CONFIG,
    );

    // Throw error if current tags + new tags more then maxTags
    const tags = await this.getTagsByGroup(groupId);
    const maxTags = Number(taggingConfig?.metaData?.['maxTags'] ?? 30);
    const tagSet = new Set([
      ...tags.map((tag) => tag.name),
      ...input.files.flatMap((file) => file.tags.flatMap((tag) => tag.name)),
    ]);
    if (tagSet.size > maxTags)
      throw new ApiException(ErrorCode.BOT_MAX_TAGS_EXCEEDED, { maxTags, tags: [...tagSet] });

    // Throw error if current tags + new tags in some file is more then maxTagsPerFile
    const maxTagsPerFile = Number(taggingConfig?.metaData?.['maxTagsPerFile'] ?? 5);
    for (const file of input.files) {
      if (file.tags.length > maxTagsPerFile)
        throw new ApiException(ErrorCode.FILE_MAX_TAGS_EXCEEDED, { maxTagsPerFile });
    }
    this.logger.debug(`validateMaxTagsOnCreate completed`);
  }

  async validateMaxTagsBeforeCreateCommit(
    trx: Prisma.TransactionClient,
    groupId: number,
    files: DocumentTagBulkCreateInput['files'],
  ) {
    this.logger.debug(
      `validateMaxTagsBeforeCreateCommit called: ${JSON.stringify({ groupId, files })}`,
    );
    const taggingConfig = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      groupId,
      FeatureFlagKey.BOT_FILE_TAGGING_CONFIG,
    );

    // Throw error if any file have more than the maxTagsPerFile before commit
    const maxTagsPerFile = (taggingConfig?.metaData?.['maxTagsPerFile'] as number) ?? 5;
    const exceededFiles = await trx.entityLabels.groupBy({
      by: ['entityId'],
      where: {
        entityId: { in: files.map((file) => file.id) },
        LabelEntityType: LabelEntityType.MODEL_FILE,
      },
      having: {
        entityId: {
          _count: { gt: maxTagsPerFile },
        },
      },
    });
    if (exceededFiles.length > 0)
      throw new ApiException(ErrorCode.FILE_MAX_TAGS_EXCEEDED, { maxTagsPerFile, exceededFiles });
    this.logger.debug(`validateMaxTagsBeforeCreateCommit completed`);
  }

  async createTag(
    trx: Prisma.TransactionClient,
    file: Required<Pick<Prisma.ModelFileWhereUniqueInput, 'id'>>,
    tag: { id?: number; name: string },
    user: AccessTokenParsed,
  ) {
    const now = new Date();
    this.logger.debug(`createTag called: ${JSON.stringify({ tag, user, now })}`);
    return await trx.entityLabels.create({
      select: { id: true, Labels: { select: { name: true } } },
      data: {
        modelFile: { connect: { id: file.id } },
        LabelEntityType: LabelEntityType.MODEL_FILE,
        Labels: tag.id
          ? { connect: { id: tag.id } }
          : {
              create: {
                name: tag.name,
                labelType: LabelType.TAG,
                createdBy: user.id,
                updatedBy: user.id,
                createdAt: now,
                updatedAt: now,
              },
            },
      },
    });
  }

  async resolveExistingTags(
    trx: Prisma.TransactionClient,
    groupId: number,
    file: Required<Pick<Prisma.ModelFileWhereUniqueInput, 'id'>>,
    tags: Pick<Prisma.LabelsCreateInput, 'name'>[],
  ) {
    // Check if the label already exists in the file
    const existingTagsInFile = await trx.entityLabels.findMany({
      select: { id: true, Labels: { select: { id: true, name: true } } },
      where: {
        entityId: file.id,
        LabelEntityType: LabelEntityType.MODEL_FILE,
        Labels: {
          name: { in: tags.map((tag) => tag.name) },
        },
      },
    });
    const existingTagsInGroup = await trx.labels.findMany({
      select: { id: true, name: true },
      where: {
        EntityLabel: {
          every: {
            LabelEntityType: LabelEntityType.MODEL_FILE,
            modelFile: { groupId },
          },
        },
      },
    });

    // filter duplicated and resolve existing tags, for skipping existing tags when creating new tags
    return tags
      .reduce<Pick<Prisma.LabelsCreateInput, 'name'>[]>((prev, curr) => {
        if (prev.every((p) => p.name !== curr.name)) prev.push(curr);
        return prev;
      }, [])
      .map((tag) => {
        const existingTagInFile = existingTagsInFile.find((t) => t.Labels.name === tag.name);
        if (existingTagInFile) return existingTagInFile;

        const existingTagInGroup = existingTagsInGroup.find((t) => t.name === tag.name);
        if (existingTagInGroup) return { Labels: existingTagInGroup };

        return { Labels: { name: tag.name } };
      });
  }

  async deleteAllTagsInFile(
    trx: Prisma.TransactionClient,
    file: Pick<Prisma.ModelFileWhereUniqueInput, 'id'>,
  ) {
    const entityLabelToBeDelete = await trx.entityLabels.findMany({
      select: {
        id: true,
        Labels: {
          select: {
            id: true,
            EntityLabel: { select: { entityId: true } },
          },
        },
      },
      where: {
        entityId: file.id,
        LabelEntityType: LabelEntityType.MODEL_FILE,
        Labels: { labelType: LabelType.TAG },
      },
    });

    await trx.entityLabels.deleteMany({
      where: { id: { in: entityLabelToBeDelete.map((e) => e.id) } },
    });
    const orphanLabelIds = entityLabelToBeDelete
      .filter((item) => item.Labels.EntityLabel.length === 1)
      .map((l) => l.Labels.id);
    await trx.labels.deleteMany({ where: { id: { in: orphanLabelIds } } });
  }

  async suggestTags(
    group: Pick<Group, 'id' | 'env' | 'name'>,
    model: MODEL,
    prompt: string,
    bucket: string,
    file: Pick<
      ModelFile,
      'id' | 'docId' | 's3Path' | 'filetype' | 'fileSize' | 'isApproved' | 'status'
    >,
    ignoreHistory: boolean,
    maxTagsPerFile: number,
    req: UserRequest,
  ): Promise<{ tags: string[]; usage: Usage }> {
    this.logger.debug(
      `suggestTags called: ${JSON.stringify({
        group,
        model,
        prompt,
        bucket,
        file,
        ignoreHistory,
        maxTagsPerFile,
      })}`,
    );

    // Get document docHash from storage
    const fileHash: string = await this.getDocHash(bucket, file);
    // Check if the document is already tagged with the same model and prompt in DocumentTagHistory
    const { _max } = await this.prisma.modelFileLabelHistory.aggregate({
      _max: { version: true },
      where: { groupId: group.id, modelFileId: file.id, fileHash, model, prompt },
    });
    const lastVersion = _max.version;
    this.logger.debug(`Last version number of file ${file.id}: ${lastVersion}`);

    // If the document has already been tagged, skip tagging and return history
    if (lastVersion && !ignoreHistory) {
      const history = await this.prisma.modelFileLabelHistory.findMany({
        select: { id: true, value: true },
        where: {
          groupId: group.id,
          modelFileId: file.id,
          fileHash,
          model,
          prompt,
          version: lastVersion,
        },
      });
      this.logger.debug(`suggestTags return history and completed: ${JSON.stringify(history)}`);
      return {
        tags: history.map((h) => h.value),
        usage: {
          promptTokens: 0,
          completionTokens: 0,
          totalCompletionTokens: 0,
          embeddingTokens: 0,
        },
      };
    }

    // Call GPT service to generate tags for document chunks
    const res: ChatResponse = await this.requestTagFromLLM(
      group,
      model,
      prompt,
      bucket,
      file,
      maxTagsPerFile,
      req,
    );

    // Record tags in DocumentTagHistory
    const tags = this.parseJsonFromChatResponse(res, maxTagsPerFile);
    try {
      await this.prisma.modelFileLabelHistory.createMany({
        data: tags.map((tag) => ({
          groupId: group.id,
          modelFileId: file.id,
          fileHash,
          prompt,
          model,
          version: lastVersion ? lastVersion + 1 : 1,
          value: tag,
          createdBy: req.user.id,
        })),
      });
      this.logger.debug(`Create history success: ${JSON.stringify(tags)}`);
    } catch (e) {
      // FIXME: Log error and continue since creating history should not block user
      this.logger.warn(`Create history failed: ${JSON.stringify(tags)}, error: ${String(e)}`);
    }

    // Return tags
    return { tags, usage: res.usage };
  }

  private async getDocHash(
    bucket: string,
    file: Pick<
      ModelFile,
      'id' | 'docId' | 's3Path' | 'filetype' | 'fileSize' | 'isApproved' | 'status'
    >,
  ) {
    try {
      const { ETag } = await this.s3Service.head(bucket, file.s3Path);
      return ETag;
    } catch (e) {
      throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR, {
        message: 'Unable to get etag from S3',
        error: String(e),
      });
    }
  }

  private formatTaggingSystemPrompt(maxTagsPerFile: number) {
    return `You are an AI language model designed to extract meaningful tags from documents. Your task is to read the document provided and identify keywords or phrases that capture the main topics, themes, or subjects. The tags should be relevant, concise, and representative of the document's content. Follow these guidelines:
  1. Relevance: Choose tags that accurately reflect the core content of the document.
  2. Conciseness: Use one or two words per tag if possible.
  3. Coverage: Ensure that the tags cover different aspects of the document.
  4. Avoid Redundancy: Do not repeat similar tags.
  5. Format: The answer must be in JSON format. It should be a Array of string which like the following EXAMPLEs:
     <EXAMPLE>["Tag1", "Tag2", "Tag3"]</EXAMPLE>
     <EXAMPLE>["FileTag1", "FileTag2", "FileTag3"]</EXAMPLE>
     <EXAMPLE>["Noun1", "Noun2", "Noun3"]</EXAMPLE>
     <EXAMPLE>[]</EXAMPLE>
  6. Number of Tags: The number of tags in the JSON response should be less than or equal to ${maxTagsPerFile}.
  7. Ordering: Order the tags by relevant.
  8. Fallback: If no document are found , you should return a empty array. e.g. []
`;
  }

  private async requestTagFromLLM(
    group: Pick<Group, 'id' | 'env' | 'name'>,
    model: MODEL,
    prompt: string,
    bucket: string,
    file: Pick<
      ModelFile,
      'id' | 'docId' | 's3Path' | 'filetype' | 'fileSize' | 'isApproved' | 'status'
    >,
    maxTagsPerFile: number,
    req: UserRequest,
  ) {
    const llmModel = await this.prisma.lLMModel.findFirst({ where: { groupId: group.id } });
    try {
      const chatLlmModelDto: ChatLlmModelDto = {
        approach: CHAT_APPROACH.CWF,
        history: [{ role: ROLE.USER, content: prompt }],
        llmModel,
        overrides: {
          model,
          top: 0,
          prompt_template: this.formatTaggingSystemPrompt(maxTagsPerFile),
          max_tokens: 1000,
          temperature: 0,
          top_p: 0.95,
          presence_penalty: 0,
          frequency_penalty: 0,
          stream: false,
          security_scan: SecurityScanType.OFF,
          api_resource: null,
        },
        files: [
          // @ts-expect-error extra properties `bucket` and `s3_key` are needed for gpt service
          { data_source: SOURCE.VECTOR_STORE, file_id: file.docId, bucket, s3_key: file.s3Path },
        ],
      };

      const prefilledLog = await this.generatePrefilledChatLog(group, req, chatLlmModelDto);
      const chatResponseOrVoid = await this.llmBackendService.chatWithModel(
        group.env,
        chatLlmModelDto,
        null,
        prefilledLog,
        false,
      );
      if (!chatResponseOrVoid)
        throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR, { chatResponseOrVoid });
      return chatResponseOrVoid;
    } catch (e) {
      this.logger.error(`Error during llm completion: ${e}`);
      if (e instanceof ApiException) throw e;
      throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR, { error: String(e) });
    }
  }

  private parseJsonFromChatResponse(res: ChatResponse, maxTagsPerFile: number) {
    if (!res.answer)
      throw new ApiException(ErrorCode.INVALID_FILE_TAG_RESPONSE, { answer: res.answer });
    if (!res.usage)
      throw new ApiException(ErrorCode.INVALID_FILE_TAG_RESPONSE, { usage: res.usage });

    let tags: string[];
    try {
      tags = JSON.parse(
        res.answer
          .replace(/^\n|\n$/g, '')
          .replace(/^```json\n/g, '')
          .replace(/```$/g, ''),
      );
      if (!Array.isArray(tags))
        throw new ApiException(ErrorCode.INVALID_FILE_TAG_RESPONSE, { answer: res.answer });
      this.logger.debug(`suggested tags: ${JSON.stringify(tags)}`);
      tags = tags.slice(0, maxTagsPerFile);
      this.logger.debug(`suggested tags after slice: ${JSON.stringify(tags)}`);
    } catch (e) {
      if (e instanceof ApiException) throw e;
      throw new ApiException(ErrorCode.INVALID_FILE_TAG_RESPONSE, {
        answer: res.answer,
        error: String(e),
      });
    }

    return tags;
  }

  formatSSE(input: { event?: string; data: string }) {
    return `${input.event ? `event: ${input.event}\n` : ''}data: ${input.data}\n\n`;
  }

  async generatePrefilledChatLog(
    group: Pick<Group, 'id' | 'env' | 'name'>,
    request: UserRequest,
    chatLlmModelDto: ChatLlmModelDto,
  ) {
    const feature = Feature.AI_TAGGING;
    const channel =
      request.headers['x-api-key'] != null ? ChannelType.API_KEY : ChannelType.PLAYGROUND;
    let requester: ChatRequester = {
      requesterId: '',
      requesterName: '',
    };

    if (channel === ChannelType.API_KEY) {
      if (request.headers['x-api-key'] && request.headers['x-api-key'].toString().length > 0) {
        const apiKey = await this.prisma.apiKey.findFirst({
          where: { apiKey: request.headers['x-api-key'].toString() },
        });
        if (!apiKey) throw new ApiException(ErrorCode.API_KEY_NOT_FOUND);
        requester = {
          requesterId: apiKey.id.toString(),
          requesterName: apiKey.name,
        };
      }
    } else {
      const user = await this.prisma.user.findUnique({ where: { id: request.user.id } });
      if (!user) throw new ApiException(ErrorCode.USER_NOT_FOUND);
      const email = await this.prisma.email.findFirst({ where: { userId: request.user.id } });
      requester = {
        requesterId: user?.id?.toString(),
        requesterName: user?.name,
        requesterStaffId: user?.staffId,
        requesterEmail: email?.email,
      };
    }

    const prefilledLog = {
      date: moment(),
      botId: group.id,
      botEnv: group.env,
      botName: group.name,
      channel,
      ...requester,
      engine: chatLlmModelDto.overrides?.model,
      engineConfig: chatLlmModelDto.overrides,
      query: String(chatLlmModelDto.history[chatLlmModelDto.history.length - 1].content).trim(),
      chatSessionName: chatLlmModelDto?.chatSession?.name,
      isChatSessionDefault: chatLlmModelDto?.chatSession?.isDefault,
      chatSessionId: chatLlmModelDto?.chatSession?.id,
      feature,
    } as any;
    return prefilledLog;
  }
}
