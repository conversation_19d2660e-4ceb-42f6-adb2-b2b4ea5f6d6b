/*
  Warnings:

  - You are about to drop the column `active` on the `Group` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "Group" DROP COLUMN "active";

-- AlterTable
ALTER TABLE "LLMModel" ADD COLUMN     "active" BOOLEAN NOT NULL DEFAULT true;

UPDATE  "PermissionGroupSetting" pg   SET "isActiveOnly" = false FROM "Permission" p
WHERE p."permissionKey"  IN (
  'group-{groupId}:write-llm-model',
  'group-{groupId}:write-entity-snapshot'
) AND p.id = pg."permissionId";

UPDATE  "PermissionGroupSetting" pg   SET "isActiveOnly" = true FROM "Permission" p
WHERE p."permissionKey"  IN (
  'group-{groupId}:read-playground'
) AND p.id = pg."permissionId";



INSERT INTO "Permission" ("description", "permissionKey", "permissionType", "envs") VALUES ('Read Chat Session Data', 'group-{groupId}:read-chat-session', 'GROUP' , '{TEST,PROD}') ON CONFLICT DO NOTHING;

INSERT INTO "PermissionGroupSetting" ("permissionId", "groupType", "isCustomRoleAllowed", "isApiKeyAllowed", "isActiveOnly" ,"updatedAt") VALUES
 ( (SELECT id FROM "Permission" WHERE "permissionKey" = 'group-{groupId}:read-chat-session'), 'BOT', true, false, false, NOW()) ON CONFLICT DO NOTHING;

INSERT INTO "RolePermission" ("roleId", "permissionId") SELECT * FROM ( VALUES (
(SELECT id FROM "Role" WHERE "systemName" = 'GROUP_OWNER' AND "roleType" = 'GROUP_DEFAULT'),
(SELECT id FROM "Permission" WHERE "permissionKey" = 'group-{groupId}:read-chat-session')))
AS v("roleId", "permissionId") WHERE v."roleId" IS NOT NULL AND v."permissionId" IS NOT NULL;

INSERT INTO "RolePermission" ("roleId", "permissionId") SELECT * FROM ( VALUES (
(SELECT id FROM "Role" WHERE "systemName" = 'GROUP_ADMIN' AND "roleType" = 'GROUP_DEFAULT'),
(SELECT id FROM "Permission" WHERE "permissionKey" = 'group-{groupId}:read-chat-session')))
AS v("roleId", "permissionId") WHERE v."roleId" IS NOT NULL AND v."permissionId" IS NOT NULL;

INSERT INTO "RolePermission" ("roleId", "permissionId") SELECT * FROM ( VALUES (
(SELECT id FROM "Role" WHERE "systemName" = 'GROUP_MEMBER' AND "roleType" = 'GROUP_DEFAULT'),
(SELECT id FROM "Permission" WHERE "permissionKey" = 'group-{groupId}:read-chat-session')))
AS v("roleId", "permissionId") WHERE v."roleId" IS NOT NULL AND v."permissionId" IS NOT NULL;


INSERT INTO "Permission" ("description", "permissionKey", "permissionType", "envs") VALUES ('Read All Chat Session Data', 'group-*:read-chat-session', 'SYSTEM' , '{TEST,PROD}') ON CONFLICT DO NOTHING;

INSERT INTO "RolePermission" ("roleId", "permissionId") SELECT * FROM ( VALUES (
(SELECT id FROM "Role" WHERE "systemName" = 'SUDO' AND "roleType" = 'SYSTEM_DEFAULT'),
(SELECT id FROM "Permission" WHERE "permissionKey" = 'group-*:read-chat-session')))
AS v("roleId", "permissionId") WHERE v."roleId" IS NOT NULL AND v."permissionId" IS NOT NULL;



INSERT INTO "Permission" ("description", "permissionKey", "permissionType", "envs") VALUES ('Write Chat Session Data', 'group-{groupId}:write-chat-session', 'GROUP' , '{TEST,PROD}') ON CONFLICT DO NOTHING;

INSERT INTO "PermissionGroupSetting" ("permissionId", "groupType", "isCustomRoleAllowed", "isApiKeyAllowed", "isActiveOnly" ,"updatedAt") VALUES
 ( (SELECT id FROM "Permission" WHERE "permissionKey" = 'group-{groupId}:write-chat-session'), 'BOT', true, false, false, NOW()) ON CONFLICT DO NOTHING;

INSERT INTO "RolePermission" ("roleId", "permissionId") SELECT * FROM ( VALUES (
(SELECT id FROM "Role" WHERE "systemName" = 'GROUP_OWNER' AND "roleType" = 'GROUP_DEFAULT'),
(SELECT id FROM "Permission" WHERE "permissionKey" = 'group-{groupId}:write-chat-session')))
AS v("roleId", "permissionId") WHERE v."roleId" IS NOT NULL AND v."permissionId" IS NOT NULL;

INSERT INTO "RolePermission" ("roleId", "permissionId") SELECT * FROM ( VALUES (
(SELECT id FROM "Role" WHERE "systemName" = 'GROUP_ADMIN' AND "roleType" = 'GROUP_DEFAULT'),
(SELECT id FROM "Permission" WHERE "permissionKey" = 'group-{groupId}:write-chat-session')))
AS v("roleId", "permissionId") WHERE v."roleId" IS NOT NULL AND v."permissionId" IS NOT NULL;

INSERT INTO "RolePermission" ("roleId", "permissionId") SELECT * FROM ( VALUES (
(SELECT id FROM "Role" WHERE "systemName" = 'GROUP_MEMBER' AND "roleType" = 'GROUP_DEFAULT'),
(SELECT id FROM "Permission" WHERE "permissionKey" = 'group-{groupId}:write-chat-session')))
AS v("roleId", "permissionId") WHERE v."roleId" IS NOT NULL AND v."permissionId" IS NOT NULL;


INSERT INTO "Permission" ("description", "permissionKey", "permissionType", "envs") VALUES ('Write All Chat Session Data', 'group-*:write-chat-session', 'SYSTEM' , '{TEST,PROD}') ON CONFLICT DO NOTHING;

INSERT INTO "RolePermission" ("roleId", "permissionId") SELECT * FROM ( VALUES (
(SELECT id FROM "Role" WHERE "systemName" = 'SUDO' AND "roleType" = 'SYSTEM_DEFAULT'),
(SELECT id FROM "Permission" WHERE "permissionKey" = 'group-*:write-chat-session')))
AS v("roleId", "permissionId") WHERE v."roleId" IS NOT NULL AND v."permissionId" IS NOT NULL;
