-- Data Patch
DO $$
	DECLARE playgroundValue BIGINT;
	        teamsValue BIGINT;
			apiKeyValue BIGINT;
	        playgroundResourceId INTEGER;
			apiKeyResourceId INTEGER;
			teamsResourceId INTEGER;
			playgroundRuleId INTEGER;
			apiKeyRuleId INTEGER;
			teamsRuleId INTEGER;
	        playgroundPlanId INTEGER;
			apiKeyPlanId INTEGER;
			teamsPlanId INTEGER;
			playgroundQuotaValueId INTEGER;
			apiKeyQuotaValueId INTEGER;
			teamsQuotaValueId INTEGER;
	        botId  INTEGER;
			botEnv TEXT;
	        featureFlagRecord RECORD;

	BEGIN
		FOR featureFlagRecord IN 
			SELECT "targetValue", fo."metaData" as "metaData", g."env" FROM "FeatureFlagOverride" fo JOIN "FeatureFlag" f ON fo."featureFlagId" = f.id 
			LEFT JOIN  "Group" g ON fo."targetValue"::INTEGER = g.id
			WHERE f."key" = 'BOT.CONFIG_BOT_CHAT_CALL_RATE_LIMIT' AND fo."targetType" = 'BOT' 
		LOOP
		
		botId:= featureFlagRecord."targetValue";
     	playgroundValue:= (featureFlagRecord."metaData"->'PLAYGROUND' ->> 'points')::BIGINT;
		botEnv:= featureFlagRecord."env";

		IF playgroundValue > 2147483647 THEN
			playgroundValue:= 2147483647;
		END IF;

		teamsValue:= (featureFlagRecord."metaData"->'TEAMS' ->> 'points')::BIGINT;
		IF teamsValue > 2147483647 THEN
			teamsValue:= 2147483647;
		END IF;
		apiKeyValue:= (featureFlagRecord."metaData"->'API_KEY' ->> 'points')::BIGINT;
		IF apiKeyValue > 2147483647 THEN
			apiKeyValue:= 2147483647;
		END IF;
		IF playgroundValue is NULL THEN
		        RAISE NOTICE 'PlaygroundValue not found on botId %',botId;
		    ELSE
				SELECT id INTO playgroundResourceId from "Resource" WHERE "resourceKey" = 'rate-limit-playground';
						
				INSERT INTO "Plan" 
				("resourceId", "planKey", "planName", "description", "entityId", "entityType", "groupEnv")
				VALUES
				(playgroundResourceId, 'rate-limit-playground-plan-'|| botId, 
				playgroundValue||' chats per hour', 'To allow maximum '|| playgroundValue  ||' chats per hour by using Playground', botId, 'BOT', botEnv:: "Environment") 
				RETURNING id INTO playgroundPlanId;
				
				SELECT id INTO playgroundRuleId from "ResourceQuotaRule" WHERE "ruleKey" = 'group-{groupId}:rate-limit-playground-quota';
						
				INSERT INTO "ResourceQuotaValue" 
				("quotaKey", "value", "ruleId") 
				VALUES
				('group-{groupId}:rate-limit-playground-quota-'||botId, playgroundValue, playgroundRuleId)
				RETURNING id INTO playgroundQuotaValueId;
				
				INSERT INTO "PlanQuota" ("planId", "quotaValueId") VALUES (playgroundPlanId, playgroundQuotaValueId);
				
				INSERT INTO "PlanSubscription" 
				("planId", "subscriberEntityType", "subscriberEntityId", "subscribeStartDate")
				VALUES
				(playgroundPlanId, 'BOT', botId, NOW());
				RAISE NOTICE 'data patch of playground rate limit, botId : %,  playground: %', botId, playgroundValue;
		END IF;
		
		IF apiKeyValue is NULL THEN
		        RAISE NOTICE 'Api Key Value not found on botId %', botId;
		    ELSE
				SELECT id INTO apiKeyResourceId from "Resource" WHERE "resourceKey" = 'rate-limit-api-key';
						
				INSERT INTO "Plan" 
				("resourceId", "planKey", "planName", "description", "entityId", "entityType", "groupEnv")
				VALUES
				(apiKeyResourceId, 'rate-limit-api-key-plan-'|| botId, 
				apiKeyValue||' chats per hour', 'To allow maximum '|| apiKeyValue  ||' chats per hour by using Api Key', botId, 'BOT', botEnv:: "Environment") 
				RETURNING id INTO apiKeyPlanId;
				
				SELECT id INTO apiKeyRuleId from "ResourceQuotaRule" WHERE "ruleKey" = 'group-{groupId}:rate-limit-api-key-quota';
						
				INSERT INTO "ResourceQuotaValue" 
				("quotaKey", "value", "ruleId") 
				VALUES
				('group-{groupId}:rate-limit-api-key-quota-'||botId, apiKeyValue, apiKeyRuleId)
				RETURNING id INTO apiKeyQuotaValueId;
				
				INSERT INTO "PlanQuota" ("planId", "quotaValueId") VALUES (apiKeyPlanId, apiKeyQuotaValueId);
				
				INSERT INTO "PlanSubscription" 
				("planId", "subscriberEntityType", "subscriberEntityId", "subscribeStartDate")
				VALUES
				(apiKeyPlanId, 'BOT', botId, NOW());
				RAISE NOTICE 'data patch of api key rate limit, botId : %,  api key: %', botId, apiKeyValue;
		
		END IF;
		
		IF teamsValue is NULL THEN
		        RAISE NOTICE 'Teams Value not found on botId %', botId;
		    ELSE
				SELECT id INTO teamsResourceId from "Resource" WHERE "resourceKey" = 'rate-limit-teams';
						
				INSERT INTO "Plan" 
				("resourceId", "planKey", "planName", "description", "entityId", "entityType", "groupEnv")
				VALUES
				(teamsResourceId, 'rate-limit-teams-plan-'|| botId, 
				teamsValue||' chats per hour', 'To allow maximum '|| teamsValue  ||' chats per hour by using Api Key', botId, 'BOT', botEnv:: "Environment") 
				RETURNING id INTO teamsPlanId;
				
				SELECT id INTO teamsRuleId from "ResourceQuotaRule" WHERE "ruleKey" = 'group-{groupId}:rate-limit-teams-quota';
						
				INSERT INTO "ResourceQuotaValue" 
				("quotaKey", "value", "ruleId") 
				VALUES
				('group-{groupId}:rate-limit-teams-quota-'||botId, teamsValue, teamsRuleId)
				RETURNING id INTO teamsQuotaValueId;
				
				INSERT INTO "PlanQuota" ("planId", "quotaValueId") VALUES (teamsPlanId, teamsQuotaValueId);
				
				INSERT INTO "PlanSubscription" 
				("planId", "subscriberEntityType", "subscriberEntityId", "subscribeStartDate")
				VALUES
				(teamsPlanId, 'BOT', botId, NOW());
				
				RAISE NOTICE 'data patch of teams rate limit, botId : %,  teams: %', botId, teamsValue;
		
		END IF;
	
		END LOOP;
	END;
$$;