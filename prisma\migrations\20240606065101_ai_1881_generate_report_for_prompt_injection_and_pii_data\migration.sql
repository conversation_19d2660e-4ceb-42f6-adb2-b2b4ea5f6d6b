-- CreateEnum
CREATE TYPE "SecurityDetectionType" AS ENUM ('PROMPT', 'OUTPUT');

-- AlterEnum
ALTER TYPE "FileType" ADD VALUE 'SECURITY_DETECTION_REPORT';

-- CreateTable
CREATE TABLE "PromptOutputSecurityDetection" (
    "id" SERIAL NOT NULL,
    "groupId" INTEGER NOT NULL,
    "user_prompt" TEXT NOT NULL,
    "output" TEXT,
    "masked_prompt" TEXT,
    "scanners" TEXT[],
    "type" "SecurityDetectionType" NOT NULL,
    "createdByUserId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PromptOutputSecurityDetection_pkey" PRIMARY KEY ("id")
);
