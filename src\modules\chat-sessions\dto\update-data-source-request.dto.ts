import { IsE<PERSON>, IsInt, <PERSON>NotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { ChatSessionType } from '@prisma/client';
import { Type } from 'class-transformer';

export enum FileHandleStrategy {
  Original = 'Original',
  Embedding = 'Embedding',
}

export enum AutoSelectType {
  Manual = 'Manual',
  All = 'All',
  Filter = 'Filter',
}

export class AutoSelectDto {
  @IsEnum(AutoSelectType)
  type: AutoSelectType;

  fileTypes?: string[];
  fileTags?: string[];
}

// TODO:: need add new feature to support the user can config the file/model file  tone .
export class DataSourceFileInfo {
  file_id: string;
  range: number;
  fileTone?: string;
}

/* 
now if the user using the Embedding(AI Search) and click will auto set the file to typeDefinition 
 TODO:: need add new feature to support the user can config the file tone .
*/
export class DataSourceModelFile extends DataSourceFileInfo {
  chatWithFile?: boolean;
}

export class DataSourceFile<T extends DataSourceFileInfo | DataSourceModelFile> {
  enable: boolean;

  @Type(() => Object)
  files: T[];

  @ValidateNested()
  @IsOptional()
  @Type(() => AutoSelectDto)
  autoSelect?: AutoSelectDto;
}

export class DataSourceGenKb {
  enable: boolean;
  kbTop?: number;
}
export class UpdateDataSourceRequestDto {
  @IsInt()
  @IsOptional()
  chatSessionId?: number;

  @IsEnum(ChatSessionType)
  @IsOptional()
  chatSessionType?: ChatSessionType;

  @IsString()
  @IsNotEmpty()
  @IsEnum(FileHandleStrategy)
  fileHandleStrategy: FileHandleStrategy;

  @Type(() => DataSourceFile<DataSourceFileInfo>)
  chatFile?: DataSourceFile<DataSourceFileInfo>;

  @Type(() => DataSourceFile<DataSourceModelFile>)
  @ValidateNested()
  @IsOptional()
  modelFile?: DataSourceFile<DataSourceModelFile>;
  genKb?: DataSourceGenKb;

  top?: number;
}

export class DataSourceFileMovedMapping {
  file_id: string;
  range: number;
  new_file_id: string;
}
