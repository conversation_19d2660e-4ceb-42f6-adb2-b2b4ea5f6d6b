-- AlterTable

-- UPDATE "User" SET "active" = false WHERE id in (
-- 	SELECT id FROM "User" WHERE "staffId" in 
-- 	(SELECT "staffId"  FROM "User" WHERE "active" = true and "loginType" = 'HKT' GROUP BY "staffId" HAVING COUNT(*) > 1 )
-- 	AND "staffId" is not null AND TRIM("staffId") != '' AND "lastLoginDate" is null
-- );

-- SELECT "staffId", "loginType", "active", "lastLoginDate" FROM "User" WHERE "staffId" is not null AND TRIM("staffId") != '' 
-- and "staffId" in (SELECT "staffId"  FROM "User" WHERE "active" = true and "loginType" = 'HKT' GROUP BY "staffId" HAVING COUNT(*) > 1 ) ;