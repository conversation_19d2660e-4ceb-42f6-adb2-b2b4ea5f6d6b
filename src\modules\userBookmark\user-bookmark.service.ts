import { Injectable } from '@nestjs/common';
import { Prisma, UserBookmarkEntityType } from '@prisma/client';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { PrismaService } from 'src/providers/prisma/prisma.service';

@Injectable()
export class UserBookmarkService {
  constructor(private readonly prisma: PrismaService) {}

  private readonly bookmarkedEntityMapping: {
    [key in UserBookmarkEntityType]: keyof Prisma.UserBookmarkInclude;
  } = {
    GROUP: 'group',
    MESSAGE_TEMPLATE: 'messageTemplate',
    PUBLIC_BOT: 'LLMModel',
  };

  async getUserBookmarkedWithType(
    userId: number,
    userBookmarkEntityType: UserBookmarkEntityType,
    skip?: number,
    take?: number,
    orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    const whereCondition: Prisma.UserBookmarkWhereInput = {
      userId,
      entityType: userBookmarkEntityType,
    };
    const userBookmarks = await this.prisma.userBookmark.findMany({
      include: {
        [this.bookmarkedEntityMapping[userBookmarkEntityType]]: true,
      },
      where: whereCondition,
      skip,
      take,
      orderBy,
    });
    const count = await this.prisma.userBookmark.count({ where: whereCondition });
    return { list: userBookmarks, count };
  }

  async getUserBookmarkedList(userId: number) {
    const userBookmarkData = {};
    for (const [key, value] of Object.entries(this.bookmarkedEntityMapping)) {
      const bookmark = await this.prisma.userBookmark.findMany({
        select: {
          id: true,
          [value]: true,
        },
        where: {
          userId,
          entityType: key as UserBookmarkEntityType,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
      userBookmarkData[value] = bookmark;
    }
    return userBookmarkData;
  }

  async removeBookmark(id: number, userId: number) {
    const bookmark = await this.prisma.userBookmark.findFirst({ where: { id, userId } });
    if (!bookmark) {
      throw new ApiException(ErrorCode.BOOKMARK_NOT_FOUND);
    }
    await this.prisma.userBookmark.delete({
      where: { id },
    });
    return bookmark;
  }

  async bookmarkedEntity(userId: number, entityId: number, entityType: UserBookmarkEntityType) {
    const entityTable = this.bookmarkedEntityMapping[entityType];
    const entity = await (this.prisma[entityTable] as any).findFirst({ where: { id: entityId } });
    if (!entity) {
      throw new ApiException(ErrorCode.BOOKMARK_ENTITY_NOT_FOUND);
    }
    const bookmark = await this.prisma.userBookmark.findFirst({
      where: { entityId, entityType, userId },
    });
    if (bookmark) {
      return bookmark;
    }
    return await this.prisma.userBookmark.create({
      data: {
        userId,
        entityType,
        entityId,
      },
    });
  }
}
