import { Injectable, Logger } from '@nestjs/common';
import { ResourceSubsciberType } from '@prisma/client';
import { ResourceQuotaDto } from '../scope/scope.dto';
import { GROUP_RESOURCE_QUOTA_KEY } from 'src/providers/redis/redis.constants';
import { RedisService } from 'src/providers/redis/redis.service';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { ScopeService } from '../scope/scope.service';

@Injectable()
export class QuotaService {
  constructor(
    private readonly redisService: RedisService,
    private readonly prisma: PrismaService,
    private readonly scopeService: ScopeService,
  ) {}
  private readonly logger = new Logger(QuotaService.name);

  // get resource quota key for a user
  async getGroupResourceQuotas(groupId?: number): Promise<ResourceQuotaDto[]> {
    if (!groupId) {
      return [];
    }
    const cacheKey = GROUP_RESOURCE_QUOTA_KEY.replace('{GROUP_ID}', groupId.toString());
    // get the resource quota keys from cache, if no cache, get from db and save into cache
    return await this.redisService.getOrSet<ResourceQuotaDto[]>(
      cacheKey,
      async () => {
        const group = await this.prisma.group.findUnique({
          where: { id: groupId },
          select: { groupType: true, env: true },
        });
        // get the additional resource quotas key based on the group subscribed plans
        const groupAdditionalQuotas = await this.scopeService.getAdditionalResourceQuotas(
          groupId,
          group.groupType as ResourceSubsciberType,
          group.env,
        );
        return groupAdditionalQuotas;
      },
      60 * 60,
    );
  }
}
