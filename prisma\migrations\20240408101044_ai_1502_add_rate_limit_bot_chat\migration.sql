-- This is an empty migration.

ALTER TABLE "FeatureFlagOverride" ALTER COLUMN "createdByUserId" DROP NOT NULL,
ALTER COLUMN "updatedByUserId" DROP NOT NULL;
-- AlterTable
ALTER TABLE "FeatureFlag" ALTER COLUMN "updatedAt" SET DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "FeatureFlagOverride" ALTER COLUMN "updatedAt" SET DEFAULT CURRENT_TIMESTAMP;

INSERT INTO "public"."FeatureFlag" ( "description", "key", "value", "metaData" , "isEnabled", "isForClientSide") VALUES
('Rate Limit of bot chat', 'BOT.RATE_LIMIT', 'Y', '{"TEAMS": {"points": 50, "duration": 3600}, "API_KEY": {"points": 50, "duration": 3600}, "PLAYGROUND": {"points": 50, "duration": 3600}}',  't', 't');

INSERT INTO "public"."FeatureFlagOverride" ("value", "targetType", "targetValue", "level", "featureFlagId", "isEnabled", "metaData") VALUES
('1', 'ENV', 'TEST', 1, (SELECT id FROM "FeatureFlag" WHERE "key" = 'BOT.RATE_LIMIT'), 't', '{"TEAMS": {"points": 50, "duration": 3600}, "API_KEY": {"points": 50, "duration": 3600}, "PLAYGROUND": {"points": 50, "duration": 3600}}');

INSERT INTO "public"."FeatureFlagOverride" ( "value", "targetType", "targetValue", "level", "featureFlagId", "isEnabled", "metaData") VALUES
('1', 'ENV', 'PROD', 1, (SELECT id FROM "FeatureFlag" WHERE "key" = 'BOT.RATE_LIMIT'),  'f', '{"TEAMS": {"points": 50, "duration": 3600}, "API_KEY": {"points": 50, "duration": 3600}, "PLAYGROUND": {"points": 50, "duration": 3600}}');