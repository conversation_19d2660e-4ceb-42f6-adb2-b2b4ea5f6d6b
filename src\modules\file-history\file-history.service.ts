import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Prisma, FileHistoryStatus, FileType, FileEntityType } from '@prisma/client';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { S3Service } from 'src/providers/s3/s3.service';
import { ConfigService } from '@nestjs/config';
import { FileHistoryEntity } from './entities/file-history.entity';
import { UserRequest } from '../auth/auth.interface';
import { MailService } from 'src/providers/mail/mail.service';

@Injectable()
export class FileHistoryService {
  private logger = new Logger(FileHistoryService.name);
  constructor(
    private prisma: PrismaService,
    private s3Service: S3Service,
    private configService: ConfigService,
    private mailService: MailService,
  ) {}

  async createMany(data: Prisma.FileHistoryCreateManyInput[]) {
    const input = data.map((item) => ({
      ...item,
      createdAt: new Date(item.createdAt),
      updatedAt: new Date(item.updatedAt),
      dateFrom: new Date(item.dateFrom),
      dateTo: new Date(item.dateTo),
    }));
    return this.prisma.fileHistory.createMany({ data: input });
  }

  async create(data: Prisma.FileHistoryUncheckedCreateInput) {
    return this.prisma.fileHistory.create({
      data: {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date(),
        dateFrom: new Date(data.dateFrom),
        dateTo: new Date(data.dateTo),
      },
    });
  }

  async updateFileHistory(fileId: string, input: Prisma.FileHistoryUpdateInput) {
    try {
      const fileHistory = await this.prisma.fileHistory.update({
        where: { fileId },
        data: {
          ...input,
          updatedAt: new Date(),
        },
      });
      return fileHistory;
    } catch (err) {
      this.logger.error(err, 'update log history file failed: ');
      throw new ApiException(ErrorCode.FILE_UPDATE_FAILED);
    }
  }

  async findByFileId(fileId: string) {
    try {
      const fileHistory = await this.prisma.fileHistory.findUnique({
        where: {
          fileId,
        },
      });
      return fileHistory;
    } catch (err) {
      this.logger.error(err, 'failed to obtain log history ');
      throw new Error();
    }
  }

  async list(
    orderBy: Record<string, 'asc' | 'desc'>,
    skip: number,
    take: number,
    where: Record<string, number | string>,
  ) {
    try {
      const fileHistoryList = await this.prisma.fileHistory.findMany({
        where,
        orderBy,
        include: {
          requester: true,
        },
        skip,
        take,
      });
      return fileHistoryList;
    } catch (err) {
      this.logger.error(err, 'failed to list log history ');
      throw new Error();
    }
  }

  async getCount(where: Record<string, number | string>) {
    try {
      const count = await this.prisma.fileHistory.count({ where });
      return count;
    } catch (err) {
      this.logger.error(err, 'failed to list log count ');
      throw new Error();
    }
  }

  //download by s3
  async downloadReport(s3FilePath: string) {
    try {
      const env = s3FilePath.includes('TEST')
        ? 'TEST'
        : s3FilePath.includes('LIVE')
          ? 'PROD'
          : null;
      if (!env) {
        throw new NotFoundException('Unable to determine environment from the S3 file path');
      }
      return this.s3Service.getObjectUnsafe(
        this.configService.get<string>(`s3.dailyReportBuckets.${env}`),
        s3FilePath,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async getSecurityDetectionReportList(params: {
    skip?: number;
    take?: number;
    request: UserRequest;
  }): Promise<FileHistoryEntity[]> {
    const { skip, take, request } = params;

    return await this.prisma.fileHistory.findMany({
      skip,
      take,
      select: {
        id: true,
        requesterId: true,
        fileType: true,
        createdAt: true,
        updatedAt: true,
        s3FilePath: true,
        status: true,
        errorMsg: true,
        dateFrom: true,
        dateTo: true,
      },
      where: { requesterId: request.user.id, fileType: FileType.SECURITY_DETECTION_REPORT },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getSecurityDetectionReportCount(request: UserRequest) {
    return await this.prisma.fileHistory.count({
      where: { requesterId: request.user.id, fileType: FileType.SECURITY_DETECTION_REPORT },
    });
  }

  async sendSecurityDetectionReport(body: any) {
    for (const email in body.emailReportMaps) {
      const emailReports = body.emailReportMaps[email];
      const fileHistoryIds = [];
      let temple = '';

      try {
        await Promise.all(
          emailReports.map(async (report) => {
            const { s3Path, historyId, env, template } = report;
            fileHistoryIds.push(historyId);
            this.logger.log(
              `Preparing attachment for email to ${email}: ${s3Path} -- ${historyId} -- ${env} -- ${template}`,
            );

            // const bucket = buckets[env];
            // if (!bucket) {
            //   throw new Error(`Unsupported environment: ${env}`);
            // }

            if (template == 'botowner') {
              temple = 'summary/botowner-detation-report';
            } else {
              temple = 'summary/security-detation-report';
            }

            // const content = await this.s3Service.get(bucket, s3Path);
            // return {
            //   filename: s3Path,
            //   content,
            // };
          }),
        );

        const res = await this.mailService.send({
          to: email,
          template: temple,
          data: {
            title: 'Gen AI Platform Prompt and Output Security Detection Report',
            GenAIHomepageURL: this.configService.get<string>('frontendUrl'),
          },
          // attachments,
        });
        this.logger.log(res, `Sending report to ${email} successful.`);

        await this.prisma.fileHistory.updateMany({
          where: {
            id: {
              in: fileHistoryIds.map((id) => parseInt(id)),
            },
          },
          data: {
            status: FileHistoryStatus.COMPLETED,
            updatedAt: new Date(),
          },
        });
      } catch (error) {
        this.logger.error(`Error sending email to ${email}: ${(error as Error).message}`);
        try {
          await this.prisma.fileHistory.updateMany({
            where: {
              id: {
                in: fileHistoryIds.map((id) => parseInt(id)),
              },
            },
            data: {
              status: FileHistoryStatus.ERROR,
              errorMsg: `Error sending email to ${email}: ${(error as Error).message}`,
              updatedAt: new Date(),
            },
          });
        } catch (updateError) {
          this.logger.error(
            `Failed to update fileHistory status to FAILED: ${(updateError as Error).message}`,
          );
        }
      }
    }
  }

  public async findCompletedByGeneratedParam(
    fromDate: Date,
    dateTo: Date,
    fileType: FileType,
    entityType?: FileEntityType,
    entityId?: number,
  ) {
    const fileHistory = await this.prisma.fileHistory.findFirst({
      where: {
        ...(entityType ? { entityType } : { entityType: null }),
        ...(entityId ? { entityId } : { entityId: null }),
        fileType,
        dateFrom: fromDate,
        dateTo: dateTo,
        status: FileHistoryStatus.COMPLETED,
      },
    });
    return fileHistory;
  }
}
