OPENAI_BACKEND_API_KEY: <same random value as X_API_KEY in lambda:bot-builder-gpt-service>
DATABASE_URL: postgresql://botbuilder:<DB_PASSWORD>@<DB_URL>:<DB_PORT>/botbuilderdb
DB_PASSWORD: <db pw, ignored when using DATABASE_URL>
SECRET_EMBEDDINGS: <same random value as SAAS_SECRET_EMBEDDINGS in lambda>
ELASTICSEARCH_AUTH_USERNAME: <opensearch username>
ELASTICSEARCH_AUTH_PASSWORD: <open search pw>
EMAIL_PASSWORD: <SMTP password>
MAXMIND_LICENSE_KEY: <optional, to get city by ip, untested feature>
GRAVITEE_API_KEY: <API key generated in gravitee console>
AWS_ACCESS_KEY_ID: <LOCAL ONLY, to access aws services, e.g. sqs for resource generation>
AWS_SECRET_ACCESS_KEY: <LOCAL ONLY, to access aws services, e.g. sqs for resource generation>
AWS_SESSION_TOKEN: <LOCAL ONLY, to access aws services, e.g. sqs for resource generation>
RESOURCE_CALLBACK_KEY: <resource generation callback key>
WIKIJS_API_KEY: <Full Access API key generated in Wiki.js manually>
SWAGGER_USER: swagger basic auth user
SWAGGER_USER_PASSWORD: swagger user basic auth password
SWAGGER_ADMIN: swagger basic auth admin 
SWAGGER_ADMIN_PASSWORD: swagger admin basic auth password
FLOWISE_API_KEY: <random value as Flowise API key>
CRON_JOB_API_KEY: An API Key for cron job access
REDIS_URL: redis://username:password@host:port/<db number>
POSTMAN_MONITOR_API_KEY: An API Key for Postman Monitors access
NOTIFICATION_API_KEY: An API Key for notification backend access
BULL_MQ_REDIS_URL: redis://username:password@host:port/<db number>
