import { Injectable, Logger } from '@nestjs/common';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { FlowBackendService } from '../../providers/flow-backend/flow-backend.service';
import {
  CreateGroupFlowRequest,
  CreateGroupFlowResponse,
  FlowChatRequest,
  FlowChatResponse,
  ChatType,
  UserIdentification,
  UpdateGroupFlowRequest,
  GetSpecificFlowResponse,
  InternalFlowChatResponse,
} from './flows.dto';
import {
  ChatMessageContentType,
  DataPromotionRequestActionType,
  DataPromotionRequestStatus,
  Environment,
  Flow,
  FlowBotStatus,
  Group,
  Prisma,
  PrismaClient,
  User,
} from '@prisma/client';
import {
  GetAllNodesResponse,
  GetSpecificChatflowResponse,
  PredictionResponse,
} from 'src/providers/flow-backend/flow-backend.interface';
import moment from 'moment';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../users/users.service';
import { Configuration } from 'src/config/configuration.interface';
import { ElasticSearchService } from 'src/providers/elasticsearch/elasticsearch.service';

import { ChatFlow } from './entity/chatFlow.entity';
import { PromotableService } from '../change-management/interfaces/promotable-service.interface';
import { UserRequest } from '../auth/auth.interface';
import { plainToClass } from 'class-transformer';
import { ScopeService } from '../scope/scope.service';
import { ROLE } from 'src/providers/llm-backend/llm-backend.interface';
import {
  ChannelType,
  HistoryMessage,
  SecurityScanType,
} from '../llm-models/dto/chat-llm-model.dto';
import { ChatSessionsService } from '../chat-sessions/chat-sessions.service';
import { FlowResponseUsage } from '../chat-sessions/dto/response-usage.dto';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { Response } from 'express';
import { ApiKeysService } from '../api-keys/api-keys.service';
import apm from 'elastic-apm-node';
import { BotSecurityService } from '../bot-security/bot-security.service';
@Injectable()
export class FlowsService implements PromotableService {
  private logger = new Logger(FlowsService.name);
  private readonly ALLOWED_DISPLAY_NODES = 'FLOWISE.CONFIG_ALLOWED_DISPLAY_NODES';

  constructor(
    private prisma: PrismaService,
    private flowBackendService: FlowBackendService,
    private configservice: ConfigService,
    private usersService: UsersService,
    private elasticSearchService: ElasticSearchService,
    private scopeService: ScopeService,
    private chatSessionsService: ChatSessionsService,
    private featureFlagService: FeatureFlagService,
    private apiKeysService: ApiKeysService,
    private readonly botSecurityService: BotSecurityService,
  ) {}

  async getSpecificFlowByGroupId(groupId: number): Promise<GetSpecificFlowResponse> {
    const flow = await this.prisma.flow.findFirst({
      where: {
        groupId: groupId,
      },
    });

    if (!flow) throw new ApiException(ErrorCode.FLOW_NOT_FOUND);

    return {
      id: flow.id,
      flowUuid: flow.flowUuid,
      createdAt: flow.createdAt,
      updatedAt: flow.updatedAt,
    } as GetSpecificFlowResponse;
  }

  async getSpecificFlow(groupId: number, flowUuid: string): Promise<GetSpecificChatflowResponse> {
    const flow = await this.prisma.flow.findFirst({
      where: { flowUuid: flowUuid },
    });

    this.validateIsRequestByMemberOfFlow(flow, groupId);

    return this.flowBackendService.getSpecificFlow(flowUuid);
  }

  async createGroupFlow(
    groupId: number,
    data: CreateGroupFlowRequest,
    tx?: Prisma.TransactionClient,
  ): Promise<CreateGroupFlowResponse> {
    this.logger.log('Create group flow');

    const prismaClient = tx ?? this.prisma;

    const group = await prismaClient.group.findUnique({
      where: {
        id: groupId,
      },
    });

    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);

    const response = await this.flowBackendService.createFlow(data.name);
    const flowUuid = response.id;

    const result = await prismaClient.flow.create({
      data: {
        flowUuid: flowUuid,
        group: { connect: { id: groupId } },
      },
      include: { group: { select: { name: true } } },
    });

    return {
      id: result.id,
      groupId: groupId,
      flowUuid: flowUuid,
      name: data.name,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    };
  }

  async updateGroupFlow(
    groupId: number,
    flowUuid: string,
    data: UpdateGroupFlowRequest,
  ): Promise<GetSpecificChatflowResponse> {
    const flow = await this.prisma.flow.findFirst({
      where: { flowUuid: flowUuid },
    });

    this.validateIsRequestByMemberOfFlow(flow, groupId);
    const response = await this.flowBackendService.updateFlow(flowUuid, data);

    return response;
  }

  async updateGroupFlowByGroupId(
    groupId: number,
    data: UpdateGroupFlowRequest,
  ): Promise<GetSpecificChatflowResponse> {
    const flow = await this.prisma.flow.findUnique({
      where: { groupId },
    });

    if (!flow) throw new ApiException(ErrorCode.FLOW_NOT_FOUND);

    const response = await this.flowBackendService.updateFlow(flow.flowUuid, data);
    return response;
  }

  async internalChat(
    req: UserRequest,
    res: Response,
    groupId: number,
    data: FlowChatRequest,
  ): Promise<InternalFlowChatResponse | Response<InternalFlowChatResponse>> {
    const { group, flow } = await this.validateFLowStatus(groupId);
    const enableOutputPIIFlag = await this.botSecurityService.isChatEnablePIIScan(
      data.overrides,
      data.channel ?? ChannelType.PLAYGROUND,
      [SecurityScanType.ON, SecurityScanType.OUTPUT],
    );
    const isStream = data.overrides?.stream || false;
    const startTime = moment();
    const question = data.history.slice(-1)[0].content;
    const user = await this.usersService.getUser(req.user.id);
    const result = await this.flowBackendService.internalPrediction(req, groupId, flow.flowUuid);

    this.storeChatLog(this.botSecurityService.convertContentsToString(question), ChatType.INTERNAL, group, startTime, result, {
      user: user as User,
    }).catch((error: Error) => this.logger.error(`storeChatLog ${error.message}`, error.stack));
    this.scopeService.checkChatDebugInfo(groupId, req.user.scopes, result);
    let response = this.formatFlowChatResponse(result) as InternalFlowChatResponse;
    response = {
      ...response,
      ...(data?.inputScannersResult
        ? {
            input_scanners_result: data.inputScannersResult,
          }
        : {}),
    };

    if (enableOutputPIIFlag) {
      const userAsk = data.history.filter((item) => item.role === 'user');
      const prompt = userAsk.at(-1).content;
      const outputScannerResult = await this.botSecurityService.outputDetect(
        prompt,
        response.answer,
        groupId,
        user.id,
      );
      response = {
        ...response,
        output_scanners_result: outputScannerResult?.output_scanners_result,
        output_scanners_result_is_valid: outputScannerResult?.output_scanners_result_is_valid,
      };
    }
    const isChatSession: boolean =
      (data.chatSessionType || data.chatSessionId) && req.user.id != undefined;
    if (isStream) {
      const streamData = `event: overall\ndata:${JSON.stringify(response)} \n\n`;
      res.write(streamData);
      res.end();
    }
    if (isChatSession) {
      const responseUsage: FlowResponseUsage = {
        tokenUsage: response.usage,
        toolUsage: response.toolUsage,
      };
      const chatSession = await this.chatSessionsService.findChatSessionOrCreateDefault(
        data.chatSessionId,
        data.chatSessionType,
        groupId,
        req.user.id,
      );
      await this.chatSessionsService.createChatHistory(
        chatSession.id,
        ChatMessageContentType.TEXT,
        response.message,
        responseUsage,
      );
    }
    return isStream ? response : res.json(response);
  }

  async chat(
    req: UserRequest,
    res: Response,
    groupId: number,
    data: FlowChatRequest,
  ): Promise<FlowChatResponse | Response<FlowChatResponse>> {
    const isStream = data.overrides?.stream || false;
    const { group, flow } = await this.validateFLowStatus(groupId);
    const startTime = moment();
    const question = data.history.slice(-1)[0].content;
    const result = await this.flowBackendService.prediction(req, groupId, flow.flowUuid, data);
    const apiKey = await this.apiKeysService.getApiKeyFromKey(req.headers['x-api-key'] as string);
    this.storeChatLog(this.botSecurityService.convertContentsToString(question), ChatType.EXTERNAL, group, startTime, result, {
      apiKey,
    }).catch((err: Error) => this.logger.error(`storeChatLog ${err.message}`, err.stack));
    this.scopeService.checkChatDebugInfo(groupId, apiKey.scopes as string[], result);
    const response = this.formatFlowChatResponse(result);
    if (isStream) {
      const streamData = `event: overall\ndata:${JSON.stringify(response)} \n\n`;
      res.write(streamData);
      res.end();
    }
    return isStream ? response : res.json(response);
  }

  private formatFlowChatResponse(response: PredictionResponse): FlowChatResponse {
    const flowChatResponse = plainToClass(FlowChatResponse, response, {
      excludeExtraneousValues: true,
    });
    const message: HistoryMessage = {
      role: ROLE.AI,
      content: response.answer,
    };
    flowChatResponse.message = message;
    return flowChatResponse;
  }
  // In Flow, the snapshot's entity data would be 'chatflow' entity from Flowise
  async generateEntityDataForSnapshot(groupId: number, entityId: string): Promise<object> {
    const flow = await this.prisma.flow.findFirst({
      where: {
        groupId,
        id: parseInt(entityId),
      },
    });

    if (!flow) throw new ApiException(ErrorCode.FLOW_NOT_FOUND);
    // Get chatflow details from Flowise, then use it as snapshot's entityData
    const chatflow = await this.flowBackendService.getSpecificFlow(flow.flowUuid);

    return chatflow;
  }

  // Since further promotion needs to handle existsing data, we only support first time promotion at this moment.
  async promoteCreate(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    entityData: ChatFlow,
    operatorId: number,
  ): Promise<string> {
    // Since PROD flow is created by default without promotion approval, call promoteUpdate here
    // TODO: When supporting multiple env, needs to update the logic of calling promoteUpdate
    const flowId = await tx.flow.findFirst({
      select: { id: true },
      where: { groupId: targetGroup.id },
    });
    if (!flowId) throw new ApiException(ErrorCode.FLOW_NOT_FOUND);
    await this.promoteUpdate(tx, targetGroup, flowId.id.toString(), entityData, operatorId);

    return flowId.id.toString();
  }

  async promoteUpdate(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string,
    entityData: ChatFlow,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    operatorId: number,
  ): Promise<void> {
    // There are 3 main steps for FLOW update promotion
    // 1. Upsert target env FlowBot relation as ACTIVE status (if needed)
    // 2. Update target env FlowBot deprecated relation as DELETED status (if needed)
    // 3. Update target chatflow's flowData on Flowise
    try {
      const targetFlow = await tx.flow.findUnique({ where: { id: Number(targetEntityId) } });
      if (!targetFlow) throw new ApiException(ErrorCode.FLOW_NOT_FOUND);

      const flowData = JSON.parse(entityData.flowData);

      const botFieldName = 'botId';
      const botFieldType = 'number';

      const botIds = flowData.nodes
        ? this.fetchFieldValuesFromJsonObject(flowData.nodes, botFieldName, botFieldType)
        : [];
      const botIdMap =
        botIds.length > 0 ? await this.mapTargetBotId(tx, botIds, targetGroup.env) : new Map();
      const targetBotIds = [...botIdMap.values()];

      // TODO: Should move this checking to createDataPromotionRequest
      // Check if the target bot promoted already
      if (targetBotIds.length > 0) {
        // Group record by targetGroupId. Supposed cannot delete LLM Model promotion, so no need to handle the DELETED case
        const promotedBotCount = await tx.dataPromotionRequest.groupBy({
          by: ['targetGroupId'],
          where: {
            actionType: DataPromotionRequestActionType.PROMOTE,
            status: DataPromotionRequestStatus.APPROVED,
            targetGroupId: {
              in: targetBotIds,
            },
          },
        });

        if (promotedBotCount.length !== targetBotIds.length) {
          this.logger.error('Cannot find promoted bot.');
          throw new ApiException(ErrorCode.INVALID_PROMOTE_FLOW_REQUEST_BOT_NOT_PROMOTED);
        }
      }

      // 1. Upsert target env FlowBot relation as ACTIVE status
      for (const [, targetBotId] of botIdMap) {
        await tx.flowBot.upsert({
          where: {
            flowGroupId_botGroupId: {
              flowGroupId: targetGroup.id,
              botGroupId: targetBotId,
            },
          },
          update: { status: FlowBotStatus.ACTIVE },
          create: {
            flowGroupId: targetGroup.id,
            botGroupId: targetBotId,
            status: FlowBotStatus.ACTIVE,
          },
        });
      }

      // 2. Update target env FlowBot deprecated relation as DELETED status
      const deleteFlowBotWhereInput: Prisma.FlowBotWhereInput = {
        flowGroupId: targetGroup.id,
      };

      if (botIdMap.size > 0) {
        deleteFlowBotWhereInput.botGroupId = { notIn: [...botIdMap.values()] };
      }

      const deleteFlowBots = await tx.flowBot.findMany({
        select: { botGroupId: true },
        where: deleteFlowBotWhereInput,
      });

      if (deleteFlowBots.length > 0) {
        await this.deleteFlowBotsByBotIds(tx, targetGroup.id, [
          ...deleteFlowBots.map((d) => d.botGroupId).values(),
        ]);
      }

      // 3. Update target chatflow's flowData on Flowise
      // Limit: Cannot rollback the update on Flowise if error is thrown afterwards
      flowData.nodes = this.replaceFieldValuesWithMap(flowData.nodes, botFieldName, botIdMap);
      const updatedFlowDataStr = JSON.stringify(flowData);
      await this.flowBackendService.updateFlow(targetFlow.flowUuid, {
        flowData: updatedFlowDataStr,
      });

      await tx.flow.update({
        data: {
          lastPromotedAt: new Date(),
        },
        where: {
          id: targetFlow.id,
        },
      });
    } catch (err) {
      if (err instanceof ApiException) {
        throw err;
      } else {
        this.logger.error(err);
        throw new ApiException(ErrorCode.PROMOTE_FLOW_FAILED);
      }
    }
  }

  // Currently not supporting delete flow
  async promoteDelete(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string,
    operatorId: number,
  ): Promise<void> {
    throw new ApiException(ErrorCode.DATA_PROMOTION_DELETE_NOT_SUPPORTED);
  }

  async checkPromotedEntityValid(targetEntityId: string) {
    return this.prisma.flow.findUnique({ where: { id: parseInt(targetEntityId) } });
  }
  private fetchFieldValuesFromJsonObject(
    jsonObj: Object,
    fieldName: string,
    fieldType?: string,
  ): number[] {
    const values: number[] = [];

    if (jsonObj === null || jsonObj === undefined) {
      return values;
    }

    for (const key in jsonObj) {
      if (key === fieldName) {
        if (!fieldType || typeof jsonObj[key] !== fieldType) return [];
        values.push(jsonObj[key]);
      } else if (typeof jsonObj[key] === 'object') {
        const nestedValues = this.fetchFieldValuesFromJsonObject(
          jsonObj[key],
          fieldName,
          fieldType,
        );
        if (nestedValues) {
          values.push(...nestedValues);
        }
      }
    }

    return [...new Set(values)]; // To remove duplicates
  }

  private async mapTargetBotId(
    tx: Prisma.TransactionClient,
    botIds: number[],
    env: Environment,
  ): Promise<Map<number, number>> {
    const botPairs = await tx.group.findMany({
      where: {
        id: {
          in: botIds,
        },
      },
      select: {
        pairId: true,
      },
    });

    const targetBots = await tx.group.findMany({
      where: {
        pairId: {
          in: botPairs.map((botPair) => botPair.pairId),
        },
        id: {
          notIn: botIds,
        },
        env: env,
      },
      select: {
        id: true,
        pairId: true,
      },
    });

    if (botIds.length !== targetBots.length) {
      this.logger.error('Cannot find bot in target env.');
      throw new ApiException(ErrorCode.INVALID_PROMOTE_FLOW_REQUEST);
    }

    // the map key 'pairId' is the id from origin env, so the map value is the target bot id
    const botIdMap = new Map<number, number>(
      targetBots.map((targetBot) => [targetBot.pairId, targetBot.id]),
    );

    return botIdMap;
  }

  private replaceFieldValuesWithMap(jsonObj: Object, fieldName: string, map: Map<any, any>) {
    for (const prop in jsonObj) {
      if (prop === fieldName) {
        jsonObj[prop] = map.get(jsonObj[prop]);
      } else if (typeof jsonObj[prop] === 'object') {
        this.replaceFieldValuesWithMap(jsonObj[prop], fieldName, map);
      }
    }

    return jsonObj;
  }

  async validateFLowStatus(groupId: number): Promise<{ group: Group; flow: Flow }> {
    const group = await this.prisma.group.findUnique({
      include: {
        flow: true,
      },
      where: {
        id: groupId,
      },
    });

    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    if (!group.flow) throw new ApiException(ErrorCode.FLOW_NOT_FOUND);
    return {
      group,
      flow: group.flow,
    };
  }

  async storeChatLog(
    question: string,
    chatType: ChatType,
    group: Group,
    startTime: moment.Moment,
    response: PredictionResponse,
    userIdentification: UserIdentification,
  ) {
    const durationInMS = moment().diff(startTime, 'milliseconds');
    const config = this.configservice.get<Configuration['tracking']>('tracking');
    const flowUsage = response.usage.reduce(
      (result, item) => {
        result.promptTokens += item.promptTokens;
        result.completionTokens += item.completionTokens;
        result.totalCompletionTokens += item.totalCompletionTokens;
        result.embeddingTokens += item.embeddingTokens ?? 0;
        return result;
      },
      {
        promptTokens: 0,
        completionTokens: 0,
        totalCompletionTokens: 0,
        embeddingTokens: 0,
      },
    );
    const logData = {
      date: startTime,
      flowId: group.id,
      flowEnv: group.env,
      flowUsage: flowUsage,
      flowName: group.name,
      query: String(question).trim(),
      answer: response.answer.trim(),
      durationInMS: durationInMS,
      botUsage: response.usage,
      toolUsage: response.toolUsage,
      callChain: JSON.stringify(response.callChain),
      traceId: apm?.currentTraceIds['trace.id'],
    } as any;
    if (chatType == ChatType.EXTERNAL) {
      logData.channel = 'API_KEY';
      logData.requesterId = userIdentification.apiKey.id;
      logData.requesterName = userIdentification.apiKey.name;
    } else {
      logData.requesterId = userIdentification.user?.id;
      logData.requesterName = userIdentification.user?.name;
      logData.requesterStaffId = userIdentification.user?.staffId;
      if (logData.requesterId) {
        const emailRes = await this.prisma.email.findFirst({
          where: { userId: logData.requesterId },
        });
        logData.requesterEmail = emailRes?.email;
      }
      logData.channel = 'PLAYGROUND';
    }
    try {
      this.elasticSearchService.logChatRecord(
        `${config.index}-${moment(startTime).format('YYYY-MM-DD')}`,
        logData,
      );
    } catch (error) {
      this.logger.error(error, 'error in elastic search');
    }
  }

  validateIsRequestByMemberOfFlow(flow: Flow, requestGroupId: number) {
    // Check if flow's groupId is matching the requested groupId
    if (!flow) throw new ApiException(ErrorCode.FLOW_NOT_FOUND);
    if (flow.groupId !== requestGroupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
  }

  async deleteFlowBotsByBotIds(
    tx: Prisma.TransactionClient,
    flowGroupId: number,
    botIds: number[],
  ) {
    await tx.flowBot.updateMany({
      data: {
        status: FlowBotStatus.DELETED,
      },
      where: {
        flowGroupId: flowGroupId,
        botGroupId: { in: botIds },
      },
    });
  }

  async deleteEntityDataForSnapshot(
    groupId: number,
    entityId: string,
    entityData: any,
  ): Promise<void> {
    return;
  }

  async getAllNodes(groupId: number): Promise<GetAllNodesResponse[]> {
    let nodes = [];

    const allowNodesFlag = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      groupId,
      this.ALLOWED_DISPLAY_NODES,
    );
    if (allowNodesFlag?.isEnabled) {
      const allowedNodes = allowNodesFlag.metaData['value']
        ? (allowNodesFlag.metaData['value'] as Prisma.JsonArray)
        : [];
      nodes = await this.flowBackendService.getAllNodes();
      nodes = nodes.filter((item) => {
        for (const index in allowedNodes) {
          const node = allowedNodes[index];
          const name = item.name;
          if (node === name) {
            return true;
          }
        }
        return false;
      });
    }
    this.logger.log(`allowNodesFlag : ${allowNodesFlag}`);
    return nodes;
  }
}
