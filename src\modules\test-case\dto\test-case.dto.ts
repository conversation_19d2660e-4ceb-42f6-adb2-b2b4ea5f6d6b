import { IsDefined, IsIn, IsNotEmpty, IsOptional } from 'class-validator';
import { MODEL } from 'src/providers/llm-backend/llm-backend.interface';

export class TestCaseLLMParams {
  @IsDefined()
  @IsIn(Object.values(MODEL))
  model: MODEL;

  @IsOptional()
  top?: number;

  @IsOptional()
  max_tokens?: number;

  @IsOptional()
  top_p?: number;

  @IsOptional()
  temperature?: number;

  @IsOptional()
  presence_penalty?: number;

  @IsOptional()
  frequency_penalty?: number;
}

export class TestCaseConfig {
  @IsDefined()
  @IsNotEmpty()
  question: string;

  @IsOptional()
  validationPrompt?: string;
}

export enum TestCaseType {
  CHAT_WITH_TEXT,
}

export enum TestCaseStatus {
  ENABLED,
  DISABLE,
}

export enum ExcelHeaderKey {
  question = 'question',
  validationPrompt = 'validationPrompt',
}

export enum ByPassPiiType {
  ALL,
  PROMPT_INJECTION,
  ANONYMIZE,
}
