/*
  Warnings:

  - You are about to drop the column `historyId` on the `DataPromotionRequest` table. All the data in the column will be lost.
  - You are about to drop the column `promotedModelId` on the `DataPromotionRequest` table. All the data in the column will be lost.
  - You are about to drop the `PrismaModelHistory` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `actionType` to the `DataPromotionRequest` table without a default value. This is not possible if the table is not empty.
  - Added the required column `entitySnapshotId` to the `DataPromotionRequest` table without a default value. This is not possible if the table is not empty.

*/

-- Custom SQL: remove function & triggers for chanage management
DROP FUNCTION IF EXISTS history_trigger_function() CASCADE;
-- Custom SQL
TRUNCATE TABLE "DataPromotionRequest";

-- CreateEnum
CREATE TYPE "DataPromotionRequestActionType" AS ENUM ('PROMOTE', 'DELETE');

-- CreateEnum
CREATE TYPE "SnapshotEntityType" AS ENUM ('API_KEY', 'LLM_MODEL');

-- DropForeignKey
ALTER TABLE "DataPromotionRequest" DROP CONSTRAINT "DataPromotionRequest_historyId_fkey";

-- AlterTable
ALTER TABLE "DataPromotionRequest" DROP COLUMN "historyId",
DROP COLUMN "promotedModelId",
ADD COLUMN     "actionType" "DataPromotionRequestActionType" NOT NULL,
ADD COLUMN     "entitySnapshotId" INTEGER NOT NULL,
ADD COLUMN     "promotedEntityId" TEXT;

-- DropTable
DROP TABLE "PrismaModelHistory";

-- CreateTable
CREATE TABLE "EntitySnapshot" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "entityType" "SnapshotEntityType" NOT NULL,
    "entityId" TEXT NOT NULL,
    "groupId" INTEGER NOT NULL,
    "entityData" JSONB NOT NULL,
    "versionDate" TIMESTAMP(3) NOT NULL,
    "createdByUserId" INTEGER NOT NULL,

    CONSTRAINT "EntitySnapshot_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "EntitySnapshot" ADD CONSTRAINT "EntitySnapshot_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EntitySnapshot" ADD CONSTRAINT "EntitySnapshot_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DataPromotionRequest" ADD CONSTRAINT "DataPromotionRequest_entitySnapshotId_fkey" FOREIGN KEY ("entitySnapshotId") REFERENCES "EntitySnapshot"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
