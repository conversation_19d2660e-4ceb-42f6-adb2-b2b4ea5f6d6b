import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import {
  Environment,
  GroupType,
  PlanSubscriptionRequestStatus,
  Prisma,
  ResourceEntityType,
  ResourceSubsciberType,
} from '@prisma/client';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import {
  CreateOrUpdateCustomPlanDto,
  CreatePlanRequestDto,
  GetResourcePlanDto,
  PlanResourceHandler,
  ResourcePlanCustomDto,
  SubscribeResourcePlanDto,
} from './plans.dto';
import { LlmEnginesService } from '../llm-engines/llm-engines.service';
import { RedisService } from 'src/providers/redis/redis.service';
import {
  GROUP_RESOURCE_QUOTA_KEY,
  GROUP_RESOURCE_SCOPE_KEY,
  USER_SCOPE_KEY,
} from 'src/providers/redis/redis.constants';
import {
  CreateBotResourcePlanDto,
  SetBotResourcePlanActivateDto,
  SetBotResourcePlanDefaultDto,
  UpdateBotResourcePlanDto,
} from '../groups/groups.dto';
import { v4 } from 'uuid';

@Injectable()
export class PlansService {
  private readonly planResourceHandler: PlanResourceHandler;
  private logger = new Logger(PlansService.name);

  constructor(
    private prisma: PrismaService,
    @Inject(forwardRef(() => LlmEnginesService))
    private llmEngineService: LlmEnginesService,
    private redisService: RedisService,
  ) {
    this.planResourceHandler = {
      [ResourceEntityType.LLM_ENGINE]: { service: llmEngineService },
    };
  }

  async getUsageUpdatedDate() {
    const summary = await this.prisma.summary.findFirst({
      select: { endDate: true },
      orderBy: { endDate: 'desc' },
    });

    return { latestDate: summary?.endDate ?? null };
  }

  async getResourceCategory(subscriberType: ResourceSubsciberType) {
    return await this.prisma.resourceCategory.findMany({
      where: {
        subscriberTypes: { has: subscriberType },
      },
    });
  }

  async getAllResourceCategory(isGroupResource: boolean) {
    return await this.prisma.resourceCategory.findMany({
      where: {
        subscriberTypes: {
          hasSome: isGroupResource ? (Object.keys(GroupType) as ResourceSubsciberType[]) : ['USER'],
        },
      },
    });
  }

  async getResources(subscriberType: ResourceSubsciberType, subscriberId: number) {
    const resources = await this.prisma.resource.findMany({
      include: {
        quotaRule: true,
        permission: true,
      },
      where: {
        subscriberTypes: { has: subscriberType },
      },
      orderBy: {
        resourceName: 'asc',
      },
    });
    const getResources = resources.map(async (resource) => {
      const isEnabled =
        (await this.planResourceHandler?.[resource.resourceEntityType]?.service?.getPlanIsEnabled({
          subscriberId,
          resourceKey: resource.resourceKey,
        })) ?? true;
      const planUsage =
        (await this.planResourceHandler?.[resource.resourceEntityType]?.service?.getResourceUsage({
          resourceId: resource.id,
          resourceKey: resource.resourceKey,
          subscriberId,
        })) ?? null;
      return {
        ...resource,
        planUsage,
        isEnabled,
      };
    });
    return await Promise.all(getResources);
  }

  async getAllResources(isGroupResource: boolean, env?: Environment) {
    const resources = await this.prisma.resource.findMany({
      include: {
        quotaRule: true,
        permission: true,
      },
      where: {
        subscriberTypes: {
          hasSome: isGroupResource ? (Object.keys(GroupType) as ResourceSubsciberType[]) : ['USER'],
        },
      },
      orderBy: {
        resourceName: 'asc',
      },
    });
    if (resources.length === 0) return [];
    const getResources = resources.map(async (resource) => {
      const isEnabled =
        (await this.planResourceHandler?.[resource.resourceEntityType]?.service?.getPlanIsEnabled({
          env,
          resourceKey: resource.resourceKey,
        })) ?? true;
      return {
        ...resource,
        isEnabled,
      };
    });
    return await Promise.all(getResources);
  }

  async getResourcePlans(data: GetResourcePlanDto) {
    // get the available plans
    const plans = await this.prisma.plan.findMany({
      include: {
        resource: {
          select: { resourceKey: true, resourceEntityType: true, resourceEntityKey: true },
        },
        planQuotas: {
          select: { quotaValue: true },
        },
      },
      where: {
        AND: [
          {
            OR: [
              {
                planRoleIdsRequired: {
                  isEmpty: true,
                },
              },
              data.requiredPlanRoleId
                ? { planRoleIdsRequired: { hasSome: [data.requiredPlanRoleId] } }
                : {},
            ],
          },
          {
            resource: {
              subscriberTypes: { has: data.subscriberType },
            },
          },
          {
            OR: [
              { entityId: null, entityType: null },
              { entityId: data.subscriberId, entityType: data.subscriberType },
            ],
          },
          { ...(data.groupEnv ? { groupEnv: data.groupEnv } : {}) },
        ],
      },
    });
    plans.sort((a, b) => {
      const seqA = a.planQuotas[0]?.quotaValue.value ?? Infinity;
      const seqB = b.planQuotas[0]?.quotaValue.value ?? Infinity;
      return seqA - seqB;
    });
    if (plans.length === 0) return [];

    // get the plan subscribed status
    const getPlans = plans.map(async (plan) => {
      let isSubscribed = false;
      const planSubscriptionCount = await this.prisma.planSubscription.count({
        where: {
          plan: { resource: { id: plan.resourceId } },
          subscriberEntityId: data.subscriberId,
          subscriberEntityType: data.subscriberType,
        },
      });
      // if no plan subscription, fallback to default plan subscription
      if (planSubscriptionCount === 0) {
        isSubscribed = plan.isDefault;
      } else {
        const planSubscription = await this.prisma.planSubscription.findFirst({
          where: {
            planId: plan.id,
            subscriberEntityId: data.subscriberId,
            subscribeEndDate: null,
            subscriberEntityType: data.subscriberType,
          },
        });
        isSubscribed = !!planSubscription;
      }

      const isResourceEnabled =
        (await this.planResourceHandler?.[
          plan.resource.resourceEntityType
        ]?.service?.getPlanIsEnabled({
          subscriberId: data.subscriberId,
          resourceKey: plan.resource.resourceKey,
        })) ?? true;

      const isCustomPlan = !!plan?.entityId && !!plan?.entityType;

      return {
        ...plan,
        isSubscribed,
        isResourceEnabled,
        isCustomPlan,
      };
    });
    return await Promise.all(getPlans);
  }

  async getAllResourcePlans(isGroupResource: boolean, env?: Environment) {
    const plans = await this.prisma.plan.findMany({
      include: {
        resource: {
          select: { resourceKey: true, resourceEntityType: true, resourceEntityKey: true },
        },
        planQuotas: {
          select: { quotaValue: true },
        },
      },
      where: {
        ...(env ? { groupEnv: env } : {}),
        entityId: null,
        entityType: null,
        resource: {
          subscriberTypes: {
            hasSome: isGroupResource
              ? (Object.keys(GroupType) as ResourceSubsciberType[])
              : ['USER'],
          },
        },
      },
    });
    plans.sort((a, b) => {
      const seqA = a.planQuotas[0]?.quotaValue.value ?? Infinity;
      const seqB = b.planQuotas[0]?.quotaValue.value ?? Infinity;
      return seqA - seqB;
    });
    const getPlans = plans.map(async (plan) => {
      const planSubscriptionCount = await this.prisma.planSubscription.count({
        where: {
          planId: plan.id,
          subscribeEndDate: null,
          subscriberEntityType: {
            in: isGroupResource ? (Object.keys(GroupType) as ResourceSubsciberType[]) : ['USER'],
          },
        },
      });
      return { ...plan, planSubscriptionCount };
    });
    return Promise.all(getPlans);
  }

  async getResourcePlansCount(data: GetResourcePlanDto) {
    const planCount = await this.prisma.plan.count({
      where: {
        AND: [
          {
            OR: [
              {
                planRoleIdsRequired: {
                  isEmpty: true,
                },
              },
              data.requiredPlanRoleId
                ? { planRoleIdsRequired: { hasSome: [data.requiredPlanRoleId] } }
                : {},
            ],
          },
          {
            resource: {
              subscriberTypes: { has: data.subscriberType },
            },
          },
          {
            OR: [
              { entityId: null, entityType: null },
              { entityId: data.subscriberId, entityType: data.subscriberType },
            ],
          },
          { ...(data.groupEnv ? { groupEnv: data.groupEnv } : {}) },
        ],
      },
    });
    return planCount;
  }

  async getPlanRequests(
    where: Prisma.PlanSubscriptionRequestWhereInput,
    orderBy: Prisma.PlanSubscriptionRequestOrderByWithRelationInput,
    isGroupRequest: boolean,
    skip?: number,
    take?: number,
  ) {
    return await this.prisma.planSubscriptionRequest.findMany({
      orderBy,
      where,
      skip,
      take,
      select: {
        id: true,
        ...(isGroupRequest
          ? { subscribedGroup: { select: { name: true, id: true, env: true } } }
          : {}),
        creator: { select: { name: true } },
        operator: { select: { name: true } },
        status: true,
        operatedDate: true,
        createdDate: true,
        rejectReason: true,
      },
    });
  }

  async getPlanRequestCount(where: Prisma.PlanSubscriptionRequestWhereInput) {
    return await this.prisma.planSubscriptionRequest.count({
      where,
    });
  }

  async getPlanRequestDetail(
    requestId: number,
    isGroupRequest: boolean,
    subscribedEntityId?: number,
  ) {
    const res = await this.prisma.planSubscriptionRequest.findUnique({
      where: { id: requestId },
      select: {
        id: true,
        ...(isGroupRequest
          ? { subscribedGroup: { select: { name: true, id: true, ccc: true, env: true } } }
          : {}),
        subscriberEntityId: true,
        subscriberEntityType: true,
        creator: { select: { name: true } },
        operator: { select: { name: true } },
        status: true,
        operatedDate: true,
        createdDate: true,
        subscribedPlanIds: true,
        customPlans: true,
        crNum: true,
        rejectReason: true,
      },
    });
    if (subscribedEntityId && res.subscriberEntityId !== subscribedEntityId) {
      this.logger.error(`Invalid plan request detail , request Id - ${requestId}`);
      throw new ApiException(ErrorCode.INVALID_PLAN_REQUEST);
    }
    return res;
  }

  async subscribeResourcePlans(data: SubscribeResourcePlanDto) {
    if (data.subscribedPlanIds.length === 0) {
      return [];
    }
    const resources = await this.prisma.resource.findMany({
      select: { id: true },
      where: {
        plans: {
          some: {
            id: {
              in: data.subscribedPlanIds,
            },
          },
        },
      },
    });
    // To make sure plan and resource are valid
    if (!resources || resources.length === 0) {
      throw new ApiException(ErrorCode.RESOURCE_NOT_FOUND);
    }

    const plans = await this.prisma.plan.findMany({
      select: { id: true },
      where: {
        id: { in: data.subscribedPlanIds },
      },
    });
    if (!plans && plans.length !== data.subscribedPlanIds.length) {
      throw new ApiException(ErrorCode.PLAN_NOT_FOUND);
    }

    const result = await this.prisma.$transaction(
      async (tx) => {
        if (!(await this.validateSubmittedPlanIds(data.subscribedPlanIds))) {
          this.logger.error(`Invalid submitted plan ids - ${data.subscribedPlanIds}`);
          throw new ApiException(ErrorCode.INVALID_PLAN);
        }
        // Mark the existing plan subscription related to the resource as ended
        await tx.planSubscription.updateMany({
          where: {
            plan: {
              resourceId: { in: resources.map((resource) => resource.id) },
            },
            subscriberEntityType: data.subscriberType,
            subscriberEntityId: data.subscriberId,
            subscribeEndDate: null,
          },
          data: {
            subscribeEndDate: new Date(),
          },
        });

        // Create new plan subscription
        return await tx.planSubscription.createMany({
          data: plans.map((plan) => ({
            planId: plan.id,
            subscriberEntityType: data.subscriberType,
            subscriberEntityId: data.subscriberId,
            subscribeStartDate: new Date(),
            subscribeEndDate: null,
          })),
        });
      },
      { timeout: 50000 },
    );
    // clear quota and permission cache when subscribed group type plan
    if (Object.keys(GroupType).includes(data.subscriberType as ResourceSubsciberType)) {
      const permissionCacheKey = GROUP_RESOURCE_SCOPE_KEY.replace(
        '{GROUP_ID}',
        data.subscriberId.toString(),
      );
      const quotaCacheKey = GROUP_RESOURCE_QUOTA_KEY.replace(
        '{GROUP_ID}',
        data.subscriberId.toString(),
      );
      this.logger.log(
        `clear group cache due to subscribing new plans, permission cache - ${permissionCacheKey}, quota cache - ${quotaCacheKey}`,
      );
      await this.redisService.batchClearCache(permissionCacheKey);
      await this.redisService.batchClearCache(quotaCacheKey);
    }
    if (data.subscriberType === ResourceSubsciberType.USER) {
      const permissionCacheKey = USER_SCOPE_KEY.replace('{USER_ID}', data.subscriberId.toString());
      this.logger.log(
        `clear user cache due to subscribing new plans, permission cache - ${permissionCacheKey}`,
      );
      await this.redisService.batchClearCache(permissionCacheKey);
    }
    return result;
  }

  async createPlanRequest(data: CreatePlanRequestDto) {
    // check subscribedPlanIds are valid
    const planCount = await this.prisma.plan.count({
      where: {
        id: { in: data.subscribedPlanIds },
      },
    });
    if (planCount !== data.subscribedPlanIds.length) {
      throw new ApiException(ErrorCode.PLAN_NOT_FOUND);
    }
    if (
      !(await this.validateSubmittedPlanIds(data.subscribedPlanIds)) ||
      !this.validateSubmittedCustomPlanRequest(data.customPlans)
    ) {
      this.logger.error(data, 'invalid created plan request');
      throw new ApiException(ErrorCode.INVALID_PLAN);
    }
    this.logger.log(data, 'created plan request successfully');
    return await this.prisma.planSubscriptionRequest.create({
      data: {
        subscriberEntityId: data.subscriberId,
        subscriberEntityType: data.subscriberType,
        createdBy: data.requesterId,
        subscribedPlanIds: data.subscribedPlanIds,
        customPlans: data.customPlans as unknown as Prisma.JsonValue[],
        status: PlanSubscriptionRequestStatus.PENDING,
      },
    });
  }

  async approvePlanRequest(requestId: number, operaterId: number, crNum?: string) {
    const planSubscriptionRequest = await this.prisma.planSubscriptionRequest.findUnique({
      select: {
        subscribedPlanIds: true,
        subscriberEntityId: true,
        subscriberEntityType: true,
        customPlans: true,
      },
      where: { id: requestId },
    });

    const customPlansChanges =
      planSubscriptionRequest.customPlans as unknown as CreateOrUpdateCustomPlanDto[];
    // If approved ,apply the custom plan setting change and return custom plan that needed to be subscribed
    const subscribedCustomPlanIds = await this.applyCustomPlanSettingAndReturnPlanIds(
      customPlansChanges,
      planSubscriptionRequest.subscriberEntityId,
      planSubscriptionRequest.subscriberEntityType,
    );
    // If approved, the plan will be subscribed
    await this.subscribeResourcePlans({
      subscribedPlanIds: [...planSubscriptionRequest.subscribedPlanIds, ...subscribedCustomPlanIds],
      subscriberId: planSubscriptionRequest.subscriberEntityId,
      subscriberType: planSubscriptionRequest.subscriberEntityType,
    });
    this.logger.log(`subscribed new plans successfully - ${requestId}`);
    // Change the status of the plan subscription request to approved
    return await this.prisma.planSubscriptionRequest.update({
      where: { id: requestId },
      data: {
        status: PlanSubscriptionRequestStatus.APPROVED,
        operatedDate: new Date(),
        operatedBy: operaterId,
        crNum,
      },
    });
  }

  async rejectPlanRequest(
    requestId: number,
    operaterId: number,
    crNum?: string,
    rejectReason?: string,
  ) {
    // Change the status of the plan subscription request to rejected
    return await this.prisma.planSubscriptionRequest.update({
      where: { id: requestId },
      data: {
        status: PlanSubscriptionRequestStatus.REJECTED,
        operatedDate: new Date(),
        operatedBy: operaterId,
        crNum,
        rejectReason,
      },
    });
  }

  async applyCustomPlanSettingAndReturnPlanIds(
    data: CreateOrUpdateCustomPlanDto[],
    subscriberId: number,
    subscriberEntityType: ResourceSubsciberType,
  ) {
    let groupEnv = null;
    if (Object.keys(GroupType).includes(subscriberEntityType as ResourceSubsciberType)) {
      if (!this.validateSubmittedCustomPlanRequest(data)) {
        this.logger.error(data, 'invalid custom plan request');
        throw new ApiException(ErrorCode.INVALID_PLAN);
      }
      const { env } = await this.prisma.group.findUnique({
        where: { id: subscriberId },
        select: { env: true },
      });
      groupEnv = env;
    }
    const appliedSubscribedCustomPlanId = [];
    for (const customPlan of data) {
      const customPlanName = 'Custom (Quota: ' + customPlan.quotaValue + ')';
      const plan = await this.prisma.plan.findFirst({
        select: { id: true },
        where: {
          resourceId: customPlan.resourceId,
          entityId: subscriberId,
          entityType: subscriberEntityType,
        },
      });
      // update existed custom plan if found
      if (plan) {
        const res = await this.updateResourcePlan(
          {
            planId: plan.id,
            name: customPlanName,
            description: '',
            quotaValue: customPlan.quotaValue,
          },
          {
            entityType: subscriberEntityType,
            entityId: subscriberId,
            groupEnv,
          },
        );
        appliedSubscribedCustomPlanId.push(res.id);
      }
      // create custom plan
      else {
        const res = await this.createResourcePlan(
          {
            name: customPlanName,
            description: '',
            resourceId: customPlan.resourceId,
            quotaValue: customPlan.quotaValue,
          },
          {
            entityType: subscriberEntityType,
            entityId: subscriberId,
            groupEnv,
          },
        );
        appliedSubscribedCustomPlanId.push(res.id);
      }
    }
    return appliedSubscribedCustomPlanId;
  }

  async createResourcePlan(data: CreateBotResourcePlanDto, customData?: ResourcePlanCustomDto) {
    const randomString = v4();
    return await this.prisma.$transaction(
      async (tx) => {
        // get the related permission from plan resource
        const { permissionId, quotaRule, resourceKey } = await tx.resource.findUnique({
          select: {
            permissionId: true,
            quotaRule: { select: { ruleKey: true, id: true } },
            resourceKey: true,
          },
          where: { id: data.resourceId },
        });

        // create plan
        const createdPlan = await tx.plan.create({
          data: {
            planKey: resourceKey + '-plan-' + randomString,
            planName: data.name,
            description: data.description,
            groupEnv: data?.env ?? null,
            isDefault: false,
            isDisabledPlan: false,
            resourceId: data.resourceId,
            ...(customData ? { ...customData } : {}),
          },
        });
        // create plan permission if permissionId exists
        if (permissionId) {
          await tx.planPermission.create({ data: { planId: createdPlan.id, permissionId } });
        }
        // create quota value
        if (quotaRule && typeof data?.quotaValue === 'number') {
          const createdQuotaValue = await tx.resourceQuotaValue.create({
            data: {
              quotaKey: quotaRule.ruleKey + '-' + randomString,
              value: data.quotaValue,
              ruleId: quotaRule.id,
            },
          });
          await tx.planQuota.create({
            data: {
              planId: createdPlan.id,
              quotaValueId: createdQuotaValue.id,
            },
          });
        }
        return createdPlan;
      },
      { timeout: 50000 },
    );
  }

  async updateResourcePlan(data: UpdateBotResourcePlanDto, customData?: ResourcePlanCustomDto) {
    const plan = await this.prisma.plan.findUnique({
      select: { id: true },
      where: { id: data.planId },
    });
    if (!plan) {
      throw new ApiException(ErrorCode.PLAN_NOT_FOUND);
    }
    return await this.prisma.$transaction(async (tx) => {
      const updatedPlan = await tx.plan.update({
        where: { id: data.planId },
        data: {
          planName: data.name,
          description: data.description,
          ...(customData ? { ...customData } : {}),
        },
      });
      // update quota value
      if (data.quotaValue) {
        const quota = await tx.planQuota.findFirst({
          where: { planId: data.planId },
          select: { quotaValueId: true },
        });
        if (quota) {
          await tx.resourceQuotaValue.update({
            where: { id: quota.quotaValueId },
            data: { value: data.quotaValue },
          });
          // clear quota cache of all group
          const quotaCacheKey = GROUP_RESOURCE_QUOTA_KEY.replace('{GROUP_ID}', '*');
          await this.redisService.batchClearCache(quotaCacheKey);
        }
      }
      return updatedPlan;
    });
  }

  async setResourcePlanDefault(data: SetBotResourcePlanDefaultDto) {
    const plan = await this.prisma.plan.findUnique({
      select: { id: true, resourceId: true, groupEnv: true },
      where: { id: data.planId },
    });
    if (!plan) {
      throw new ApiException(ErrorCode.PLAN_NOT_FOUND);
    }
    await this.redisService.batchClearCache(GROUP_RESOURCE_SCOPE_KEY.replace('{GROUP_ID}', '*'));
    await this.redisService.batchClearCache(GROUP_RESOURCE_QUOTA_KEY.replace('{GROUP_ID}', '*'));
    return await this.prisma.$transaction(async (tx) => {
      // update the plans related to same resource to false
      await tx.plan.updateMany({
        where: { resourceId: plan.resourceId, groupEnv: plan.groupEnv },
        data: {
          isDefault: false,
        },
      });
      // set default plan
      return await tx.plan.update({ where: { id: data.planId }, data: { isDefault: true } });
    });
  }

  async setResourcePlanActivate(data: SetBotResourcePlanActivateDto) {
    const plan = await this.prisma.plan.findUnique({
      select: { isActive: true, isDefault: true, isDisabledPlan: true },
      where: { id: data.planId },
    });
    if (!plan.isActive) {
      return this.prisma.plan.update({ where: { id: data.planId }, data: { isActive: true } });
    }
    if (plan.isDefault || plan.isDisabledPlan) {
      this.logger.error(plan, 'failed to deactivate plan');
      throw new ApiException(ErrorCode.DEACTIVATE_PLAN_FAILED);
    }
    return this.prisma.plan.update({ where: { id: data.planId }, data: { isActive: false } });
  }

  async getResourceSubscribedPlan(
    subscriberId: number,
    subscriberType: ResourceSubsciberType,
    resourceKey: string,
  ) {
    const planSubscription = await this.prisma.planSubscription.findFirst({
      select: { plan: true },
      where: {
        subscriberEntityId: subscriberId,
        subscribeEndDate: null,
        subscriberEntityType: subscriberType,
        plan: { resource: { resourceKey } },
      },
    });
    // if no plan subscription, fallback to default plan subscription
    if (!planSubscription) {
      let groupEnv = null;
      if (Object.keys(GroupType).includes(subscriberType as ResourceSubsciberType)) {
        const group = await this.prisma.group.findUnique({
          where: { id: subscriberId },
          select: { env: true },
        });
        groupEnv = group.env;
      }
      return await this.prisma.plan.findFirst({
        where: {
          resource: { resourceKey },
          groupEnv,
          isDefault: true,
        },
      });
    } else {
      return planSubscription.plan;
    }
  }

  async getQuotaValueFromPlan(planId: number) {
    const quota = await this.prisma.planQuota.findFirst({
      where: { planId },
      select: { quotaValue: true },
    });
    return quota.quotaValue;
  }

  async validateSubmittedPlanIds(planIds: number[]) {
    const plans = await this.prisma.plan.findMany({
      where: { id: { in: planIds } },
      select: { resourceId: true, id: true, isActive: true },
    });
    const reosurceIdMap = new Map<number, number>();
    for (const plan of plans) {
      if (!plan.isActive) {
        return false;
      }
      if (reosurceIdMap.has(plan.resourceId)) {
        return false;
      }

      reosurceIdMap.set(plan.resourceId, plan.id);
    }
    return true;
  }
  validateSubmittedCustomPlanRequest(customPlans: CreateOrUpdateCustomPlanDto[]) {
    for (const customPlan of customPlans) {
      if (typeof customPlan.quotaValue !== 'number' || typeof customPlan.resourceId !== 'number')
        return false;
    }
    return true;
  }
}
