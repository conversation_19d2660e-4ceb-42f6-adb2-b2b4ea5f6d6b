import { IsBoolean } from 'class-validator';
import { MODEL } from '../../../providers/llm-backend/llm-backend.interface';
import {} from 'class-validator';
import { ApiProperty, ApiPropertyOptional, getSchemaPath } from '@nestjs/swagger';

export class AzureSpeechTtsOverrides {
  @ApiProperty({ description: 'The text to be synthesized.' })
  text: string;

  @ApiProperty({ description: 'The voice name', default: 'zh-HK-HiuGaaiNeural' })
  voice_name: string;

  @ApiPropertyOptional({ description: 'stream response', default: false})
  stream?: boolean;
}

export class AzureSpeechSttOverrides {
  @ApiProperty({ description: 'The speech file to be converted.' })
  file: string;

  @ApiProperty({ description: 'The recognition language.', default: 'zh-HK' })
  recognition_language: string;

  @ApiPropertyOptional({ description: 'stream response', default: false })
  stream?: boolean;
}

export class GenerateLlmModelDto {
  @IsBoolean()
  @ApiPropertyOptional({ description: 'Make the generation in async or not', default: false })
  async?: boolean;

  @ApiProperty({ enum: MODEL, description: 'Model name.', default: MODEL.AZURE_SPEECH })
  model: MODEL;

  @ApiProperty({ description: 'Function name to be run.' })
  function: string;

  @ApiProperty({
    oneOf: [
      { $ref: getSchemaPath(AzureSpeechTtsOverrides) },
      { $ref: getSchemaPath(AzureSpeechSttOverrides) },
    ],
  })
  overrides: AzureSpeechTtsOverrides | AzureSpeechSttOverrides;
}
