
-- CreateEnum
CREATE TYPE "FileVerifyStatus" AS ENUM ('VERIFYING', 'VERIFY_FAILED', 'VERIFY_SUCCESS');

-- AlterTable
ALTER TABLE "ChatFile"
    ALTER COLUMN "verifyStatus" TYPE "FileVerifyStatus" USING "verifyStatus"::text::"FileVerifyStatus";

-- AlterTable
ALTER TABLE "ModelFile" ADD COLUMN     "verifyErrCode" TEXT,
ADD COLUMN     "verifyErrorMsg" TEXT,
ADD COLUMN     "verifyStatus" "FileVerifyStatus";

-- Data migration
update "ModelFile" set "verifyStatus" = 'VERIFY_SUCCESS' where "status" in ('VERIFY_SUCCESS','VERIFY_FAILED') AND "hasPii" IN ('YES','NO');

-- DropEnum
DROP TYPE "ChatFileVerifyStatus";
