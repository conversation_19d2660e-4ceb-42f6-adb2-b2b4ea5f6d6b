/*
  Warnings:

  - You are about to drop the `BotUsage` table. If the table is not empty, all the data it contains will be lost.

*/

-- Change Name
ALTER TABLE "BotUsage" RENAME TO "Summary";

ALTER TABLE "Summary" RENAME CONSTRAINT "BotUsage_pkey" TO "Summary_pkey";

ALTER TYPE "BotUsageEntityType" RENAME TO "SummaryEntityType";

ALTER TABLE "Summary" RENAME COLUMN "llmEngineSlug" TO "engineSlug";

ALTER TABLE "Summary" ALTER COLUMN "engineSlug" DROP NOT NULL;

DROP INDEX "entityId_type_index";

DROP INDEX "entityType_key_llmEngineSlug_entityId_index";

-- CreateIndex
CREATE INDEX "entityId_type_index" ON "Summary"("entityType", "key", "entityId", "date", "engineSlug");

-- CreateIndex
CREATE INDEX "entityType_key_engineSlug_entityId_index" ON "Summary"("entityType", "key", "engineSlug", "date", "entityId");


