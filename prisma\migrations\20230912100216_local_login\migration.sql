-- CreateEnum
CREATE TYPE "LoginType" AS ENUM ('HKT', 'LOCAL');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "VerificationStatus" AS ENUM ('PENDING', 'VERIFY_SUCCESS');

-- CreateEnum
CREATE TYPE "VerificationType" AS ENUM ('ACTIVATE_USER');

-- AlterTable
ALTER TABLE "Session" ADD COLUMN     "loginType" "LoginType";

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "loginType" "LoginType";

-- CreateTable
CREATE TABLE "KYCVerification" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiredAt" TIMESTAMP(3) NOT NULL,
    "verifiedAt" TIMESTAMP(3),
    "code" TEXT NOT NULL,
    "requestorId" INTEGER NOT NULL,
    "userId" INTEGER NOT NULL,
    "type" "VerificationType" NOT NULL,
    "status" "VerificationStatus" NOT NULL DEFAULT 'PENDING',

    CONSTRAINT "KYCVerification_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "KYCVerification.search_index" ON "KYCVerification"("code", "userId", "type");

UPDATE "User" SET "loginType" = 'HKT' ;
UPDATE "Session" SET "loginType" = 'HKT' ;