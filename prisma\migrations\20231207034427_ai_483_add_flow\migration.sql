-- CreateEnum
CREATE TYPE "FlowBotStatus" AS ENUM ('ACTIVE', 'DELETED');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "FlowBotRequestType" AS ENUM ('REQUEST_BOT', 'DELETE_BOT', 'LEAVE_FLOW');

-- CreateEnum
CREATE TYPE "FlowBotRequestStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- AlterEnum
ALTER TYPE "SnapshotEntityType" ADD VALUE 'FLOW';

-- CreateTable
CREATE TABLE "Flow" (
    "id" SERIAL NOT NULL,
    "groupId" INTEGER NOT NULL,
    "flowUuid" UUID NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Flow_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FlowBot" (
    "id" SERIAL NOT NULL,
    "flowGroupId" INTEGER NOT NULL,
    "botGroupId" INTEGER NOT NULL,
    "status" "FlowBotStatus" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FlowBot_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FlowBotRequest" (
    "id" SERIAL NOT NULL,
    "flowGroupId" INTEGER NOT NULL,
    "botGroupId" INTEGER NOT NULL,
    "requestType" "FlowBotRequestType" NOT NULL,
    "dataSnapshot" JSONB,
    "requesterId" INTEGER NOT NULL,
    "requestNotes" TEXT,
    "operatorId" INTEGER,
    "operationNotes" TEXT,
    "status" "FlowBotRequestStatus" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),

    CONSTRAINT "FlowBotRequest_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Flow_groupId_key" ON "Flow"("groupId");

-- CreateIndex
CREATE UNIQUE INDEX "FlowBot_flowGroupId_botGroupId_key" ON "FlowBot"("flowGroupId", "botGroupId");

-- CreateIndex
CREATE UNIQUE INDEX "FlowBotRequest_id_key" ON "FlowBotRequest"("id");
