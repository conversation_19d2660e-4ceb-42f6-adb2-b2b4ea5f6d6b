import { ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Test, TestingModule } from '@nestjs/testing';
import { DeepMockProxy, mockDeep } from 'jest-mock-extended';
import { LlmEnginesService } from '../llm-engines/llm-engines.service';
import { QuotaInterceptor } from './quota.interceptor';
import { QuotaService } from '../quotas/quota.service';

describe('QuotaInterceptor', () => {
  let interceptor: QuotaInterceptor;
  let reflector: Reflector;
  let llmEnginesService: DeepMockProxy<LlmEnginesService>;
  let quotaService: DeepMockProxy<QuotaService>;

  beforeEach(async () => {
    llmEnginesService = mockDeep<LlmEnginesService>();
    quotaService = mockDeep<QuotaService>();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuotaInterceptor,
        {
          provide: Reflector,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: LlmEnginesService,
          useValue: llmEnginesService,
        },
        {
          provide: QuotaService,
          useValue: quotaService,
        },
      ],
    }).compile();

    interceptor = module.get<QuotaInterceptor>(QuotaInterceptor);
    reflector = module.get<Reflector>(Reflector);
  });

  describe('intercept', () => {
    it('should return the result of next.handle() if requiredQuotaKey is not defined', async () => {
      // Arrange
      const context = { getHandler: jest.fn() } as unknown as ExecutionContext;
      reflector.get = jest.fn().mockReturnValue(null);
      const nextHandleSpy = jest.fn().mockReturnValue('result');
      const next = { handle: nextHandleSpy };

      // Act
      const result = await interceptor.intercept(context, next);

      // Assert
      expect(result).toBe('result');
      expect(reflector.get).toHaveBeenCalledWith('quota', context.getHandler());
      expect(nextHandleSpy).toHaveBeenCalled();
    });

    it('should return result if quota is not reached', async () => {
      // Arrange
      const context = {
        getHandler: jest.fn(),
        switchToHttp: jest.fn().mockReturnThis(),
        getRequest: jest.fn().mockReturnValue({
          params: {
            groupId: '10111111',
          },
        }),
      } as unknown as ExecutionContext;
      reflector.get = jest.fn().mockReturnValue('group-{groupId}:llm-engine-{llmEngine}-quota');
      interceptor.replaceQuotaPlaceholderByRequest = jest
        .fn()
        .mockReturnValue('group-10111111:llm-engine-gpt-4-quota');
      quotaService.getGroupResourceQuotas.mockResolvedValue([
        {
          quotaKey: 'group-10111111:llm-engine-gpt-4-quota-1',
          quotaType: 'TokenLimitPerMonth',
        },
      ] as any[]);
      llmEnginesService.checkQuotaIsAllowed.mockResolvedValue(false);

      // Act
      const nextHandleSpy = jest.fn().mockReturnValue('result');
      const next = { handle: nextHandleSpy };
      const result = await interceptor.intercept(context, next);
      // Assert
      expect(result).toBe('result');
    });
    it('should throw error if quota is not reached', async () => {
      // Arrange
      const context = {
        getHandler: jest.fn(),
        switchToHttp: jest.fn().mockReturnThis(),
        getRequest: jest.fn().mockReturnValue({
          params: {
            groupId: '10111111',
          },
        }),
      } as unknown as ExecutionContext;
      reflector.get = jest.fn().mockReturnValue('group-{groupId}:llm-engine-{llmEngine}-quota');
      interceptor.replaceQuotaPlaceholderByRequest = jest
        .fn()
        .mockReturnValue('group-10111111:llm-engine-gpt-4-quota');
      llmEnginesService.checkQuotaIsAllowed.mockResolvedValue(true);

      // Assert
      await expect(interceptor.intercept(context, null)).rejects.toThrow();
    });
  });
});
