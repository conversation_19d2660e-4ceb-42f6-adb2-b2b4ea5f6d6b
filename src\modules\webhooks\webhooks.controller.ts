import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { Webhook } from '@prisma/client';
import { CursorPipe } from '../../pipes/cursor.pipe';
import { OptionalIntPipe } from '../../pipes/optional-int.pipe';
import { OrderByPipe } from '../../pipes/order-by.pipe';
import { WherePipe } from '../../pipes/where.pipe';
import { Expose } from '../../providers/prisma/prisma.interface';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { Scopes } from '../auth/scope.decorator';
import { CreateWebhookDto, ReplaceWebhookDto, UpdateWebhookDto } from './webhooks.dto';
import { WebhooksService } from './webhooks.service';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@Controller('groups/:groupId/webhooks')
@ApiBearerAuth('bearer-auth')
@ApiTags('Webhook')
export class WebhookController {
  constructor(private webhooksService: WebhooksService) {}

  /** Create a webhook for a group */
  @Post()
  @AuditLog('create-webhook')
  @Scopes('group-{groupId}:write-webhook')
  async create(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: CreateWebhookDto,
  ): Promise<Expose<Webhook>> {
    return this.webhooksService.createWebhook(groupId, data);
  }

  /** Get webhooks for a group */
  @Get()
  @Scopes('group-{groupId}:read-webhook')
  async getAll(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<{ list: Expose<Webhook[]>; count: number }> {
    const list = await this.webhooksService.getWebhooks(groupId, {
      skip,
      take,
      orderBy,
      where,
    });
    const count = await this.webhooksService.getWebhooksCount(groupId, where);
    return { list, count };
  }

  /** Get a webhook for a group */
  @Get(':id')
  @Scopes('group-{groupId}:read-webhook')
  async get(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Expose<Webhook>> {
    return this.webhooksService.getWebhook(groupId, id);
  }

  /** Update a webhook for a group */
  @Patch(':id')
  @AuditLog('update-webhook')
  @Scopes('group-{groupId}:write-webhook')
  async update(
    @Body() data: UpdateWebhookDto,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Expose<Webhook>> {
    return this.webhooksService.updateWebhook(groupId, id, data);
  }

  /** Delete a webhook for a group */
  @Delete(':id')
  @AuditLog('delete-webhook')
  @Scopes('group-{groupId}:delete-webhook')
  async remove(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Expose<Webhook>> {
    return this.webhooksService.deleteWebhook(groupId, id);
  }
}
