import { Module, forwardRef } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { FlowBotsService } from './flow-bots.service';
import { ConfigService } from '@nestjs/config';
import { GroupsModule } from '../groups/groups.module';
import { FlowBotRequestsModule } from '../flow-bot-requests/flow-bot-requests.module';

@Module({
  controllers: [],
  imports: [PrismaModule, forwardRef(() => GroupsModule), forwardRef(() => FlowBotRequestsModule)],
  providers: [FlowBotsService, ConfigService],
  exports: [FlowBotsService],
})
export class FlowBotsModule {}
