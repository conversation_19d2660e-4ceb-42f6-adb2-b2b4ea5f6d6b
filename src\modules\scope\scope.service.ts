import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../providers/prisma/prisma.service';
import {
  Environment,
  Group,
  GroupType,
  PermissionType,
  ResourceSubsciberType,
} from '@prisma/client';
import { PredictionResponse } from 'src/providers/flow-backend/flow-backend.interface';
import { ConfigService } from '@nestjs/config';
import { Configuration } from 'src/config/configuration.interface';
import { ResourceQuotaDto } from './scope.dto';
import { botNominationPermissions } from 'prisma/seedData/groupPermission.seedData';

@Injectable()
export class ScopeService {
  private enableFlowDebug: boolean;
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
  ) {}

  async getUserPermissions(userId: number) {
    const userPermissions = await this.prisma.permission.findMany({
      where: {
        permissionType: PermissionType.USER,
      },
    });
    return userPermissions.map((permission) =>
      permission.permissionKey.replace('{userId}', userId.toString()),
    );
  }

  async getSystemPermissions(roleId: number) {
    const systemPermissions = await this.prisma.permission.findMany({
      where: {
        permissionType: PermissionType.SYSTEM,
        roles: {
          some: {
            roleId,
          },
        },
      },
    });
    return systemPermissions.map((permission) => permission.permissionKey);
  }

  async getResourcePlanPermissions(
    entityId: number,
    entityType: ResourceSubsciberType,
    groupEnv?: Environment,
  ) {
    const additionalPermissionKeys: string[] = [];
    const resources = await this.prisma.resource.findMany({
      select: { id: true, resourceKey: true },
      where: { subscriberTypes: { has: entityType } },
    });

    for (const resource of resources) {
      let subscribedResourcePlanId = null;
      // get the resource subscription plan
      const planSubscriptionCount = await this.prisma.planSubscription.count({
        where: {
          plan: { resource: { id: resource.id } },
          subscriberEntityId: entityId,
          subscriberEntityType: entityType,
        },
      });
      // if the user has a subscription, use the planId from the subscription, else use the default plan
      if (planSubscriptionCount === 0) {
        const defaultSubscribedPlan = await this.prisma.plan.findFirst({
          select: { id: true },
          where: { resourceId: resource.id, isDefault: true, ...(groupEnv ? { groupEnv } : {}) },
        });
        subscribedResourcePlanId = defaultSubscribedPlan.id;
      } else {
        const planSubscription = await this.prisma.planSubscription.findFirst({
          select: { planId: true },
          where: {
            plan: {
              resource: {
                id: resource.id,
              },
            },
            subscriberEntityType: entityType,
            subscriberEntityId: entityId,
            subscribeEndDate: null,
          },
        });
        if (planSubscription) {
          subscribedResourcePlanId = planSubscription.planId;
        }
      }
      // get the permissions from the plan
      const additionalPermissions = await this.prisma.planPermission.findMany({
        select: { permission: { select: { permissionKey: true } } },
        where: {
          planId: subscribedResourcePlanId,
        },
      });

      if (entityType === ResourceSubsciberType.USER) {
        additionalPermissionKeys.push(
          ...additionalPermissions.map((permission) =>
            permission.permission.permissionKey.replace('{userId}', entityId.toString()),
          ),
        );
      }
      if ((Object.keys(GroupType) as ResourceSubsciberType[]).includes(entityType)) {
        additionalPermissionKeys.push(
          ...additionalPermissions.map((permission) =>
            permission.permission.permissionKey.replace('{groupId}', entityId.toString()),
          ),
        );
      }
    }
    return additionalPermissionKeys;
  }

  async getResourceQuota(
    resourceId: number,
    entityId: number,
    entityType: ResourceSubsciberType,
    groupEnv?: Environment,
  ) {
    let subscribedResourcePlanId = null;
    // get the resource subscription plan
    const planSubscriptionCount = await this.prisma.planSubscription.count({
      where: {
        plan: { resource: { id: resourceId } },
        subscriberEntityId: entityId,
        subscriberEntityType: entityType,
      },
    });
    // if the user has a subscription, use the planId from the subscription, else use the default plan
    if (planSubscriptionCount === 0) {
      const defaultSubscribedPlan = await this.prisma.plan.findFirst({
        select: { id: true },
        where: { resourceId: resourceId, isDefault: true, ...(groupEnv ? { groupEnv } : {}) },
      });
      subscribedResourcePlanId = defaultSubscribedPlan.id;
    } else {
      const planSubscription = await this.prisma.planSubscription.findFirst({
        select: { planId: true },
        where: {
          plan: {
            resource: {
              id: resourceId,
            },
          },
          subscriberEntityType: entityType,
          subscriberEntityId: entityId,
          subscribeEndDate: null,
        },
      });
      if (planSubscription) {
        subscribedResourcePlanId = planSubscription.planId;
      }
    }
    // get the permissions from the plan
    const planQuota = await this.prisma.planQuota.findMany({
      select: { quotaValue: { select: { rule: true, value: true, quotaKey: true } } },
      where: {
        planId: subscribedResourcePlanId,
      },
    });

    return planQuota;
  }

  async getAdditionalResourceQuotas(
    entityId: number,
    entityType: ResourceSubsciberType,
    groupEnv?: Environment,
  ) {
    const additionalQuotaKeys: ResourceQuotaDto[] = [];
    const resources = await this.prisma.resource.findMany({
      select: { id: true, resourceKey: true },
      where: { subscriberTypes: { has: entityType } },
    });

    for (const resource of resources) {
      // get the permissions from the plan
      const additionalQuotas = await this.getResourceQuota(
        resource.id,
        entityId,
        entityType,
        groupEnv,
      );
      if (entityType === ResourceSubsciberType.USER) {
        additionalQuotaKeys.push(
          ...additionalQuotas.map((quota) => ({
            quotaValue: quota.quotaValue.value,
            quotaType: quota.quotaValue.rule.quotaType,
            quotaKey: quota.quotaValue.quotaKey.replace('{userId}', entityId.toString()),
            ruleKey: quota.quotaValue.rule.ruleKey.replace('{userId}', entityId.toString()),
          })),
        );
      }
      if ((Object.keys(GroupType) as ResourceSubsciberType[]).includes(entityType)) {
        additionalQuotaKeys.push(
          ...additionalQuotas.map((quota) => ({
            quotaValue: quota.quotaValue.value,
            quotaType: quota.quotaValue.rule.quotaType,
            quotaKey: quota.quotaValue.quotaKey.replace('{groupId}', entityId.toString()),
            ruleKey: quota.quotaValue.rule.ruleKey.replace('{groupId}', entityId.toString()),
          })),
        );
      }
    }
    return additionalQuotaKeys;
  }

  async getGroupPermissions(roleId: number, group: Group) {
    const lLMModel = await this.prisma.lLMModel.findFirst({
      select: { active: true },
      where: { groupId: group.id },
    });
    const isGroupActive = lLMModel?.active ?? true;
    const groupPermissions = await this.prisma.permission.findMany({
      where: {
        permissionType: PermissionType.GROUP,
        envs: { has: group.env },
        permissionGroupSetting: {
          some: {
            groupType: group.groupType,
            ...(isGroupActive ? {} : { isActiveOnly: false }),
          },
        },
        roles: {
          some: {
            roleId,
          },
        },
      },
    });
    return groupPermissions.map((permission) =>
      permission.permissionKey.replace('{groupId}', group.id.toString()),
    );
  }
  async getBotNorminationPermission(groupId: number, membershipId: number) {
    const normination = await this.prisma.botReviewNomination.findFirst({
      where: {
        membershipId,
        startDate: { lte: new Date() },
        endDate: { gte: new Date() },
      },
    });
    if (!normination) {
      return [];
    }
    return botNominationPermissions.map((key) => key.replace('{groupId}', groupId.toString()));
  }

  checkChatDebugInfo(groupId: number, scopes: string[], res: PredictionResponse) {
    const debugGroupScope = 'group-{groupId}:read-flow-debug'.replace(
      '{groupId}',
      groupId.toString(),
    );
    const debugSystemScope = 'group-*:read-flow-debug';
    // TODO the process.env.FLOW_DEBUG will on the bot level Feature flag to get the value
    const flowConfig = this.configService.get<Configuration['flow']>('flow');
    this.enableFlowDebug = flowConfig.enableChatDebug;
    const needShowChatDebugInfo =
      (scopes.includes(debugGroupScope) || scopes.includes(debugSystemScope)) &&
      this.enableFlowDebug;
    if (!needShowChatDebugInfo) {
      delete res.callChain;
    }
  }
}
