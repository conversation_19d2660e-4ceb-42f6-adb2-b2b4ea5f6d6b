import { EntityLabels, Labels, Prisma } from '@prisma/client';
import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMaxSize,
  ArrayNotEmpty,
  IsArray,
  IsInt,
  IsNotEmpty,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Usage, MODEL } from 'src/providers/llm-backend/llm-backend.interface';
import { Type } from 'class-transformer';

class DocumentTagBulkCreateInputFileTag {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: Labels['name'];
}

class DocumentTagBulkCreateInputFile {
  @ApiProperty()
  @IsInt()
  id: Prisma.ModelFileWhereUniqueInput['id'];

  @ApiProperty()
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => DocumentTagBulkCreateInputFileTag)
  tags: DocumentTagBulkCreateInputFileTag[];
}

export class DocumentTagBulkCreateInput {
  @ApiProperty({
    example: [
      {
        id: 123,
        tags: [{ name: 'tag1' }, { name: 'tag2' }],
      },
    ],
  })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => DocumentTagBulkCreateInputFile)
  files: DocumentTagBulkCreateInputFile[];
}

export class DocumentTagBulkCreateResponse {
  @ApiProperty({
    example: [
      {
        id: 123,
        tags: [
          { id: 1, name: 'tag1' },
          { id: 2, name: 'tag2' },
        ],
      },
    ],
  })
  files: {
    id: Prisma.ModelFileWhereUniqueInput['id'];
    tags: {
      id: EntityLabels['id'];
      name: Labels['name'];
    }[];
  }[];
}

class DocumentTagBulkDeleteAllInputFile {
  @ApiProperty()
  @IsInt()
  id: Prisma.ModelFileWhereUniqueInput['id'];
}

export class DocumentTagBulkDeleteAllInput {
  @ApiProperty({ example: [{ id: 123 }] })
  @IsArray()
  @ArrayNotEmpty()
  @ArrayMaxSize(100)
  @ValidateNested({ each: true })
  @Type(() => DocumentTagBulkDeleteAllInputFile)
  files: DocumentTagBulkDeleteAllInputFile[];
}

class DocumentTagBulkSuggestInputFile {
  @ApiProperty()
  @IsInt()
  id: Prisma.ModelFileWhereUniqueInput['id'];
}

export class DocumentTagBulkSuggestInput {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  model: MODEL;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  prompt: Prisma.ModelFileLabelHistoryCreateInput['prompt'];

  @ApiProperty()
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => DocumentTagBulkSuggestInputFile)
  files: DocumentTagBulkSuggestInputFile[];

  @ApiProperty()
  stream?: boolean;

  @ApiProperty()
  ignoreHistory?: boolean;
}

export class DocumentTagBulkSuggestResponse {
  @ApiProperty()
  model: MODEL;

  @ApiProperty()
  prompt: Prisma.ModelFileLabelHistoryCreateInput['prompt'];

  @ApiProperty()
  files: {
    id: Prisma.ModelFileWhereUniqueInput['id'];
    tags: Prisma.LabelsCreateInput['name'][];
  }[];

  @ApiProperty()
  usage: Usage;
}
