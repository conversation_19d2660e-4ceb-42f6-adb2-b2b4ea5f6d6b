-- AlterTable
ALTER TABLE "FeatureFlag" ALTER COLUMN "metaData" SET DEFAULT '{}';
UPDATE "FeatureFlag"
SET "metaData" = (jsonb_build_object('jsonValue', "metaData")->>'jsonValue')::jsonb;

UPDATE "FeatureFlagOverride"
SET "metaData" = (jsonb_build_object('jsonValue', "metaData")->>'jsonValue')::jsonb;

UPDATE "FeatureFlag" set "metaData" = jsonb_build_object('value', string_to_array("metaData"->>'value',',')),
description = 'Updating metaData value using JSON array format'
where  "metaData"->>'value' is not null and key in ('ACCOUNT_MANAGEMENT.CONFIG_SSO_EMAIL_WHITELIST','REQUIRE_SECONDARY_FILE_APPROVAL_ENVS','ADMIN.CONFIG_FLOW_FEATURE_FLAGS_FOR_GROUP_ADMIN','ADMIN.CONFIG_BOT_FEATURE_FLAGS_FOR_GROUP_ADMIN');