import { Injectable } from '@nestjs/common';
import { AlertHistory, Prisma } from '@prisma/client';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { PrismaService } from 'src/providers/prisma/prisma.service';

@Injectable()
export class AlertHistoryService {
  constructor(private prisma: PrismaService) {}

  public async getAlertHistory(id: number) {
    const alertHistory = await this.prisma.alertHistory.findUnique({ where: { id } });
    if (!alertHistory) {
      throw new ApiException(ErrorCode.ALERT_HISTORY_NOT_FOUND);
    }
    return alertHistory;
  }

  public async findAll(params: {
    skip?: number;
    take?: number;
    where?: Prisma.AlertHistoryWhereInput;
    orderBy?: Prisma.LlmEngineOrderByWithRelationInput;
  }): Promise<AlertHistory[]> {
    const { skip, take, where, orderBy } = params;
    const list = await this.prisma.alertHistory.findMany({
      skip,
      take,
      where: where,
      orderBy,
    });
    return list;
  }

  async count(where?: Prisma.AlertHistoryWhereInput) {
    return this.prisma.alertHistory.count({ where: where });
  }
}
