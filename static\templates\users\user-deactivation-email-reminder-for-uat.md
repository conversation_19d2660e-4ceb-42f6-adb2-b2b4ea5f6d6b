Dear {{username}},

We have noticed that you haven't logged into your UAT account on Gen AI Platform for {{dateForNotLogin}} days. To ensure that your account remains accessible, kindly remind you to log in to your account again before {{deactivationDate}}.

- For UAT login, https://uat.bot-builder.pccw.com/auth/login

According to the regulations from control parties, if there is no login activity on your account within {{dayOfDeactivateActiveUser}} days after the last login, the system will automatically consider the account as inactive. Once the account is inactive, access to Gen AI Platform will be restricted.

How to Keep Your Account Active:

1. Maintain a regular login to your account and use features to keep your account active.
2. If your account has already been inactive, you can:  
   - For UAT, please follow the [SLA](https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/Login%20issue%20in%20UAT.aspx#troubleshoot-login-issues-with-enterprise-genai-platform-(gpt-bot-builder)) on the website and apply to reactivate your account with the Customer Success <NAME_EMAIL>. The process will take 10-14 working days.

Please login immediately to avoid account deactivation. Thank you.

On Behalf of Gen AI Customer Success