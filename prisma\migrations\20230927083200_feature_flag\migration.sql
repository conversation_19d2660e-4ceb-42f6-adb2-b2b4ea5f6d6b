-- CreateTable
CREATE TABLE "FeatureFlag" (
    "id" SERIAL NOT NULL,
    "description" TEXT,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "metaData" JSONB DEFAULT '"{}"',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FeatureFlag_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "FeatureFlag_key_constraint" ON "FeatureFlag"("key");

-- CreateIndex
CREATE INDEX "FeatureFlag_key_index" ON "FeatureFlag"("key");
