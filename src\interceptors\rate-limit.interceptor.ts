import { CallHand<PERSON>, ExecutionContext, Injectable, Logger, NestInterceptor } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { getClientIp } from 'request-ip';
import { Observable } from 'rxjs';
import { ApiException, ErrorCode } from '../errors/errors.constants';
import { UserRequest } from '../modules/auth/auth.interface';

import { RateLimitService } from 'src/providers/rate-limit/rate-limit.service';

@Injectable()
export class RateLimitInterceptor implements NestInterceptor {
  private logger = new Logger(RateLimitInterceptor.name);
  constructor(
    private readonly reflector: Reflector,
    private rateLimitService: RateLimitService,
  ) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<unknown>> {
    const points = this.reflector.get<number>('usePoints', context.getHandler()) ?? 1;

    const request = context.switchToHttp().getRequest() as UserRequest;
    const response = context.switchToHttp().getResponse();

    const ip = getClientIp(request).replace(/^.*:/, '');
    const apiPath = request.path;

    try {
      const result = await this.rateLimitService.handleRateLimitOnApiLevel(ip, apiPath, points);

      response.header('Retry-After', Math.ceil(result.msBeforeNext / 1000));
      response.header('X-RateLimit-Limit', points);
      response.header('X-Retry-Remaining', result.remainingPoints);
      response.header('X-Retry-Reset', new Date(Date.now() + result.msBeforeNext).toUTCString());
    } catch (result) {
      this.logger.error(result, 'Api level Rate limit error');
      response.header('Retry-After', Math.ceil(((result as any)?.msBeforeNext ?? 0) / 1000));
      throw new ApiException(ErrorCode.RATE_LIMIT_EXCEEDED);
    }

    return next.handle();
  }
}
