import { ApiProperty } from '@nestjs/swagger';

export class botSecurityScannerResultDTO {
  @ApiProperty({
    example: 'Anonymize',
    description: 'Scanners name for check PII',
  })
  name: string;
  @ApiProperty({
    example: '1',
    description: 'risk scores in scan results, 0.0 - 1.0 (highest risk)',
  })
  risk_score: number;
  @ApiProperty({
    example: 'SOFT',
    description: 'the level corresponding to the scanner(OFF/SOFT/HARD)',
  })
  security_level: string;

  pii: string;
}

export class ScannersResultDTO {
  @ApiProperty({
    description: 'Scanners list',
    type: [botSecurityScannerResultDTO],
  })
  scanners: botSecurityScannerResultDTO[];
  @ApiProperty({
    example: 'Sure! Here is an example of a phone number in the United States:\n<PHONE_NUMBER>',
    description: 'Text optimized for PII checking',
  })
  sanitized_output: string;
  @ApiProperty({
    example: 'Sure! Here is an example of a phone number in the United States:\n\n**************',
    description: 'Original text returned by GPT',
  })
  original_output: string;
}

export class outputScannersResultDTO {
  @ApiProperty({
    example: false,
    description: `The value is set to 'true' if the output passed all security checks, and 'false' otherwise.`,
  })
  output_scanners_result_is_valid: boolean;
  output_scanners_result: ScannersResultDTO;
}
