/*
  Warnings:

  - You are about to drop the column `status` on the `Group` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "Group" DROP COLUMN "status",
ADD COLUMN     "active" BOOLEAN NOT NULL DEFAULT true;

-- DropEnum
DROP TYPE "GroupStatus";


ALTER TABLE "PermissionGroupSetting" ADD COLUMN   "isActiveOnly" BOOLEAN NOT NULL DEFAULT true;


INSERT INTO "Permission" ("description", "permissionKey", "permissionType", "envs") VALUES ('Activate/Deactivate Group', 'group-{groupId}:activate', 'GROUP' , '{TEST,PROD}');

INSERT INTO "PermissionGroupSetting" ("permissionId", "groupType", "isCustomRoleAllowed", "isApiKeyAllowed", "isActiveOnly" ,"updatedAt") VALUES
 ( (SELECT id FROM "Permission" WHERE "permissionKey" = 'group-{groupId}:activate'), 'BOT', false, false, false, NOW());

INSERT INTO "PermissionGroupSetting" ("permissionId", "groupType", "isCustomRoleAllowed", "isApiKeyAllowed", "isActiveOnly", "updatedAt" ) VALUES
 ( (SELECT id FROM "Permission" WHERE "permissionKey" = 'group-{groupId}:activate'), 'FLOW', false, false, false, NOW());




UPDATE  "PermissionGroupSetting" pg   SET "isActiveOnly" = true FROM "Permission" p
WHERE p."permissionKey"  IN (
  'group-{groupId}:write-api-key',
  'group-{groupId}:delete-api-key',
  'group-{groupId}:write-info',
  'group-{groupId}:delete',
  'group-{groupId}:write-membership',
  'group-{groupId}:delete-membership',
  'group-{groupId}:write-llm-model',
  'group-{groupId}:write-llm-model-files',
  'group-{groupId}:upload-llm-model-files',
  'group-{groupId}:approve-or-process-llm-model-files',
  'group-{groupId}:download-llm-model-files',
  'group-{groupId}:delete-llm-model-files',
  'group-{groupId}:write-role',
  'group-{groupId}:delete-role',
  'group-{groupId}:write-ai-resource',
  'group-{groupId}:write-webhook',
  'group-{groupId}:delete-webhook',
  'group-{groupId}:write-message-template',
  'group-{groupId}:delete-message-template',
  'group-{groupId}:read-playground',
  'group-{groupId}:write-playground',
  'group-{groupId}:export-logs',
  'group-{groupId}:write-gen-kb',
  'group-{groupId}:write-entity-snapshot',
  'group-{groupId}:delete-entity-snapshot',
  'group-{groupId}:write-data-promotion-request',
  'group-{groupId}:write-owner-role',
  'group-{groupId}:write-flow',
  'group-{groupId}:delete-flow',
  'group-{groupId}:write-flow-bot-request',
  'group-{groupId}:read-flow-playground',
  'group-{groupId}:write-api-resource',
  'group-{groupId}:delete-api-resource',
  'group-{groupId}:delete-api-resource-file',
  'group-{groupId}:write-api-resource-file',
  'group-{groupId}:write-feature-flag',
  'group-{groupId}:delete-flow-bot'
) AND p.id = pg."permissionId";

UPDATE  "PermissionGroupSetting" pg   SET "isActiveOnly" = false FROM "Permission" p
WHERE p."permissionKey"  IN (
  'group-{groupId}:read-api-key',
  'group-{groupId}:read-api-key-logs',
  'group-{groupId}:read-audit-log',
  'group-{groupId}:read-info',
  'group-{groupId}:read-membership',
  'group-{groupId}:read-llm-model',
  'group-{groupId}:read-llm-model-files',
  'group-{groupId}:read-role',
  'group-{groupId}:read-ai-resource',
  'group-{groupId}:read-webhook',
  'group-{groupId}:read-message-template',
  'group-{groupId}:read-dashboard',
  'group-{groupId}:read-gen-kb',
  'group-{groupId}:read-entity-snapshot',
  'group-{groupId}:read-data-promotion-request',
  'group-{groupId}:read-flow',
  'group-{groupId}:read-flow-bot-request',
  'group-{groupId}:read-api-resource',
  'group-{groupId}:read-flow-debug',
  'group-{groupId}:read-feature-flag',
  'group-{groupId}:activate'
) AND p.id = pg."permissionId";

INSERT INTO "Role" ("name", "systemName", "updatedAt", "groupId", "roleType") VALUES ('OWNER', 'GROUP_OWNER', NOW(), 0, 'GROUP_DEFAULT') ON CONFLICT DO NOTHING;

 INSERT INTO "RolePermission" ("roleId", "permissionId") VALUES
 ( (SELECT id FROM "Role" WHERE "systemName" = 'GROUP_OWNER' AND "roleType" = 'GROUP_DEFAULT')   , (SELECT id FROM "Permission" WHERE "permissionKey" = 'group-{groupId}:activate'));