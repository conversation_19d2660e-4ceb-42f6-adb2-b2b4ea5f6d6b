import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsPositive,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ChatSettingDto } from './chat-setting.dto';
import { ChatSessionType } from '@prisma/client';

export class CreateChatSessionRequestDto {
  @IsString()
  name: string;

  @IsEnum(ChatSessionType)
  chatSessionType: ChatSessionType;

  @IsOptional()
  @IsPositive()
  @IsNumber()
  messageTemplateId?: number;

  @ValidateNested()
  @Type(() => ChatSettingDto)
  @IsObject()
  chatSetting: ChatSettingDto;
}
