/*
  Warnings:

  - You are about to drop the column `groupEnvs` on the `Plan` table. All the data in the column will be lost.
  - Added the required column `groupEnv` to the `Plan` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "Plan_resourceId_entityType_entityId_planRoleIdsRequired_gro_idx";


ALTER TABLE "Plan" ADD COLUMN   "groupEnv" "Environment";


-- AlterTable
ALTER TABLE "Plan" DROP COLUMN "groupEnvs";

-- CreateIndex
CREATE INDEX "Plan_resourceId_entityType_entityId_planRoleIdsRequired_gro_idx" ON "Plan"("resourceId", "entityType", "entityId", "planRoleIdsRequired", "groupEnv");
