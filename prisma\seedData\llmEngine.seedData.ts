export interface LlMEngineSeed {
  name: string;
  slug: string;
  reportSlug: string;
  platform: string;
  isActive: boolean;
  sequence: number;
  config: string;
}

export const llmEnginesData: LlMEngineSeed[] = [
  {
    name: 'ChatGPT o1 mini',
    slug: 'o1-mini',
    reportSlug: 'o1-mini',
    platform: 'AZURE',
    sequence: 1,
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":1,"max":1,"default":1,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p":{"label":"Top P","min":1,"max":1,"default":1,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":65536,"default":1000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"max_input_token":{"label":"Input max Tokens","min":256,"max":128000,"default":1000,"type":"number","input":"slider","step":1,"description":"Input max tokens", "readOnly":true},"presence_penalty":{"label":"Presence Penalty","min":0,"max":0,"default":0,"type":"number","input":"slider","step":0.01,"description":"Number between 0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model\'s likelihood to talk about new topics."},"frequency_penalty":{"label":"Frequency Penalty","min":0,"max":0,"default":0,"type":"number","input":"slider","step":0.01,"description":"Number between 0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"past message","min":1,"max":50,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'ChatGPT o1 preview',
    slug: 'o1-preview',
    reportSlug: 'o1-preview',
    platform: 'AZURE',
    sequence: 1,
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":1,"max":1,"default":1,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p":{"label":"Top P","min":1,"max":1,"default":1,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":32768,"default":1000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"max_input_token":{"label":"Input max Tokens","min":256,"max":128000,"default":1000,"type":"number","input":"slider","step":1,"description":"Input max tokens", "readOnly":true},"presence_penalty":{"label":"Presence Penalty","min":0,"max":0,"default":0,"type":"number","input":"slider","step":0.01,"description":"Number between 0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model\'s likelihood to talk about new topics."},"frequency_penalty":{"label":"Frequency Penalty","min":0,"max":0,"default":0,"type":"number","input":"slider","step":0.01,"description":"Number between 0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"past message","min":1,"max":50,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Deepseek-r1',
    slug: 'deepseek-r1',
    reportSlug: 'deepseek-r1',
    platform: 'AZURE',
    sequence: 1,
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":1,"max":1,"default":1,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p":{"label":"Top P","min":0,"max":1,"default":0.95,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":32768,"default":1000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"max_input_token":{"label":"Input max Tokens","min":256,"max":128000,"default":1000,"type":"number","input":"slider","step":1,"description":"Input max tokens", "readOnly":true},"presence_penalty":{"label":"Presence Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.01,"description":"Number between 0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model\'s likelihood to talk about new topics."},"frequency_penalty":{"label":"Frequency Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.01,"description":"Number between 0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"past message","min":1,"max":50,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Deepseek-v3',
    slug: 'deepseek-v3',
    reportSlug: 'deepseek-v3',
    platform: 'ALIBABA',
    sequence: 1,
    isActive: false,
    config: `{"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user's query"},"max_input_token":{"label":"Max Input Tokens","min":256,"max":57344,"default":10000,"type":"number","input":"slider","step":1,"description":"Input max tokens","readOnly":true},"max_tokens":{"label":"Max Output Tokens","min":256,"max":8192,"default":4000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model's context length."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":2,"description":"Number of past messages to include in each new API request."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.95,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"temperature":{"label":"Temperature","min":0,"max":1,"default":1,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."}}`,
  },
  {
    name: 'QwQ 32B',
    slug: 'qwq-32b',
    reportSlug: 'qwq-32b',
    platform: 'ALIBABA',
    sequence: 1,
    isActive: false,
    config: `{"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user's query"},"max_input_token":{"label":"Max Input Tokens","min":256,"max":98304,"default":10000,"type":"number","input":"slider","step":1,"description":"Input max tokens","readOnly":true},"max_tokens":{"label":"Max Output Tokens","min":256,"max":65536,"default":8192,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model's context length."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":2,"description":"Number of past messages to include in each new API request."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.95,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"temperature":{"label":"Temperature","min":0,"max":1,"default":1,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."}}`,
  },
  {
    name: 'Phi-4',
    slug: 'phi-4',
    reportSlug: 'phi-4',
    platform: 'AZURE',
    sequence: 1,
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":1,"max":1,"default":0.5,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p":{"label":"Top P","min":0,"max":1,"default":1,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":32768,"default":1000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"max_input_token":{"label":"Input max Tokens","min":256,"max":128000,"default":1000,"type":"number","input":"slider","step":1,"description":"Input max tokens", "readOnly":true},"presence_penalty":{"label":"Presence Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.01,"description":"Number between 0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model\'s likelihood to talk about new topics."},"frequency_penalty":{"label":"Frequency Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.01,"description":"Number between 0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"past message","min":1,"max":50,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'ChatGPT 4o mini',
    slug: 'gpt-4o-mini',
    reportSlug: 'gpt-4o-mini',
    platform: 'AZURE',
    sequence: 1,
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p":{"label":"Top P","min":0,"max":1,"default":0.95,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":4000,"default":1000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"max_input_token":{"label":"Input max Tokens","min":256,"max":128000,"default":1000,"type":"number","input":"slider","step":1,"description":"Input max tokens", "readOnly":true},"presence_penalty":{"label":"Presence Penalty","min":0,"max":2,"default":0,"type":"number","input":"slider","step":0.01,"description":"Number between 0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model\'s likelihood to talk about new topics."},"frequency_penalty":{"label":"Frequency Penalty","min":0,"max":2,"default":0,"type":"number","input":"slider","step":0.01,"description":"Number between 0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"past message","min":1,"max":50,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'ChatGPT 3.5 Turbo 16K',
    slug: 'gpt-35-turbo-16k',
    reportSlug: 'gpt-35-16k',
    platform: 'AZURE',
    isActive: true,
    sequence: 1,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p":{"label":"Top P","min":1,"max":10,"default":1,"type":"number","input":"slider","step":1,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":16384,"default":1000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"presence_penalty":{"label":"Presence Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model\'s likelihood to talk about new topics."},"frequency_penalty":{"label":"Frequency Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'ChatGPT 4 32K',
    slug: 'gpt-4-32k',
    reportSlug: 'gpt-4-32k',
    platform: 'AZURE',
    isActive: true,
    sequence: 2,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p":{"label":"Top P","min":1,"max":10,"default":1,"type":"number","input":"slider","step":1,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":32768,"default":1000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"presence_penalty":{"label":"Presence Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model\'s likelihood to talk about new topics."},"frequency_penalty":{"label":"Frequency Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'ChatGPT 4o',
    slug: 'gpt-4o',
    reportSlug: 'gpt-4o',
    platform: 'AZURE',
    sequence: 2,
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p":{"label":"Top P","min":0,"max":1,"default":0.95,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":4096,"default":1000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"max_input_token":{"label":"Input max Tokens","min":256,"max":128000,"default":1000,"type":"number","input":"slider","step":1,"description":"Input max tokens", "readOnly":true},"presence_penalty":{"label":"Presence Penalty","min":0,"max":2,"default":0,"type":"number","input":"slider","step":0.01,"description":"Number between 0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model\'s likelihood to talk about new topics."},"frequency_penalty":{"label":"Frequency Penalty","min":0,"max":2,"default":0,"type":"number","input":"slider","step":0.01,"description":"Number between 0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"past message","min":1,"max":50,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'ChatGPT 4.1',
    slug: 'gpt-4.1',
    reportSlug: 'gpt-4.1',
    platform: 'AZURE',
    sequence: 2,
    isActive: false,
    config: `{"max_input_token":{"label":"Max Input Tokens","min":256,"max":1047576,"default":50000,"type":"number","input":"slider","step":1,"description":"Input max tokens","readOnly":true},"max_tokens":{"label":"Max Output Tokens","min":256,"max":32768,"default":8192,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model's context length."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":2,"description":"Number of past messages to include in each new API request."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.95,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"temperature":{"label":"Temperature","min":0,"max":1,"default":1,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."}}`,
  },
  {
    name: 'ChatGPT 4.1 mini',
    slug: 'gpt-4.1-mini',
    reportSlug: 'gpt-4.1-mini',
    platform: 'AZURE',
    sequence: 2,
    isActive: false,
    config: `{"max_input_token":{"label":"Max Input Tokens","min":256,"max":1047576,"default":50000,"type":"number","input":"slider","step":1,"description":"Input max tokens","readOnly":true},"max_tokens":{"label":"Max Output Tokens","min":256,"max":32768,"default":8192,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model's context length."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":2,"description":"Number of past messages to include in each new API request."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.95,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"temperature":{"label":"Temperature","min":0,"max":1,"default":1,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."}}`,
  },
  {
    name: 'ChatGPT 4.1 nano',
    slug: 'gpt-4.1-nano',
    reportSlug: 'gpt-4.1-nano',
    platform: 'AZURE',
    sequence: 2,
    isActive: false,
    config: `{"max_input_token":{"label":"Max Input Tokens","min":256,"max":1047576,"default":50000,"type":"number","input":"slider","step":1,"description":"Input max tokens","readOnly":true},"max_tokens":{"label":"Max Output Tokens","min":256,"max":32768,"default":8192,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model's context length."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":2,"description":"Number of past messages to include in each new API request."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.95,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"temperature":{"label":"Temperature","min":0,"max":1,"default":1,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."}}`,
  },
  {
    name: 'o3',
    slug: 'o3',
    reportSlug: 'o3',
    platform: 'AZURE',
    sequence: 2,
    isActive: false,
    config: `{"max_input_token":{"label":"Max Input Tokens","min":256,"max":200000,"default":50000,"type":"number","input":"slider","step":1,"description":"Input max tokens","readOnly":true},"max_tokens":{"label":"Max Output Tokens","min":256,"max":100000,"default":10000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model's context length."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":2,"description":"Number of past messages to include in each new API request."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.95,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"temperature":{"label":"Temperature","min":0,"max":1,"default":1,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."}}`,
  },
  {
    name: 'o3-mini',
    slug: 'o3-mini',
    reportSlug: 'o3-mini',
    platform: 'AZURE',
    sequence: 2,
    isActive: false,
    config: `{"max_input_token":{"label":"Max Input Tokens","min":256,"max":200000,"default":50000,"type":"number","input":"slider","step":1,"description":"Input max tokens","readOnly":true},"max_tokens":{"label":"Max Output Tokens","min":256,"max":100000,"default":10000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model's context length."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":2,"description":"Number of past messages to include in each new API request."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.95,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"temperature":{"label":"Temperature","min":0,"max":1,"default":1,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."}}`,
  },
  {
    name: 'o4-mini',
    slug: 'o4-mini',
    reportSlug: 'o4-mini',
    platform: 'AZURE',
    sequence: 2,
    isActive: false,
    config: `{"max_input_token":{"label":"Max Input Tokens","min":256,"max":200000,"default":50000,"type":"number","input":"slider","step":1,"description":"Input max tokens","readOnly":true},"max_tokens":{"label":"Max Output Tokens","min":256,"max":100000,"default":10000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model's context length."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":2,"description":"Number of past messages to include in each new API request."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.95,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"temperature":{"label":"Temperature","min":0,"max":1,"default":1,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."}}`,
  },
  {
    name: 'Chat Bison 32K@Latest',
    slug: 'vertexai-chat-bison-32k',
    reportSlug: 'chat-bison-32k',
    platform: 'VERTEX_AI',
    sequence: 3,
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0.2,"type":"number","input":"slider","step":0.1,"description":"Temperature controls the degree of randomness in token selection. Lower temperatures are good for prompts that expect a true or correct response, while higher temperatures can lead to more diverse or unexpected results. A temperature of 0 is deterministic: the highest probability token is always selected. For most use cases, try starting with a temperature of .2."},"max_tokens":{"label":"Token Limit","min":1,"max":32000,"default":1000,"type":"number","input":"slider","step":1,"description":"Token limit determines the maximum amount of text output from one prompt. A token is approximately four characters. The default value is 1000."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.8,"type":"number","input":"slider","step":0.01,"description":"Top-p changes how the model selects tokens for output. Tokens are selected from most probable to least until the sum of their probabilities equals the top-p value. For example, if tokens A, B, and C have a probability of .3, .2, and .1 and the top-p value is .5, then the model will select either A or B as the next token (using temperature). The default top-p value is .8."},"top_k":{"label":"Tok K","min":1,"max":40,"default":40,"type":"number","input":"slider","step":1,"description":"Top-k changes how the model selects tokens for output. A top-k of 1 means the selected token is the most probable among all tokens in the model’s vocabulary (also called greedy decoding), while a top-k of 3 means that the next token is selected from among the 3 most probable tokens (using temperature). The default top-k value is 40."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Codechat Bison@Latest',
    slug: 'vertexai-codechat-bison-latest',
    reportSlug: 'codechat-bison',
    sequence: 4,
    platform: 'VERTEX_AI',
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0.2,"type":"number","input":"slider","step":0.1,"description":"Temperature controls the degree of randomness in token selection. Lower temperatures are good for prompts that expect a true or correct response, while higher temperatures can lead to more diverse or unexpected results. A temperature of 0 is deterministic: the highest probability token is always selected. For most use cases, try starting with a temperature of .2."},"max_tokens":{"label":"Token Limit","min":1,"max":1024,"default":256,"type":"number","input":"slider","step":1,"description":"Token limit determines the maximum amount of text output from one prompt. A token is approximately four characters. The default value is 256."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'ChatGPT 4 128K@0125',
    slug: 'gpt-4-0125',
    reportSlug: 'gpt-4-0125',
    platform: 'AZURE',
    isActive: false,
    sequence: 5,
    config:
      '{"temperature": {"label": "Temperature","min": 0,"max": 1,"default": 0,"type": "number","input": "slider","step": 0.1,"description": "What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top": {"label": "Num. of relevant docs to retrieve","min": 0,"max": 50,"default": 3,"type": "number","input": "slider","step": 1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p": {"label": "Top P","min": 0,"max": 1,"default": 1,"type": "number","input": "slider","step": 0.1,"description": "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens": {"label": "Max Tokens","min": 256,"max": 8192,"default": 4000,"type": "number","input": "slider","step": 1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"presence_penalty": {"label": "Presence Penalty","min": 0,"max": 2,"default": 0,"type": "number","input": "slider","step": 0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model\'s likelihood to talk about new topics."},"frequency_penalty": {"label": "Frequency Penalty","min": 0,"max": 2,"default": 0,"type": "number","input": "slider","step": 0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Gemini 1.5 Pro@Preview',
    slug: 'vertexai-gemini-1.5-pro-preview-0409',
    reportSlug: 'gemini-1.5-pro',
    platform: 'VERTEX_AI',
    isActive: false,
    sequence: 6,
    config:
      '{"temperature": {"label": "Temperature","min": 0,"max": 1,"default": 0.2,"type": "number","input": "slider","step": 0.1,"description": "What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top": {"label": "Num. of relevant docs to retrieve","min": 0,"max": 50,"default": 3,"type": "number","input": "slider","step": 1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p": {"label": "Top P","min": 0,"max": 1,"default": 0.94,"type": "number","input": "slider","step": 0.01,"description": "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens": {"label": "Max Tokens","min": 256,"max": 8192,"default": 4000,"type": "number","input": "slider","step": 1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Gemini 1.5 Flash@Preview',
    slug: 'vertexai-gemini-1.5-flash-preview-0514',
    reportSlug: 'gemini-1.5-flash',
    platform: 'VERTEX_AI',
    isActive: false,
    sequence: 7,
    config:
      '{"temperature": {"label": "Temperature","min": 0,"max": 1,"default": 0.2,"type": "number","input": "slider","step": 0.1,"description": "What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top": {"label": "Num. of relevant docs to retrieve","min": 0,"max": 50,"default": 3,"type": "number","input": "slider","step": 1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p": {"label": "Top P","min": 0,"max": 1,"default": 0.94,"type": "number","input": "slider","step": 0.01,"description": "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens": {"label": "Max Tokens","min": 256,"max": 8192,"default": 4000,"type": "number","input": "slider","step": 1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Gemini 1.5 Pro@001',
    slug: 'vertexai-gemini-1.5-pro-001',
    reportSlug: 'gemini-1.5-pro',
    platform: 'VERTEX_AI',
    isActive: false,
    sequence: 6,
    config:
      '{"temperature": {"label": "Temperature","min": 0,"max": 1,"default": 0.2,"type": "number","input": "slider","step": 0.1,"description": "What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top": {"label": "Num. of relevant docs to retrieve","min": 0,"max": 50,"default": 3,"type": "number","input": "slider","step": 1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p": {"label": "Top P","min": 0,"max": 1,"default": 0.94,"type": "number","input": "slider","step": 0.01,"description": "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens": {"label": "Max Tokens","min": 256,"max": 8192,"default": 4000,"type": "number","input": "slider","step": 1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length."},"internet_search": {"label": "Internet Search","min": 0,"max": 1,"default": 0,"type": "number","input": "slider","step": 1,"description": "Internet Search capability"},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Gemini 1.5 Pro@002',
    slug: 'vertexai-gemini-1.5-pro-002',
    reportSlug: 'gemini-1.5-pro-002',
    platform: 'VERTEX_AI',
    isActive: false,
    sequence: 6,
    config:
      '{"temperature": {"label": "Temperature","min": 0,"max": 1,"default": 0.2,"type": "number","input": "slider","step": 0.1,"description": "What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top": {"label": "Num. of relevant docs to retrieve","min": 0,"max": 50,"default": 3,"type": "number","input": "slider","step": 1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p": {"label": "Top P","min": 0,"max": 1,"default": 0.94,"type": "number","input": "slider","step": 0.01,"description": "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens": {"label": "Max Tokens","min": 256,"max": 8192,"default": 4000,"type": "number","input": "slider","step": 1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length."},"internet_search": {"label": "Internet Search","min": 0,"max": 1,"default": 0,"type": "number","input": "slider","step": 1,"description": "Internet Search capability"},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":2,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Gemini 1.5 Flash@001',
    slug: 'vertexai-gemini-1.5-flash-001',
    reportSlug: 'gemini-1.5-flash',
    platform: 'VERTEX_AI',
    isActive: false,
    sequence: 7,
    config:
      '{"temperature": {"label": "Temperature","min": 0,"max": 1,"default": 0.2,"type": "number","input": "slider","step": 0.1,"description": "What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top": {"label": "Num. of relevant docs to retrieve","min": 0,"max": 50,"default": 3,"type": "number","input": "slider","step": 1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p": {"label": "Top P","min": 0,"max": 1,"default": 0.94,"type": "number","input": "slider","step": 0.01,"description": "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens": {"label": "Max Tokens","min": 256,"max": 8192,"default": 4000,"type": "number","input": "slider","step": 1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length."},"internet_search": {"label": "Internet Search","min": 0,"max": 1,"default": 0,"type": "number","input": "slider","step": 1,"description": "Internet Search capability"},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Gemini 1.5 Flash@002',
    slug: 'vertexai-gemini-1.5-flash-002',
    reportSlug: 'gemini-1.5-flash-002',
    platform: 'VERTEX_AI',
    isActive: false,
    sequence: 7,
    config:
      '{"temperature": {"label": "Temperature","min": 0,"max": 1,"default": 0.2,"type": "number","input": "slider","step": 0.1,"description": "What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top": {"label": "Num. of relevant docs to retrieve","min": 0,"max": 50,"default": 3,"type": "number","input": "slider","step": 1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p": {"label": "Top P","min": 0,"max": 1,"default": 0.94,"type": "number","input": "slider","step": 0.01,"description": "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens": {"label": "Max Tokens","min": 256,"max": 8192,"default": 4000,"type": "number","input": "slider","step": 1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length."},"internet_search": {"label": "Internet Search","min": 0,"max": 1,"default": 0,"type": "number","input": "slider","step": 1,"description": "Internet Search capability"},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":2,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Gemini 2.0 Flash@001',
    slug: 'vertexai-gemini-2.0-flash-001',
    reportSlug: 'gemini-2.0-flash-001',
    platform: 'VERTEX_AI',
    isActive: false,
    sequence: 11,
    config:
      '{ "top": { "label": "Num. of relevant docs to retrieve", "min": 0, "max": 50, "default": 3, "type": "number", "input": "slider", "step": 1, "description": "Controls number of documents retrieving from OpenSearch on each user\'s query" }, "max_input_token": { "label": "Input max Tokens", "min": 256, "max": 1048576, "default": 5000, "type": "number", "input": "slider", "step": 1, "description": "Input max tokens", "readOnly": true }, "max_tokens": { "label": "Max Tokens", "min": 256, "max": 8192, "default": 4000, "type": "number", "input": "slider", "step": 1, "description": "The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length." }, "past_message": { "label": "Past message", "min": 1, "max": 9, "default": 1, "type": "number", "input": "slider", "step": 2, "description": "Number of past messages to include in each new API request." }, "top_p": { "label": "Top P", "min": 0, "max": 1, "default": 0.95, "type": "number", "input": "slider", "step": 0.01, "description": "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered." }, "temperature": { "label": "Temperature", "min": 0, "max": 1, "default": 1, "type": "number", "input": "slider", "step": 0.1, "description": "What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic." }}',
  },
  {
    name: 'Gemini 2.0 Flash Thinking Exp',
    slug: 'vertexai-gemini-2.0-flash-thinking-exp',
    reportSlug: 'gemini-2.0-flash-thinking-exp',
    platform: 'VERTEX_AI',
    isActive: false,
    sequence: 7,
    config:
      '{ "top": { "label": "Num. of relevant docs to retrieve", "min": 0, "max": 50, "default": 3, "type": "number", "input": "slider", "step": 1, "description": "Controls number of documents retrieving from OpenSearch on each user\'s query" }, "max_input_token": { "label": "Input max Tokens", "min": 256, "max": 1048576, "default": 5000, "type": "number", "input": "slider", "step": 1, "description": "Input max tokens", "readOnly": true }, "max_tokens": { "label": "Max Tokens", "min": 256, "max": 8192, "default": 4000, "type": "number", "input": "slider", "step": 1, "description": "The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length." }, "past_message": { "label": "Past message", "min": 1, "max": 9, "default": 1, "type": "number", "input": "slider", "step": 2, "description": "Number of past messages to include in each new API request." }, "top_p": { "label": "Top P", "min": 0, "max": 1, "default": 0.95, "type": "number", "input": "slider", "step": 0.01, "description": "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered." }, "temperature": { "label": "Temperature", "min": 0, "max": 1, "default": 1, "type": "number", "input": "slider", "step": 0.1, "description": "What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic." }}',
  },
  {
    name: 'Gemini 2.0 Flash Lite Preview',
    slug: 'vertexai-gemini-2.0-flash-lite-preview',
    reportSlug: 'gemini-2.0-flash-lite-preview',
    platform: 'VERTEX_AI',
    isActive: false,
    sequence: 13,
    config:
      '{ "top": { "label": "Num. of relevant docs to retrieve", "min": 0, "max": 50, "default": 3, "type": "number", "input": "slider", "step": 1, "description": "Controls number of documents retrieving from OpenSearch on each user\'s query" }, "max_input_token": { "label": "Input max Tokens", "min": 256, "max": 1048576, "default": 5000, "type": "number", "input": "slider", "step": 1, "description": "Input max tokens", "readOnly": true }, "max_tokens": { "label": "Max Tokens", "min": 256, "max": 8192, "default": 4000, "type": "number", "input": "slider", "step": 1, "description": "The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length." }, "past_message": { "label": "Past message", "min": 1, "max": 9, "default": 1, "type": "number", "input": "slider", "step": 2, "description": "Number of past messages to include in each new API request." }, "top_p": { "label": "Top P", "min": 0, "max": 1, "default": 0.95, "type": "number", "input": "slider", "step": 0.01, "description": "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered." }, "temperature": { "label": "Temperature", "min": 0, "max": 1, "default": 1, "type": "number", "input": "slider", "step": 0.1, "description": "What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic." }}',
  },
  {
    name: 'Gemini 2.0 Flash Lite',
    slug: 'vertexai-gemini-2.0-flash-lite',
    reportSlug: 'gemini-2.0-flash-lite',
    platform: 'VERTEX_AI',
    isActive: false,
    sequence: 12,
    config: `{"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user's query"},"max_input_token":{"label":" Max Input Tokens ","min":256,"max":1048576,"default":10000,"type":"number","input":"slider","step":1,"description":"Input max tokens","readOnly":true},"max_tokens":{"label":" Max Output Tokens ","min":256,"max":8192,"default":4096,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model's context length."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":2,"description":"Number of past messages to include in each new API request."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.95,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"temperature":{"label":"Temperature","min":0,"max":1,"default":1,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."}}`,
  },
  {
    name: 'Gemini 2.0 Pro',
    slug: 'vertexai-gemini-2.0-pro',
    reportSlug: 'gemini-2.0-pro',
    platform: 'VERTEX_AI',
    isActive: false,
    sequence: 9,
    config:
      '{ "top": { "label": "Num. of relevant docs to retrieve", "min": 0, "max": 50, "default": 3, "type": "number", "input": "slider", "step": 1, "description": "Controls number of documents retrieving from OpenSearch on each user\'s query" }, "max_input_token": { "label": "Input max Tokens", "min": 256, "max": 1048576, "default": 5000, "type": "number", "input": "slider", "step": 1, "description": "Input max tokens", "readOnly": true }, "max_tokens": { "label": "Max Tokens", "min": 256, "max": 8192, "default": 4000, "type": "number", "input": "slider", "step": 1, "description": "The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length." }, "past_message": { "label": "Past message", "min": 1, "max": 9, "default": 1, "type": "number", "input": "slider", "step": 2, "description": "Number of past messages to include in each new API request." }, "top_p": { "label": "Top P", "min": 0, "max": 1, "default": 0.95, "type": "number", "input": "slider", "step": 0.01, "description": "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered." }, "temperature": { "label": "Temperature", "min": 0, "max": 1, "default": 1, "type": "number", "input": "slider", "step": 0.1, "description": "What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic." }}',
  },
  {
    name: 'Gemini 2.5 Pro',
    slug: 'vertexai-gemini-2.5-pro',
    reportSlug: 'gemini-2.5-pro',
    platform: 'VERTEX_AI',
    isActive: false,
    sequence: 8,
    config: `{"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user's query"},"max_input_token":{"label":"Max Input Tokens","min":256,"max":1048576,"default":10000,"type":"number","input":"slider","step":1,"description":"Input max tokens","readOnly":true},"max_tokens":{"label":"Max Output Tokens","min":256,"max":65536,"default":8192,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model's context length."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":2,"description":"Number of past messages to include in each new API request."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.95,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"temperature":{"label":"Temperature","min":0,"max":1,"default":1,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."}}`,
  },
  {
    name: 'SenseChat 5 Cantonese',
    slug: 'nova-sensechat-5-cantonese',
    reportSlug: 'sensechat-5-cantonese',
    platform: 'SENSENOVA',
    sequence: 8,
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":0.01,"max":2,"default":0.8,"type":"number","input":"slider","step":0.01,"description":"What sampling temperature to use, between 0.01 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p":{"label":"Top P","min":0.01,"max":0.99,"default":0.7,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":32768,"default":2000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"repetition_penalty":{"label":"Repetition Penalty","min":0.05,"max":2,"default":1.05,"type":"number","input":"slider","step":0.01,"description":"Number between 0.05 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'SenseChat 5',
    slug: 'nova-sensechat-5',
    reportSlug: 'sensechat-5',
    platform: 'SENSENOVA',
    sequence: 9,
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":0.01,"max":2,"default":0.8,"type":"number","input":"slider","step":0.01,"description":"What sampling temperature to use, between 0.01 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p":{"label":"Top P","min":0.01,"max":0.99,"default":0.7,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":131072,"default":2000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"repetition_penalty":{"label":"Repetition Penalty","min":0.05,"max":2,"default":1.0,"type":"number","input":"slider","step":0.01,"description":"Number between 0.05 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'SenseChat 128K',
    slug: 'nova-sensechat-128K',
    reportSlug: 'sensechat-128k',
    platform: 'SENSENOVA',
    sequence: 10,
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":0.01,"max":2,"default":0.8,"type":"number","input":"slider","step":0.01,"description":"What sampling temperature to use, between 0.01 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p":{"label":"Top P","min":0.01,"max":0.99,"default":0.7,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":131072,"default":2000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"repetition_penalty":{"label":"Repetition Penalty","min":0.05,"max":2,"default":1.05,"type":"number","input":"slider","step":0.01,"description":"Number between 0.05 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'SenseNova XL',
    slug: 'nova-ptc-xl-v1',
    reportSlug: 'ptc-xl',
    platform: 'SENSENOVA',
    sequence: 11,
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top_p":{"label":"Top P","min":1,"max":10,"default":1,"type":"number","input":"slider","step":1,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":2048,"default":2000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"repetition_penalty":{"label":"Repetition Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'SenseNova XS',
    slug: 'nova-ptc-xs-v1',
    reportSlug: 'ptc-xs',
    platform: 'SENSENOVA',
    sequence: 12,
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top_p":{"label":"Top P","min":1,"max":10,"default":1,"type":"number","input":"slider","step":1,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":2048,"default":2000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"repetition_penalty":{"label":"Repetition Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'SenseNova YUE',
    slug: 'nova-ptc-yue-xl-v1',
    reportSlug: 'ptc-yue',
    platform: 'SENSENOVA',
    sequence: 13,
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top_p":{"label":"Top P","min":1,"max":10,"default":1,"type":"number","input":"slider","step":1,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":2048,"default":2000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"repetition_penalty":{"label":"Repetition Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'ChatGPT 4',
    slug: 'gpt-4',
    reportSlug: 'gpt-4',
    platform: 'AZURE',
    sequence: 14,
    isActive: true,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p":{"label":"Top P","min":1,"max":10,"default":1,"type":"number","input":"slider","step":1,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":8192,"default":1000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"presence_penalty":{"label":"Presence Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model\'s likelihood to talk about new topics."},"frequency_penalty":{"label":"Frequency Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'ChatGPT 4 Turbo',
    slug: 'gpt-4-turbo',
    reportSlug: 'gpt-4-turbo',
    platform: 'AZURE',
    sequence: 15,
    isActive: true,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p":{"label":"Top P","min":1,"max":10,"default":1,"type":"number","input":"slider","step":1,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":8192,"default":1000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"presence_penalty":{"label":"Presence Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model\'s likelihood to talk about new topics."},"frequency_penalty":{"label":"Frequency Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Chat Bison@Latest',
    slug: 'vertexai-chat-bison',
    reportSlug: 'chat-bison',
    platform: 'VERTEX_AI',
    sequence: 16,
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0.2,"type":"number","input":"slider","step":0.1,"description":"Temperature controls the degree of randomness in token selection. Lower temperatures are good for prompts that expect a true or correct response, while higher temperatures can lead to more diverse or unexpected results. A temperature of 0 is deterministic: the highest probability token is always selected. For most use cases, try starting with a temperature of .2."},"max_tokens":{"label":"Token Limit","min":1,"max":1024,"default":256,"type":"number","input":"slider","step":1,"description":"Token limit determines the maximum amount of text output from one prompt. A token is approximately four characters. The default value is 256."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.8,"type":"number","input":"slider","step":0.01,"description":"Top-p changes how the model selects tokens for output. Tokens are selected from most probable to least until the sum of their probabilities equals the top-p value. For example, if tokens A, B, and C have a probability of .3, .2, and .1 and the top-p value is .5, then the model will select either A or B as the next token (using temperature). The default top-p value is .8."},"top_k":{"label":"Tok K","min":1,"max":40,"default":40,"type":"number","input":"slider","step":1,"description":"Top-k changes how the model selects tokens for output. A top-k of 1 means the selected token is the most probable among all tokens in the model’s vocabulary (also called greedy decoding), while a top-k of 3 means that the next token is selected from among the 3 most probable tokens (using temperature). The default top-k value is 40."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Chat Bison@001',
    slug: 'vertexai-chat-bison-001',
    reportSlug: 'chat-bison-001',
    platform: 'VERTEX_AI',
    sequence: 17,
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0.2,"type":"number","input":"slider","step":0.1,"description":"Temperature controls the degree of randomness in token selection. Lower temperatures are good for prompts that expect a true or correct response, while higher temperatures can lead to more diverse or unexpected results. A temperature of 0 is deterministic: the highest probability token is always selected. For most use cases, try starting with a temperature of .2."},"max_tokens":{"label":"Token Limit","min":1,"max":1024,"default":256,"type":"number","input":"slider","step":1,"description":"Token limit determines the maximum amount of text output from one prompt. A token is approximately four characters. The default value is 256."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.8,"type":"number","input":"slider","step":0.01,"description":"Top-p changes how the model selects tokens for output. Tokens are selected from most probable to least until the sum of their probabilities equals the top-p value. For example, if tokens A, B, and C have a probability of .3, .2, and .1 and the top-p value is .5, then the model will select either A or B as the next token (using temperature). The default top-p value is .8."},"top_k":{"label":"Tok K","min":1,"max":40,"default":40,"type":"number","input":"slider","step":1,"description":"Top-k changes how the model selects tokens for output. A top-k of 1 means the selected token is the most probable among all tokens in the model’s vocabulary (also called greedy decoding), while a top-k of 3 means that the next token is selected from among the 3 most probable tokens (using temperature). The default top-k value is 40."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Codechat Bison@001',
    slug: 'vertexai-codechat-bison-001',
    reportSlug: 'codechat-bison-001',
    platform: 'VERTEX_AI',
    isActive: false,
    sequence: 18,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0.2,"type":"number","input":"slider","step":0.1,"description":"Temperature controls the degree of randomness in token selection. Lower temperatures are good for prompts that expect a true or correct response, while higher temperatures can lead to more diverse or unexpected results. A temperature of 0 is deterministic: the highest probability token is always selected. For most use cases, try starting with a temperature of .2."},"max_tokens":{"label":"Token Limit","min":1,"max":1024,"default":256,"type":"number","input":"slider","step":1,"description":"Token limit determines the maximum amount of text output from one prompt. A token is approximately four characters. The default value is 256."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Chat Bison@002',
    slug: 'vertexai-chat-bison-002',
    reportSlug: 'chat-bison-002',
    platform: 'VERTEX_AI',
    sequence: 17,
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0.2,"type":"number","input":"slider","step":0.1,"description":"Temperature controls the degree of randomness in token selection. Lower temperatures are good for prompts that expect a true or correct response, while higher temperatures can lead to more diverse or unexpected results. A temperature of 0 is deterministic: the highest probability token is always selected. For most use cases, try starting with a temperature of .2."},"max_tokens":{"label":"Token Limit","min":1,"max":2048,"default":256,"type":"number","input":"slider","step":1,"description":"Token limit determines the maximum amount of text output from one prompt. A token is approximately four characters. The default value is 256."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.8,"type":"number","input":"slider","step":0.01,"description":"Top-p changes how the model selects tokens for output. Tokens are selected from most probable to least until the sum of their probabilities equals the top-p value. For example, if tokens A, B, and C have a probability of .3, .2, and .1 and the top-p value is .5, then the model will select either A or B as the next token (using temperature). The default top-p value is .8."},"top_k":{"label":"Tok K","min":1,"max":40,"default":40,"type":"number","input":"slider","step":1,"description":"Top-k changes how the model selects tokens for output. A top-k of 1 means the selected token is the most probable among all tokens in the model’s vocabulary (also called greedy decoding), while a top-k of 3 means that the next token is selected from among the 3 most probable tokens (using temperature). The default top-k value is 40."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Codechat Bison@002',
    slug: 'vertexai-codechat-bison-002',
    reportSlug: 'codechat-bison-002',
    platform: 'VERTEX_AI',
    isActive: false,
    sequence: 18,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0.2,"type":"number","input":"slider","step":0.1,"description":"Temperature controls the degree of randomness in token selection. Lower temperatures are good for prompts that expect a true or correct response, while higher temperatures can lead to more diverse or unexpected results. A temperature of 0 is deterministic: the highest probability token is always selected. For most use cases, try starting with a temperature of .2."},"max_tokens":{"label":"Token Limit","min":1,"max":2048,"default":256,"type":"number","input":"slider","step":1,"description":"Token limit determines the maximum amount of text output from one prompt. A token is approximately four characters. The default value is 256."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'ChatGPT 3.5 Turbo@0613',
    slug: 'gpt-35-turbo-0613',
    reportSlug: 'gpt-35-0613',
    platform: 'AZURE',
    sequence: 19,
    isActive: true,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p":{"label":"Top P","min":1,"max":10,"default":1,"type":"number","input":"slider","step":1,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":4096,"default":1000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"presence_penalty":{"label":"Presence Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model\'s likelihood to talk about new topics."},"frequency_penalty":{"label":"Frequency Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'ChatGPT 3.5 Turbo@1106',
    slug: 'gpt-35-turbo-1106',
    reportSlug: 'gpt-35-1106',
    platform: 'AZURE',
    sequence: 20,
    isActive: true,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p":{"label":"Top P","min":1,"max":10,"default":1,"type":"number","input":"slider","step":1,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":4096,"default":1000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"presence_penalty":{"label":"Presence Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model\'s likelihood to talk about new topics."},"frequency_penalty":{"label":"Frequency Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'ChatGPT 3.5 Turbo',
    slug: 'gpt-35-turbo',
    reportSlug: 'gpt-35',
    sequence: 21,
    platform: 'AZURE',
    isActive: false,
    config:
      '{"temperature":{"label":"Temperature","min":0,"max":1,"default":0,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."},"top":{"label":"Num. of relevant docs to retrieve","min":0,"max":50,"default":3,"type":"number","input":"slider","step":1,"description":"Controls number of documents retrieving from OpenSearch on each user\'s query"},"top_p":{"label":"Top P","min":1,"max":10,"default":1,"type":"number","input":"slider","step":1,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"max_tokens":{"label":"Max Tokens","min":256,"max":4096,"default":1000,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model\'s context length. Example Python code for counting tokens."},"presence_penalty":{"label":"Presence Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model\'s likelihood to talk about new topics."},"frequency_penalty":{"label":"Frequency Penalty","min":-2,"max":2,"default":0,"type":"number","input":"slider","step":0.2,"description":"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model\'s likelihood to repeat the same line verbatim."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Stable Diffusion@Text to Image',
    slug: 'sd-txt2img',
    reportSlug: 'txt2img',
    platform: 'AWS',
    sequence: 22,
    isActive: false,
    config:
      '{ "height": { "label": "Height", "min": 64, "max": 2048, "default": 512, "type": "number", "input": "slider", "step": 64, "description": "" }, "width": { "label": "Width", "min": 64, "max": 2048, "default": 512, "type": "number", "input": "slider", "step": 64, "description": "" }, "batch_size": { "label": "Batch Size", "min": 1, "max": 8, "default": 1, "type": "number", "input": "slider", "step": 1, "description": "Number of images in a batch" }, "cfg_scale": { "label": "CFG Scale", "min": 1.0, "max": 30.0, "default": 7.0, "type": "number", "input": "slider", "step": 0.5, "description": "The CFG scale adjusts how much the image looks closer to the prompt" }, "enable_hr": { "label": "Hires. fix", "default": false, "type": "checkbox", "description": "Enable Hires. fix" }, "hr_scale": { "label": "Hires. fix Scale", "min": 1.0, "max": 4.0, "default": 2.0, "type": "number", "input": "slider", "step": 0.05, "description": "Require Hires. fix" } }',
  },
  {
    name: 'MetaGPT',
    slug: 'metagpt',
    reportSlug: 'metagpt',
    platform: 'OPENAI',
    isActive: false,
    sequence: 23,
    config: '{}',
  },
  {
    name: 'Dalle 3',
    slug: 'dalle-3',
    reportSlug: 'dalle-3',
    platform: 'AZURE',
    isActive: false,
    sequence: 24,
    config:
      '{"size":{"label":"size","input":"dropdown","type":"string","values":["1024x1024","1792x1024","1024x1792"],"default":"1024x1024","description":"Size of image"},"style":{"label":"style","input":"dropdown","type":"string","values":["vivid","natural"],"default":"vivid","description":"Vivid style generates more hyper-real and cinematic images."},"quality":{"label":"quality","input":"dropdown","type":"string","values":["standard","hd"],"default":"standard","description":"hd creates images with finer details and greater consistency across the image. standard images can be generated faster"},"past_message":{"label":"Past message","min":1,"max":1,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Imagen 3 @001',
    slug: 'vertexai-imagen-3.0-generate-001',
    reportSlug: 'vertexai-imagen-3.0-generate-001',
    platform: 'VERTEX_AI',
    isActive: false,
    sequence: 25,
    config:
      '{"feature":{"label":"feature","input":"dropdown","type":"string","values":["image_generation"],"default":"image_generation","description":"feature"},"number_of_images":{"label":"number of images","min":1,"max":4,"default":1,"type":"number","input":"slider","description":"Number of images to be generated"},"aspect_ratio":{"label":"aspect ratio","input":"dropdown","type":"string","values":["1:1","9:16","16:9","3:4","4:3"],"default":"16:9","description":"Aspect ratio of the image"},"person_generation":{"label":"person / face generation","input":"dropdown","type":"string","values":["allow_all","allow_adult","dont_allow"],"default":"allow_all","description":"If set to Don\'t allow, images with people and faces may be blocked. Allow All for allowing all ages. Allow Adult for allowing adults only."},"safety_filter_level":{"label":"safety filter","input":"dropdown","type":"string","values":["block_few","block_some","block_most"],"default":"block_few","description":"Adjust the likelihood of receiving a model response that could contain harmful content."},"past_message":{"label":"Past message","min":1,"max":1,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Imagen 3 Fast@001',
    slug: 'vertexai-imagen-3.0-fast-generate-001',
    reportSlug: 'vertexai-imagen-3.0-fast-generate-001',
    platform: 'VERTEX_AI',
    isActive: false,
    sequence: 26,
    config:
      '{"feature":{"label":"feature","input":"dropdown","type":"string","values":["image_generation"],"default":"image_generation","description":"feature"},"number_of_images":{"label":"number of images","min":1,"max":4,"default":1,"type":"number","input":"slider","description":"Number of images to be generated"},"aspect_ratio":{"label":"aspect ratio","input":"dropdown","type":"string","values":["1:1","9:16","16:9","3:4","4:3"],"default":"16:9","description":"Aspect ratio of the image"},"person_generation":{"label":"person / face generation","input":"dropdown","type":"string","values":["allow_all","allow_adult","dont_allow"],"default":"allow_all","description":"If set to Don\'t allow, images with people and faces may be blocked. Allow All for allowing all ages. Allow Adult for allowing adults only."},"safety_filter_level":{"label":"safety filter","input":"dropdown","type":"string","values":["block_few","block_some","block_most"],"default":"block_few","description":"Adjust the likelihood of receiving a model response that could contain harmful content."},"past_message":{"label":"Past message","min":1,"max":1,"default":1,"type":"number","input":"slider","step":1,"description":"Number of past messages to include in each new API request."}}',
  },
  {
    name: 'Qwen Max',
    slug: 'qwen-max',
    reportSlug: 'qwen-max',
    platform: 'ALIBABA',
    isActive: false,
    sequence: 3,
    config: `{"max_input_token":{"label":"Max Input Tokens","min":256,"max":32768,"default":24000,"type":"number","input":"slider","step":1,"description":"Input max tokens","readOnly":true},"max_tokens":{"label":"Max Output Tokens","min":256,"max":8192,"default":4096,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model's context length."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":2,"description":"Number of past messages to include in each new API request."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.95,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"temperature":{"label":"Temperature","min":0,"max":1,"default":1,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."}}`,
  },
  {
    name: 'Qwen Plus',
    slug: 'qwen-plus',
    reportSlug: 'qwen-plus',
    platform: 'ALIBABA',
    isActive: false,
    sequence: 3,
    config: `{"max_input_token":{"label":"Max Input Tokens","min":256,"max":131072,"default":50000,"type":"number","input":"slider","step":1,"description":"Input max tokens","readOnly":true},"max_tokens":{"label":"Max Output Tokens","min":256,"max":8192,"default":4096,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model's context length."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":2,"description":"Number of past messages to include in each new API request."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.95,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"temperature":{"label":"Temperature","min":0,"max":1,"default":1,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."}}`,
  },
  {
    name: 'Qwen Turbo',
    slug: 'qwen-turbo',
    reportSlug: 'qwen-turbo',
    platform: 'ALIBABA',
    isActive: false,
    sequence: 3,
    config: `{"max_input_token":{"label":"Max Input Tokens","min":256,"max":1000000,"default":256000,"type":"number","input":"slider","step":1,"description":"Input max tokens","readOnly":true},"max_tokens":{"label":"Max Output Tokens","min":256,"max":8192,"default":8192,"type":"number","input":"slider","step":1,"description":"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model's context length."},"past_message":{"label":"Past message","min":1,"max":9,"default":1,"type":"number","input":"slider","step":2,"description":"Number of past messages to include in each new API request."},"top_p":{"label":"Top P","min":0,"max":1,"default":0.95,"type":"number","input":"slider","step":0.01,"description":"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered."},"temperature":{"label":"Temperature","min":0,"max":1,"default":1,"type":"number","input":"slider","step":0.1,"description":"What sampling temperature to use, between 0 and 1. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."}}`,
  },
];

// get unique llm engine platform
export const getAllLlmEnginePlatforms: () => string[] = () => [
  ...new Set(llmEnginesData.map((engine) => engine.platform)),
];

// get unique llm engine
export const getAllEngines: () => LlMEngineSeed[] = () =>
  llmEnginesData.reduce((acc, curr) => {
    if (!acc.some((item) => item.slug === curr.slug)) {
      acc.push(curr);
    }
    return acc;
  }, [] as LlMEngineSeed[]);
