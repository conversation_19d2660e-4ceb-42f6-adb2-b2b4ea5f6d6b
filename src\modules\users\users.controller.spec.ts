import { UsersService } from './users.service';
import { ModuleMocker, MockFunctionMetadata } from 'jest-mock';
import { mockDeep, DeepMockProxy } from 'jest-mock-extended';
import { Test, TestingModule } from '@nestjs/testing';

import { UserController } from './users.controller';
import { RedisService } from 'src/providers/redis/redis.service';
const moduleMocker = new ModuleMocker(global);

describe('UsersService', () => {
  let usersService: DeepMockProxy<UsersService>;
  let redisService: DeepMockProxy<RedisService>;

  let userController: UserController;
  beforeEach(async () => {
    usersService = mockDeep<UsersService>();
    redisService = mockDeep<RedisService>();
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        { provide: UsersService, useValue: usersService },
        { provide: RedisService, useValue: redisService },
      ],
    })
      .useMocker((token) => {
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>;
          const Mock = moduleMocker.generateFromMetadata(mockMetadata);
          return new Mock();
        }
      })
      .compile();
    userController = module.get(UserController);
  });

  describe('get completness', () => {
    it('test', async () => {
      usersService.getUserCompleteness.mockResolvedValue(true);
      const res = await userController.getCompletenessStatus(1);
      expect(res).toEqual({ completeness: true });
    });
  });
});
