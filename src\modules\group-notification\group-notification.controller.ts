import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Scopes } from '../auth/scope.decorator';
import { OptionalIntPipe } from '../../pipes/optional-int.pipe';
import { WherePipe } from '../../pipes/where.pipe';
import { OrderByPipe } from '../../pipes/order-by.pipe';
import { CronJobApiKeyScopes } from '../../constants/cron-job';
import { GroupNotificationConfigDto, SendGroupNotificationDto } from './group-notification.dto';
import { GroupNotificationService } from './group-notification.service';

@Controller('group-notification')
@ApiBearerAuth('bearer-auth')
@ApiTags('Group Notification')
export class GroupNotificationController {
  constructor(private readonly groupNotificationService: GroupNotificationService) {}

  @HttpCode(HttpStatus.OK)
  @Get(':groupId/config')
  @Scopes('group-{groupId}:read-notifications-config')
  async getGroupNotification(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    const list = await this.groupNotificationService.getGroupNotificationsConfig(
      groupId,
      skip,
      take,
      where,
      orderBy,
    );
    const count = await this.groupNotificationService.getGroupNotificationsConfigCount(
      groupId,
      where,
    );
    return { list, count };
  }

  @HttpCode(HttpStatus.OK)
  @Patch(':groupId/config/:groupNotificationId')
  @Scopes('group-{groupId}:write-notifications-config')
  async updateGroupNotification(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('groupNotificationId', ParseIntPipe) groupNotificationId: number,
    @Body() groupNotificationConfigDto: GroupNotificationConfigDto,
  ) {
    return await this.groupNotificationService.updateGroupNotificationsConfig(
      groupId,
      groupNotificationId,
      groupNotificationConfigDto,
    );
  }

  @HttpCode(HttpStatus.OK)
  @Post(':groupId/send')
  @Scopes(CronJobApiKeyScopes.SEND_GROUP_NOTIFICATION)
  async sendGroupNotification(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() sendGroupNotificationDto: SendGroupNotificationDto,
  ) {
    return await this.groupNotificationService.sendGroupNotification(
      groupId,
      sendGroupNotificationDto,
    );
  }

  @HttpCode(HttpStatus.OK)
  @Get('enable/groups')
  @Scopes(CronJobApiKeyScopes.READ_GROUP_NOTIFICATION_CONFIG)
  async getEnableNotificationConfig(@Query('notificationKey') notificationKey: string) {
    const list =
      await this.groupNotificationService.getEnabledGroupNotificationsConfig(notificationKey);
    return { list };
  }
}
