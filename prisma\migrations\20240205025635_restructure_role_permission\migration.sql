/*
  Warnings:

  - You are about to drop the column `apiKeyAllowed` on the `Permission` table. All the data in the column will be lost.
  - You are about to drop the column `isOwnerOnly` on the `Permission` table. All the data in the column will be lost.
  - You are about to drop the column `isGlobal` on the `Role` table. All the data in the column will be lost.
  - You are about to drop the `PermissionGroupType` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `UserPermission` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[name,groupId,systemName]` on the table `Role` will be added. If there are existing duplicate values, this will fail.
  - Made the column `groupId` on table `Role` required. This step will fail if there are existing NULL values in that column.

*/
-- CreateEnum
CREATE TYPE "SystemName" AS ENUM ('SUDO', 'OPERATION_TEAM', 'SECURITY_TEAM', 'ACCOUNT_MANAGEMENT', 'SENIOR_MANAGEMENT', 'BOT_CREATOR', 'USER', '<PERSON><PERSON><PERSON>_OWNER', 'G<PERSON>UP_ADMIN', 'G<PERSON>UP_MEMBER', 'GROUP_CUSTOM');

-- CreateEnum
CREATE TYPE "PermissionType" AS ENUM ('SYSTEM', 'USER', 'GROUP');

-- CreateEnum
CREATE TYPE "RoleType" AS ENUM ('SYSTEM_DEFAULT', 'GROUP_DEFAULT', 'GROUP_CUSTOM');

-- AlterEnum
ALTER TYPE "Environment" ADD VALUE 'ALL';

-- DropIndex
DROP INDEX "Role_groupId_idx";

-- DropIndex
DROP INDEX "Role_isGlobal_idx";

-- DropIndex
DROP INDEX "Role_name_groupId_key";

--- AlterTable
Alter TABLE "Membership" ADD COLUMN "tmpRoleId" INTEGER;
UPDATE "Membership" m SET "tmpRoleId" =  m."roleId";

-- AlterTable
ALTER TABLE "Permission" DROP COLUMN "apiKeyAllowed",
DROP COLUMN "isOwnerOnly",
ADD COLUMN   "env" "Environment",
ADD COLUMN   "permissionType" "PermissionType" NOT NULL DEFAULT 'SYSTEM';

-- AlterTable
ALTER TABLE "Role" DROP COLUMN "isGlobal",
ADD COLUMN  "roleType" "RoleType" NOT NULL DEFAULT 'SYSTEM_DEFAULT',
ADD COLUMN  "systemName" "SystemName" NOT NULL DEFAULT 'USER',
ALTER COLUMN "groupId" SET NOT NULL,
ALTER COLUMN "groupId" SET DEFAULT 0;

-- AlterTable
ALTER TABLE "User" ADD COLUMN  "roleId" INTEGER DEFAULT 0;

-- DropTable
DROP TABLE "PermissionGroupType";

-- DropTable
DROP TABLE "UserPermission";

-- CreateTable
CREATE TABLE "PermissionGroupSetting" (
    "permissionId" INTEGER NOT NULL,
    "groupType" "GroupType" NOT NULL DEFAULT 'BOT',
    "createdAt" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(6),
    "isCustomRoleAllowed" BOOLEAN NOT NULL DEFAULT true,
    "isApiKeyAllowed" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "PermissionGroupSetting_pkey" PRIMARY KEY ("permissionId","groupType")
);

-- CreateIndex
CREATE INDEX "PermissionGroupSetting_permissionId_groupType_isCustomRoleA_idx" ON "PermissionGroupSetting"("permissionId", "groupType", "isCustomRoleAllowed");

-- CreateIndex
CREATE INDEX "PermissionGroupSetting_permissionId_groupType_isApiKeyAllow_idx" ON "PermissionGroupSetting"("permissionId", "groupType", "isApiKeyAllowed");

-- CreateIndex
CREATE INDEX "Permission_permissionKey_idx" ON "Permission"("permissionKey");

-- CreateIndex
CREATE INDEX "Permission_permissionType_env_idx" ON "Permission"("permissionType", "env");

-- CreateIndex
CREATE INDEX "Role_roleType_groupId_idx" ON "Role"("roleType", "groupId");

-- CreateIndex
CREATE UNIQUE INDEX "Role_name_groupId_systemName_key" ON "Role"("name", "groupId", "systemName");
