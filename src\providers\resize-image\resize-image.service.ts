import { Injectable, Logger } from '@nestjs/common';
import sharp from 'sharp';

@Injectable()
export class ResizeImageService {
  private logger = new Logger(ResizeImageService.name);

  constructor() {}

  async resizeImage(file: Buffer, width: number, height: number): Promise<Buffer> {
    try {
      const resizedImage = await sharp(file).resize(width, height).toBuffer();
      return resizedImage;
    } catch (error) {
      this.logger.error(error, 'Failed to resize Image err');
      throw new Error();
    }
  }
}
