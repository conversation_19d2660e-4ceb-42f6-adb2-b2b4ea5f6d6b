apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "bot-builder-backend.fullname" . }}-test-connection"
  labels:
    {{- include "bot-builder-backend.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "bot-builder-backend.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
