import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { GeolocationModule } from '../../providers/geolocation/geolocation.module';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { ApprovedSubnetsService } from './approved-subnets.service';

@Module({
  imports: [PrismaModule, ConfigModule, GeolocationModule],
  providers: [ApprovedSubnetsService],
})
export class ApprovedSubnetsModule {}
