import { forwardRef, Module } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { LlmEnginesController } from './llm-engines.controller';
import { LlmEnginesService } from './llm-engines.service';
import { RedisModule } from 'src/providers/redis/redis.module';
import { ScopeModule } from '../scope/scope.module';
import { LabelsModule } from '../labels/labels.module';
import { PlansModule } from '../plans/plans.module';
import { QuotaModule } from '../quotas/quota.module';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';
import { ConfigService } from '@nestjs/config';
import { ResizeImageModule } from 'src/providers/resize-image/resize-image.module';
import { S3Module } from '../../providers/s3/s3.module';
import { TokensModule } from 'src/providers/tokens/tokens.module';

@Module({
  imports: [
    PrismaModule,
    RedisModule,
    ScopeModule,
    LabelsModule,
    forwardRef(() => PlansModule),
    QuotaModule,
    FeatureFlagModule,
    ResizeImageModule,
    S3Module,
    TokensModule,
  ],
  controllers: [LlmEnginesController],
  providers: [LlmEnginesService, ConfigService],
  exports: [LlmEnginesService],
})
export class LlmEnginesModule {}
