import {
  Body,
  Controller,
  Get,
  Logger,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  UsePipes,
  ValidationPipe,
  Version,
  VERSION_NEUTRAL,
} from '@nestjs/common';
import { ApiBearerAuth, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { Environment, GroupType, Prisma, ResourceSubsciberType } from '@prisma/client';
import { AuditLog } from 'src/modules/audit-logs/audit-log.decorator';
import { UserRequest } from 'src/modules/auth/auth.interface';
import { LLMModelsService } from 'src/modules/llm-models/llm-models.service';
import { PlansService } from 'src/modules/plans/plans.service';
import { OptionalIntPipe } from '../../../pipes/optional-int.pipe';
import { OrderByPipe } from '../../../pipes/order-by.pipe';
import { WherePipe } from '../../../pipes/where.pipe';
import { Scopes } from '../../auth/scope.decorator';
import { SummaryService } from '../../summary/summary.service';
import {
  ActivateGroupDto,
  CreateBotResourcePlanDto,
  GroupListRes,
  GroupsQueueDto,
  PlanRequestOperateDto,
  SetBotResourcePlanActivateDto,
  SetBotResourcePlanDefaultDto,
  UpdateBotResourcePlanDto,
} from '../groups.dto';
import { GroupsService } from '../groups.service';
import { EnumPipe } from 'src/pipes/enum.pipe';

@ApiSecurity('x-api-key')
@ApiBearerAuth('bearer-auth')
@ApiTags('Groups')
@Controller('groups')
export class GroupAdminController {
  private logger = new Logger(GroupAdminController.name);
  constructor(
    private groupsService: GroupsService,
    private plansService: PlansService,
    private summaryService: SummaryService,
    private llmModelService: LLMModelsService,
  ) {}
  /** Get groups */
  @Get()
  @Scopes('group-*:read-info')
  @UsePipes(
    new ValidationPipe({
      whitelist: true,
    }),
  )
  async getAll(@Query() queueDto: GroupsQueueDto): Promise<GroupListRes> {
    const count = await this.summaryService.getGroupCount(queueDto.where.groupType, queueDto.where);
    const list = await this.summaryService.getGroups(queueDto);
    return {
      list,
      count,
    };
  }

  @Patch(':groupId/activate')
  @AuditLog('activate-or-deactivate-group')
  @Scopes('group-{groupId}:activate')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async activate(@Body() data: ActivateGroupDto, @Param('groupId', ParseIntPipe) id: number) {
    return this.llmModelService.activateGroup(id, data);
  }

  @Post(':groupId/deprecate')
  @AuditLog('deprecate-group')
  @Scopes('group-{groupId}:deprecate')
  async deprecateGroup(@Param('groupId', ParseIntPipe) groupId: number) {
    return this.groupsService.deprecateGroup(groupId);
  }

  // list all group request list
  @Get('resources/plans/requests')
  @Scopes('system:read-resource-plan')
  async getGroupPlanRequestList(
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, unknown>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    const newWhere: Record<string, unknown> = {
      ...where,
      subscriberEntityType: { in: Object.keys(GroupType) as ResourceSubsciberType[] },
    };
    if (where?.['groupName']) {
      newWhere['subscribedGroup'] = { name: where['groupName'] };
      delete newWhere['groupName'];
    }
    const list = await this.plansService.getPlanRequests(newWhere, orderBy, true, skip, take);
    const count = await this.plansService.getPlanRequestCount(newWhere);
    return { list, count };
  }

  // get group request detail
  @Get('resources/plans/request/:requestId')
  @Scopes('system:read-resource-plan')
  async getGroupPlanRequestDetail(@Param('requestId') requestId: number) {
    return await this.plansService.getPlanRequestDetail(requestId, true);
  }

  // approve or reject request detail
  @Post('resources/plans/request/:requestId/:status')
  @Scopes('system:operate-resource-plan')
  @AuditLog('operate-plan-request')
  async operateGroupPlanRequest(
    @Param('requestId') requestId: number,
    @Param('status') status: 'approve' | 'reject',
    @Body() data: PlanRequestOperateDto,
    @Req() request: UserRequest,
  ) {
    if (status === 'approve') {
      return this.plansService.approvePlanRequest(requestId, request.user.id, data.crNum);
    } else {
      return this.plansService.rejectPlanRequest(
        requestId,
        request.user.id,
        data.crNum,
        data.rejectReason,
      );
    }
  }

  // list all group resource categories
  @Get('/resources/category/bots/:env')
  @Scopes('system:read-resource-plan')
  async getAllGroupResourceCategories() {
    return await this.groupsService.getAllGroupResourceCategories();
  }

  // list all group plan resources
  @Get('/resources/bots/:env')
  @Scopes('system:read-resource-plan')
  async getAllGroupResources(@Param('env') env: Environment) {
    return await this.groupsService.getAllGroupResources(env);
  }

  // list all group plan resource plans
  @Get('/resources/plans/bots/:env')
  @Scopes('system:read-resource-plan')
  async getAllGroupResourcePlans(@Param('env') env: Environment) {
    return await this.groupsService.getAllGroupResourcePlans(env);
  }

  // create new bot plan
  @Post('/resources/plans/bots')
  @Scopes('system:write-resource-plan')
  async createBotResourcePlan(@Body() data: CreateBotResourcePlanDto) {
    return await this.groupsService.createBotResourcePlan(data);
  }

  // update bot plan
  @Patch('/resources/plans/bots')
  @Scopes('system:write-resource-plan')
  async updateBotResourcePlan(@Body() data: UpdateBotResourcePlanDto) {
    return await this.groupsService.updateBotResourcePlan(data);
  }

  // update bot plan default
  @Patch('/resources/plans/default/bots')
  @Scopes('system:write-resource-plan')
  async updateBotResourcePlanDefault(@Body() data: SetBotResourcePlanDefaultDto) {
    return await this.groupsService.setBotResourcePlanDefault(data);
  }

  // deactivate or activate bot plan
  @Patch('/resources/plans/activate/bots')
  @Scopes('system:write-resource-plan')
  async updateBotResourcePlanActivate(@Body() data: SetBotResourcePlanActivateDto) {
    return await this.groupsService.setBotResourcePlanActivate(data);
  }
}
