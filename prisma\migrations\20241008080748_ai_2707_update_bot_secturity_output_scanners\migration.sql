UPDATE "BotSecurity"
SET "outputScanners" = '[{"type": "Sensitive", "params": [{"name": "threshold", "type": "float", "value": 0.5}, {"name": "redact", "type": "bool", "value": true}, {"name": "allowed_names", "type": "Array", "value": []}], "explanation": " A class used to detect sensitive (PII) data in the output of a language model.This class uses the Presidio Analyzer Engine and predefined internally patterns (patterns.py) to analyze the output for specified entity types.If no entity types are specified, it defaults to checking for all entity types.", "securityLevel": "SOFT"}]'::jsonb
WHERE "securityId" = 'DefaultTEST';

UPDATE "BotSecurity"
SET "outputScanners" = '[{"type": "Sensitive", "params": [{"name": "threshold", "type": "float", "value": 0.5}, {"name": "redact", "type": "bool", "value": true}, {"name": "allowed_names", "type": "Array", "value": []}], "explanation": " A class used to detect sensitive (PII) data in the output of a language model.This class uses the Presidio Analyzer Engine and predefined internally patterns (patterns.py) to analyze the output for specified entity types.If no entity types are specified, it defaults to checking for all entity types.", "securityLevel": "SOFT"}]'::jsonb
WHERE "securityId" = 'DefaultPROD';