import { Module } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { MutilpleLevelFeatureFlagsController } from './mutilple-level-feature-flags.controller';
import { MutilpleLevelFeatureFlagsService } from './mutilple-level-feature-flags.service';
import { MutilpleLevelFeatureFlagsGroupController } from './mutilple-level-feature-flags.controller-group';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';
import { GroupsModule } from '../groups/groups.module';

@Module({
  imports: [PrismaModule, FeatureFlagModule, GroupsModule],
  controllers: [MutilpleLevelFeatureFlagsController, MutilpleLevelFeatureFlagsGroupController],
  providers: [MutilpleLevelFeatureFlagsService],
  exports: [MutilpleLevelFeatureFlagsService],
})
export class MutilpleLevelFeatureFlagsModule {}
