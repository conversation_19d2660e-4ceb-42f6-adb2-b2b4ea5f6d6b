import { PrismaService } from 'src/providers/prisma/prisma.service';
import { ModuleMocker, MockFunctionMetadata } from 'jest-mock';
import { DeepMockProxy, mockDeep } from 'jest-mock-extended';
import { Test, TestingModule } from '@nestjs/testing';
import { Environment, LlmEngine, PrismaClient } from '@prisma/client';
import { LlmEnginesService } from './llm-engines.service';
import { CreateLlmEngineDto, UpdateLlmEngineDto } from './llm-engines.dto';
import { UserRequest } from '../auth/auth.interface';

const moduleMocker = new ModuleMocker(global);

describe('FeatureFlagService', () => {
  let llmEnginesService: LlmEnginesService;
  let prismaService: DeepMockProxy<{ [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'> }>;
  beforeEach(async () => {
    prismaService = mockDeep<PrismaClient>() as unknown as DeepMockProxy<{
      [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'>;
    }>;
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LlmEnginesService,
        {
          provide: PrismaService,
          useValue: prismaService,
        },
      ],
    })
      .useMocker((token) => {
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>;
          const Mock = moduleMocker.generateFromMetadata(mockMetadata);
          return new Mock();
        }
      })
      .compile();
    llmEnginesService = module.get(LlmEnginesService);
  });

  describe('findAll', () => {
    it('should return llm Engine list', async () => {
      const llmEngineList = [
        {
          id: 1,
          name: 'ChatGPT 4 128K@0125',
          slug: 'gpt-4-0125',
        },
        {
          id: 2,
          name: 'Chat Bison 32K@Latest',
          slug: 'vertexai-chat-bison-32k',
        },
      ] as LlmEngine[];
      prismaService.llmEngine.findMany.mockResolvedValue(llmEngineList);
      const res = await llmEnginesService.findAll({});
      expect(res).toEqual(llmEngineList);
    });
  });

  describe('count', () => {
    it('should return llm Engine count', async () => {
      const llmEngineCount = 10;
      prismaService.llmEngine.count.mockResolvedValue(llmEngineCount);
      const res = await llmEnginesService.count({ includesInactive: true });
      expect(res).toEqual(llmEngineCount);
    });
  });

  describe('create', () => {
    it('should return llm Engine', async () => {
      const req = {
        name: 'test',
        slug: 'slug-test',
        reportSlug: 'reportSlug-test',
      } as CreateLlmEngineDto;

      const llmEngine = {
        id: 1,
        slug: 'gpt-4-0125',
      } as LlmEngine;
      prismaService.llmEngine.create.mockResolvedValue(llmEngine);
      const res = await llmEnginesService.create(req);
      expect(res).toEqual(llmEngine);
      expect(prismaService.llmEngine.create).toHaveBeenCalledWith({ data: req });
    });
  });

  describe('update', () => {
    it('should return llm Engine', async () => {
      const req = {
        name: 'test',
        slug: 'slug-test',
        reportSlug: 'reportSlug-test',
      } as UpdateLlmEngineDto;

      const userReq = {
        user: { id: 1 },
      };

      const llmEngine = {
        id: 1,
        slug: 'gpt-4-0125',
      } as LlmEngine;
      prismaService.llmEngine.update.mockResolvedValue(llmEngine);
      const res = await llmEnginesService.update(1, req, userReq as UserRequest);
      expect(res).toEqual(llmEngine);
      expect(prismaService.llmEngine.update).toHaveBeenCalledWith({ where: { id: 1 }, data: req });
    });
  });

  describe('findDefaulLlmEngine', () => {
    it('should return defaut llm Engine', async () => {
      const llmEngine = {
        id: 1,
        slug: 'gpt-4-0125',
      } as LlmEngine;
      prismaService.llmEngine.findFirst.mockResolvedValue(llmEngine);
      const res = await llmEnginesService.findDefaulLlmEngine('slug-test');
      expect(res).toEqual(llmEngine);
      expect(prismaService.llmEngine.findFirst).toHaveBeenCalledWith({
        where: { slug: 'slug-test', env: Environment.TEST },
      });
    });
  });
});
