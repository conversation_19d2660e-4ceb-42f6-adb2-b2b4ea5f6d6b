-- Custom SQL:
UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":20,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":1,\"max\":10,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":4096,\"default\":1000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"presence_penalty\":{\"label\":\"Presence Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model''s likelihood to talk about new topics.\"},\"frequency_penalty\":{\"label\":\"Frequency Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"}}"'::jsonb
	WHERE slug IN ('gpt-35-turbo', 'gpt-35-turbo-0613', 'gpt-35-turbo-1106');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":20,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":1,\"max\":10,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":16384,\"default\":1000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"presence_penalty\":{\"label\":\"Presence Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model''s likelihood to talk about new topics.\"},\"frequency_penalty\":{\"label\":\"Frequency Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"}}"'::jsonb
	WHERE slug IN ('gpt-35-turbo-16k');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":20,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":1,\"max\":10,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":8192,\"default\":1000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"presence_penalty\":{\"label\":\"Presence Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model''s likelihood to talk about new topics.\"},\"frequency_penalty\":{\"label\":\"Frequency Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"}}"'::jsonb
	WHERE slug IN ('gpt-4', 'gpt-4-turbo');

UPDATE public."LlmEngine"
	SET config='"{\"temperature\":{\"label\":\"Temperature\",\"min\":0,\"max\":1,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.1,\"description\":\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"},\"top\":{\"label\":\"Num. of relevant docs to retrieve\",\"min\":0,\"max\":20,\"default\":3,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"Controls number of documents retrieving from OpenSearch on each user''s query\"},\"top_p\":{\"label\":\"Top P\",\"min\":1,\"max\":10,\"default\":1,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\"},\"max_tokens\":{\"label\":\"Max Tokens\",\"min\":256,\"max\":32768,\"default\":1000,\"type\":\"number\",\"input\":\"slider\",\"step\":1,\"description\":\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model''s context length. Example Python code for counting tokens.\"},\"presence_penalty\":{\"label\":\"Presence Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model''s likelihood to talk about new topics.\"},\"frequency_penalty\":{\"label\":\"Frequency Penalty\",\"min\":-2,\"max\":2,\"default\":0,\"type\":\"number\",\"input\":\"slider\",\"step\":0.2,\"description\":\"Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model''s likelihood to repeat the same line verbatim.\"}}"'::jsonb
	WHERE slug IN ('gpt-4-32k');