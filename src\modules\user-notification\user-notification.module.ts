import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { UserNotificationController } from './user-notification.controller';
import { UserNotificationService } from './user-notification.service';
import { NotificationBackendModule } from '../../providers/notification-backend/notification-backend.module';
import { UserNotificationAdminController } from './user-notification-admin.controller';

@Module({
  imports: [PrismaModule, NotificationBackendModule],
  controllers: [UserNotificationController, UserNotificationAdminController],
  providers: [UserNotificationService],
  exports: [UserNotificationService],
})
export class UserNotificationModule {}