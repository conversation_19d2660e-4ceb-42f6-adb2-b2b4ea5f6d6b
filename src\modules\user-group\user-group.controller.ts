import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { UserGroupService } from './user-group.service';
import { Scopes } from '../auth/scope.decorator';
import { OptionalIntPipe } from '../../pipes/optional-int.pipe';
import { CursorPipe } from '../../pipes/cursor.pipe';
import { WherePipe } from '../../pipes/where.pipe';
import { OrderByPipe } from '../../pipes/order-by.pipe';
import { Email, UserGroup } from '@prisma/client';
import { UpdateUserGroupDto, UserGroupDto } from './user-group.dto';
import { UserRequest } from '../auth/auth.interface';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { USER_GROUP_FILTER_FIELD } from './user-group.constants';

@Controller('user-group')
@ApiBearerAuth('bearer-auth')
@ApiTags('User Group')
export class UserGroupController {
  private logger = new Logger(UserGroupController.name);

  constructor(private userGroupService: UserGroupService) {}

  @Get()
  @Scopes('system:read-user-group')
  async getAll(
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<{ list: UserGroup[]; count: number }> {
    const list: UserGroup[] = await this.userGroupService.getAll(skip, take, where, orderBy);
    const count = await this.userGroupService.getCount(where);
    return {
      list,
      count,
    };
  }

  @Post()
  @AuditLog('create-user-group')
  @Scopes('system:write-user-group')
  async create(@Req() request: UserRequest, @Body() body: UserGroupDto): Promise<UserGroup> {
    return await this.userGroupService.create(body, request.user.id);
  }

  @Put(':id')
  @AuditLog('update-user-group')
  @Scopes('system:write-user-group')
  async update(
    @Req() request: UserRequest,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UpdateUserGroupDto,
  ): Promise<UserGroup> {
    return await this.userGroupService.update(id, body, request.user.id);
  }

  @Delete(':id')
  @AuditLog('delete-user-group')
  @Scopes('system:write-user-group')
  async delete(
    @Req() request: UserRequest,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ userGroupId: number }> {
    return this.userGroupService.delete(id, request.user.id);
  }

  //TODO move to email module
  @Get('emails')
  @Scopes('system:read-user-group')
  async getEmails(
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<{ list: Email[] }> {
    const list: Email[] = await this.userGroupService.getEmails(skip, take, where, orderBy);
    return { list };
  }

  @Get('filters')
  @Scopes('system:read-user-group')
  async getFilters(): Promise<{ list: string[] }> {
    const list = Object.values(USER_GROUP_FILTER_FIELD);
    return { list };
  }
}
