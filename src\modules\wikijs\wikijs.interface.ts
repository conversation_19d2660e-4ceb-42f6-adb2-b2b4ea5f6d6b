import { WikiJSGroupRole } from '@prisma/client';
import { IsEmail, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

export interface EnableWikijsDTO {
  enabled: boolean;
}

export class CreateWikijsMembershipDTO {
  role: WikiJSGroupRole;
  @IsEmail()
  email: string;
}

export class DeleteWikiMembershipDTO {
  userId: number;
  roles: WikiJSGroupRole[];
}

export class WikiPeerDTO {
  @IsNumber()
  kbId: number;
  @IsString()
  token: string;
  @IsOptional()
  description?: string;
}

export class WikiPeerKbDTO {
  peerKbs: WikiPeerDTO[];
}
