
INSERT INTO "Permission" ("description", "permissionKey", "permissionType", "envs") 
VALUES ('Write or update llm engine', 'system:write-llm-engine', 'SYSTEM' , '{TEST,PROD}') ON CONFLICT DO NOTHING;


INSERT INTO "RolePermission" ("roleId", "permissionId") SELECT * FROM ( VALUES (
(SELECT id FROM "Role" WHERE "systemName" = 'SUDO' AND "roleType" = 'SYSTEM_DEFAULT'),
(SELECT id FROM "Permission" WHERE "permissionKey" = 'system:write-llm-engine')))
AS v("roleId", "permissionId") WHERE v."roleId" IS NOT NULL AND v."permissionId" IS NOT NULL;