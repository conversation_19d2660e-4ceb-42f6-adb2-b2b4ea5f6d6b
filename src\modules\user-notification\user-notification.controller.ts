import { <PERSON>, Get, Logger, Param, ParseIntPipe, Patch, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { OptionalIntPipe } from '../../pipes/optional-int.pipe';
import { WherePipe } from '../../pipes/where.pipe';
import { OrderByPipe } from '../../pipes/order-by.pipe';
import { UserNotificationService } from './user-notification.service';
import { Scopes } from '../auth/scope.decorator';

@Controller('user/:userId/notification')
@ApiBearerAuth('bearer-auth')
@ApiTags('User Notification')
export class UserNotificationController {
  private logger = new Logger(UserNotificationController.name);

  constructor(private userNotificationService: UserNotificationService) {
  }

  @Get()
  @Scopes('user-{userId}:read-info')
  async getAll(
    @Param('userId', ParseIntPipe) userId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    where = where ? { ...where, userId: userId } : where;
    return await this.userNotificationService.getAll(
      skip,
      take,
      where,
      orderBy,
    );
  }

  @Get('count')
  @Scopes('user-{userId}:read-info')
  async getCount(
    @Param('userId', ParseIntPipe) userId: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
  ) {
    where = where ? { ...where, userId: userId } : where;
    return await this.userNotificationService.count(
      where,
    );
  }

  @Patch('view-status/:id')
  @Scopes('user-{userId}:read-info')
  async update(
    @Param('id', ParseIntPipe) id: number,
  ) {
    return await this.userNotificationService.updateViewStatus(id);
  }
}