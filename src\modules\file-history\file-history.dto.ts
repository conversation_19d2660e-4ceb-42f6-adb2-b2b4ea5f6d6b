import { IsIn, IsN<PERSON>ber, IsOptional, IsString, IsDate } from 'class-validator';

export class LogHistoryDTO {
  @IsNumber()
  botId: number;

  @IsNumber()
  requesterId: number;

  @IsString()
  @IsOptional()
  s3FilePath?: string;

  @IsString()
  @IsIn(['PROCESSING', 'COMPLETED', 'EXPIRED'])
  @IsOptional()
  status?: 'PROCESSING' | 'COMPLETED' | 'EXPIRED';

  @IsDate()
  logDate: Date;

  @IsString()
  batchId: string;
}
