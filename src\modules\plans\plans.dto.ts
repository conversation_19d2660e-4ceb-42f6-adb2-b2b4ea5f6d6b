import { Environment, ResourceEntityType, ResourceSubsciberType } from '@prisma/client';
import { IsNumber } from 'class-validator';

export class GetResourcePlanDto {
  subscriberType: ResourceSubsciberType;
  subscriberId: number;
  requiredPlanRoleId?: number;
  groupEnv?: Environment;
}

export class GetResoucePlanCountDto {
  subscriberType: ResourceSubsciberType;
  requiredPlanRoleId?: number;
}

export class SubscribeResourcePlanDto {
  subscriberType: ResourceSubsciberType;
  subscriberId: number;
  subscribedPlanIds: number[];
}

export class CreatePlanRequestDto {
  subscriberType: ResourceSubsciberType;
  subscriberId: number;
  requesterId: number;
  subscribedPlanIds: number[];
  customPlans: CreateOrUpdateCustomPlanDto[];
}

export class CreateOrUpdateCustomPlanDto {
  @IsNumber()
  quotaValue: number;
  @IsNumber()
  resourceId: number;

  prevValue: number | null;
}

export class getPlanIsEnabledDto {
  resourceKey: string;
  subscriberId?: number;
  env?: Environment;
}

export class getResourceUsageDto {
  resourceId: number;
  resourceKey: string;
  subscriberId: number;
}

export type PlanResourceHandler = {
  [entityType in ResourceEntityType]?: {
    service: PlanResourceHandleService;
  };
};

export interface PlanResourceHandleService {
  getPlanIsEnabled?: (input: getPlanIsEnabledDto) => Promise<boolean>;
  getResourceUsage?: (input: getResourceUsageDto) => Promise<ResourceUsageDto | null>;
}

export interface ResourcePlanCustomDto {
  entityType: ResourceSubsciberType;
  entityId: number;
  groupEnv: Environment;
}

export interface ResourceUsageDto {
  limit: number;
  consumption: number;
}
