import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { <PERSON>pi<PERSON><PERSON>, <PERSON>pi<PERSON>lan } from '@prisma/client';
import { CursorPipe } from '../../pipes/cursor.pipe';
import { OptionalIntPipe } from '../../pipes/optional-int.pipe';
import { OrderByPipe } from '../../pipes/order-by.pipe';
import { WherePipe } from '../../pipes/where.pipe';
import { Expose } from '../../providers/prisma/prisma.interface';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { Scopes } from '../auth/scope.decorator';
import { CreateApiKeyDto, UpdateApiKeyDto } from './api-keys.dto';
import { ApiKeysService } from './api-keys.service';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@Controller('groups/:groupId/api-keys')
@ApiTags('Group Api Key')
@ApiBearerAuth('bearer-auth')
export class ApiKeyGroupController {
  logger = new Logger(ApiKeyGroupController.name);
  constructor(private apiKeysService: ApiKeysService) {}

  /** Create an API key for a group */
  @Post()
  @AuditLog('create-api-key')
  @Scopes('group-{groupId}:write-api-key')
  async create(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: CreateApiKeyDto,
  ): Promise<Expose<ApiKey>> {
    return await this.apiKeysService.createApiKeyForGroup(groupId, data);
  }

  /** Sync an API key to gravitee for a group */
  /** For the key before gravitee integration*/
  @Post(':id/sync')
  @Scopes('group-{groupId}:write-api-key')
  async sync(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Expose<ApiKey>> {
    return await this.apiKeysService.syncApiKeyToGraviteeForGroup(groupId, id);
  }

  /** Get API keys for a group */
  @Get()
  @Scopes('group-{groupId}:read-api-key')
  async getAll(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<{ list: ApiKey[]; count: number }> {
    const list = await this.apiKeysService.getApiKeysForGroup(groupId, {
      skip,
      take,
      orderBy,
      where,
    });
    const count = await this.apiKeysService.getApiKeysCountForGroup(groupId, where);
    return { list, count };
  }

  /** Get Api Key minimum subnet mask number */
  @Get('subnetMask')
  @Scopes('group-{groupId}:read-api-key')
  async subnetMask(@Param('groupId', ParseIntPipe) groupId: number) {
    return this.apiKeysService.getSubnetMaskForGroup(groupId);
  }
  /** Get API key scopes for a group */
  @Get('scopes')
  @Scopes('group-{groupId}:read-api-key')
  async scopes(@Param('groupId', ParseIntPipe) groupId: number): Promise<Record<string, string>> {
    return this.apiKeysService.getApiKeyScopesForGroup(groupId);
  }

  /** Get list of available API Plan*/
  @Get('plans')
  @Scopes('group-{groupId}:read-api-key')
  async plans(@Param('groupId', ParseIntPipe) groupId: number): Promise<Expose<ApiPlan>[]> {
    return this.apiKeysService.getApiPlans();
  }

  /** Get an API key */
  @Get(':id')
  @Scopes('group-{groupId}:read-api-key')
  async get(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Expose<ApiKey>> {
    return this.apiKeysService.getApiKeyForGroup(groupId, id);
  }

  /** Update an API key */
  @Patch(':id')
  @AuditLog('update-api-key')
  @Scopes('group-{groupId}:write-api-key')
  async update(
    @Body() data: UpdateApiKeyDto,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Expose<ApiKey>> {
    return this.apiKeysService.updateApiKeyForGroup(groupId, id, data);
  }

  /** Delete an API key */
  @Delete(':id')
  @AuditLog('delete-api-key')
  @Scopes('group-{groupId}:write-api-key')
  async remove(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Expose<ApiKey>> {
    return this.apiKeysService.deleteApiKeyForGroup(groupId, id);
  }

  /** Get logs for an API key */
  @Get(':id/logs')
  @Scopes('group-{groupId}:read-api-key-logs')
  async getLogs(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('cursor', CursorPipe) cursor?: Record<string, number | string>,
    @Query('where', WherePipe) where?: Record<string, number | string>,
  ): Promise<Record<string, any>[]> {
    return this.apiKeysService.getApiKeyLogsForGroup(groupId, id, {
      take,
      cursor,
      where,
    });
  }
}
