import { Injectable, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>roy, OnModuleInit } from '@nestjs/common';
import { ApprovedSubnet, Email, Session, User } from '@prisma/client';
import { Expose } from './prisma.interface';
import { PrismaExtendClient } from './prisma-extended-client';

@Injectable()
export class PrismaService extends PrismaExtendClient implements OnModuleInit, OnModuleDestroy {
  constructor() {
    super({
      log: [
        {
          emit: 'event',
          level: 'query',
        },
      ],
    });
  }

  async onModuleInit() {
    // this.$on('query', (e) => {
    //   this.logger.log(`executed ${e.query} ${e.params}`);
    // });

    await this.$connect();
  }

  async onModuleDestroy() {
    await this.$disconnect();
    console.log('received close event <======================================================>');
  }

  /** Delete sensitive keys from an object */
  expose<T>(item: T): Expose<T> {
    if (!item) return {} as T;
    if ((item as any as Partial<User>).password) (item as any).hasPassword = true;
    delete (item as any as Partial<User>).password;
    delete (item as any as Partial<User>).twoFactorSecret;
    delete (item as any as Partial<Session>).token;
    delete (item as any as Partial<Email>).emailSafe;
    delete (item as any as Partial<ApprovedSubnet>).subnet;
    return item;
  }
}
