/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable, Logger } from '@nestjs/common';
import { ChatSessionType, LLMModel, Prisma } from '@prisma/client';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { convertToJsonObj } from 'src/helpers/object';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { TokensService } from 'src/providers/tokens/tokens.service';
import { UserRequest } from '../auth/auth.interface';
import { ChatSessionsService } from '../chat-sessions/chat-sessions.service';
import { ChatSettingDto } from '../chat-sessions/dto/chat-setting.dto';
import { GroupsService } from '../groups/groups.service';
import { LLMModelsService } from '../llm-models/llm-models.service';
import { CreateShareChatDto } from './dto/create-share-chat.dto';

@Injectable()
export class ShareChatService {
  private readonly logger = new Logger(ShareChatService.name);
  private readonly MAX_RETRY_GEN_SHARED_ID = 3;

  constructor(
    private readonly chatSessionsService: ChatSessionsService,
    private readonly prisma: PrismaService,
    private readonly lLMModelsService: LLMModelsService,
    private readonly groupsService: GroupsService,
    private readonly tokenService: TokensService,
  ) {}

  async create(groupId: number, userReq: UserRequest, createShareChatDto: CreateShareChatDto) {
    const group = await this.groupsService.getGroup(groupId, { include: { llmModel: true } });
    const { llmModel } = group as unknown as { llmModel: LLMModel };
    if (!llmModel?.canShareChat) {
      throw new ApiException(ErrorCode.CHAT_HISTORY_NO_ALLOWED_TO_SHARED);
    }
    await this.lLMModelsService.checkUserHasPermissionsOrIsPublicBot(userReq, llmModel, group.env);
    const chatSession = await this.chatSessionsService.findChatHistoriesByIds(
      createShareChatDto.chatHistoriesId,
      createShareChatDto.chatSessionId,
    );
    delete (chatSession?.chatSetting as any)?.dataSource?.chatFile;
    const chatSetting: ChatSettingDto = {
      botSetting: {
        tone: llmModel.tone,
        typeDefinition: llmModel.typeDefinition,
        startupMessage: llmModel.startupMessage,
        modelEngine: llmModel.modelEngine,
      },
      ...(chatSession.isDefault
        ? (llmModel?.parameters as any) || {}
        : (chatSession?.chatSetting as any) || {}),
    };
    const chatHistories = convertToJsonObj(chatSession.chatHistories);
    const chatSettings = convertToJsonObj(chatSetting);
    if (!chatHistories || !chatSettings) {
      this.logger.error(
        `chatHistories or chatSettings is not  valid json data chatHistories: ${JSON.stringify(
          chatSession.chatHistories,
        )} , chatSettings: ${JSON.stringify(chatSetting)}`,
      );
      throw new ApiException(ErrorCode.SHARE_CHAR_LINK_GEN_FAILED);
    }
    const shareChatInputData: Prisma.ShareChatUncheckedCreateInput = {
      name: chatSession.isDefault ? llmModel.name : chatSession.name,
      shareId: await this.tokenService.generateRandomString(7),
      chatHistories,
      chatSetting: chatSettings,
      groupId,
      createdBy: userReq.user.id,
      shareChatSessionType: chatSession.chatSessionType,
      isPersonalChatShare:
        !chatSession.isDefault && chatSession.chatSessionType != ChatSessionType.PUBLIC,
    };
    let shareChat;
    for (let i = 1; i <= this.MAX_RETRY_GEN_SHARED_ID; i++) {
      try {
        shareChat = await this.prisma.shareChat.create({
          data: shareChatInputData,
        });
        break;
      } catch (e: any) {
        this.logger.error(`gen share chat failed ${JSON.stringify(e)}`);
        // ref https://www.prisma.io/docs/orm/reference/error-reference#error-codes
        if (
          e instanceof PrismaClientKnownRequestError &&
          e?.code == 'P2002' &&
          e.message.includes('shareId')
        ) {
          shareChatInputData.shareId = await this.tokenService.generateRandomString(7 + i);
        } else {
          throw e;
        }
      }
    }
    if (!shareChat) {
      throw new ApiException(ErrorCode.SHARE_CHAR_LINK_GEN_FAILED);
    }
    return shareChat;
  }

  async findByShareId(shareId: string, userReq: UserRequest) {
    const shareChat = await this.prisma.shareChat.findUnique({
      include: {
        group: {
          select: {
            env: true,
            groupType: true,
            llmModel: true,
            profilePictureUrl: true,
            isDeprecated: true,
          },
        },
        createdShareChatUser: {
          select: {
            name: true,
            id: true,
            profilePictureS3Path: true,
          },
        },
      },
      where: {
        shareId,
      },
    });
    if (!shareChat) {
      throw new ApiException(ErrorCode.SHARE_CHAT_NOT_FOUND);
    }
    const llmModel = shareChat?.group?.llmModel;
    const isBotMember = await this.groupsService.userHasAccessOrIsSudo(userReq, llmModel.groupId);
    const isPublicBot =
      llmModel.makeLiveToPublic && llmModel.active && shareChat.group.env == 'PROD';
    const isCanContinueChat =
      ((isPublicBot && shareChat.shareChatSessionType == ChatSessionType.PUBLIC) || isBotMember) &&
      !shareChat?.group?.isDeprecated;
    return {
      ...shareChat,
      isCanContinueChat,
      group: {
        ...shareChat.group,
        llmModel: undefined,
      },
    };
  }

  async createContinueChatSession(shareId: string, userReq: UserRequest) {
    const shareChat = await this.findByShareId(shareId, userReq);
    if (!shareChat.isCanContinueChat) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    const chatSession = await this.chatSessionsService.createChatSessionWithShare(
      shareChat,
      userReq,
    );
    return chatSession;
  }
}
