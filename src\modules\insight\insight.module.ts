import { Module } from '@nestjs/common';
import { InsightController } from './insight.controller';
import { InsightService } from './insight.service';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from 'src/providers/prisma/prisma.module';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';
@Module({
  imports: [ConfigModule, PrismaModule, FeatureFlagModule],
  controllers: [InsightController],
  providers: [InsightService],
})
export class InsightModule {}
