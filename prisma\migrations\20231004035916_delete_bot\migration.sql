-- CreateEnum
CREATE TYPE "GroupStatus" AS ENUM ('ACTIVE', 'TRASH');

-- DropForeignKey
ALTER TABLE "AiResource" DROP CONSTRAINT "AiResource_groupId_fkey";

-- DropForeignKey
ALTER TABLE "Membership" DROP CONSTRAINT "Membership_groupId_fkey";

-- DropForeignKey
ALTER TABLE "UserPermission" DROP CONSTRAINT "UserPermission_groupId_fkey";

-- AlterTable
ALTER TABLE "Group" ADD COLUMN     "deletedAt" TIMESTAMP(3),
ADD COLUMN     "status" "GroupStatus" NOT NULL DEFAULT 'ACTIVE';

-- AddForeignKey
ALTER TABLE "Membership" ADD CONSTRAINT "Membership_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddFore<PERSON>Key
ALTER TABLE "UserPermission" ADD CONSTRAINT "UserPermission_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "AiResource" ADD CONSTRAINT "AiResource_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE CASCADE ON UPDATE CASCADE;

UPDATE "Group" SET "status" = 'ACTIVE' WHERE "status" IS NULL;
