import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ChatMessageContentType, Group } from '@prisma/client';
import axios, { AxiosInstance } from 'axios';
import { Response } from 'express';
import { Configuration } from 'src/config/configuration.interface';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { UserRequest } from 'src/modules/auth/auth.interface';
import { ChatSessionsService } from 'src/modules/chat-sessions/chat-sessions.service';
import {
  ChannelType,
  ChatLlmModelDto,
  HistoryMessage,
} from 'src/modules/llm-models/dto/chat-llm-model.dto';
import { LLMModelsService } from 'src/modules/llm-models/llm-models.service';
import { ROLE } from '../llm-backend/llm-backend.interface';
import { FeatureFlagService } from 'src/modules/feature-flags/feature-flags.service';
import { ChatWithDataResponse } from './chat-with-data.interface';
import apm from 'elastic-apm-node';
import { SecretHashService } from '../secret-hash/secret-hash.service';
import moment from 'moment';
import { BotSecurityService } from 'src/modules/bot-security/bot-security.service';

@Injectable()
export class ChatWithDataService {
  private logger = new Logger(ChatWithDataService.name);
  private chatWithDataClient?: AxiosInstance;
  private readonly supportedLLMModelsKey = 'BOT.CONFIG_CHAT_WITH_DATA_SUPPORTED_LLM_MODELS';
  private config: Configuration['chatWithData'];
  private errorCodeList = {
    '400-1767-2': ErrorCode.CHAT_APPROACH_INCORRECT,
    '400-1767-3': ErrorCode.CHAT_WITH_DATA_FAILED_PARSE_DATA,
  };
  constructor(
    private readonly configService: ConfigService,
    private readonly llmModelsService: LLMModelsService,
    private readonly chatSessionsService: ChatSessionsService,
    private readonly featureFlagService: FeatureFlagService,
    private readonly secretHashService: SecretHashService,
    private readonly botSecurityService: BotSecurityService,
  ) {
    this.config = this.configService.get<Configuration['chatWithData']>('chatWithData');
    this.chatWithDataClient = axios.create({
      baseURL: this.config.clientUrl,
      timeout: this.config.timeout,
    });
    this.chatWithDataClient.interceptors.response.use(
      (res) => res,
      (error) => {
        this.logger.error({ error: error, data: error?.response?.data });
        const errorResponse = error?.response?.data;
        if (errorResponse && errorResponse?.code) {
          if (this.errorCodeList[errorResponse.code]) {
            throw new ApiException(this.errorCodeList[errorResponse.code]);
          }
          const errorMsg = `${errorResponse?.code}: ${errorResponse?.message}`;
          throw new ApiException(errorMsg);
        }
        if (errorResponse && errorResponse?.error) {
          const errorMsg = `${errorResponse.error?.code}: ${errorResponse.error?.message}`;
          throw new ApiException(errorMsg);
        }
        throw new ApiException(ErrorCode.REQUEST_CHAT_WITH_DATA_SERVICE_FAILED);
      },
    );
    this.chatWithDataClient.interceptors.request.use((chatWithDataRequest) => {
      this.logger.debug({ chatWithDataRequest });
      return chatWithDataRequest;
    });
  }

  async chatWithData(
    group: Group,
    request: UserRequest,
    chatRequest: ChatLlmModelDto,
    res: Response,
  ) {
    await this.verifySupportedLLMModel(chatRequest.overrides.model);
    const isStream: boolean = chatRequest.overrides?.stream;
    const bucket = this.configService.get<string>(`s3.staticFilesBuckets.${group.env}`);
    chatRequest.files = chatRequest.files.map((item) => ({
      ...item,
      bucket,
      s3_key: '/' + (item as any).s3_key,
    }));
    chatRequest.groupId = group.id;
    const prefilledLog = await this.llmModelsService.generatePrefilledChatLog(
      group,
      request,
      chatRequest,
    );
    const channel = chatRequest.channel
      ? chatRequest.channel
      : request.headers['x-api-key'] != null
        ? ChannelType.API_KEY
        : ChannelType.PLAYGROUND;
    const isChatSession: boolean =
      (chatRequest.chatSessionType || chatRequest.chatSessionId) && channel !== ChannelType.API_KEY;
    let heartbeatKey;

    const chatWithDataReq = {
      ...chatRequest,
      userId: request.user.id,
      apmTraceparentString:
        apm.currentTraceparent ??
        `00-${apm.currentTraceIds['trace.id']}-${apm.currentTraceIds['transaction.id']}-01`,
    };
    if (isStream) {
      const streamAccepted = `event: connect \ndata:${JSON.stringify({ connect: true })} \n\n`;
      res.write(streamAccepted);
      heartbeatKey = setInterval(() => {
        const heartbeat = `event: ping \ndata:${JSON.stringify({ ping: Date() })} \n\n`;
        res.write(heartbeat);
      }, 15000);
    }
    const { data: chatWithdataRes } = await this.chatWithDataClient.post<ChatWithDataResponse>(
      '/',
      chatWithDataReq,
    );
    let resourceEntity = chatWithdataRes.chatwithdata?.resource_entity;
    delete chatWithdataRes.chatwithdata?.resource_entity;
    if (chatWithdataRes?.chatwithdata?.is_image) {
      resourceEntity = null;
      const markdownImage = `![](${chatWithdataRes.answer})`;
      chatWithdataRes.answer = markdownImage;
      chatWithdataRes.message.content = markdownImage;
    }
    const userAsk = chatRequest.history.at(-1).content;
    const prompt = userAsk;
    const outputScannerResult = await this.botSecurityService.outputDetect(
      prompt,
      chatWithdataRes.answer,
      group.id,
      request.user.id,
    );
    const response = {
      ...chatWithdataRes,
      output_scanners_result: outputScannerResult?.output_scanners_result,
      output_scanners_result_is_valid: outputScannerResult?.output_scanners_result_is_valid,
    };
    if (isChatSession) {
      const message: HistoryMessage = { content: chatWithdataRes.answer, role: ROLE.AI };
      const showReferenceFlag = chatRequest.overrides?.show_reference ?? true;
      const chatHistory = await this.chatSessionsService.createChatHistory(
        chatRequest.chatSessionId,
        ChatMessageContentType.TEXT,
        message,
        chatWithdataRes.usage,
        undefined,
        chatWithdataRes?.chatwithdata,
        resourceEntity?.id,
        chatWithdataRes?.chatwithdata?.is_image
          ? moment().add(1, 'months').toDate()
          : resourceEntity?.expiresAt,
        showReferenceFlag,
      );
      chatWithdataRes.chatFile = resourceEntity;
      chatWithdataRes.file_expired_at = chatWithdataRes?.chatwithdata?.is_image
        ? moment().add(1, 'months').toDate()
        : resourceEntity?.expiresAt;
      prefilledLog['chatHistoryId'] = chatHistory.id;
      chatWithdataRes['chatHistoryId'] = chatHistory.id;
    }

    this.llmModelsService.logChatResponse(
      prefilledLog,
      chatWithdataRes.usage,
      chatWithdataRes.answer,
    );
    if (!isStream) {
      return response;
    } else {
      clearInterval(heartbeatKey);
    }
    // when the chat with data supprot stream need need remove below code and add SseEvent to return the response.
    const streamData = `event: overall\ndata:${JSON.stringify(response)} \n\n`;
    res.write(streamData);
    res.end();
  }

  async verifySupportedLLMModel(llmModelName: string) {
    const supportedLLMFeatureFlag = await this.featureFlagService.getOne(
      this.supportedLLMModelsKey,
    );
    const supportedLLmList: string[] = supportedLLMFeatureFlag.metaData['values'] ?? [];
    if (!supportedLLmList.includes(llmModelName)) {
      throw new ApiException(ErrorCode.CHAT_WITH_DATA_MODULE_NOT_SUPPORT_RESPONSE);
    }
  }
}
