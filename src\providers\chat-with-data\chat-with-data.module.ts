import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ChatSessionsModule } from 'src/modules/chat-sessions/chat-sessions.module';
import { ChatWithDataService } from './chat-with-data.service';
import { LLMModelsModule } from 'src/modules/llm-models/llm-models.module';
import { FeatureFlagModule } from 'src/modules/feature-flags/feature-flags.module';
import { SecretHashModule } from '../secret-hash/secret-hash.module';
import { BotSecurityModule } from 'src/modules/bot-security/bot-security.module';

@Module({
  imports: [
    ConfigModule,
    ChatSessionsModule,
    LLMModelsModule,
    FeatureFlagModule,
    SecretHashModule,
    BotSecurityModule,
  ],
  providers: [ChatWithDataService],
  exports: [ChatWithDataService],
})
export class ChatWithDataModule {}
