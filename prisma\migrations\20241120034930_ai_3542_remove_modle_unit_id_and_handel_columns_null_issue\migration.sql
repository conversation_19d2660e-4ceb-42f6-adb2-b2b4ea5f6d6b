/*
  Warnings:

  - You are about to drop the column `modelPriceUnitId` on the `Summary` table. All the data in the column will be lost.
  - You are about to drop the column `modelPriceUnitId` on the `SummaryAll` table. All the data in the column will be lost.

*/
-- DropIndex
DROP INDEX "summary_with_feature_priceUnitId_index";

-- DropIndex
DROP INDEX "summary_all_with_feature_priceUnitId_index";

-- AlterTable
ALTER TABLE "Summary" DROP COLUMN "modelPriceUnitId";

-- AlterTable
ALTER TABLE "SummaryAll" DROP COLUMN "modelPriceUnitId";




-- Summary triggers
CREATE OR REPLACE FUNCTION summary_trigger_function()
RETURNS TRIGGER AS $$
BEGIN 
    IF NEW."callingAttributes" is NULL THEN 
      IF EXISTS (SELECT 1 FROM "SummaryAll" WHERE "engineSlug" = NEW."engineSlug" AND "callingType" = NEW."callingType" AND "callingBy" = NEW."callingBy" AND "groupId" = NEW."groupId" AND "flowId" = NEW."flowId" AND "key" = NEW."key" AND "callingAttributes" is null AND "feature" = NEW."feature" )
      THEN
          UPDATE "SummaryAll" sa SET value = (sa."value" + NEW."value")
          WHERE "engineSlug" = NEW."engineSlug" AND "callingType" = NEW."callingType" AND  "callingBy" = NEW."callingBy" AND "groupId" = NEW."groupId" AND "flowId" = NEW."flowId" AND "key" = NEW."key" AND "callingAttributes" is null AND "feature" = NEW."feature" ;
      ELSE INSERT INTO "SummaryAll" ("value", "engineSlug", "callingType", "callingBy", "groupId", "flowId", "key", "callingAttributes", "feature")
      VALUES (NEW."value", NEW."engineSlug", NEW."callingType", NEW."callingBy", NEW."groupId", NEW."flowId", NEW."key", NEW."callingAttributes", NEW."feature");
      END IF;
    ELSE
      IF EXISTS (SELECT 1 FROM "SummaryAll" WHERE "engineSlug" = NEW."engineSlug" AND "callingType" = NEW."callingType" AND "callingBy" = NEW."callingBy" AND "groupId" = NEW."groupId" AND "flowId" = NEW."flowId" AND "key" = NEW."key" AND "callingAttributes" = NEW."callingAttributes" AND "feature" = NEW."feature" )
      THEN
          UPDATE "SummaryAll" sa SET value = (sa."value" + NEW."value")
          WHERE "engineSlug" = NEW."engineSlug" AND "callingType" = NEW."callingType" AND  "callingBy" = NEW."callingBy" AND "groupId" = NEW."groupId" AND "flowId" = NEW."flowId" AND "key" = NEW."key" AND "callingAttributes" = NEW."callingAttributes" AND "feature" = NEW."feature" ;
      ELSE INSERT INTO "SummaryAll" ("value", "engineSlug", "callingType", "callingBy", "groupId", "flowId", "key", "callingAttributes", "feature")
      VALUES (NEW."value", NEW."engineSlug", NEW."callingType", NEW."callingBy", NEW."groupId", NEW."flowId", NEW."key", NEW."callingAttributes", NEW."feature");
      END IF;
    END if;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER Summary_trigger
AFTER INSERT ON public."Summary"
FOR EACH ROW
EXECUTE FUNCTION summary_trigger_function();

