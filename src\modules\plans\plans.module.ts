import { PrismaModule } from 'src/providers/prisma/prisma.module';
import { PlansService } from './plans.service';
import { forwardRef, Module } from '@nestjs/common';
import { LlmEnginesModule } from '../llm-engines/llm-engines.module';
import { RedisModule } from 'src/providers/redis/redis.module';
import { TokensModule } from 'src/providers/tokens/tokens.module';
import { ScopeModule } from '../scope/scope.module';
@Module({
  imports: [
    PrismaModule,
    forwardRef(() => LlmEnginesModule),
    RedisModule,
    TokensModule,
    ScopeModule,
  ],
  providers: [PlansService],
  exports: [PlansService],
})
export class PlansModule {}
