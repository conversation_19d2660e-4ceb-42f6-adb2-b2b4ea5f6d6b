-- CreateEnum
CREATE TYPE "ResourceSubsciberType" AS ENUM ('USER', 'BOT', 'FLOW');

-- CreateTable
CREATE TABLE "Resource" (
    "id" SERIAL NOT NULL,
    "subscriberType" "ResourceSubsciberType" NOT NULL,
    "resourceKey" TEXT NOT NULL,
    "resourceName" TEXT NOT NULL,
    "description" TEXT NOT NULL,

    CONSTRAINT "Resource_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Plan" (
    "id" SERIAL NOT NULL,
    "resourceId" INTEGER NOT NULL,
    "planKey" TEXT NOT NULL,
    "planName" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "planRoleIdsRequired" INTEGER[] DEFAULT ARRAY[]::INTEGER[],

    CONSTRAINT "Plan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PlanSubscription" (
    "id" SERIAL NOT NULL,
    "planId" INTEGER NOT NULL,
    "subscriberEntityType" "ResourceSubsciberType" NOT NULL,
    "subscriberEntityId" INTEGER NOT NULL,
    "subscribeStartDate" TIMESTAMP(3) NOT NULL,
    "subscribeEndDate" TIMESTAMP(3),

    CONSTRAINT "PlanSubscription_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PlanPermission" (
    "planId" INTEGER NOT NULL,
    "permissionId" INTEGER NOT NULL,

    CONSTRAINT "PlanPermission_pkey" PRIMARY KEY ("planId","permissionId")
);

-- CreateIndex
CREATE UNIQUE INDEX "Resource_resourceKey_key" ON "Resource"("resourceKey");

-- CreateIndex
CREATE INDEX "Resource_subscriberType_idx" ON "Resource"("subscriberType");

-- CreateIndex
CREATE UNIQUE INDEX "Plan_planKey_key" ON "Plan"("planKey");

-- CreateIndex
CREATE INDEX "Plan_resourceId_idx" ON "Plan"("resourceId");

-- CreateIndex
CREATE INDEX "Plan_planRoleIdsRequired_idx" ON "Plan"("planRoleIdsRequired");

-- CreateIndex
CREATE INDEX "Plan_resourceId_planRoleIdsRequired_idx" ON "Plan"("resourceId", "planRoleIdsRequired");

-- CreateIndex
CREATE INDEX "plan_subscription_index" ON "PlanSubscription"("subscriberEntityId", "subscriberEntityType", "subscribeEndDate");

-- CreateIndex
CREATE INDEX "plan_subscription_index2" ON "PlanSubscription"("subscriberEntityType");

-- CreateIndex
CREATE INDEX "plan_subscription_index3" ON "PlanSubscription"("planId");

-- CreateIndex
CREATE INDEX "plan_permission_index" ON "PlanPermission"("permissionId");
