export const isValidEmail = (email: string) =>
  new RegExp(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/).test(email);

export const getWordCount = (words: string) => words.match(/[\u00ff-\uffff]|\w+/g).length;

export const isJSON = (input: string) => {
  try {
    return !!JSON.parse(input);
  } catch (err) {
    return false;
  }
};

export const isIpAddressValid = (input: string) =>
  new RegExp('^((25[0-5]|(2[0-4]|1\\d|[1-9]|)\\d)\\.?\\b){4}(/(([1-2]\\d)|(3[0-2])|\\d))?$').test(
    input.trim(),
  );
