import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  MessageAttributeValue,
  SendMessageBatchCommandInput,
  SendMessageCommandInput,
} from '@aws-sdk/client-sqs';
import axios, { AxiosInstance } from 'axios';
import { Configuration } from 'src/config/configuration.interface';
import { v4 } from 'uuid';
import { QueueServiceInterface } from '../queue/queue.service.interface';

@Injectable()
export class BullMqService implements QueueServiceInterface {
  private logger = new Logger(BullMqService.name);
  private client: AxiosInstance;
  constructor(private configService: ConfigService) {
    const config = this.configService.get<Configuration['jobQueue']>('jobQueue');

    this.client = axios.create({
      baseURL: config.backendPrefix,
    });

    this.client.interceptors.request.use((req) => {
      this.logger.debug(`send request :${JSON.stringify(req)}`);
      return req;
    });
  }
  public sendMessageBatch(
    params: SendMessageBatchCommandInput,
    callback: (err: any, data: any) => void,
  ) {
    const bullmqBatchBody = params.Entries.map((item) =>
      this.canverSendMessageRequestToBullMQ(item as unknown as SendMessageCommandInput),
    );
    this.client
      .post(`/producer/${params.QueueUrl}/batchAddJobs`, { data: bullmqBatchBody })
      .then((res) => callback(null, res))
      .catch((err) => {
        callback(err, null);
      });
  }
  public sendMessage(params: SendMessageCommandInput, callback: (err: any, data: any) => void) {
    const bullmqRequestBody = this.canverSendMessageRequestToBullMQ(params);
    this.client
      .post(`/producer/${params.QueueUrl}/addJob`, bullmqRequestBody)
      .then((res) => callback(null, res))
      .catch((err) => {
        callback(err, null);
      });
  }

  private canverSendMessageRequestToBullMQ(params: SendMessageCommandInput) {
    const delay = params.DelaySeconds ? { delay: params.DelaySeconds } : {};
    const deduplication = params.MessageDeduplicationId
      ? { deduplication: { id: params.MessageDeduplicationId } }
      : {};
    const bullmqRequestBody = {
      name: params.MessageGroupId ?? v4(),
      data: {
        body: params.MessageBody,
        messageAttributes: this.transformAttributes(params?.MessageAttributes),
      },
      opts: { ...deduplication, ...delay },
    };
    return bullmqRequestBody;
  }

  public transformAttributes(messageAttributes: Record<string, MessageAttributeValue>): {
    [key: string]: { [key: string]: any };
  } {
    const newMessageAttributes: { [key: string]: { [key: string]: any } } = {};

    for (const key in messageAttributes) {
      const attr = messageAttributes[key];
      newMessageAttributes[key] = {};

      for (const k1 in attr) {
        const v1 = attr[k1];
        const newKey = k1.charAt(0).toLowerCase() + k1.slice(1);
        newMessageAttributes[key][newKey] = v1; 
      }
    }
    return newMessageAttributes;
  }
}
