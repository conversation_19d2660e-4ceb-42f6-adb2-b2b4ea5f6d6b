import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { S3Module } from 'src/providers/s3/s3.module';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { ChatFilesController } from './chat-files.controller';
import { ChatFilesService } from './chat-files.service';
import { SecretHashModule } from 'src/providers/secret-hash/secret-hash.module';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';
import { FortiSanboxModule } from '../../providers/fortisandbox/fortisandbox.module';
import { LLMModelsModule } from '../llm-models/llm-models.module';
import { LLMBackendModule } from '../../providers/llm-backend/llm-backend.module';
import { RedisModule } from 'src/providers/redis/redis.module';

@Module({
  imports: [
    PrismaModule,
    ConfigModule,
    S3Module,
    SecretHashModule,
    FeatureFlagModule,
    FortiSanboxModule,
    forwardRef(() => LLMModelsModule),
    forwardRef(() => LLMBackendModule),
    RedisModule,
  ],
  controllers: [ChatFilesController],
  providers: [ChatFilesService],
  exports: [ChatFilesService],
})
export class ChatFilesModule {}
