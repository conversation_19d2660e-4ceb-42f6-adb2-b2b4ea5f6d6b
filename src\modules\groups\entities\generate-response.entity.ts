import { ApiPropertyOptional } from '@nestjs/swagger';
import { GenerateResponse } from "src/providers/llm-backend/llm-backend.interface";

export class GenerateResponseData {
    @ApiPropertyOptional({ description: 'stt generated text' })
    text?: string;
    
    @ApiPropertyOptional({ description: 'tts generated file', deprecated: true })
    file?: string;

    @ApiPropertyOptional({ description: 'tts generated url'})
    url?: string;
}

export class GenerateResponseUsage {
    @ApiPropertyOptional({ description: 'stt characters count' })
    chars_count?: number;
    
    @ApiPropertyOptional({ description: 'tts file length' })
    file_length?: number;    
}

export class GenerateResponseEntity implements GenerateResponse {
    @ApiPropertyOptional({ description: 'tts file', deprecated: true })
    file?: string;
    
    @ApiPropertyOptional({ description: 'tts text', deprecated: true })
    text?: string;

    @ApiPropertyOptional({ description: 'generated result' })
    data?: GenerateResponseData;

    @ApiPropertyOptional({ description: 'usage' })
    usage: GenerateResponseUsage;
}