-- CreateEnum
CREATE TYPE "LabelEntityType" AS ENUM ('LLM_ENGINE', 'MESSAGE_TEMPLATE');

-- CreateEnum
CREATE TYPE "LabelType" AS ENUM ('CATEGORIES', 'LABELS');

-- CreateTable
CREATE TABLE "Labels" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "color" TEXT,
    "labelType" "LabelType" NOT NULL,
    "updatedBy" INTEGER NOT NULL,
    "createdBy" INTEGER NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Labels_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntityLabels" (
    "id" SERIAL NOT NULL,
    "labelsId" INTEGER NOT NULL,
    "seq" SERIAL NOT NULL,
    "LabelEntityType" "LabelEntityType" NOT NULL,
    "entityId" INTEGER NOT NULL,

    CONSTRAINT "EntityLabels_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "updateLabelsUser" ON "Labels"("updatedBy");

-- CreateIndex
CREATE INDEX "createLabelsUser" ON "Labels"("createdBy");

-- CreateIndex
CREATE INDEX "EntityLabels_labelsId_idx" ON "EntityLabels"("labelsId");

-- CreateIndex
CREATE INDEX "EntityLabels_entityId_idx" ON "EntityLabels"("entityId");
