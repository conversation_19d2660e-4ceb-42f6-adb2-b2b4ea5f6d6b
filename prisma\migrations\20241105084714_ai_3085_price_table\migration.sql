-- CreateTable
CREATE TABLE "ModelPriceUnit" (
    "id" SERIAL NOT NULL,
    "llmEngineId" INTEGER NOT NULL,
    "modelName" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdBy" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedBy" INTEGER NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ModelPriceUnit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ModelPrice" (
    "id" SERIAL NOT NULL,
    "year" INTEGER NOT NULL,
    "month" INTEGER NOT NULL,
    "suggestion" JSONB NOT NULL,
    "cost" JSONB NOT NULL,
    "price" JSONB NOT NULL,
    "margin" JSONB NOT NULL,
    "metadata" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "lastEvent" INTEGER NOT NULL,

    CONSTRAINT "ModelPrice_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ModelPrice_year_month_key" ON "ModelPrice"("year", "month");

-- CreateIndex
CREATE INDEX "event" ON "AuditLog"("event");
