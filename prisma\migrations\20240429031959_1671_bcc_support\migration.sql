-- This is an empty migration.
BEGIN;

DO $$

DECLARE recipient text;
DECLARE cc text;

BEGIN

Select value INTO recipient from "FeatureFlag" WHERE key = 'ADMIN.CONFIG_WEEKLY_EMAIL_BOT_REPORT_RECIPIENT';
Select value INTO cc from "FeatureFlag" WHERE key = 'ADMIN.CONFIG_WEEKLY_EMAIL_BOT_REPORT_CC';
UPDATE "FeatureFlag" SET "metaData" = json_build_object('recipient', recipient,'cc', cc, 'bcc', cc) , key = 'ADMIN.CONFIG_WEEKLY_EMAIL_BOT_REPORT' 
WHERE key = 'ADMIN.ENABLE_WEEKLY_EMAIL_BOT_REPORT';

DELETE FROM "FeatureFlag"  WHERE key =  'ADMIN.CONFIG_WEEKLY_EMAIL_BOT_REPORT_RECIPIENT';
DELETE FROM "FeatureFlag"  WHERE key = 'ADMIN.CONFIG_WEEKLY_EMAIL_BOT_REPORT_CC';

END; $$;

COMMIT;


BEGIN;

DO $$

DECLARE recipient text;
DECLARE cc text;

BEGIN

Select value INTO recipient from "FeatureFlag" WHERE key = 'ACCOUNT_MANAGEMENT.CONFIG_DAILY_EMAIL_USER_LIST_RECIPIENT';
Select value INTO cc from "FeatureFlag" WHERE key = 'ACCOUNT_MANAGEMENT.CONFIG_DAILY_EMAIL_USER_LIST_CC';
UPDATE "FeatureFlag" SET "metaData" = json_build_object('recipient', recipient,'cc', cc, 'bcc', cc) , key = 'ACCOUNT_MANAGEMENT.CONFIG_DAILY_EMAIL_USER_LIST' 
WHERE key = 'ACCOUNT_MANAGEMENT.ENABLE_DAILY_EMAIL_USER_LIST';

DELETE FROM "FeatureFlag"  WHERE key = 'ACCOUNT_MANAGEMENT.CONFIG_DAILY_EMAIL_USER_LIST_RECIPIENT';
DELETE FROM "FeatureFlag"   WHERE key = 'ACCOUNT_MANAGEMENT.CONFIG_DAILY_EMAIL_USER_LIST_CC';

END; $$;


COMMIT;