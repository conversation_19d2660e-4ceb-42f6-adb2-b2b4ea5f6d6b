-- AlterTable
ALTER TABLE "LLMModel" ADD COLUMN     "lastModUserId" INTEGER;

-- CreateTable
CREATE TABLE "LLMModelHistory" (
    "id" SERIAL NOT NULL,
    "version" TEXT NOT NULL,
    "modelId" TEXT NOT NULL,
    "tone" TEXT,
    "startupMessage" TEXT,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "modelEngine" TEXT,
    "userId" INTEGER,

    CONSTRAINT "LLMModelHistory_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "LLMModel" ADD CONSTRAINT "LLMModel_lastModUserId_fkey" FOREIGN KEY ("lastModUserId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LLMModelHistory" ADD CONSTRAINT "LLMModelHistory_modelId_fkey" FOREIGN KEY ("modelId") REFERENCES "LLMModel"("modelId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LLMModelHistory" ADD CONSTRAINT "LLMModelHistory_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
