import { Module } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { InternationalizationController } from './internationalization.controller';
import { InternationalizationService } from './internationalization.service';

@Module({
  imports: [PrismaModule],
  controllers: [InternationalizationController],
  providers: [InternationalizationService],
  exports: [InternationalizationService],
})
export class InternationalizationModule {}
