-- AlterTable
ALTER TABLE "LLMModel" ADD COLUMN     "canShareChat" BOOLEAN DEFAULT false;

-- CreateTable
CREATE TABLE "ShareChat" (
    "id" SERIAL NOT NULL,
    "shareId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "chatHistories" JSONB[],
    "chatSetting" JSONB NOT NULL,
    "groupId" INTEGER NOT NULL,
    "createdBy" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "shareChatSessionType" "ChatSessionType" NOT NULL,

    CONSTRAINT "ShareChat_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ShareChat_createdBy_idx" ON "ShareChat"("createdBy");

-- CreateIndex
CREATE INDEX "ShareChat_groupId_idx" ON "ShareChat"("groupId");

-- CreateIndex
CREATE UNIQUE INDEX "ShareChat_shareId_key" ON "ShareChat"("shareId");
