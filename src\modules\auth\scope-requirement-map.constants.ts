import { FileType } from '@prisma/client';
import { CronJobApiKeyScopes } from 'src/constants/cron-job';
import { ScopeRequirementMap } from './scope-requirement-map.interface';

export const postReportScopeMap: ScopeRequirementMap = {
  '$.body.fileType': [
    {
      whenValues: [FileType.AUDIT_LOG],
      checkScopes: ['group-*:read-audit-log', 'system:read-audit-log'],
      entityIdScopes: ['group-{$.body.entityId}:read-audit-log'],
    },
    {
      whenValues: [
        FileType.BOT_SUMMARY,
        FileType.SUMMARY,
        FileType.FLOW_SUMMARY,
        FileType.LLM_ENGINE_SUMMARY,
        FileType.LEADER_BOARD,
        FileType.USER_SUMMARY,
        FileType.LLM_ENGINE_MEMBER_SUMMARY,
        FileType.SCORING_RATING,
        FileType.INSIGHT_GENERATOR_SUMMARY,
      ],
      checkScopes: [
        CronJobApiKeyScopes.SEND_BOT_LIST_REPORT,
        'group-*:read-info',
        'group-*:read-dashboard',
      ],
      entityIdScopes: [
        'group-{$.body.entityId}:read-dashboard',
        'group-{$.body.entityId}:read-info',
      ],
    },
    {
      whenValues: [FileType.QUERY_LOG],
      checkScopes: [],
      entityIdScopes: ['group-{$.body.entityId}:export-logs'],
    },
    {
      whenValues: [FileType.USER_SUMMARY],
      checkScopes: ['send-user-list-report', 'user-*:read-info'],
    },
    {
      whenValues: [FileType.SYSTEM_ROLE_PERMISSION_SUMMARY],
      checkScopes: ['system:read-role'],
    },
    {
      whenValues: [FileType.PLAN_SUBSCRIPTION_HISTORY],
      checkScopes: ['system:read-resource-plan'],
      entityIdScopes: ['group-{$.body.entityId}:read-resource-plan'],
    },
    {
      whenValues: [FileType.MEMBERSHIP_SUMMARY],
      checkScopes: [],
      entityIdScopes: ['group-{$.body.entityId}:read-membership'],
    },
    {
      whenValues: [FileType.MODEL_PRICE_EVENTS],
      checkScopes: ['system:read-price-unit-event'],
    },
  ],
};

export const getReportScopeMap: ScopeRequirementMap = {
  '$.query.fileType': [
    {
      whenValues: [FileType.AUDIT_LOG],
      checkScopes: ['group-*:read-audit-log', 'system:read-audit-log'],
      entityIdScopes: ['group-{$.query.entityId}:read-audit-log'],
    },
    {
      whenValues: [
        FileType.BOT_SUMMARY,
        FileType.SUMMARY,
        FileType.FLOW_SUMMARY,
        FileType.LLM_ENGINE_SUMMARY,
        FileType.LEADER_BOARD,
        FileType.USER_SUMMARY,
        FileType.LLM_ENGINE_MEMBER_SUMMARY,
        FileType.MONTHLY_PRICE_REPORT_PER_GROUP,
        FileType.INSIGHT_GENERATOR_SUMMARY,
      ],
      checkScopes: [
        CronJobApiKeyScopes.SEND_BOT_LIST_REPORT,
        'group-*:read-info',
        'group-*:read-dashboard',
      ],
      entityIdScopes: [
        'group-{$.query.entityId}:read-dashboard',
        'group-{$.query.entityId}:read-info',
      ],
    },
    {
      whenValues: [FileType.QUERY_LOG],
      checkScopes: [],
      entityIdScopes: ['group-{$.query.entityId}:export-logs'],
    },
    {
      whenValues: [FileType.USER_SUMMARY],
      checkScopes: ['user-*:read-info'],
    },
    {
      whenValues: [FileType.SYSTEM_ROLE_PERMISSION_SUMMARY],
      checkScopes: ['system:read-role'],
    },
    {
      whenValues: [FileType.PLAN_SUBSCRIPTION_HISTORY],
      checkScopes: ['system:read-resource-plan'],
      entityIdScopes: ['group-{$.query.entityId}:read-resource-plan'],
    },
    {
      whenValues: [FileType.MEMBERSHIP_SUMMARY],
      checkScopes: [],
      entityIdScopes: ['group-{$.query.entityId}:read-membership'],
    },
    {
      whenValues: [FileType.MONTHLY_PRICE_REPORT],
      checkScopes: ['system:read-system-price-report'],
    },
    {
      whenValues: [FileType.MONTHLY_PRICE_REPORT_PER_GROUP],
      checkScopes: [],
      entityIdScopes: ['group-{$.query.entityId}:read-group-price-report'],
    },
    {
      whenValues: [FileType.MODEL_PRICE_EVENTS],
      checkScopes: ['system:read-price-unit-event'],
    },
  ],
};
