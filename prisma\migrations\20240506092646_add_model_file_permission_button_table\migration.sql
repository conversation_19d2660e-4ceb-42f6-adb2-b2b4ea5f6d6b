/*
  Warnings:

  - You are about to drop the `ButtonCondition` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropTable
DROP TABLE "ButtonCondition";

-- CreateTable
CREATE TABLE "ModelFilePermissionButton" (
    "id" SERIAL NOT NULL,
    "isApproved" BOOLEAN,
    "status" "FileStatus",
    "hasPII" "HasPii",
    "isRequireSecondaryApproval" BOOLEAN,
    "fileClassification" "FileClassification",
    "roleId" INTEGER NOT NULL,
    "buttonList" TEXT[],
    "env" "Environment" NOT NULL DEFAULT 'TEST',

    CONSTRAINT "ModelFilePermissionButton_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ModelFilePermissionButton_isApproved_status_hasPII_isRequir_key" ON "ModelFilePermissionButton"("isApproved", "status", "hasPII", "isRequireSecondaryApproval", "fileClassification", "roleId", "env");
