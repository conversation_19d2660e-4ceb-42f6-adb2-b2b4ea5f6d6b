-- AlterTable
ALTER TABLE "ModelFile" ADD COLUMN     "fullScanReportVersion" INTEGER,
ADD COLUMN     "rescanPiiErrorMsg" TEXT;

-- CreateTable
CREATE TABLE "HistoricalFileSecurityReport" (
    "id" SERIAL NOT NULL,
    "docId" TEXT,
    "fullScanReportPath" TEXT,
    "fullScanReportCreatedAt" TIMESTAMP(3),
    "fullScanReportUpdatedAt" TIMESTAMP(3),
    "hasPii" "HasPii",
    "hasPromptInjection" "HasPromptInjection",
    "detectedPii" TEXT,
    "piiFileStatus" "PiiFileStatus",
    "fullScanReportVersion" INTEGER,
    "errorMsg" TEXT,
    "errCode" TEXT,

    CONSTRAINT "HistoricalFileSecurityReport_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "HistoricalFileSecurityReport_docId_fullScanReportVersion_key" ON "HistoricalFileSecurityReport"("docId", "fullScanReportVersion");

UPDATE "ModelFile"  set "fullScanReportVersion" = 1 where ("errCode" not like 'DP%' or "errCode" is null) and "deletedBy" is null and "s3Path" is not null and "fullScanReportPath" is not null;
