apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "bot-builder-backend.fullname" . }}-configmap
  labels:
    {{- include "bot-builder-backend.labels" . | nindent 4 }}
data:
  {{- with .Values.configs }}
    {{- toYaml . | nindent 2 }}
  {{- end }}
---
{{- range .Values.secondaryContainers }}
{{- with .config }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "bot-builder-backend.fullname" $ }}-{{ .name }}-configmap
  labels:
    {{- include "bot-builder-backend.labels" $ | nindent 4 }}
data:
  {{- toYaml .data | nindent 2 }}
{{- end }}
---
{{- end }}