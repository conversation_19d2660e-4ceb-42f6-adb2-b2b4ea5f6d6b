import { FlowBotRequestStatus, FlowBotRequestType } from '@prisma/client';
import { IsIn, IsString, IsOptional, IsNumber } from 'class-validator';

export interface GetAvailableBotsForFlowResponse {
  id: number;
  groupId: number;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface RequestBotResponse {
  id: number;
  flowGroupId: number;
  botGroupId: number;
  status: string;
  botName: string;
  createdAt: Date;
  updatedAt: Date;
}

export class CreateFlowBotRequest {
  @IsNumber()
  targetGroupId: number;

  @IsIn([FlowBotRequestType.REQUEST_BOT])
  requestType: FlowBotRequestType;

  @IsString()
  @IsOptional()
  requestNotes?: string;
}

export class UpdateFlowBotRequestRequest {
  @IsIn([FlowBotRequestStatus.APPROVED, FlowBotRequestStatus.REJECTED])
  status: FlowBotRequestStatus;

  @IsString()
  @IsOptional()
  operationNotes?: string;
}

export interface UpdateFlowBotRequestResponse {
  id: number;
  flowGroupId: number;
  botGroupId: number;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface GetFlowBotRequestResponse {
  id: number;
  flowGroupId: number;
  botGroupId: number;
  requestType: string;
  name: string;
  requesterId: number;
  requesterName: string;
  requestNotes: string;
  operatorId: number;
  operatorName: string;
  operationNotes: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}
