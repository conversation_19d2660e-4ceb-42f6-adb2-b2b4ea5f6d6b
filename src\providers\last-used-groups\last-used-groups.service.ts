import { Injectable, Logger } from '@nestjs/common';
import { GroupType, LastUsedGroups } from '@prisma/client';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class LastUsedGroupsService {
  private logger = new Logger(LastUsedGroupsService.name);

  constructor(private prisma: PrismaService) {}

  async getLastUsedGroups(userId: number): Promise<LastUsedGroups[]> {
    return await this.prisma.lastUsedGroups.findMany({
      where: {
        userId,
      },
    });
  }

  async getLastUsedGroupsByGroupType(
    userId: number,
    groupType: GroupType,
  ): Promise<LastUsedGroups> {
    return await this.prisma.lastUsedGroups.findUnique({
      where: {
        userId_groupType: {
          userId: userId,
          groupType: groupType,
        },
      },
    });
  }

  async createLastUsedGroups(
    userId: number,
    groupType: GroupType,
    groupId: number,
  ): Promise<LastUsedGroups> {
    return await this.prisma.lastUsedGroups.create({
      data: {
        userId,
        groupType: groupType,
        lastUsedHistory: [{ groupId, lastAccessTime: new Date() }],
      },
    });
  }

  async updateLastUsedGroups(
    userId: number,
    groupType: GroupType,
    data: any,
  ): Promise<LastUsedGroups> {
    return await this.prisma.lastUsedGroups.update({
      where: {
        userId_groupType: {
          userId: userId,
          groupType: groupType,
        },
      },
      data: {
        lastUsedHistory: data,
      },
    });
  }
}
