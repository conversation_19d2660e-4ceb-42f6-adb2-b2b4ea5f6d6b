import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Req,
} from '@nestjs/common';

import { Role } from '@prisma/client';
import { Scopes } from '../auth/scope.decorator';
import { RolesService } from './roles.service';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { UserRequest } from '../auth/auth.interface';

@Controller('groups/:groupId/roles')
@ApiBearerAuth('bearer-auth')
@ApiTags('Group Role')
export class RolesGroupController {
  constructor(private rolesService: RolesService) {}

  /** Get roles */
  @Get()
  @Scopes('group-{groupId}:read-role')
  async getRoles(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
  ): Promise<Role[]> {
    return this.rolesService.getGroupRoles(groupId);
  }

  @Post()
  @AuditLog('create-role')
  @Scopes('group-{groupId}:write-role')
  async createRole(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body('roleName') roleName: string,
    @Body('permissionIdList') permissionIdList: number[],
  ): Promise<Role> {
    return this.rolesService.createCustomRole(groupId, roleName, permissionIdList);
  }

  @Patch(':roleId')
  @AuditLog('update-role')
  @Scopes('group-{groupId}:write-role')
  async updateRole(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('roleId') roleId: number,
    @Body('permissionIdList') permissionIdList: number[],
    @Body('roleName') roleName: string,
  ): Promise<Role> {
    return this.rolesService.updateCustomRole(groupId, roleId, roleName, permissionIdList);
  }

  @Delete(':roleId')
  @AuditLog('update-role')
  @Scopes('group-{groupId}:write-role')
  async deleteRole(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('roleId') roleId: number,
  ): Promise<Role> {
    return this.rolesService.deleteCustomRole(groupId, roleId);
  }
}
