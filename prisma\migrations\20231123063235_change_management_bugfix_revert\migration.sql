/*
  Warnings:

  - Made the column `entitySnapshotId` on table `DataPromotionRequest` required. This step will fail if there are existing NULL values in that column.

*/

-- Custom SQL
TRUNCATE TABLE "DataPromotionRequest";

-- DropForeignKey
ALTER TABLE "DataPromotionRequest" DROP CONSTRAINT "DataPromotionRequest_entitySnapshotId_fkey";

-- AlterTable
ALTER TABLE "DataPromotionRequest" ALTER COLUMN "entitySnapshotId" SET NOT NULL;

-- AddForeignKey
ALTER TABLE "DataPromotionRequest" ADD CONSTRAINT "DataPromotionRequest_entitySnapshotId_fkey" FOREIGN KEY ("entitySnapshotId") REFERENCES "EntitySnapshot"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
