import { ArgumentMetadata, Injectable, PipeTransform } from '@nestjs/common';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';

/** Convert a string like "1" to a number, but without NaN */
@Injectable()
export class OptionalIntPipe implements PipeTransform {
  transform(value: string, metadata: ArgumentMetadata): number | undefined {
    if (!value) return undefined;
    const num = Number(value);
    if (isNaN(num))
      throw new ApiException(ErrorCode.OPTIONAL_INT_PIPE_NUMBER.replace('$key', metadata.data));
    return num;
  }
}
