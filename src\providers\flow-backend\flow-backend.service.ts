import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { Configuration } from '../../config/configuration.interface';
import { ConfigService } from '@nestjs/config';
import {
  CreateChatflowResponse,
  GetSpecificChatflowResponse,
  GetAllNodesResponse,
  PredictionResponse,
  PredictionRequest,
  PredictionHistoryType,
} from './flow-backend.interface';
import { flowData } from '../../../static/templates/flows/default-flow-data';
import {
  FlowChatRequest,
  LoadMethodOption,
  UpdateGroupFlowRequest,
} from 'src/modules/flows/flows.dto';
import { Request } from 'express';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { BotSecurityService } from '../../modules/bot-security/bot-security.service';


@Injectable()
export class FlowBackendService {
  axios?: AxiosInstance;
  private logger = new Logger(FlowBackendService.name);
  private readonly predictionMessageTypeMap = {
    user: PredictionHistoryType.userMessage,
    assistant: PredictionHistoryType.apiMessage,
  };

  constructor(
    private configService: ConfigService,
    private botSecurityService: BotSecurityService,
  ) {
    const flowConfig = this.configService.get<Configuration['flow']>('flow');
    if (flowConfig && flowConfig.backend) {
      this.axios = axios.create({
        baseURL: flowConfig.backend,
        timeout: flowConfig.backendTimeout || 30000,
        headers: {
          [flowConfig.apiKeyHeader]: `${flowConfig.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (flowConfig.api_log) {
        this.axios.interceptors.request.use((req) => {
          const requestCopy = { ...req };
          if (!flowConfig.api_log_include_header) {
            delete requestCopy.headers;
          }
          this.logger.log({ flowBackendReq: requestCopy });
          return req;
        });
      }

      this.axios.interceptors.response.use(
        (res) => {
          if (flowConfig.api_log) {
            const responseCopy = { ...res };
            if (!flowConfig.api_log_include_header) {
              delete responseCopy?.config?.headers;
              delete responseCopy.request;
              delete responseCopy.headers;
            }
            this.logger.log({ flowBackendRes: responseCopy });
          }
          return res;
        },
        (error) => {
          const errorCopy = { ...error };
          if (!flowConfig.api_log_include_header) {
            delete errorCopy?.config?.headers;
            delete errorCopy?.headers;
            delete errorCopy?.request;
          }
          this.logger.error({ flowBackendRes: errorCopy });
          const errorResponse = error?.response?.data;
          if (
            errorResponse &&
            errorResponse?.error &&
            errorResponse?.error?.code &&
            errorResponse?.error?.message
          ) {
            const errorMsg = `${errorResponse?.error?.code}:  ${errorResponse?.error?.message}`;
            throw new ApiException(errorMsg);
          }

          // To use interceptor catch all the errors and throw as a HttpException, the http-exception.filter will handle it properly.
          // Unless we need to do special error handling, then use try catch to handle.
          throw new ApiException(ErrorCode.REQUEST_FLOW_BACKEND_SERVICE_FAILED);
        },
      );
    } else {
      this.logger.error('Flow backend URL not set.');
      throw new Error('Flow backend URL not set.');
    }
  }

  async getSpecificFlow(flowUuid: string): Promise<GetSpecificChatflowResponse> {
    const response = await this.axios.get(`/api/v1/chatflows/${flowUuid}`);
    return response.data;
  }

  async createFlow(name: string): Promise<CreateChatflowResponse> {
    const response = await this.axios.post(`/api/v1/chatflows`, {
      name: name,
      deployed: false,
      isPublic: false,
      flowData: JSON.stringify(flowData),
    });

    return response.data;
  }

  async getAllNodes(): Promise<GetAllNodesResponse[]> {
    const response = await this.axios.get(`/api/v1/nodes`);
    return response.data;
  }

  async updateFlow(
    flowUuid: string,
    data: UpdateGroupFlowRequest,
  ): Promise<GetSpecificChatflowResponse> {
    const response = await this.axios.put(`/api/v1/chatflows/${flowUuid}`, data);
    return response.data;
  }

  async prediction(
    req: Request,
    groupId: number,
    flowUuid: string,
    data: FlowChatRequest,
  ): Promise<PredictionResponse> {
    const response = await this.axios.post(
      `/api/v1/prediction/${flowUuid}?flowId=${groupId}`,
      this.formatBotRequestBodyToFlowRequestBody(data),
      {
        headers: {
          'x-api-key': req.headers['x-api-key'],
        },
      },
    );
    return response.data;
  }

  async internalPrediction(
    req: Request,
    groupId: number,
    flowUUid: string,
  ): Promise<PredictionResponse> {
    const { data } = await this.axios.post(
      `/api/v1/internal-prediction/${flowUUid}?flowId=${groupId}`,
      this.formatBotRequestBodyToFlowRequestBody(req.body),
      {
        headers: {
          Authorization: req.headers.authorization,
        },
      },
    );
    return data;
  }

  async callNodeItemMethod(request: Request, nodeName: string): Promise<LoadMethodOption[]> {
    const { data } = await this.axios.post(`/api/v1/node-load-method/${nodeName}`, request.body, {
      headers: {
        Authorization: request.headers.authorization,
      },
    });
    return data;
  }

  private formatBotRequestBodyToFlowRequestBody(
    flowChatRequest: FlowChatRequest,
  ): PredictionRequest {
    const question = flowChatRequest.history.pop().content;

    const requestBody: PredictionRequest = {
      question: this.botSecurityService.convertContentsToString(question),
      appendVariables: flowChatRequest.appendVariables,
    };
    if (flowChatRequest.history.length > 0) {
      requestBody.history = flowChatRequest.history.map((item) => {
        return {
          type: this.predictionMessageTypeMap[item.role],
          message: this.botSecurityService.convertContentsToString(item.content),
        };
      });
    }
    return requestBody;
  }
}
