import { ApiProperty } from '@nestjs/swagger';
import { ModelPriceType } from '@prisma/client';
import { IsBoolean, IsEnum, IsIn, IsInt, IsOptional, <PERSON>, <PERSON>, IsNumber } from 'class-validator';

export class BulkCreatePriceItem {
  @ApiProperty()
  @IsInt()
  @IsOptional()
  id?: number;

  @ApiProperty()
  @IsInt()
  year: number;

  @ApiProperty()
  @IsInt()
  @Min(1)
  @Max(12)
  month: number;

  @ApiProperty()
  @IsInt()
  modelPriceUnitId: number;

  @ApiProperty({ enum: ModelPriceType })
  @IsIn([ModelPriceType.COST, ModelPriceType.PRICE])
  @IsEnum(ModelPriceType)
  modelPriceType: ModelPriceType;

  @ApiProperty()
  @IsNumber({ maxDecimalPlaces: 20 })
  value: number;

  @ApiProperty()
  @IsBoolean()
  overrideAutoSync: boolean;
}
