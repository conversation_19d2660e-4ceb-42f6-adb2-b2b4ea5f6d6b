import { FeatureFlagTargetType } from '@prisma/client';
import { JsonValue } from '../feature-flags/feature-flags-model.dto';
import { Transform } from 'class-transformer';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { IsOptional } from 'class-validator';

export class MutilpleLevelFeatureFlagsModelDto {
  id?: number;
  value?: string;
  targetType: FeatureFlagTargetType;
  targetValue: string;
  level: number;
  featureFlagId: number;
  createdByUserId?: number;
  createdAt?: Date | string;
  updatedAt: Date | string;
  updatedByUserId?: number;
  isEnabled: boolean;

  @Transform((val) => {
    try {
      return JSON.parse(val);
    } catch (err) {
      throw new ApiException(ErrorCode.INVALID_META_DATA);
    }
  })
  @IsOptional()
  metaData?: JsonValue;
}
