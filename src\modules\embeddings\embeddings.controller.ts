import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Param,
  Post,
  UseInterceptors,
  Logger,
  Req,
} from '@nestjs/common';
import { Scopes } from '../auth/scope.decorator';
import { LLMModelsService } from '../llm-models/llm-models.service';
import { UpdateModelFileStatusDto } from './embeddings.dto';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { UserRequest } from '../auth/auth.interface';

@Controller('embeddings')
@ApiBearerAuth('bearer-auth')
@ApiTags('Embedding')
export class EmbeddingsController {
  private logger = new Logger(EmbeddingsController.name);

  constructor(private llmModelService: LLMModelsService) {}
  @Post('/files/:docId')
  @Scopes('update-callback')
  async updateEmbeddingsStatus(
    @Param('docId') docId: string,
    @Req() request: UserRequest,
    @Body() data: UpdateModelFileStatusDto,
  ) {
    // set docId to COMPLETED
    this.logger.log({
      Action: "Set docId to COMPLETED' ",
      docId: docId,
      'data.status': data.status,
      'data.error': data.error,
    });
    return this.llmModelService.updateModelFileStatus(docId, data, request);
  }

  @Post('/verify/files/:docId')
  @Scopes('update-callback')
  @AuditLog('validate-file')
  async updateEmbeddingsVerificationStatus(
    @Param('docId') docId: string,
    @Body() data: UpdateModelFileStatusDto,
  ) {
    // set docId to COMPLETED
    this.logger.log({
      Action: "Update verification status and add audit log 'validate-file' ",
      docId: docId,
      'data.status': data.status,
      'data.error': data.error,
      'data.hasPii': data.hasPii,
      'data.errCode': data.errCode,
      'data.detectedPii': data.detectedPii,
    });
    return this.llmModelService.updateEmbeddingsVerificationStatus(docId, data);
  }

  @Post('/verify/files-without-llmguard/:docId')
  @Scopes('update-callback')
  @AuditLog('validate-file')
  async updateEmbeddingsVerificationStatusWithOutLLmguard(
    @Param('docId') docId: string,
    @Body() data: UpdateModelFileStatusDto,
  ) {
    this.logger.log({
      Action: "Update verification status without llmguard scan and add audit log 'validate-file' ",
      docId: docId,
      'data.status': data.status,
      'data.error': data.error,
      'data.hasPii': data.hasPii,
      'data.errCode': data.errCode,
    });
    return this.llmModelService.updateEmbeddingsVerificationStatusWithOutLLmguard(docId, { status: data.status, errCode: data.errCode, error: data.error });
  }

  @Post('/verify/modelFile/:fileId')
  @Scopes('update-callback')
  @AuditLog('validate-file')
  async updateModelFileVerifyResult(
    @Param('fileId') fileId: number,
    @Body() data: { status: string; errCode: string; error: string },
  ) {
    this.logger.log({
      Action: "Update model file verify result and add audit log 'validate-file' ",
      fileId: fileId,
      'data.status': data.status,
      'data.errCode': data.errCode,
      'data.error': data.error,
    });
    return await this.llmModelService.updateFileVerifyResult('modelFile', fileId, data);
  }

  @Post('/verify/chatFile/:fileId')
  @Scopes('update-callback')
  @AuditLog('validate-file')
  async updateChatFileVerifyResult(
    @Param('fileId') fileId: number,
    @Body() data: { status: string; errCode: string; error: string },
  ) {
    this.logger.log({
      Action: "Update verification status without llmguard scan and add audit log 'validate-file' ",
      fileId: fileId,
      'data.status': data.status,
      'data.errCode': data.errCode,
      'data.error': data.error,
    });
    return await this.llmModelService.updateFileVerifyResult('chatFile', fileId, data);
  }

}
