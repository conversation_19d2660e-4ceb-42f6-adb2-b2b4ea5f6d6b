-- migration role column to role Id column
UPDATE "Membership" SET "roleId" = "tmpRoleId";
ALTER TABLE "Membership" ALTER COLUMN "roleId" SET NOT NULL;
ALTER TABLE "Membership" DROP COLUMN "tmpRoleId" ;
ALTER TABLE "User" ALTER COLUMN "role" DROP DEFAULT;
ALTER TABLE "User" ALTER COLUMN "role" TYPE TEXT ;
ALTER TABLE "User" ALTER COLUMN "role" TYPE "SystemName" Using (role::"SystemName");
UPDATE "User" u SET "roleId" = (SELECT id FROM "Role" r WHERE r."systemName" = u.role);
ALTER TABLE "User" ALTER COLUMN "roleId" SET NOT NULL;
ALTER TABLE "User" DROP COLUMN "role";
DROP TYPE "UserRole";
