-- maually update system name of existing roles
UPDATE "Role" SET "systemName" = 'GROUP_OWNER' WHERE name = 'OWNER' AND "groupId" = 0;
UPDATE "Role" SET "systemName" = 'GROUP_ADMIN' WHERE name = 'ADMIN' AND "groupId" = 0;
UPDATE "Role" SET "systemName" = 'GROUP_MEMBER' WHERE name = 'MEMBER' AND "groupId" = 0;
UPDATE "Role" SET "systemName" = 'GROUP_CUSTOM', "roleType" = 'GROUP_CUSTOM' WHERE name not in ('OWNER', 'ADMIN' , 'MEMBER') AND NOT "groupId" = 0;