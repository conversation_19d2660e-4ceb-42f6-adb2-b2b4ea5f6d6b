import { BadRequestException, Injectable, PipeTransform } from '@nestjs/common';
import { ApiException, ErrorCode } from '../errors/errors.constants';
import { parseObjectLiteral } from '../helpers/parse-object-literal';

/** Convert a string like "slug: "ok", name: '<PERSON>'" to { slug: "ok", name: "<PERSON>" } */
@Injectable()
export class CursorSlugPipe implements PipeTransform {
  transform(value: string): Record<string, string> | undefined {
    if (value == null) return undefined;
    if (!value.includes(':')) value = `slug:${value}`;
    try {
      const rules = parseObjectLiteral(value);
      const items: Record<string, string> = {};
      rules.forEach((rule) => {
        const num = String(rule[1]);
        if (!num) items[rule[0]] = num;
        else if (rule[1]) items[rule[0]] = rule[1];
      });
      return items;
    } catch (_) {
      throw new ApiException(ErrorCode.CURSOR_PIPE_FORMAT);
    }
  }
}
