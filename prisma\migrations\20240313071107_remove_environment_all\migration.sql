-- AlterTable
ALTER TABLE "Permission" ALTER COLUMN "env" SET DATA TYPE "Environment"[] USING array[env];
UPDATE "Permission" SET env = '{TEST,PROD}' WHERE 'ALL' = ANY(env);

-- AlterEnum
BEGIN;
CREATE TYPE "Environment_new" AS ENUM ('TEST', 'PROD');
ALTER TABLE "LlmEngine" ALTER COLUMN "env" DROP DEFAULT;
ALTER TABLE "ApiPlan" ALTER COLUMN "env" DROP DEFAULT;
ALTER TABLE "Group" ALTER COLUMN "env" DROP DEFAULT;
ALTER TABLE "Group" ALTER COLUMN "env" TYPE "Environment_new" USING ("env"::text::"Environment_new");
ALTER TABLE "ApiPlan" ALTER COLUMN "env" TYPE "Environment_new" USING ("env"::text::"Environment_new");
ALTER TABLE "Permission" ALTER COLUMN "env" TYPE "Environment_new"[] USING ("env"::text::"Environment_new"[]);
ALTER TABLE "LlmEngine" ALTER COLUMN "env" TYPE "Environment_new" USING ("env"::text::"Environment_new");
ALTER TYPE "Environment" RENAME TO "Environment_old";
ALTER TYPE "Environment_new" RENAME TO "Environment";
DROP TYPE "Environment_old";
ALTER TABLE "LlmEngine" ALTER COLUMN "env" SET DEFAULT 'TEST';
ALTER TABLE "ApiPlan" ALTER COLUMN "env" SET DEFAULT 'TEST';
ALTER TABLE "Group" ALTER COLUMN "env" SET DEFAULT 'TEST';
COMMIT;


-- DropIndex
DROP INDEX "Permission_permissionType_env_idx";

-- AlterTable
ALTER TABLE "Permission" ADD COLUMN  "envs" "Environment"[];
UPDATE "Permission" p SET envs = p."env";
ALTER TABLE "Permission" DROP COLUMN "env";


-- CreateIndex
CREATE INDEX "Permission_permissionType_envs_idx" ON "Permission"("permissionType", "envs");
