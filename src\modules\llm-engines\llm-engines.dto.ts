import { Prisma } from '@prisma/client';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  IsBoolean,
  IsIn,
  IsJSON,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  <PERSON>Length,
  <PERSON>Length,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { PatchLabelsDto } from '../labels/dto/patch-labels.dto';

export class CreateLlmEngineDto {
  @IsString()
  @MinLength(3)
  @MaxLength(255)
  name: string;

  @IsString()
  @MinLength(3)
  @MaxLength(255)
  slug: string;

  @IsString()
  @MinLength(3)
  @MaxLength(255)
  reportSlug: string;

  @IsString()
  @IsIn(['TEST', 'PROD'])
  @IsOptional()
  env?: 'TEST' | 'PROD';

  @IsString()
  @IsIn(['OPENAI', 'AZURE', 'AWS', 'HUGGING_FACE', 'VERTEX_AI', 'SENSENOVA', 'ALIBABA'])
  @IsOptional()
  platform?: 'OPENAI' | 'AZURE' | 'AWS' | 'HUGGING_FACE' | 'VERTEX_AI' | 'SENSENOVA' | 'ALIBABA';

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsJSON()
  @IsOptional()
  config?: Prisma.NullableJsonNullValueInput | Prisma.InputJsonValue;
}

export class UpdateLlmEngineDto {
  @IsString()
  @MinLength(3)
  @MaxLength(255)
  @IsOptional()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsBoolean()
  @Transform((value) => {
    if (typeof value === 'string' && (value === 'false' || value === 'true')) {
      return value === 'true';
    }
    return value;
  })
  isActive?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform((value) => {
    if (typeof value === 'string' && (value === 'false' || value === 'true')) {
      return value === 'true';
    }
    return value;
  })
  isClientDeprecated?: boolean;

  @IsBoolean()
  @IsOptional()
  @Transform((value) => {
    if (typeof value === 'string' && (value === 'false' || value === 'true')) {
      return value === 'true';
    }
    return value;
  })
  isRecommended?: boolean;

  @IsOptional()
  @IsNumber()
  @Transform((value) => {
    if (typeof value === 'string') {
      return Number(value);
    }
    return value;
  })
  sequence?: number;

  @IsString()
  @IsOptional()
  config?: Prisma.NullableJsonNullValueInput | Prisma.InputJsonValue;

  @Type(() => PatchLabelsDto)
  @ValidateNested({ each: true })
  @IsOptional()
  @Transform((value) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch (error) {
        throw new Error('Invalid JSON string for entityLabels');
      }
    }
    return value;
  })
  entityLabels?: PatchLabelsDto[];

  @IsString()
  @IsOptional()
  iconPictureUrl?: string;

  @IsOptional()
  @IsString()
  learningMaterial?: string;
}

export class BatchUpdateDto {
  @IsArray()
  @Type(() => BatchUpdateLlmEngineDto)
  llmEngines: BatchUpdateLlmEngineDto[];
}

export class BatchUpdateLlmEngineDto {
  @IsString()
  @IsNotEmpty()
  slug: string;

  @IsString()
  @MinLength(3)
  @MaxLength(255)
  @IsOptional()
  name: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsBoolean()
  isClientDeprecated?: boolean;

  @IsBoolean()
  @IsOptional()
  isRecommended?: boolean;

  @IsOptional()
  @IsNumber()
  sequence?: number;

  @IsOptional()
  @IsIn(['wine', 'orange', 'yellow', 'green', 'tiffany', 'blue', 'purple'])
  labelColor?: string;

  @IsOptional()
  labelName?: string;

  @IsOptional()
  @IsIn(['CREATE', 'REMOVE'])
  labelAction?: 'CREATE' | 'REMOVE';

  @IsOptional()
  categoryName?: string;

  @IsOptional()
  @IsIn(['CREATE', 'REMOVE'])
  categoryAction?: 'CREATE' | 'REMOVE';
}
