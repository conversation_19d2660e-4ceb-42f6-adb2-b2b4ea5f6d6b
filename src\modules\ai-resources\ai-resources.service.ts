import { Injectable, Logger, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AiResource } from '@prisma/client';
import { Configuration } from 'src/config/configuration.interface';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { S3Service } from 'src/providers/s3/s3.service';
import { LLMModelsService } from '../llm-models/llm-models.service';
import { QueueService } from 'src/providers/queue/queue.service';

type GenerateResourceQueueMessage = {
  groupId: number;
  aiResourceId: number;
  engineId: number;
  engineSlug: string;
  tone: string;
  data: object;
};

@Injectable()
export class AiResourcesService {
  private config: Configuration['aiResource'];
  constructor(
    private prisma: PrismaService,
    private s3Service: S3Service,
    private sqsService: QueueService,
    private configService: ConfigService,
    private llmModelsService: LLMModelsService,
  ) {
    this.config = this.configService.get<Configuration['aiResource']>('aiResource');
  }
  private logger = new Logger(AiResourcesService.name);

  private getObjectKeyPrefix(aiResource: AiResource): string {
    return `generated-resources/${aiResource.groupId}/${aiResource.llmEngineId}/${aiResource.id}/`;
  }

  async getAiResourcesForGroup(
    groupId: number,
    skip?: number,
    take?: number,
    where?: Record<string, number | string>,
  ): Promise<AiResource[]> {
    const aiResources = await this.prisma.aiResource.findMany({
      skip,
      take,
      where: { ...where, groupId },
      orderBy: { createdAt: 'desc' },
      include: { llmEngine: { select: { name: true } } },
    });
    return aiResources;
  }

  async getAiResourcesCountForGroup(groupId: number, where?: Record<string, number | string>) {
    return await this.prisma.aiResource.count({
      where: { ...where, groupId },
      orderBy: { createdAt: 'desc' },
    });
  }

  async generateAiResources(
    groupId: number,
    request: { engineId: number; engineSlug: string; data: { prompt: string } },
  ): Promise<AiResource> {
    return await this.prisma.$transaction(async (tx) => {
      const model = await this.llmModelsService.findOneByGroupId(groupId);
      const aiResource = await tx.aiResource.create({
        data: {
          groupId,
          llmEngineId: request.engineId,
          prompt: request.data.prompt,
          status: 'PROCESSING',
        },
        include: { group: true },
      });

      const queueMessage: GenerateResourceQueueMessage = {
        groupId,
        aiResourceId: aiResource.id,
        engineId: request.engineId,
        engineSlug: request.engineSlug,
        tone: model.llmEngineId === request.engineId ? model.tone : '',
        data: request.data,
      };

      let queueUrl: string;

      if (aiResource.group.env === 'PROD') queueUrl = this.config.genResQueue.live;
      else queueUrl = this.config.genResQueue.test;

      await this.sqsService.sendMessage({
        QueueUrl: queueUrl,
        MessageBody: JSON.stringify(queueMessage),
      });

      return aiResource;
    });
  }

  async downloadAiResource(groupId: number, id: number, filename: string) {
    const aiResource = await this.prisma.aiResource.findUnique({
      where: { id },
      include: { group: true, llmEngine: true },
    });

    if (!aiResource.files.includes(filename)) throw new NotFoundException();

    if (aiResource.groupId !== groupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);

    return this.s3Service.getObjectUnsafe(
      this.configService.get<string>(`s3.staticFilesBuckets.${aiResource.group.env}`),
      `${this.getObjectKeyPrefix(aiResource)}${filename}`,
    );
  }

  async resourceGenerationCallback(
    id: number,
    callback: { filenames: string[] },
  ): Promise<AiResource> {
    if (callback.filenames == null || callback.filenames.length === 0) {
      return await this.prisma.aiResource.update({
        where: { id },
        data: { status: 'FAILED' },
      });
    }

    return await this.prisma.aiResource.update({
      where: { id },
      data: { status: 'GENERATED', files: callback.filenames },
    });
  }
}
