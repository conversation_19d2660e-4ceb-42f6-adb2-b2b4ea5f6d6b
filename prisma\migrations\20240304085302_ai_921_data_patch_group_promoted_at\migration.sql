-- Manual data patch of lastPromotedAt in Flow table
UPDATE
	"Flow"
SET
	"lastPromotedAt" = p.lastPromotedAt
FROM (
	SELECT
		groupId,
		lastPromotedAt
	FROM (
		SELECT
			g.id groupId,
			d. "operatedDate" lastPromotedAt,
			row_number() OVER (PARTITION BY g.id ORDER BY d. "operatedDate" DESC) AS rn
		FROM
			"Group" g
			JOIN "DataPromotionRequest" d ON g.id = d. "targetGroupId"
				AND d. "actionType" = 'PROMOTE'
				AND d.status = 'APPROVED') p
	WHERE
		rn = 1 ORDER BY
			groupId) AS p
WHERE
	"groupId" = p.groupId;

-- Manual data patch of lastPromotedAt in LLMModel table
UPDATE
	"LLMModel"
SET
	"lastPromotedAt" = p.lastPromotedAt
FROM (
	SELECT
		groupId,
		lastPromotedAt
	FROM (
		SELECT
			g.id groupId,
			d. "operatedDate" lastPromotedAt,
			row_number() OVER (PARTITION BY g.id ORDER BY d. "operatedDate" DESC) AS rn
		FROM
			"Group" g
			JOIN "DataPromotionRequest" d ON g.id = d. "targetGroupId"
				AND d. "actionType" = 'PROMOTE'
				AND d.status = 'APPROVED') p
	WHERE
		rn = 1 ORDER BY
			groupId) AS p
WHERE
	"groupId" = p.groupId;