import {
  <PERSON>,
  Controller,
  Get,
  Logger,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Req,
  Res,
} from '@nestjs/common';
import { InsightService } from './insight.service';
import { CreateInsightCaseDto, CreateInsightReportDto, UpdateInsightCaseDto } from './insight.dto';
import { Scopes } from '../auth/scope.decorator';
import { UserRequest } from '../auth/auth.interface';
import { Request, Response } from 'express';

@Controller('insights')
export class InsightController {
  logger = new Logger(InsightController.name);
  constructor(private insightService: InsightService) {}

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/cases')
  async getCases(@Param('groupId', ParseIntPipe) groupId: number, @Req() req: Request) {
    const queryStr = req.url.split('?')?.[1] ?? '';
    return await this.insightService.getInsightCases(groupId, queryStr);
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/cases/:id/search-engines')
  async getSettingSearchEngines(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) caseId: number,
  ) {
    return await this.insightService.getInsightCaseSearchEngines(caseId, groupId);
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/cases/:id/llm-engines')
  async getSettingLLMEngines(@Param('groupId', ParseIntPipe) groupId: number) {
    return await this.insightService.getInsightCaseLLMEngines(groupId);
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/cases/:id')
  async getSetting(
    @Param('id', ParseIntPipe) caseId: number,
    @Param('groupId', ParseIntPipe) groupId: number,
  ) {
    return await this.insightService.getInsightCaseById(caseId, groupId);
  }

  @Scopes('group-{groupId}:write-insight')
  @Post('/:groupId/cases')
  async createInsightCase(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() createInsightCaseDto: CreateInsightCaseDto,
    @Req() request: UserRequest,
  ) {
    const userId = request.user.id;
    return await this.insightService.createInsightCase(userId, groupId, createInsightCaseDto);
  }

  @Scopes('group-{groupId}:write-insight')
  @Patch('/:groupId/cases/:id')
  async updateInsightCase(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) insightId: number,
    @Body() updateInsightCaseDto: UpdateInsightCaseDto,
    @Req() request: UserRequest,
  ) {
    const userId = request.user.id;

    return await this.insightService.updateInsightCase(
      insightId,
      updateInsightCaseDto,
      groupId,
      userId,
    );
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/reports')
  async getReports(@Param('groupId', ParseIntPipe) groupId: number, @Req() req: Request) {
    const queryStr = req.url.split('?')?.[1] ?? '';
    return await this.insightService.getInsightReports(groupId, queryStr);
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/reports/case-options')
  async getReportCaseOptions(@Param('groupId', ParseIntPipe) groupId: number) {
    return await this.insightService.getInsightReportCaseOptions(groupId);
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/reports/:id')
  async getReport(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) insightReportId: number,
  ) {
    return await this.insightService.getInsightReportById(insightReportId, groupId);
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/reports/:id/status')
  async getReportStatus(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) insightReportId: number,
  ) {
    return await this.insightService.getInsightReportStatusById(insightReportId, groupId);
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/reports/:id/details')
  async getReportDetails(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) insightReportId: number,
    @Req() req: Request,
  ) {
    const queryStr = req.url.split('?')?.[1] ?? '';
    return await this.insightService.getInsightReportDetails(insightReportId, queryStr, groupId);
  }

  @Scopes('group-{groupId}:write-insight')
  @Post('/:groupId/reports')
  async createInsightReport(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() createInsightReportDto: CreateInsightReportDto,
    @Req() request: UserRequest,
  ) {
    const userId = request.user.id;
    return await this.insightService.createInsightReport(
      groupId,
      userId,
      createInsightReportDto.insightCaseId,
    );
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/reports/:id/report-file')
  async getInsightReportFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) insightReportId: number,
    @Res() res: Response,
  ) {
    const file = await this.insightService.getInsightReportFile(insightReportId, groupId);
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="insightReport-${insightReportId}.xlsx"`,
    );
    file.pipe(res);
  }
}
