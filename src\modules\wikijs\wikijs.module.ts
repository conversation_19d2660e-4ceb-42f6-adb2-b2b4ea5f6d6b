import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { LLMBackendModule } from 'src/providers/llm-backend/llm-backend.module';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { WikijsController } from './wikijs.controller';
import { WikijsService } from './wikijs.service';
import { LLMModelsModule } from '../llm-models/llm-models.module';
import { TokensService } from 'src/providers/tokens/tokens.service';
import { GroupsModule } from '../groups/groups.module';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
    forwardRef(() => LLMBackendModule),
    forwardRef(() => LLMModelsModule),
    forwardRef(() => GroupsModule),
    forwardRef(() => FeatureFlagModule),
  ],
  providers: [WikijsService, TokensService],
  exports: [WikijsService],
  controllers: [WikijsController],
})
export class WikijsModule {}
