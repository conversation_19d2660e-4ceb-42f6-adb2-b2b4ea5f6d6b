import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PassportModule } from '@nestjs/passport';
import { GeolocationModule } from '../../providers/geolocation/geolocation.module';
import { MailModule } from '../../providers/mail/mail.module';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { PwnedModule } from '../../providers/pwned/pwned.module';
import { TokensModule } from '../../providers/tokens/tokens.module';
import { TwilioModule } from '../../providers/twilio/twilio.module';
import { ApiKeysModule } from '../api-keys/api-keys.module';
import { ApprovedSubnetsModule } from '../approved-subnets/approved-subnets.module';
import { ApprovedSubnetsService } from '../approved-subnets/approved-subnets.service';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { StaartStrategy } from './staart.strategy';
import { SsoModule } from '../../providers/sso/sso.module';
import { SecretHashModule } from 'src/providers/secret-hash/secret-hash.module';

import { ScopeService } from '../scope/scope.service';
import { GroupsModule } from '../groups/groups.module';
import { S3Module } from 'src/providers/s3/s3.module';
import { WikijsModule } from '../wikijs/wikijs.module';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';
import { UsersModule } from '../users/users.module';
import { RedisModule } from 'src/providers/redis/redis.module';
import { RateLimitModule } from 'src/providers/rate-limit/rate-limit.module';
import { PlansModule } from '../plans/plans.module';
import { LastUsedGroupsModule } from '../../providers/last-used-groups/last-used-groups.module';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    PrismaModule,
    MailModule,
    TokensModule,
    ConfigModule,
    PwnedModule,
    forwardRef(() => ApiKeysModule),
    TwilioModule,
    GeolocationModule,
    ApprovedSubnetsModule,
    SsoModule,
    SecretHashModule,
    forwardRef(() => GroupsModule),
    S3Module,
    WikijsModule,
    FeatureFlagModule,
    forwardRef(() => UsersModule),
    RedisModule,
    RateLimitModule,
    forwardRef(() => PlansModule),
    LastUsedGroupsModule,
  ],
  controllers: [AuthController],
  exports: [AuthService],
  providers: [AuthService, StaartStrategy, ApprovedSubnetsService, ScopeService],
})
export class AuthModule {}
