-- AlterEnum
ALTER TYPE "ModelPriceEventType" ADD VALUE 'SNAPSHOT';

-- DropIndex
DROP INDEX "ModelPrice_year_month_key";

-- AlterTable
ALTER TABLE "ModelPrice" DROP COLUMN "cost",
DROP COLUMN "lastEvent",
DROP COLUMN "margin",
DROP COLUMN "price",
DROP COLUMN "suggestion",
ADD COLUMN     "createdBy" INTEGER NOT NULL,
ADD COLUMN     "modelPriceType" "ModelPriceType" NOT NULL,
ADD COLUMN     "modelPriceUnitId" INTEGER NOT NULL,
ADD COLUMN     "updatedBy" INTEGER NOT NULL,
ADD COLUMN     "value" DOUBLE PRECISION NOT NULL;

-- AlterTable
ALTER TABLE "ModelPriceEvent" DROP COLUMN "modelPriceSource";

-- CreateIndex
CREATE INDEX "ModelPrice_year_month_idx" ON "ModelPrice"("year", "month");

-- CreateIndex
CREATE UNIQUE INDEX "ModelPrice_year_month_modelPriceType_modelPriceUnitId_key" ON "ModelPrice"("year", "month", "modelPriceType", "modelPriceUnitId");
