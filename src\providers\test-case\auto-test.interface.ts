import { CreateTestCaseDto } from 'src/modules/test-case/dto/create-auto-test.dto';
import { UpdateTestCaseDto } from 'src/modules/test-case/dto/update-auto-test.dto';
import { Usage } from '../llm-backend/llm-backend.interface';

export class AutoTestCreateTestCaseDto extends CreateTestCaseDto {
  groupId: number;
  updatedBy: number;
  createdBy: number;
}
export class AutoTestUpdateTestCaseDto extends UpdateTestCaseDto {
  updatedBy: number;
}

export enum TestCaseExecutionStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  FAILED = 'FAILED',
  SUCCESS = 'SUCCESS',
}

type TestCaseExecutionStatusMap = { [key in TestCaseExecutionStatus]: number };

export type TestCaseExecutionResultInfo = {
  [key: number]: {
    resultsStatus: TestCaseExecutionStatusMap;
    completeAt: Date | null;
    tokenUsage: Usage;
    status: TestCaseExecutionStatus;
  };
};

export type TestCasePageListDto = AutoTestCreateTestCaseDto & {
  latestExecution: TestCaseExecutionStatusMap;
};

export class TestCasePageDto {
  list: TestCasePageListDto[];
  count: number;
}

export class TestCaseExecutionResult {
  id: number;
  testCaseExecutionId: number;
  iterationsRound: number;
  question: string;
  response: string | null;
  validationPrompt: string;
  validationResponse: string | null;
  testCaseExecutionResultStatus: TestCaseExecutionStatus;
  tokenUsage: Usage | null;
  responseTime: number | null;
}

export class TestCaseExecutionResultPage {
  count: number;
  list: TestCaseExecutionResult[];
}
