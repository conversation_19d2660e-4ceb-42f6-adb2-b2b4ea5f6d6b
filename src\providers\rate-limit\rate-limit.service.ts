import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '../redis/redis.service';
import {
  IRateLimiterRedisOptions,
  RateLimiterMemory,
  RateLimiterRedis,
} from 'rate-limiter-flexible';
import Redis from 'ioredis';
import { Configuration } from 'src/config/configuration.interface';
import { ResourceQuotaService } from 'src/modules/auth/quota.interface';
import { ResourceQuotaDto } from 'src/modules/scope/scope.dto';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { FeatureFlagKey } from '../../modules/feature-flags/feature-flags.constants';
import { v4 } from 'uuid';
import {
  SendGroupNotificationContentType,
  SendGroupNotificationDto,
} from '../../modules/group-notification/group-notification.dto';
import { FeatureFlagService } from '../../modules/feature-flags/feature-flags.service';
import { GroupNotificationService } from '../../modules/group-notification/group-notification.service';
import { GroupsService } from '../../modules/groups/groups.service';
import pRetry from 'p-retry';
@Injectable()
export class RateLimitService implements ResourceQuotaService {
  private logger = new Logger(RateLimitService.name);
  private client: Redis;
  private globalRateLimiter: RateLimiterRedis;
  private insuranceLimiter: RateLimiterMemory;
  constructor(
    private configService: ConfigService,
    private redisService: RedisService,
    private featureFlagService: FeatureFlagService,
    private groupNotificationService: GroupNotificationService,
    private groupsService: GroupsService,
  ) {
    this.client = this.redisService.client;
    const rateLimitOptions = this.configService.get<Configuration['rateLimit']>('rateLimit') ?? {
      points: 50,
      duration: 3600,
    };
    this.logger.log('initializing global rate limiter');
    this.insuranceLimiter = new RateLimiterMemory({
      keyPrefix: 'rlglobal',
      points: rateLimitOptions.points,
      duration: rateLimitOptions.duration,
    });

    this.globalRateLimiter = new RateLimiterRedis({
      keyPrefix: 'rlglobal',
      storeClient: this.client,
      insuranceLimiter: this.insuranceLimiter,
      ...rateLimitOptions,
    } as IRateLimiterRedisOptions);
  }

  private getLimiterForGroup(
    groupId: number,
    channel: string,
    quotaValue: number,
  ): RateLimiterRedis {
    this.logger.log(`initializing group rate limiter , groupId: ${groupId} , channel: ${channel}`);
    return new RateLimiterRedis({
      keyPrefix: 'rlgroup',
      storeClient: this.client,
      points: quotaValue,
      duration: 3600,
      insuranceLimiter: new RateLimiterMemory({
        keyPrefix: 'rlgroup',
        points: 1,
        duration: 3600,
      }),
    });
  }

  async handleRateLimitOnApiLevel(ip: string, apiPath: string, points: number) {
    const rateLimitKey = `${ip}-${apiPath}`;
    const rateLimiter = this.globalRateLimiter;
    const result = await rateLimiter.consume(rateLimitKey, points);
    this.logger.log(
      JSON.stringify({
        message: 'api level rate limiting',
        prefix: rateLimiter.keyPrefix,
        key: rateLimitKey,
        totalPoints: rateLimiter.points,
        result,
      }),
    );
    return result;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async checkQuotaIsAllowed(input: ResourceQuotaDto, response: any): Promise<boolean> {
    let groupId;
    let rateLimitKey;
    let quota;
    let channel;
    try {
      // quota will be in group-{groupId}:rate-limit-{channel}-quota-{x} format
      // split the group id and chat channel from quota key
      groupId = parseInt(input.quotaKey.match(/group-(\d+)/)[1]);
      channel = input.quotaKey.split('rate-limit-')[1].split('-quota')[0];
      quota = input.quotaValue;
      this.logger.log('getting quota for group: ' + groupId + ' channel: ' + channel);
      const rateLimiter = this.getLimiterForGroup(groupId, channel, quota);
      rateLimitKey = `${groupId} - ${channel}`;
      const rateLimitRes = await rateLimiter.get(rateLimitKey);
      if (rateLimitRes.remainingPoints === 0) {
        this.logger.error(`Checking group rate limit by getting key - ${rateLimitKey}`);
        throw rateLimitRes;
      }
      const result = await rateLimiter.consume(rateLimitKey, 1);
      this.logger.log(
        JSON.stringify({
          message: 'group level rate limiting',
          prefix: rateLimiter.keyPrefix,
          key: rateLimitKey,
          totalPoints: rateLimiter.points,
          result,
        }),
        'checked group quota of rate limit',
      );
      response.header('Retry-After', Math.ceil(result.msBeforeNext / 1000));
      response.header('X-RateLimit-Limit', quota);
      response.header('X-Retry-Remaining', result.remainingPoints);
      response.header('X-Retry-Reset', new Date(Date.now() + result.msBeforeNext).toUTCString());
      return true;
    } catch (err) {
      response.header('Retry-After', Math.ceil(((err as any)?.msBeforeNext ?? 0) / 1000));
      this.logger.error(err, 'Checking group rate limit error');
      // get expire time
      this.logger.log(
        `[RateLimitService][checkQuotaIsAllowed] get ttl key: rlgroup:${rateLimitKey}`,
      );
      const ttl = await this.client.ttl(`rlgroup:${rateLimitKey}`);
      const exTime = Math.abs(Math.ceil(ttl / 60));
      this.logger.log(`[RateLimitService][checkQuotaIsAllowed] get ttl time: ${exTime}`);
      // get redis key
      rateLimitKey = `${groupId} - ${channel} - ratelimit`;
      const result = await this.client.get(rateLimitKey);
      // if not exist, set key and expire time
      if (!result) {
        this.logger.log(
          `[RateLimitService][checkQuotaIsAllowed] can not get redis key: ${rateLimitKey}`,
        );
        // handle send email and send email
        await this.handleRateLimitSendEmail(groupId, rateLimitKey, channel, quota, exTime);
      }
      throw new ApiException(ErrorCode.RATE_LIMIT_EXCEEDED);
    }
  }

  async handleRateLimitSendEmail(
    groupId: number,
    rateLimitKey: string,
    backendChannel: string,
    rateLimitPlanNumber: string,
    exTime: number,
  ) {
    // get emai template
    const featureFlag = await this.featureFlagService.getDefaultFeatureFlagFromDb(
      FeatureFlagKey.CONFIG_RATE_LIMIT_SEND_ALERT_TEMPLATE,
    );
    const emailContent = JSON.stringify(featureFlag.metaData['emailContent']);
    const subject = JSON.stringify(featureFlag.metaData['subject']);
    // component data
    const group = await this.groupsService.getGroup(groupId, {});
    const env = group.env === 'PROD' ? 'LIVE' : group.env;
    const sendGroupNotificationDto = {
      sourceId: v4(),
      contentType: SendGroupNotificationContentType.Normal,
      title: JSON.parse(
        subject
          .replace('{{botName}}', group.name)
          .replace('{{env}}', process.env['BACKEND_ENV'] + ' - ' + env),
      ),
      htmlContent: JSON.parse(
        emailContent
          .replace('{{groupId}}', groupId + '')
          .replace('{{botName}}', group.name)
          .replace('{{backendChannle}}', backendChannel)
          .replace('{{backendChannled}}', backendChannel)
          .replace('{{rateLimitPlanNumber}}', rateLimitPlanNumber + '')
          .replace('{{minutes}}', exTime + '-minutes'),
      ),
      notificationKey: 'rate-limit',
    } as SendGroupNotificationDto;
    try {
      await this.groupNotificationService.sendGroupNotification(groupId, sendGroupNotificationDto);
    } catch (err) {
      this.logger.error(
        err,
        '[RateLimitService][handleRateLimitSendEmail] send rate limit email error',
      );
    }
  }
}
