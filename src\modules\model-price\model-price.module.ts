import { Modu<PERSON> } from '@nestjs/common';
import { ModelPriceService } from './model-price.service';
import { ModelPriceController } from './model-price.controller';
import { PrismaModule } from 'src/providers/prisma/prisma.module';
import { ConfigModule } from '@nestjs/config';
import { SummaryModule } from '../summary/summary.module';
import { TokensModule } from 'src/providers/tokens/tokens.module';
import { QueueModule } from 'src/providers/queue/queue.module';

@Module({
  imports: [ConfigModule, PrismaModule, QueueModule, SummaryModule, TokensModule],
  providers: [ModelPriceService],
  controllers: [ModelPriceController],
})
export class ModelPriceModule {}
