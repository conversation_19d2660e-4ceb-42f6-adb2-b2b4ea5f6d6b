import { LLMModel } from '@prisma/client';
import { Transform, Type } from 'class-transformer';
import { IsOptional, ValidateNested } from 'class-validator';
import { LabelEntityFilter } from 'src/modules/labels/dto/entity-lable-query.dto';
import { PrismaPaginationDto, PrismaStringFilterDto } from 'src/providers/prisma/prisma.interface';

export class LlmModelWhereDto extends LabelEntityFilter {
  @IsOptional()
  @ValidateNested()
  @Type(() => PrismaStringFilterDto)
  name?: PrismaStringFilterDto;

  @IsOptional()
  @Transform((value) => {
    if (typeof value === 'string' && (value === 'false' || value === 'true')) {
      return value === 'true';
    }
    return value;
  })
  official?: boolean;
}

export class PublicLLMModelWhereDto extends PrismaPaginationDto<LLMModel> {
  @IsOptional()
  @ValidateNested()
  @Type(() => LlmModelWhereDto)
  where: LlmModelWhereDto;
}
