export interface BroadcastResponse {
  id: number;
  title: string;
  content: string;
  sentAt: Date;
  numberSent: string;
  channel: string;
  status: string;
  createdByUserId: number;
  userGroupId: number;
  targetType: string;
  scheduleTime: Date;
  scheduleType: string;
  jobId: string;
}

export interface BroadcastCreateRequest extends BroadcastRequest {
  userId: number;
}

export interface BroadcastRequest {
  title: string;
  htmlContent: string;
  textContent: string;
  channel: string;
  userGroupId: number;
  targetType: string;
  scheduleTime: Date;
  scheduleType: string;
}

export interface NotificationRecipientData {
  key: string;
  value: string;
}

export interface NotificationRecipient {
  userId?: number;
  email: string;
  data?: NotificationRecipientData[];
}

export interface NotificationContent {
  html: string;
  text: string;
  subject: string;
  isMarkdownText?: boolean;
}

export enum NotificationContentType {
  SIMPLE = 'SIMPLE',
  TEMPLATE = 'TEMPLATE',
}

export interface NotificationRequest {
  source: string;
  sourceId: string;
  channel: string;
  contentType: NotificationContentType;
  recipients: NotificationRecipient[];
  content: NotificationContent;
  attachmentFileBucket?: string;
  attachmentFilePaths?: string[];
}