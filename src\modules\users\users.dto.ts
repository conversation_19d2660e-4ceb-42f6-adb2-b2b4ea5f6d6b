import { Optional } from '@nestjs/common';
import { LoginType, Prisma } from '@prisma/client';
import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsEmail,
  IsOptional,
  IsString,
  MaxLength,
  MinLength,
  IsNumber,
  IsEnum,
  IsNotEmpty,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { OmitType } from '@nestjs/swagger';
import { PrismaStringFilterDto } from 'src/providers/prisma/prisma.interface';

export class UpdateUserProfleDto {
  @IsString()
  @MinLength(3)
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  staffId?: string;

  @IsString()
  @IsNotEmpty()
  businessUnit: string;

  @IsString()
  @IsNotEmpty()
  department?: string;

  @IsString()
  @IsNotEmpty()
  ccc: string;

  @IsString()
  @IsOptional()
  profilePictureS3Path: string;
}

export class UpdateUserDto {
  @IsString()
  @MinLength(3)
  name: string;

  @IsString()
  @IsOptional()
  businessUnit?: string;

  @IsString()
  @IsOptional()
  department?: string;

  @IsString()
  @IsOptional()
  ccc?: string;

  @IsEmail()
  @IsOptional()
  email?: string;

  @IsBoolean()
  active: boolean;

  @IsNumber()
  roleId: number;

  @IsString()
  @IsOptional()
  ssoId?: string;

  @IsOptional()
  @MaxLength(100)
  remarks?: string;

  lastActiveDate?: Date;

  loginProviderUniqueKey?: string;
}

export class CreateUserDTO {
  @IsString()
  @MinLength(3)
  name: string;

  @IsString()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsString()
  @IsOptional()
  businessUnit?: string;

  @IsString()
  @IsOptional()
  department?: string;

  @IsString()
  @IsOptional()
  ccc?: string;

  @IsNumber()
  @IsNotEmpty()
  roleId: number;

  @IsEnum(LoginType)
  loginType: LoginType;

  @IsOptional()
  staffId?: string;

  @IsString()
  @IsOptional()
  ssoId?: string;

  @IsOptional()
  remarks?: string;
}

export class BatchCreateOrUpdateUserRecordDto extends OmitType(CreateUserDTO, [
  'name',
  'email',
  'loginType',
]) {
  name: string;

  email: string;

  loginType: LoginType;
}

export class ActivateAccountDTO {
  @IsString()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsEnum(LoginType)
  loginType: LoginType;

  @IsString()
  @Optional()
  staffId?: string;

  @IsBoolean()
  @IsOptional()
  active?: boolean;

  lastActiveDate?: Date;
}

export class CreateUserRecordBatchDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BatchCreateOrUpdateUserRecordDto)
  users: BatchCreateOrUpdateUserRecordDto[];
}

export class DeleteUserRecordBatchDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateUserDTO)
  users: CreateUserDTO[];
}

export class UpdateUserRecordBatchDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BatchCreateOrUpdateUserRecordDto)
  users: BatchCreateOrUpdateUserRecordDto[];
}

export class ActivateAccountRecordBatchDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ActivateAccountDTO)
  users: ActivateAccountDTO[];
}

export class ActivateUserDTO {
  password: string;
  confirmedPassword: string;
  code: string;
}

export class ResendActivateCodeDTO {
  userId: number;
}

export class ChangePasswordDto {
  originalPassword: string;
  password: string;
  confirmedPassword: string;
}

export class UserProfileCompletenessRes {
  completeness: boolean;
}

export interface BatchUploadTemplate {
  email: string;
  name: string;
  role: string;
  staffId: string;
  loginType: string;
  remarks: string;
  action: string;
  department: string;
  ccc: string;
  businessUnit: string;
}

export class SubscribeUserResourcePlanDto {
  @IsArray()
  subscribedPlanIds: number[];
}

export class EmailWhereDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => PrismaStringFilterDto)
  email?: PrismaStringFilterDto;
}
export class UserSomeWhereDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => EmailWhereDto)
  some?: EmailWhereDto;
}

export class UserWhereDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => PrismaStringFilterDto)
  staffId?: PrismaStringFilterDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => UserSomeWhereDto)
  emails?: UserSomeWhereDto;
}
export class PublicUserWhereDto {
  @IsArray()
  @IsOptional()
  @ValidateNested()
  @Type(() => UserWhereDto)
  OR?: UserWhereDto[];
}
