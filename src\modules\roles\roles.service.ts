import { Injectable } from '@nestjs/common';
import type { Role } from '@prisma/client';
import minimatch from 'minimatch';
import { PermissionType, RoleType, SystemName } from '@prisma/client';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { GroupsService } from '../groups/groups.service';
import { PermissionsService } from '../permissions/permissions.service';
import { AccessTokenParsed } from '../auth/auth.interface';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { RoleQueryData } from './roles.dto';
@Injectable()
export class RolesService {
  constructor(
    private prisma: PrismaService,
    private groupsService: GroupsService,
    private permissionsService: PermissionsService,
    private featureFlagService: FeatureFlagService,
  ) {}

  async getSystemRoles(): Promise<Role[]> {
    const roles = await this.prisma.role.findMany({
      include: {
        permissions: {
          include: {
            permission: true,
          },
        },
      },
      where: {
        roleType: RoleType.SYSTEM_DEFAULT,
      },
    });
    return roles;
  }

  async getSystemRolesForDisplay(): Promise<Role[]> {
    const roles = await this.getSystemRoles();
    const userPermissions = await this.permissionsService.getUserPermissions();

    roles.forEach(
      (role) =>
        role['permissions']?.push(
          ...userPermissions.map((userPermission) => {
            // transform in to many to many structure
            return { permission: userPermission };
          }),
        ),
    );
    return roles;
  }

  async getGroupRolesForDisplay(groupType: string): Promise<RoleQueryData[]> {
    const roles = (await this.prisma
      .$queryRaw`select  r.id as roleId, r."roleType", r.name,r."systemName",
    p.id as permissionId, p.description, p."permissionKey", p.envs,
    pgs."groupType", pgs."isActiveOnly", pgs."isCustomRoleAllowed", pgs."isApiKeyAllowed"  from "Role" r 
    inner join "RolePermission" rp on r.id=rp."roleId" inner join "Permission" p on p.id=rp."permissionId" 
    inner join "PermissionGroupSetting" pgs on p.id = pgs."permissionId" 
    where "roleType" = 'GROUP_DEFAULT' and cast(pgs."groupType" as text) = ${groupType}`) as RoleQueryData[];
    return roles;
  }

  async getGroupRoles(groupId: number): Promise<Role[]> {
    const roleList = await this.getGroupRoleList(groupId);
    const getRolesWithMembershipCount = roleList.map(async (role) => {
      const membershipNumber = await this.getMembershipNumberByRoleAndGroup(role.id, groupId);
      return { ...role, membershipNumber };
    });
    return await Promise.all(getRolesWithMembershipCount);
  }

  private async getMembershipNumberByRoleAndGroup(roleId: number, groupId: number) {
    return await this.prisma.membership.count({
      where: {
        roleId: roleId,
        groupId: groupId,
      },
    });
  }

  private async checkIsCustomRolePermissionValid(permissionIdList: number[]): Promise<boolean> {
    for (const permissionId of permissionIdList) {
      const permission = await this.prisma.permission.findFirst({
        where: {
          id: permissionId,
          permissionType: PermissionType.GROUP,
          permissionGroupSetting: {
            every: {
              isCustomRoleAllowed: true,
            },
          },
        },
      });
      if (!permission) {
        throw new ApiException(ErrorCode.INVALID_PERMISSION);
      }
    }

    return true;
  }

  async createCustomRole(
    groupId: number,
    roleName: string,
    permissionIdList: number[],
  ): Promise<Role> {
    // const isShowGroupRoleAllow = await this.isEnableGroupRolePage(groupId);
    // if (!isShowGroupRoleAllow) {
    //   throw new ApiException(ErrorCode.INVALID_PERMISSION);
    // }
    const existedRole = await this.prisma.role.findFirst({
      where: {
        groupId,
        name: roleName,
        systemName: SystemName.GROUP_CUSTOM,
        roleType: RoleType.GROUP_CUSTOM,
      },
    });

    if (existedRole) {
      throw new ApiException(ErrorCode.ROLE_CONFLICT);
    }

    await this.checkIsCustomRolePermissionValid(permissionIdList);

    const result = await this.prisma.role.create({
      data: {
        name: roleName,
        groupId: groupId,
        roleType: RoleType.GROUP_CUSTOM,
        systemName: SystemName.GROUP_CUSTOM,
        permissions: {
          create: permissionIdList.map((permissionId) => ({
            permission: {
              connect: {
                id: permissionId,
              },
            },
          })),
        },
      },
    });
    return result;
  }

  async updateCustomRole(
    groupId: number,
    roleId: number,
    roleName: string,
    permissionIdList: number[],
  ): Promise<Role> {
    const role = await this.prisma.role.findUniqueOrThrow({
      where: {
        id: roleId,
      },
    });

    if (role.groupId !== groupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    await this.checkIsCustomRolePermissionValid(permissionIdList);

    const result = await this.prisma.role.update({
      where: {
        id: roleId,
      },
      data: {
        name: roleName,
        permissions: {
          upsert: permissionIdList.map((permissionId) => ({
            where: {
              roleId_permissionId: {
                roleId,
                permissionId,
              },
            },
            update: {
              updatedAt: new Date(),
            },
            create: {
              permission: {
                connect: {
                  id: permissionId,
                },
              },
            },
          })),
          deleteMany: {
            roleId,
            permissionId: {
              notIn: permissionIdList,
            },
          },
        },
      },
    });
    return result;
  }

  async deleteCustomRole(groupId: number, roleId: number): Promise<Role> {
    const membershipCount = await this.getMembershipNumberByRoleAndGroup(roleId, groupId);
    if (membershipCount > 0) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    const existingRole = await this.prisma.role.findUnique({
      select: { groupId: true, roleType: true },
      where: { id: roleId },
    });
    if (
      !existingRole ||
      existingRole.groupId !== groupId ||
      existingRole.roleType !== RoleType.GROUP_CUSTOM
    ) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    return await this.prisma.$transaction(async (tx) => {
      await tx.rolePermission.deleteMany({
        where: {
          roleId: roleId,
        },
      });
      return await tx.role.delete({
        where: {
          id: roleId,
        },
      });
    });
  }

  async getRolesByType(type: RoleType): Promise<Role[]> {
    const roles = await this.prisma.role.findMany({
      where: {
        roleType: type,
      },
    });
    return roles;
  }

  async getGroupRoleList(groupId: number): Promise<Role[]> {
    return await this.prisma.role.findMany({
      where: {
        OR: [
          {
            roleType: RoleType.GROUP_DEFAULT,
          },

          {
            roleType: RoleType.GROUP_CUSTOM,
            groupId: groupId,
          },
        ],
      },
      orderBy: {
        order: 'asc',
      },
    });
  }

  async getEditableMembershipRoleList(groupId: number, userScopes: string[]): Promise<Role[]> {
    const systemNames = userScopes
      .filter((scope) => minimatch(scope, `group-${groupId}:role-*`))
      .map((item) => item.replace(`group-${groupId}:role-`, '').toUpperCase() as SystemName);

    const isIncludeCustomRole = systemNames.includes(SystemName.GROUP_CUSTOM);

    return await this.prisma.role.findMany({
      where: {
        OR: [
          {
            roleType: RoleType.GROUP_DEFAULT,
            systemName: { in: systemNames },
          },
          ...(isIncludeCustomRole
            ? [
                {
                  roleType: RoleType.GROUP_CUSTOM,
                  groupId: groupId,
                },
              ]
            : []),
        ],
      },
      orderBy: {
        order: 'asc',
      },
    });
  }

  async isEnableGroupRolePage(groupId: number): Promise<boolean> {
    const featureFlag = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      groupId,
      FeatureFlagKey.ENABLE_GROUP_ROLE_PAGE,
    );
    return featureFlag.isEnabled;
  }
}
