import { Controller, Get, Param, ParseIntPipe, Query } from '@nestjs/common';
import { AuditLog } from '@prisma/client';
import { CursorPipe } from '../../pipes/cursor.pipe';
import { OptionalIntPipe } from '../../pipes/optional-int.pipe';
import { OrderByPipe } from '../../pipes/order-by.pipe';
import { WherePipe } from '../../pipes/where.pipe';
import { Expose } from '../../providers/prisma/prisma.interface';
import { Scopes } from '../auth/scope.decorator';
import { AuditLogsService } from './audit-logs.service';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@Controller('groups/:groupId/audit-logs')
@ApiTags('Group Audit Log')
@ApiBearerAuth('bearer-auth')
export class AuditLogGroupController {
  constructor(private auditLogsService: AuditLogsService) {}

  /** Get audit logs for a group */
  @Get()
  @Scopes('group-{groupId}:read-audit-log')
  async getAll(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<{ list: Expose<AuditLog>[]; count: number }> {
    const list = await this.auditLogsService.getAuditLogs({
      skip,
      take,
      orderBy,
      where: { ...where, group: { id: groupId } },
    });
    const count = await this.auditLogsService.getCount({ ...where, group: { id: groupId } });
    return { list, count };
  }
}
