import { Injectable, Logger } from '@nestjs/common';
import { CreateEntityLabelsDto, CreateLabelsDto } from './dto/create-labels.dto';
import { LabelEntityType, LabelType, Prisma } from '@prisma/client';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { UserRequest } from '../auth/auth.interface';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { UpdateLabelsDto } from './dto/patch-labels.dto';

@Injectable()
export class LabelsService {
  private logger = new Logger(LabelsService.name);

  constructor(private readonly prisma: PrismaService) {}

  public async create(createLabelsDto: CreateLabelsDto, userReq: UserRequest) {
    const createInput = {
      ...createLabelsDto,
      entityType: undefined,
      entityId: undefined,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as unknown as Prisma.LabelsCreateInput;
    const label = await this.prisma.$transaction(async (tx) => {
      const currentUser = { connect: { id: userReq.user.id } };
      createInput.createdLabelsUser = currentUser;
      createInput.updateLabelsUser = currentUser;
      const labelCreatedData = await tx.labels.create({
        data: createInput,
      });
      if (createLabelsDto.entityId && createLabelsDto.entityType) {
        await tx.entityLabels.create({
          data: {
            entityId: createLabelsDto.entityId,
            LabelEntityType: createLabelsDto.entityType,
            labelsId: labelCreatedData.id,
          },
        });
      }
      return labelCreatedData;
    });
    return label;
  }

  async findAll(
    take: number,
    skip: number,
    where: Record<string, string | number>,
    orderBy: Record<string, 'asc' | 'desc'>,
  ) {
    let newWhere = {};
    if (where?.['labelEntityType']) {
      if (!Object.keys(LabelEntityType).includes(where['labelEntityType'] as string)) {
        throw new ApiException(ErrorCode.WHERE_PIPE_FORMAT);
      }
      newWhere = { ...where };
      delete newWhere['labelEntityType'];
    } else {
      newWhere = { ...where };
    }
    const list = await this.prisma.labels.findMany({
      where: {
        ...newWhere,
        ...((where?.['labelEntityType']
          ? { EntityLabel: { some: { LabelEntityType: where['labelEntityType'] } } }
          : {}) as any),
      },
      take,
      skip,
      ...(orderBy ?? { orderBy: { seq: 'asc' } }),
    });
    const count = await this.prisma.labels.count({
      where: {
        ...newWhere,
        ...((where?.['labelEntityType']
          ? { EntityLabel: { some: { LabelEntityType: where['labelEntityType'] } } }
          : {}) as any),
      },
    });
    return { list, count };
  }

  public async updateLabels(id: number, updateDto: UpdateLabelsDto, req: UserRequest) {
    const update = await this.prisma.labels.update({
      where: { id },
      data: { ...updateDto, updatedAt: new Date(), updatedBy: req.user.id },
    });
    return update;
  }

  public async removeEntityLabels(entityLabelsId: number) {
    const entityLabel = await this.prisma.entityLabels.findFirst({
      include: {
        Labels: {
          include: {
            EntityLabel: true,
          },
        },
      },
      where: {
        id: entityLabelsId,
      },
    });
    if (!entityLabel) {
      throw new ApiException(ErrorCode.LABELS_NOT_FOUND);
    }
    await this.prisma.$transaction(async (tx) => {
      await tx.entityLabels.delete({ where: { id: entityLabelsId } });
      if (entityLabel.Labels.EntityLabel?.length == 1) {
        await tx.labels.delete({ where: { id: entityLabel.Labels.id } });
      }
    });
  }

  public async addEntityLabels(id: number, createEntityLabelsDto: CreateEntityLabelsDto) {
    const labels = await this.prisma.labels.findFirst({ where: { id } });
    if (!labels) {
      throw new ApiException(ErrorCode.LABELS_NOT_FOUND);
    }
    const isCheckLabelExist = await this.prisma.entityLabels.findFirst({
      where: {
        LabelEntityType: createEntityLabelsDto.entityType,
        entityId: createEntityLabelsDto.entityId,
        labelsId: id,
      },
    });
    if (isCheckLabelExist) {
      throw new ApiException(ErrorCode.LABELS_ALREADY_EXIST);
    }
    const entityLabel = await this.prisma.entityLabels.create({
      data: {
        LabelEntityType: createEntityLabelsDto.entityType,
        entityId: createEntityLabelsDto.entityId,
        labelsId: id,
      } as any,
    });
    return { ...labels, entityLabelsId: entityLabel.id };
  }

  public formatPatchLabelsDto(
    data: { entityLabels: any[] },
    userReq: UserRequest,
    entityType: LabelEntityType,
    entityId: number,
  ) {
    let needRemoveIds = [];
    let createEntityLabels = [];
    let createLabels = [];
    if (data.entityLabels && data.entityLabels.length > 0) {
      needRemoveIds = data.entityLabels
        .filter((item) => item.actionType == 'REMOVE')
        .map((item) => item.entityLabelsId);
      createLabels = data.entityLabels
        .filter((item) => item.actionType == 'CREATE' && !item.id)
        .map(
          (item) =>
            ({
              color: item.color,
              labelType: item.labelType,
              name: item.name,
              createdAt: new Date(),
              updatedAt: new Date(),
              createdLabelsUser: { connect: { id: userReq.user.id } },
              updateLabelsUser: { connect: { id: userReq.user.id } },
            }) as unknown as Prisma.LabelsCreateInput,
        );
      createEntityLabels = data.entityLabels
        .filter((item) => item.actionType == 'CREATE' && item.id)
        .map(
          (item) =>
            ({
              entityId: entityId,
              labelsId: item.id,
              LabelEntityType: entityType,
            }) as Prisma.EntityLabelsCreateManyInput,
        );
    }
    return { needRemoveIds, createEntityLabels, createLabels };
  }

  public async patchUpdateWithTransaction(
    patchUpdateData: {
      needRemoveIds: number[];
      createEntityLabels: Prisma.EntityLabelsCreateManyInput[];
      createLabels: Prisma.LabelsCreateInput[];
    },
    tx: Prisma.TransactionClient,
    LabelEntityType: LabelEntityType,
    entityId: number,
  ) {
    if (patchUpdateData.needRemoveIds.length > 0) {
      const linkedLabels = await tx.entityLabels.findMany({
        where: { id: { in: patchUpdateData.needRemoveIds } },
      });
      const linkedLabelsIds = linkedLabels.map((item) => item.labelsId);
      await tx.entityLabels.deleteMany({
        where: {
          id: { in: patchUpdateData.needRemoveIds },
        },
      });
      const labels = await tx.labels.findMany({
        include: {
          EntityLabel: { take: 1 },
        },
        where: { id: { in: linkedLabelsIds } },
      });
      const noNeedToRemoveLabelsId = labels
        .filter((item) => item.EntityLabel.length > 0)
        .map((item) => item.id);
      if (noNeedToRemoveLabelsId.length != linkedLabelsIds.length) {
        await tx.labels.deleteMany({
          where: {
            id: { in: linkedLabelsIds.filter((item) => !noNeedToRemoveLabelsId.includes(item)) },
          },
        });
      }
    }
    if (patchUpdateData.createLabels.length > 0) {
      for (const item of patchUpdateData.createLabels) {
        const createLabel = await tx.labels.create({ data: item });
        patchUpdateData.createEntityLabels.push({
          entityId,
          labelsId: createLabel.id,
          LabelEntityType,
        });
      }
    }
    if (patchUpdateData.createEntityLabels.length > 0) {
      await tx.entityLabels.createMany({
        data: patchUpdateData.createEntityLabels,
      });
    }
  }

  public async removeEntityLabelsWithEntity(
    tx: Prisma.TransactionClient,
    entityId: number,
    LabelEntityType: LabelEntityType,
  ) {
    const entityLabels = await tx.entityLabels.findMany({
      include: {
        Labels: {
          include: {
            EntityLabel: {
              take: 2,
            },
          },
        },
      },
      where: {
        entityId,
        LabelEntityType,
      },
    });
    const needRemoveLabels = entityLabels.filter((item) => item.Labels.EntityLabel.length == 1);
    await tx.entityLabels.deleteMany({
      where: {
        entityId,
        LabelEntityType,
      },
    });
    await tx.labels.deleteMany({
      where: {
        id: { in: needRemoveLabels.map((item) => item.labelsId) },
      },
    });
  }

  public formatLabelsAndCategoriesData(rootData: any & { EntityLabels: any[] }) {
    const data = {
      ...rootData,
      labels: rootData.EntityLabels.filter((item) => item.Labels.labelType === 'LABELS').map(
        (entityLabel) => ({
          ...entityLabel.Labels,
          entityLabelsId: entityLabel.id,
          labelId: entityLabel.Labels.id,
        }),
      ),
      categories: rootData.EntityLabels.filter(
        (item) => item.Labels.labelType === 'CATEGORIES',
      ).map((entityLabel) => ({
        ...entityLabel.Labels,
        entityLabelsId: entityLabel.id,
        labelId: entityLabel.Labels.id,
      })),
      EntityLabels: undefined,
    };
    return data;
  }

  public formatLabelsAndCategoriesFilter(
    LabelEntityType: LabelEntityType,
    where?: any & {
      labels?: { in: string[] } | number[];
      categories?: { in: string[] } | number[];
    },
  ) {
    const newWhere = {
      ...where,
      EntityLabels: undefined,
    };
    if (where && where?.labels) {
      newWhere.EntityLabels = {
        some: {
          LabelEntityType: LabelEntityType,
          labelsId: {
            in:
              where?.labels instanceof Array
                ? where.labels
                : where.labels.in.map((item) => parseInt(item)),
          },
          Labels: {
            labelType: 'LABELS',
          },
        },
      };
      delete (newWhere as any).labels;
    }
    if (where && where.categories) {
      const categoriesWhere = {
        LabelEntityType: LabelEntityType,
        labelsId: {
          in:
            where?.categories instanceof Array
              ? where.categories
              : where.categories.in.map((item) => parseInt(item)),
        },
        Labels: {
          labelType: 'CATEGORIES',
        },
      } as Prisma.EntityLabelsWhereInput;
      if (where?.labels && where?.labels?.in?.length != 0) {
        const labelsWhere = newWhere.EntityLabels.some;
        newWhere.EntityLabels = {
          some: {
            AND: [{ ...labelsWhere }, { ...categoriesWhere }],
          },
        };
      } else {
        newWhere.EntityLabels = {
          some: {
            ...categoriesWhere,
          },
        };
      }
      delete (newWhere as any).categories;
    }
    return newWhere;
  }

  /**
   * @description get the Label selected object and EntityLabels where condition
   * @param LabelEntityType
   * @returns
   */
  public getLabelsPrismaQuery(LabelEntityType: LabelEntityType) {
    return {
      EntityLabels: {
        where: {
          LabelEntityType,
        },
        orderBy: {
          seq: 'asc',
        },
        include: {
          Labels: {
            select: {
              id: true,
              name: true,
              color: true,
              desc: true,
              labelType: true,
            },
          },
        },
      },
    };
  }

  public async formatLabelsAndCategoriesByCondition(
    name: string,
    type: LabelType,
    actionType: string,
    messageTemplateId: number,
    color?: string,
  ) {
    const where: Prisma.LabelsWhereInput = {
      name: name,
      color: color ?? undefined,
      labelType: type,
    };
    const label = await this.prisma.labels.findFirst({
      include: {
        EntityLabel: true,
      },
      where,
    });
    if (actionType && actionType.toUpperCase() !== 'REMOVE') {
      return {
        name,
        labelType: type,
        color,
        actionType,
        id: label?.id ?? undefined,
      };
    } else {
      const entityLabel = label.EntityLabel.find(
        (item) =>
          item.LabelEntityType === LabelEntityType.MESSAGE_TEMPLATE &&
          item.entityId === messageTemplateId,
      );
      if (entityLabel) {
        return {
          actionType: 'REMOVE',
          entityLabelsId: entityLabel.id,
        };
      } else {
        return {};
      }
    }
  }
}
