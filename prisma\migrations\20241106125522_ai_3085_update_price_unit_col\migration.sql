-- CreateEnum
CREATE TYPE "ModelPriceType" AS ENUM ('COST_SUGGESTION', 'COST', 'PRICE', 'MARGIN');

-- CreateEnum
CREATE TYPE "ModelPriceSource" AS ENUM ('ADMIN', 'LITELLM', 'AZURE', 'SENSENOVA');

-- CreateEnum
CREATE TYPE "ModelPriceEventType" AS ENUM ('UPDATE', 'DELETE');

-- AlterTable
ALTER TABLE "ModelPriceUnit" ADD COLUMN     "displayName" TEXT NOT NULL,
ADD COLUMN     "metadata" JSONB NOT NULL;

-- CreateTable
CREATE TABLE "ModelPriceEvent" (
    "id" SERIAL NOT NULL,
    "modelPriceSource" "ModelPriceSource" NOT NULL,
    "modelPriceEventType" "ModelPriceEventType" NOT NULL,
    "modelPriceType" "ModelPriceType" NOT NULL,
    "modelPriceUnitId" INTEGER NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "metadata" JSONB NOT NULL,
    "createdBy" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ModelPriceEvent_pkey" PRIMARY KEY ("id")
);
