import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createCipheriv, createDecipheriv, createHash, createHmac, randomBytes } from 'crypto';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';

@Injectable()
export class SecretHashService {
  private embeddingSecret = this.configService.get<string>('secrets.embeddings');
  private aesKey;

  constructor(private configService: ConfigService) {
    this.aesKey = createHash('sha256')
      .update(String(this.configService.get<string>('secrets.aesKey')))
      .digest('base64')
      .substr(0, 32);
  }

  validateSign(message: string, sign: string): boolean {
    const hash = createHmac('sha256', this.embeddingSecret).update(message, 'utf8').digest('hex');

    if (hash != sign) throw new ApiException(ErrorCode.INVALID_CREDENTIALS);

    return true;
  }

  async decodeAES(secret: string) {
    const ivCiphertext = Buffer.from(secret, 'base64url');
    const iv = ivCiphertext.subarray(0, 16);
    const ciphertext = ivCiphertext.subarray(16);
    const cipher = createDecipheriv('aes-256-cbc', this.aesKey, iv);
    const decrypted = Buffer.concat([cipher.update(ciphertext), cipher.final()]);
    return decrypted.toString('utf-8');
  }

  async encodeAES(encodeText: string) {
    const iv = randomBytes(16);
    const cipher = createCipheriv('aes-256-cbc', this.aesKey, iv);
    const encrypted = Buffer.concat([iv, cipher.update(encodeText, 'utf8'), cipher.final()]);
    return encrypted.toString('base64url');
  }
}
